import { DynamicBreadcrumbs } from "@/components/layout/DynamicBreadcrumbs";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";

//Custom labels for specific breadcrumbs paths
const pathMap = {
  dashboard: "Dashboard",
  "admin/dashboard": "Admin Panel",
  "client/dashboard": "Client Portal",
  "writer/dashboard": "Writer Portal",
  //add more custom labels as needed
};

//Exclude paths that shouldn't appear in breadcrums
const excludePaths = [
  "api",
  "_next",
  "static",
  "public",
  "favicon.ico",
  "robots.txt",
  "sitemap.xml",
  "images",
  "fonts",
  "styles",
  "scripts",
  "assets",
];

export function SiteHeader() {
  return (
    <header className="flex h-(--header-height) shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-(--header-height)">
      <div className="flex w-full items-center gap-1 px-4 lg:gap-2 lg:px-6">
        <SidebarTrigger className="-ml-1" />
        <Separator
          orientation="vertical"
          className="mx-2 data-[orientation=vertical]:h-4"
        />
        {/* <h1 className="text-base font-medium">Documents</h1> */}
        <DynamicBreadcrumbs pathMap={pathMap} excludePaths={excludePaths} />
        <div className="ml-auto flex items-center gap-2">
          <Button variant="ghost" asChild size="sm" className="hidden sm:flex">
            {/* <a
              href="https://github.com/shadcn-ui/ui/tree/main/apps/v4/app/(examples)/dashboard"
              rel="noopener noreferrer"
              target="_blank"
              className="dark:text-foreground"
            >
              GitHub
            </a> */}
          </Button>
        </div>
      </div>
    </header>
  );
}
