import { useEffect, useState } from "react";
import { pricingService } from "@/lib/pricing-service";

type PriceConfig = {
  // Percentage the writer gets from the total price
  writerPercentage?: number;
  // Minimum price per page for writers
  minimumPricePerPage?: number;
};

export function useWriterPrice(
  adminPrice: number | undefined,
  pageCount: number = 1,
  config: PriceConfig = {}
) {
  const [writerCompensation, setWriterCompensation] = useState<number>(0);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!adminPrice || adminPrice <= 0) {
      setWriterCompensation(0);
      return;
    }

    const calculateCompensation = async () => {
      setLoading(true);
      try {
        const compensation = await pricingService.calculateWriterCompensation(
          adminPrice,
          pageCount
        );
        setWriterCompensation(compensation.finalAmount);
      } catch (error) {
        console.error("Error calculating writer compensation:", error);
        // Fallback to legacy calculation
        const {
          writerPercentage = 0.35,
          minimumPricePerPage = 3,
        } = config;

        const calculatedPrice = adminPrice * writerPercentage;
        const minimumPrice = pageCount * minimumPricePerPage;
        setWriterCompensation(Math.max(calculatedPrice, minimumPrice));
      } finally {
        setLoading(false);
      }
    };

    calculateCompensation();
  }, [adminPrice, pageCount, config]);

  return {
    writerPrice: Number(writerCompensation.toFixed(2)),
    adminPrice: adminPrice || 0,
    profit: Number((adminPrice || 0) - writerCompensation).toFixed(2),
    loading,
  };
}
