// components/footer/SocialIcons.tsx
import Link from 'next/link';
import { Facebook, Instagram, Twitter, Youtube } from 'lucide-react';
import { TiktokIcon } from './TiktokIcon';


export function SocialIcons() {
  return (
    <div className="flex items-center justify-evenly space-x-4">
      <Link href="https://facebook.com" aria-label="Facebook" className="footer-link transition-colors">
        <Facebook size={20} />
      </Link>
      <Link href="https://instagram.com" aria-label="Instagram" className="footer-link transition-colors">
        <Instagram size={20} />
      </Link>
      <Link href="https://twitter.com" aria-label="Twitter" className="footer-link transition-colors">
        <Twitter size={20} />
      </Link>
      <Link href="https://tiktok.com" aria-label="TikTok" className="footer-link transition-colors">
        <TiktokIcon size={20} />
      </Link>
      <Link href="https://youtube.com" aria-label="YouTube" className="footer-link transition-colors">
        <Youtube size={20} />
      </Link>
    </div>
  );
}