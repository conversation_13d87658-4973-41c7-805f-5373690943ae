// src/app/api/assignments/[id]/route.ts
import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import {
  apiSuccess,
  apiError,
  parseRequestBody,
  getCurrentUserId,
  getCurrentUserRole,
} from "@/lib/api-utils";
import { assignmentUpdateSchema } from "@/lib/validations";
import { AssignmentStatus, UserRole } from "@prisma/client";
import type { AssignmentResponse, BidResponse } from "@/types/api";
import { z } from "zod";

// CLAUDE ERROR: Adding extended interface for the response with bids
interface AssignmentResponseWithBids extends AssignmentResponse {
  bids?: BidResponse[];
  bidCount?: number;
}

// Get a single assignment by ID
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const currentUserId = await getCurrentUserId();
    const userRole = await getCurrentUserRole();

    if (!currentUserId) {
      return apiError("Authentication required", 401);
    }

    const { id: assignmentId } = await params;

    // Get assignment with related data
    const assignment = await prisma.assignment.findUnique({
      where: { id: assignmentId },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            email: true,
            accountId: true,
          },
        },
        assignedWriter: {
          select: {
            id: true,
            name: true,
            email: true,
            accountId: true, // Add this
            rating: true, // Add this
            competencies: true, // Add this
          },
        },
        bids: {
          select: {
            id: true,
            message: true,
            status: true,
            writerId: true,
            assignmentId: true,
            createdAt: true,
            updatedAt: true,
            writer: {
              select: {
                id: true,
                accountId: true, // Add this field
                name: true,
                email: true,
              },
            },
          },
        },
        _count: {
          select: { bids: true },
        },
      },
    });

    if (!assignment) {
      return apiError("Assignment not found", 404);
    }

    // Check permissions - who can access this assignment
    const canAccess =
      userRole === UserRole.ADMIN ||
      assignment.clientId === currentUserId ||
      assignment.assignedWriterId === currentUserId ||
      // Writers can see assignments open for bidding
      (userRole === UserRole.WRITER &&
        assignment.status === AssignmentStatus.POSTED);

    if (!canAccess) {
      return apiError("You don't have permission to view this assignment", 403);
    }

    // CLAUDE ERROR: Format the bids properly based on role
    const filteredBids =
      userRole === UserRole.ADMIN || assignment.clientId === currentUserId
        ? assignment.bids
        : assignment.bids.filter((bid) => bid.writerId === currentUserId);

    // Format the bids to match the BidResponse type
    const formattedBids: BidResponse[] = filteredBids.map((bid) => ({
      id: bid.id,
      message: bid.message,
      status: bid.status,
      writerId: bid.writerId,
      assignmentId: bid.assignmentId,
      createdAt: bid.createdAt.toISOString(),
      updatedAt: bid.updatedAt.toISOString(),
      writer: bid.writer,
    }));

    // Format the response based on role
    const formattedAssignment: AssignmentResponseWithBids = {
      ...assignment,
      estTime: assignment.estTime.toISOString(),
      createdAt: assignment.createdAt.toISOString(),
      updatedAt: assignment.updatedAt.toISOString(),
      // Only show client details to admins
      client: userRole === UserRole.ADMIN ? assignment.client : undefined,
      // Show assigned writer to admins, clients, and the assigned writer themselves
      assignedWriter: assignment.assignedWriter ?? undefined,
      // Add the formatted bids and count
      bids: formattedBids,
      bidCount: assignment._count.bids,
    };

    return apiSuccess(formattedAssignment);
  } catch (error) {
    console.error("Error fetching assignment:", error);
    return apiError("Failed to fetch assignment details", 500);
  }
}

// Update an assignment
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const currentUserId = await getCurrentUserId();
    const userRole = await getCurrentUserRole();

    if (!currentUserId) {
      return apiError("Authentication required", 401);
    }

    const { id: assignmentId } = await params;

    // Check if assignment exists
    const existingAssignment = await prisma.assignment.findUnique({
      where: { id: assignmentId },
    });

    if (!existingAssignment) {
      return apiError("Assignment not found", 404);
    }

    // Check permissions based on role and status
    let canUpdate = false;

    switch (userRole) {
      case UserRole.ADMIN:
        // Admins can update any assignment
        canUpdate = true;
        break;
      case UserRole.CLIENT:
        // CLAUDE ERROR: Fixed the type error with explicit status check for better type safety
        canUpdate =
          existingAssignment.clientId === currentUserId &&
          (existingAssignment.status === AssignmentStatus.DRAFT ||
            existingAssignment.status === AssignmentStatus.PENDING ||
            existingAssignment.status === AssignmentStatus.POSTED);
        break;
      case UserRole.WRITER:
        // Writers can't update assignments directly
        canUpdate = false;
        break;
    }

    if (!canUpdate) {
      return apiError(
        "You don't have permission to update this assignment",
        403
      );
    }

    // Parse and validate the request body
    const result = await parseRequestBody(req, assignmentUpdateSchema);

    // CLAUDE ERROR: Improved type safety for the parsed result
    if ("success" in result && !result.success) {
      return apiError(result.message, 400, result.errors);
    }

    // Now we can safely treat result as the valid parsed data
    const updateData = result as z.infer<typeof assignmentUpdateSchema>;

    // If client is trying to change assignedWriterId, prevent it
    if (
      userRole === UserRole.CLIENT &&
      updateData.assignedWriterId &&
      updateData.assignedWriterId !== existingAssignment.assignedWriterId
    ) {
      return apiError("Clients cannot assign writers directly", 403);
    }

    // If trying to update to ASSIGNED status but no assignedWriterId is provided
    if (
      updateData.status === AssignmentStatus.ASSIGNED &&
      !updateData.assignedWriterId &&
      !existingAssignment.assignedWriterId
    ) {
      return apiError(
        "Cannot mark as assigned without an assigned writer",
        400
      );
    }

    // Process dates if they are strings
    const processedUpdateData = { ...updateData };
    if (
      processedUpdateData.estTime &&
      typeof processedUpdateData.estTime === "string"
    ) {
      processedUpdateData.estTime = new Date(
        processedUpdateData.estTime
      ).toISOString();
    }

    // Update the assignment
    const updatedAssignmentRaw = await prisma.assignment.update({
      where: { id: assignmentId },
      data: updateData,
      include: {
        client: {
          select: {
            id: true,
            name: true,
            email: true,
            accountId: true,
          },
        },
        assignedWriter: {
          select: {
            id: true,
            name: true,
            email: true,
            accountId: true,
            rating: true,
            competencies: true,
          },
        },
      },
    });

    // Format the response
    const updatedAssignment: AssignmentResponse = {
      ...updatedAssignmentRaw,
      estTime: updatedAssignmentRaw.estTime.toISOString(),
      createdAt: updatedAssignmentRaw.createdAt.toISOString(),
      updatedAt: updatedAssignmentRaw.updatedAt.toISOString(),
      client:
        userRole === UserRole.ADMIN ? updatedAssignmentRaw.client : undefined,
      assignedWriter: updatedAssignmentRaw.assignedWriter ?? undefined,
    };

    return apiSuccess(updatedAssignment, "Assignment updated successfully");
  } catch (error) {
    console.error("Error updating assignment:", error);

    // Check for duplicate taskId
    if (error instanceof Error && error.message.includes("Unique constraint")) {
      return apiError("An assignment with this Task ID already exists", 409);
    }

    return apiError("Failed to update assignment", 500);
  }
}

// Delete an assignment
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const currentUserId = await getCurrentUserId();
    const userRole = await getCurrentUserRole();

    if (!currentUserId) {
      return apiError("Authentication required", 401);
    }

    const { id: assignmentId } = await params;

    // Check if assignment exists
    const existingAssignment = await prisma.assignment.findUnique({
      where: { id: assignmentId },
      include: {
        jobAssignment: true,
        bids: true,
      },
    });

    if (!existingAssignment) {
      return apiError("Assignment not found", 404);
    }

    // Check permissions based on role and status
    let canDelete = false;

    switch (userRole) {
      case UserRole.ADMIN:
        // Admins can delete any assignment
        canDelete = true;
        break;
      case UserRole.CLIENT:
        // CLAUDE ERROR: Fixed the type error with explicit status check for better type safety
        canDelete =
          existingAssignment.clientId === currentUserId &&
          (existingAssignment.status === AssignmentStatus.DRAFT ||
            existingAssignment.status === AssignmentStatus.PENDING ||
            existingAssignment.status === AssignmentStatus.POSTED);
        break;
      case UserRole.WRITER:
        // Writers can't delete assignments
        canDelete = false;
        break;
    }

    if (!canDelete) {
      return apiError(
        "You don't have permission to delete this assignment",
        403
      );
    }

    // Check if there are related job assignments
    if (
      existingAssignment.jobAssignment &&
      existingAssignment.jobAssignment.length > 0
    ) {
      return apiError(
        "Cannot delete an assignment with active job assignments. Please delete the job assignments first.",
        400
      );
    }

    // Delete related bids first
    await prisma.bid.deleteMany({
      where: { assignmentId },
    });

    // Delete the assignment
    await prisma.assignment.delete({
      where: { id: assignmentId },
    });

    return apiSuccess({ deleted: true }, "Assignment deleted successfully");
  } catch (error) {
    console.error("Error deleting assignment:", error);
    return apiError("Failed to delete assignment", 500);
  }
}
