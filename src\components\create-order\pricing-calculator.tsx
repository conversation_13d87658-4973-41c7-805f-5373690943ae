"use client";

import { useMemo } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Calculator, Clock, GraduationCap, FileText, DollarSign, Loader2 } from "lucide-react";
import {
  priorityOptions,
  academicLevelOptions,
  spacingOptions,
  type CreateOrderForm
} from "@/types/order";
import { usePricingRealtime } from "@/hooks/use-pricing-realtime";
import { AcademicLevel, Priority, Spacing } from "@prisma/client";

interface PricingCalculatorProps {
  formData: Partial<CreateOrderForm>;
  className?: string;
}



export function PricingCalculator({ formData, className = "" }: PricingCalculatorProps) {
  // Check if we have valid form data
  const hasValidData = formData.pageCount && formData.pageCount > 0 &&
                      formData.academicLevel && formData.priority && formData.spacing;

  // Use real-time pricing hook
  const { pricingData, loading, error, lastUpdated } = usePricingRealtime({
    academicLevel: (formData.academicLevel as AcademicLevel) || AcademicLevel.UNDERGRADUATE,
    priority: (formData.priority as Priority) || Priority.MEDIUM,
    spacing: (formData.spacing as Spacing) || Spacing.DOUBLE,
    pageCount: formData.pageCount || 1,
  });

  const calculatedPrice = hasValidData ? (pricingData?.finalPrice || 0) : 0;
  const priceBreakdown = hasValidData ? pricingData?.priceBreakdown : null;



  // Generate breakdown display from API data
  const breakdownItems = useMemo(() => {
    if (!priceBreakdown || !formData.pageCount) return [];

    const items = [];

    items.push({
      label: `Base Price (${formData.pageCount} pages)`,
      value: priceBreakdown.basePrice,
      multiplier: 1,
    });

    if (priceBreakdown.academicLevelMultiplier !== 1) {
      const levelOption = academicLevelOptions.find(opt => opt.value === formData.academicLevel);
      items.push({
        label: `Academic Level (${levelOption?.label || 'Unknown'})`,
        value: priceBreakdown.basePrice * (priceBreakdown.academicLevelMultiplier - 1),
        multiplier: priceBreakdown.academicLevelMultiplier,
      });
    }

    if (priceBreakdown.priorityMultiplier !== 1) {
      const priorityOption = priorityOptions.find(opt => opt.value === formData.priority);
      items.push({
        label: `Priority (${priorityOption?.label || 'Unknown'})`,
        value: priceBreakdown.basePrice * priceBreakdown.academicLevelMultiplier * (priceBreakdown.priorityMultiplier - 1),
        multiplier: priceBreakdown.priorityMultiplier,
      });
    }

    if (priceBreakdown.spacingMultiplier !== 1) {
      const spacingOption = spacingOptions.find(opt => opt.value === formData.spacing);
      items.push({
        label: `Spacing (${spacingOption?.label || 'Unknown'})`,
        value: priceBreakdown.subtotal - (priceBreakdown.basePrice * priceBreakdown.academicLevelMultiplier * priceBreakdown.priorityMultiplier),
        multiplier: priceBreakdown.spacingMultiplier,
      });
    }

    return items;
  }, [priceBreakdown, formData.pageCount, formData.academicLevel, formData.priority, formData.spacing]);

  const getPriorityInfo = () => {
    if (!formData.priority) return null;
    return priorityOptions.find(opt => opt.value === formData.priority);
  };

  const getAcademicLevelInfo = () => {
    if (!formData.academicLevel) return null;
    return academicLevelOptions.find(opt => opt.value === formData.academicLevel);
  };

  const priorityInfo = getPriorityInfo();
  const academicLevelInfo = getAcademicLevelInfo();

  return (
    <Card className={`sticky top-4 ${className}`}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Calculator className="w-5 h-5 text-primary" />
          Price Calculator
          {lastUpdated && (
            <Badge variant="secondary" className="text-xs ml-auto">
              Updated {lastUpdated.toLocaleTimeString()}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-3">
          <div className="text-center p-3 bg-muted/50 rounded-lg">
            <FileText className="w-4 h-4 mx-auto mb-1 text-muted-foreground" />
            <div className="text-sm font-medium">{formData.pageCount || 0}</div>
            <div className="text-xs text-muted-foreground">Pages</div>
          </div>
          <div className="text-center p-3 bg-muted/50 rounded-lg">
            <DollarSign className="w-4 h-4 mx-auto mb-1 text-muted-foreground" />
            <div className="text-sm font-medium">${calculatedPrice.toFixed(2)}</div>
            <div className="text-xs text-muted-foreground">Total</div>
          </div>
        </div>

        {/* Priority Badge */}
        {priorityInfo && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm">Priority:</span>
            </div>
            <Badge className={priorityInfo.color}>
              {priorityInfo.label}
            </Badge>
          </div>
        )}

        {/* Academic Level Badge */}
        {academicLevelInfo && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <GraduationCap className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm">Level:</span>
            </div>
            <Badge variant="secondary">
              {academicLevelInfo.label}
            </Badge>
          </div>
        )}

        <Separator />

        {/* Price Breakdown */}
        {breakdownItems.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-muted-foreground">Price Breakdown</h4>
            {breakdownItems.map((item, index) => (
              <div key={index} className="flex justify-between items-center text-sm">
                <span className="text-muted-foreground">{item.label}</span>
                <span className={item.value >= 0 ? "text-foreground" : "text-green-600"}>
                  {item.value >= 0 ? '+' : ''}${Math.abs(item.value).toFixed(2)}
                </span>
              </div>
            ))}
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-4">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
            <span className="ml-2 text-sm text-muted-foreground">Calculating...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="text-sm text-red-700">
              Failed to calculate price. Please try again.
            </div>
          </div>
        )}

        <Separator />

        {/* Total Price */}
        <div className="flex justify-between items-center text-lg font-semibold">
          <span>Total Price:</span>
          <span className="text-primary">${calculatedPrice.toFixed(2)}</span>
        </div>

        {/* Delivery Time */}
        {priorityInfo && (
          <div className="p-3 bg-primary/5 rounded-lg border border-primary/20">
            <div className="flex items-center gap-2 text-sm">
              <Clock className="w-4 h-4 text-primary" />
              <span className="font-medium">Delivery:</span>
              <span className="text-primary">{priorityInfo.description}</span>
            </div>
          </div>
        )}

        {/* Pricing Notes */}
        <div className="text-xs text-muted-foreground space-y-1">
          <p>• Prices are calculated per page (275 words)</p>
          <p>• Final price may vary based on complexity</p>
          <p>• All prices are in USD</p>
        </div>

        {/* Spacing Info */}
        {formData.spacing === "SINGLE" && (
          <div className="p-2 bg-orange-50 border border-orange-200 rounded-lg">
            <div className="flex items-center gap-2 text-sm text-orange-700">
              <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                2x CONTENT
              </Badge>
              <span>Single spacing has twice the content</span>
            </div>
          </div>
        )}
        {formData.spacing === "DOUBLE" && (
          <div className="p-2 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-2 text-sm text-blue-700">
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                STANDARD
              </Badge>
              <span>Double spacing - standard format</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
