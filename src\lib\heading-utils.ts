// Utility functions for handling blog headings and generating IDs

/**
 * Generates a URL-friendly ID from heading text
 * Handles special characters, symbols, and punctuation properly
 */
export function generateHeadingId(text: string, fallbackIndex?: number): string {
  const id = text
    .toLowerCase()
    .trim()
    // Replace common symbols and punctuation with words or remove them
    .replace(/\(/g, "-") // Replace opening parenthesis with dash
    .replace(/\)/g, "-") // Replace closing parenthesis with dash
    .replace(/\[/g, "-") // Replace opening bracket with dash
    .replace(/\]/g, "-") // Replace closing bracket with dash
    .replace(/\{/g, "-") // Replace opening brace with dash
    .replace(/\}/g, "-") // Replace closing brace with dash
    .replace(/['"]/g, "") // Remove quotes
    .replace(/[&]/g, "and") // Replace & with "and"
    .replace(/[%]/g, "percent") // Replace % with "percent"
    .replace(/[#]/g, "number") // Replace # with "number"
    .replace(/[+]/g, "plus") // Replace + with "plus"
    .replace(/[=]/g, "equals") // Replace = with "equals"
    .replace(/[<>]/g, "") // Remove angle brackets
    .replace(/[!@$^*|\\/:;?.,]/g, "") // Remove other special characters
    .replace(/\s+/g, "-") // Replace spaces with hyphens
    .replace(/-+/g, "-") // Replace multiple consecutive dashes with single dash
    .replace(/^-|-$/g, ""); // Remove leading/trailing dashes

  // Return the generated ID or fallback
  return id || (fallbackIndex !== undefined ? `heading-${fallbackIndex}` : "heading");
}

/**
 * Extracts headings from a DOM element and generates consistent IDs
 */
export function extractHeadings(container: Element): Array<{ id: string; text: string; level: number }> {
  const headingElements = container.querySelectorAll("h1, h2, h3, h4, h5, h6");
  
  // First pass: Generate and assign IDs
  headingElements.forEach((heading, index) => {
    if (!heading.id) {
      const text = heading.textContent || "";
      const id = generateHeadingId(text, index);
      heading.id = id;
      
      // Debug log to see the transformation
      console.log(`Generated ID for "${text}" -> "${id}"`);
    }
  });

  // Second pass: Create the headings array
  return Array.from(headingElements).map((heading) => ({
    id: heading.id,
    text: heading.textContent || "",
    level: parseInt(heading.tagName.charAt(1)),
  }));
}

/**
 * Test function to preview what ID would be generated for a given text
 */
export function previewHeadingId(text: string): string {
  return generateHeadingId(text);
}
