"use client";

import * as React from "react";
import {
  Book<PERSON>pen,
  Bo<PERSON>,
  // Frame,
  GalleryVerticalEnd,
  // Map,
  // PieChart,
  Settings2,
  SquareTerminal,
} from "lucide-react";

import { NavMain } from "@/components/dashboard/writer/nav-main";
import { NavUser } from "@/components/dashboard/writer/nav-user";
import { TeamSwitcher } from "@/components/dashboard/writer/team-switcher";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";
import { useCurrentUserId } from "@/hooks/use-session-user-id";
import { useCompanyInfo } from "@/hooks/use-company-info";




export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
   const [userData, setUserData] = React.useState({
      name: "",
      email: "",
      avatar: "/avatars/shadcn.jpg", // fallback image
    });
  
    // const { userId, loading, error } = useCurrentUserId();
    const { userId } = useCurrentUserId();
    const { companyInfo } = useCompanyInfo();
  
    React.useEffect(() => {
      async function fetchUserData() {
        if (!userId) return;
  
        try {
          const response = await fetch(`/api/users/writers/${userId}`);
          if (!response.ok) throw new Error("Failed to fetch user data");
  
          const data = await response.json();
          if (data.success && data.data) {
            setUserData({
              name: data.data.name || "",
              email: data.data.email || "",
              avatar: data.data.imageUrl || "/avatars/shadcn.jpg",
            });
          }
        } catch (err) {
          console.error("Error fetching user data:", err);
        }
      }
  
      fetchUserData();
    }, [userId]);


    //data 
    const data = {
      user: userData,     
      teams: [
        {
          name: companyInfo?.companyName || "Essay App",
          logo: GalleryVerticalEnd,
          plan: "Enterprise",
        },
      ],
      navMain: [
        {
          title: "Assignment List",
          url: "#",
          icon: SquareTerminal,
          isActive: true,
          items: [
            {
              title: "Available",
              url: "/writer/orders",
            },
            {
              title: "Assigned",
              url: "/writer/assigned-orders",
            },
            {
              title: "Completed",
              url: "/writer/completed-orders",
            },
          ],
        },
        {
          title: "Payments",
          url: "#",
          icon: Bot,
          items: [
            {
              title: "Pending",
              url: "/writer/payments/pending",
            },
            {
              title: "Approved",
              url: "/writer/payments/approved",
            },
          ],
        },
        {
          title: "Guidelines",
          url: "#",
          icon: BookOpen,
          items: [
            {
              title: "Introduction",
              url: "/writer/guidelines",
            },
            {
              title: "Get Started",
              url: "/writer/guidelines",
            },
            {
              title: "Terms of Service",
              url: "/writer/guidelines",
            },
           
          ],
        },
        {
          title: "Settings",
          url: "#",
          icon: Settings2,
          items: [
            {
              title: "Update Profile",
              url: "/writer/dashboard",
            },
            // {
            //   title: "Update Billing",
            //   url: "/writer/billing",
            // },
          ],
        },
      ],
    };
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader className="flex-shrink-0">
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
