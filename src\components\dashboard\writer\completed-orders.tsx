// src/components/dashaboard/writer/completed-orders.tsx

"use client";

import React, { useState, useCallback, Suspense } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/data-table/data-table";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { DataTableToolbar } from "@/components/data-table/data-table-toolbar";
import { DataTableFacetedFilter } from "@/components/data-table/data-table-faceted-filter";
import { DataTableDateFilter } from "@/components/data-table/data-table-date-filter";
import { DataTableSliderFilter } from "@/components/data-table/data-table-slider-filter";
import { useDataTable } from "@/hooks/use-data-table";
import { ColumnDef } from "@tanstack/react-table";
import {
  CheckCircle,
  Circle,
  Clock,
  XCircle,
  AlertCircle,
  FileText,
  <PERSON>r,
  <PERSON>,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {
  calculateHoursUntilDeadline,
  formatHoursDisplay,
  getDeadlineColorClass,
} from "@/lib/est-time-utils";
import { useWriterPrice } from "@/hooks/use-writer-price";

// Original Assignment type (matches your API response)
type Assignment = {
  id: string;
  taskId: string;
  title: string;
  description: string;
  assignmentType:
    | "ARTICLE_REVIEW"
    | "BOOK_REVIEW"
    | "CASE_STUDY"
    | "DISCUSSION"
    | "DISSERTATION"
    | "ESSAY"
    | "LAB_REPORT"
    | "LITERATURE_REVIEW"
    | "PERSONAL_STATEMENT"
    | "REFLECTION_PAPER"
    | "RESEARCH_PAPER"
    | "TERM_PAPER"
    | "THESIS"
    | "OTHER";
  subject: string;
  service: string;
  pageCount: number;
  priority: "LOW" | "MEDIUM" | "HIGH";
  academicLevel:
    | "HIGH_SCHOOL"
    | "UNDERGRADUATE"
    | "MASTERS"
    | "PHD"
    | "PROFESSIONAL";
  spacing: "SINGLE" | "DOUBLE";
  languageStyle: "ENGLISH_US" | "ENGLISH_UK" | "ENGLISH_AU" | "OTHER";
  formatStyle: "APA" | "MLA" | "CHICAGO" | "HARVARD" | "IEEE" | "OTHER";
  numSources: number;
  guidelines?: string;
  estTime: string; // ISO date string from database
  clientId: string;
  assignedWriterId?: string;
  status:
    | "DRAFT"
    | "PENDING"
    | "POSTED"
    | "ASSIGNED"
    | "COMPLETED"
    | "REVISION"
    | "CANCELLED";
  createdAt: string; // ISO string from API
  updatedAt: string; // ISO string from API
  client?: {
    id: string;
    name: string | null;
    email: string;
  };
  assignedWriter?: {
    id: string;
    name: string | null;
    email: string;
  };
  price?: number;
};

// Enhanced Assignment type with computed hours for display
type EnhancedAssignment = Assignment & {
  hoursRemaining: number; // Computed field for display and filtering
  price?: number;
};

function WriterCompletedOrdersContent() {
  const router = useRouter();
  const [rawAssignments, setRawAssignments] = useState<Assignment[]>([]);
  const [enhancedAssignments, setEnhancedAssignments] = useState<
    EnhancedAssignment[]
  >([]);
  const [totalCount, setTotalCount] = useState(0);

  // Function to enhance assignments with calculated hours
  const enhanceAssignments = useCallback((assignments: Assignment[]) => {
    return assignments.map((assignment): EnhancedAssignment => {
      const hoursRemaining = calculateHoursUntilDeadline(
        assignment.estTime,
        assignment.createdAt
      );

      return {
        ...assignment,
        hoursRemaining,
      };
    });
  }, []);

  const handleDataChange = useCallback(
    (newAssignments: Assignment[], newTotalCount: number) => {
      setRawAssignments(newAssignments);
      setEnhancedAssignments(enhanceAssignments(newAssignments));
      setTotalCount(newTotalCount);
    },
    [enhanceAssignments]
  );

  // Handle row click navigation
  const handleRowClick = useCallback(
    (orderId: string) => {
      router.push(`/order/${orderId}`);
    },
    [router]
  );

  // Refresh enhanced assignments periodically to update countdown
  React.useEffect(() => {
    const interval = setInterval(() => {
      if (rawAssignments.length > 0) {
        setEnhancedAssignments(enhanceAssignments(rawAssignments));
      }
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [rawAssignments, enhanceAssignments]);

  const PriceCell = ({ price, pageCount }: { price: number | undefined; pageCount?: number }) => {
    const { writerPrice, loading } = useWriterPrice(price, pageCount || 1);
    return (
      <div className="text-right font-medium">
        {loading ? (
          <div className="animate-pulse bg-muted h-4 w-12 rounded"></div>
        ) : (
          `$${writerPrice}`
        )}
      </div>
    );
  };

  const columns: ColumnDef<EnhancedAssignment>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <input
          type="checkbox"
          className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e) => table.toggleAllPageRowsSelected(!!e.target.checked)}
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
          checked={row.getIsSelected()}
          onChange={(e) => row.toggleSelected(!!e.target.checked)}
          onClick={(e) => e.stopPropagation()} // Prevent row click when clicking checkbox
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "taskId",
      header: () => <span>Task</span>,
      cell: ({ row }) => (
        <div className="flex items-center">
          <span>{row.original.taskId}</span>
        </div>
      ),
      enableSorting: true,
      enableHiding: false,
    },
    {
      accessorKey: "title",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Title" />
      ),
      cell: ({ row }) => {
        const assignmentType = row.original.assignmentType;

        const getTypeDisplay = (type: string) => {
          switch (type) {
            case "RESEARCH_PAPER":
              return "research paper";
            case "TERM_PAPER":
              return "term paper";
            case "BOOK_REVIEW":
              return "book review";
            case "ARTICLE_REVIEW":
              return "article review";
            case "CASE_STUDY":
              return "case study";
            case "DISCUSSION":
              return "discussion";
            case "LAB_REPORT":
              return "lab report";
            case "LITERATURE_REVIEW":
              return "literature review";
            case "PERSONAL_STATEMENT":
              return "personal statement";
            case "REFLECTION_PAPER":
              return "reflection paper";
            default:
              return type.toLowerCase().replace("_", " ");
          }
        };
        const getTypeColor = (type: string) => {
          switch (type) {
            case "ESSAY":
              return "bg-blue-50 text-blue-700 border-blue-200";
            case "RESEARCH_PAPER":
              return "bg-green-50 text-green-700 border-green-200";
            case "TERM_PAPER":
              return "bg-teal-50 text-teal-700 border-teal-200";
            case "DISSERTATION":
              return "bg-purple-50 text-purple-700 border-purple-200";
            case "THESIS":
              return "bg-violet-50 text-violet-700 border-violet-200";
            case "BOOK_REVIEW":
              return "bg-orange-50 text-orange-700 border-orange-200";
            case "ARTICLE_REVIEW":
              return "bg-rose-50 text-rose-700 border-rose-200";
            case "CASE_STUDY":
              return "bg-red-50 text-red-700 border-red-200";
            case "LAB_REPORT":
              return "bg-pink-50 text-pink-700 border-pink-200";
            case "DISCUSSION":
              return "bg-yellow-50 text-yellow-700 border-yellow-200";
            case "LITERATURE_REVIEW":
              return "bg-emerald-50 text-emerald-700 border-emerald-200";
            case "PERSONAL_STATEMENT":
              return "bg-indigo-50 text-indigo-700 border-indigo-200";
            case "REFLECTION_PAPER":
              return "bg-amber-50 text-amber-700 border-amber-200";
            case "OTHER":
              return "bg-gray-50 text-gray-700 border-gray-200";
            default:
              return "bg-slate-50 text-slate-700 border-slate-200";
          }
        };

        return (
          <div className="flex items-center space-x-2">
            <Badge
              variant="outline"
              className={`${getTypeColor(assignmentType)} text-xs`}
            >
              {getTypeDisplay(assignmentType)}
            </Badge>
            <span className="max-w-[500px] truncate">{row.original.title}</span>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: false,
    },
    {
      accessorKey: "subject",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Subject" />
      ),
      cell: ({ row }) => (
        <span className="truncate">{row.original.subject}</span>
      ),
      enableSorting: true,
      enableHiding: false,
    },
    {
      accessorKey: "service",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Service" />
      ),
      cell: ({ row }) => (
        <span className="truncate">{row.original.service}</span>
      ),
      enableSorting: true,
      enableHiding: false,
    },
    {
      accessorKey: "pageCount",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Pages" />
      ),
      cell: ({ row }) => (
        <div className="text-right">{row.original.pageCount}</div>
      ),
      enableSorting: true,
      enableHiding: false,
    },
    {
      accessorKey: "price",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Price" />
      ),
      cell: ({ row }) => <PriceCell price={row.original.price} pageCount={row.original.pageCount} />,
      meta: {
        label: "Writer Price",
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "academicLevel",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Academic Level" />
      ),
      cell: ({ row }) => {
        const level = row.original.academicLevel;
        const getLevelDisplay = (level: string) => {
          switch (level) {
            case "HIGH_SCHOOL":
              return "High School";
            case "UNDERGRADUATE":
              return "Undergraduate";
            case "MASTERS":
              return "Masters";
            case "PHD":
              return "PhD";
            case "PROFESSIONAL":
              return "Professional";
            default:
              return level;
          }
        };
        return (
          <Badge variant="outline" className="text-xs">
            {getLevelDisplay(level)}
          </Badge>
        );
      },
      meta: {
        label: "Academic Level",
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "spacing",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Spacing" />
      ),
      cell: ({ row }) => (
        <span className="text-sm">{row.original.spacing.toLowerCase()}</span>
      ),
      meta: {
        label: "Spacing",
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "formatStyle",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Format" />
      ),
      cell: ({ row }) => (
        <Badge variant="outline" className="text-xs">
          {row.original.formatStyle}
        </Badge>
      ),
      meta: {
        label: "Format Style",
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "numSources",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Sources" />
      ),
      cell: ({ row }) => (
        <div className="text-right">{row.original.numSources}</div>
      ),
      meta: {
        label: "Num of Sources",
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Status" />
      ),
      cell: ({ row }) => {
        const status = row.original.status;
        return (
          <div className="flex items-center">
            {status === "POSTED" && (
              <Badge
                variant="outline"
                className="flex items-center gap-1 rounded-md border-gray-200 bg-gray-50 text-gray-700"
              >
                <Circle className="h-3.5 w-3.5" />
                <span>Posted</span>
              </Badge>
            )}
            {status === "PENDING" && (
              <Badge
                variant="outline"
                className="flex items-center gap-1 rounded-md border-blue-200 bg-blue-50 text-blue-700"
              >
                <Clock className="h-3.5 w-3.5" />
                <span>In-Progress</span>
              </Badge>
            )}
            {status === "ASSIGNED" && (
              <Badge
                variant="outline"
                className="flex items-center gap-1 rounded-md border-yellow-200 bg-yellow-50 text-yellow-700"
              >
                <User className="h-3.5 w-3.5" />
                <span>Assigned</span>
              </Badge>
            )}
            {status === "COMPLETED" && (
              <Badge
                variant="outline"
                className="flex items-center gap-1 rounded-md border-green-200 bg-green-50 text-green-700"
              >
                <CheckCircle className="h-3.5 w-3.5" />
                <span>Completed</span>
              </Badge>
            )}
            {status === "REVISION" && (
              <Badge
                variant="outline"
                className="flex items-center gap-1 rounded-md border-orange-200 bg-orange-50 text-orange-700"
              >
                <AlertCircle className="h-3.5 w-3.5" />
                <span>Revision</span>
              </Badge>
            )}
            {status === "CANCELLED" && (
              <Badge
                variant="outline"
                className="flex items-center gap-1 rounded-md border-red-200 bg-red-50 text-red-700"
              >
                <XCircle className="h-3.5 w-3.5" />
                <span>Cancelled</span>
              </Badge>
            )}
            {status === "DRAFT" && (
              <Badge
                variant="outline"
                className="flex items-center gap-1 rounded-md border-gray-200 bg-gray-50 text-gray-700"
              >
                <FileText className="h-3.5 w-3.5" />
                <span>Draft</span>
              </Badge>
            )}
          </div>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
      meta: {
        label: "Status",
      },
      enableSorting: true,
      enableHiding: true,
      enableColumnFilter: true,
    },
    {
      accessorKey: "priority",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Priority" />
      ),
      cell: ({ row }) => {
        const priority = row.original.priority;
        return (
          <div className="flex items-center">
            {priority === "LOW" && (
              <Badge
                variant="outline"
                className="flex items-center gap-1 rounded-md border-gray-200 bg-gray-50 text-gray-700"
              >
                <span className="inline-block h-2 w-2">↓</span>
                <span>Low</span>
              </Badge>
            )}
            {priority === "MEDIUM" && (
              <Badge
                variant="outline"
                className="flex items-center gap-1 rounded-md border-blue-200 bg-blue-50 text-blue-700"
              >
                <span className="inline-block h-2 w-2">→</span>
                <span>Medium</span>
              </Badge>
            )}
            {priority === "HIGH" && (
              <Badge
                variant="outline"
                className="flex items-center gap-1 rounded-md border-red-200 bg-red-50 text-red-700"
              >
                <span className="inline-block h-2 w-2">↑</span>
                <span>High</span>
              </Badge>
            )}
          </div>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
      meta: {
        label: "Priority",
      },
      enableSorting: true,
      enableHiding: true,
      enableColumnFilter: true,
    },
    {
      accessorKey: "hoursRemaining",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Est. Time" />
      ),
      cell: ({ row }) => {
        const hoursRemaining = row.original.hoursRemaining;
        const colorClass = getDeadlineColorClass(hoursRemaining);

        return (
          <div className={`text-right font-medium ${colorClass}`}>
            {hoursRemaining <= 0 ? (
              <span className="flex items-center justify-end gap-1">
                <AlertCircle className="h-3 w-3" />
                Overdue
              </span>
            ) : (
              formatHoursDisplay(hoursRemaining)
            )}
          </div>
        );
      },
      enableSorting: true,
      enableHiding: false,
      enableColumnFilter: true,
      meta: {
        range: [0, 48], // 0 to 48 hours (2 days) - for urgent assignments only
        unit: "hrs",
      },
      filterFn: (row, id, value) => {
        if (!Array.isArray(value) || value.length !== 2) return true;
        const rowValue = row.getValue(id) as number;
        const [min, max] = value as [number, number];
        return rowValue >= min && rowValue <= max;
      },
      sortingFn: (rowA, rowB) => {
        const a = rowA.original.hoursRemaining;
        const b = rowB.original.hoursRemaining;
        // Sort by urgency: overdue first (negative values), then ascending hours
        if (a <= 0 && b > 0) return -1;
        if (a > 0 && b <= 0) return 1;
        if (a <= 0 && b <= 0) return b - a; // More overdue first
        return a - b; // Less time remaining first
      },
    },
    {
      accessorKey: "updatedAt",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Updated At" />
      ),
      cell: ({ row }) => {
        const updatedAt = new Date(row.original.updatedAt);
        return <div>{updatedAt.toLocaleDateString("en-US")}</div>;
      },
      meta: {
        label: "Updated At",
      },
      enableSorting: true,
      enableHiding: true,
      enableColumnFilter: true,
      filterFn: (row, id, value) => {
        if (!Array.isArray(value) || value.length !== 2) return true;

        if (!value[0] && !value[1]) return true;

        const rowValue = row.getValue(id);
        const rowDate = new Date(rowValue as string);

        let startDate: Date | undefined;
        if (value[0]) {
          startDate = new Date(value[0]);
          startDate.setHours(0, 0, 0, 0);
        }

        let endDate: Date | undefined;
        if (value[1]) {
          endDate = new Date(value[1]);
          endDate.setHours(23, 59, 59, 999);
        }

        if (startDate && endDate) {
          return rowDate >= startDate && rowDate <= endDate;
        } else if (startDate) {
          return rowDate >= startDate;
        } else if (endDate) {
          return rowDate <= endDate;
        }

        return true;
      },
    },
    {
      id: "actions",
      cell: ({ row }) => (
        <div className="text-right" onClick={(e) => e.stopPropagation()}>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleRowClick(row.original.id)}
            className="h-8 w-8 p-0"
          >
            <Eye className="h-4 w-4" />
            <span className="sr-only">View details</span>
          </Button>
        </div>
      ),
    },
  ];

  const { table, loading, error, refetch } = useDataTable({
    data: enhancedAssignments,
    columns,
    pageCount: Math.ceil(totalCount / 10),
    manualPagination: true,
    manualSorting: false,
    manualFiltering: false,
    // apiUrl: "/api/assignments/assigned",
    // In your useDataTable hook configuration
    apiUrl: "/api/assignments/assigned?statusFilter=COMPLETED",
    onDataChange: handleDataChange,
    initialState: {
      pagination: {
        pageIndex: 0,
        pageSize: 10,
      },
      sorting: [
        {
          id: "updatedAt",
          desc: true, // Most recently updated first
        },
      ],
    },
  });

  const statusOptions = React.useMemo(
    () => [
      {
        label: "Posted",
        value: "POSTED",
        icon: Circle,
        count: enhancedAssignments.filter(
          (assignment) => assignment.status === "POSTED"
        ).length,
      },
      {
        label: "In-Progress",
        value: "PENDING",
        icon: Clock,
        count: enhancedAssignments.filter(
          (assignment) => assignment.status === "PENDING"
        ).length,
      },
      {
        label: "Assigned",
        value: "ASSIGNED",
        icon: User,
        count: enhancedAssignments.filter(
          (assignment) => assignment.status === "ASSIGNED"
        ).length,
      },
      {
        label: "Completed",
        value: "COMPLETED",
        icon: CheckCircle,
        count: enhancedAssignments.filter(
          (assignment) => assignment.status === "COMPLETED"
        ).length,
      },
      {
        label: "Revision",
        value: "REVISION",
        icon: AlertCircle,
        count: enhancedAssignments.filter(
          (assignment) => assignment.status === "REVISION"
        ).length,
      },
      {
        label: "Cancelled",
        value: "CANCELLED",
        icon: XCircle,
        count: enhancedAssignments.filter(
          (assignment) => assignment.status === "CANCELLED"
        ).length,
      },
      {
        label: "Draft",
        value: "DRAFT",
        icon: FileText,
        count: enhancedAssignments.filter(
          (assignment) => assignment.status === "DRAFT"
        ).length,
      },
    ],
    [enhancedAssignments]
  );

  const priorityOptions = React.useMemo(
    () => [
      {
        label: "Low",
        value: "LOW",
        icon: () => <span className="inline-block h-2 w-2">↓</span>,
        count: enhancedAssignments.filter(
          (assignment) => assignment.priority === "LOW"
        ).length,
      },
      {
        label: "Medium",
        value: "MEDIUM",
        icon: () => <span className="inline-block h-2 w-2">→</span>,
        count: enhancedAssignments.filter(
          (assignment) => assignment.priority === "MEDIUM"
        ).length,
      },
      {
        label: "High",
        value: "HIGH",
        icon: () => <span className="inline-block h-2 w-2">↑</span>,
        count: enhancedAssignments.filter(
          (assignment) => assignment.priority === "HIGH"
        ).length,
      },
    ],
    [enhancedAssignments]
  );

  const statusColumn = React.useMemo(() => table.getColumn("status"), [table]);
  const priorityColumn = React.useMemo(
    () => table.getColumn("priority"),
    [table]
  );
  const hoursRemainingColumn = React.useMemo(
    () => table.getColumn("hoursRemaining"),
    [table]
  );
  const updatedAtColumn = React.useMemo(
    () => table.getColumn("updatedAt"),
    [table]
  );

  if (loading && enhancedAssignments.length === 0) {
    return (
      <div className="p-4 lg:p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading completed assignments...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error && enhancedAssignments.length === 0) {
    return (
      <div className="p-4 lg:p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-2">
              Error loading completed assignments
            </p>
            <p className="text-gray-600 text-sm">{error}</p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => refetch()}
            >
              Retry
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 lg:p-6">
      <div className="mb-4 flex items-center justify-between">
        <div className="text-sm text-gray-600">
          Total completed assignments: {totalCount}
          {enhancedAssignments.filter(
            (a) => a.hoursRemaining <= 48 && a.hoursRemaining > 0
          ).length > 0 && (
            <span className="ml-2 text-orange-600 font-medium">
              •{" "}
              {
                enhancedAssignments.filter(
                  (a) => a.hoursRemaining <= 48 && a.hoursRemaining > 0
                ).length
              }{" "}
              urgent (≤48h)
            </span>
          )}
        </div>
        {loading && (
          <div className="flex items-center text-sm text-gray-500">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400 mr-2"></div>
            Loading...
          </div>
        )}
      </div>

      <DataTable table={table} onRowClick={(row) => handleRowClick(row.id)}>
        <DataTableToolbar table={table}>
          {statusColumn && (
            <DataTableFacetedFilter
              column={statusColumn}
              title="Status"
              options={statusOptions}
            />
          )}
          {priorityColumn && (
            <DataTableFacetedFilter
              column={priorityColumn}
              title="Priority"
              options={priorityOptions}
            />
          )}
          {hoursRemainingColumn && (
            <DataTableSliderFilter
              column={hoursRemainingColumn}
              title="Urgent (≤48h)"
              domain={[0, 48]}
              step={3}
            />
          )}
          {updatedAtColumn && (
            <DataTableDateFilter
              column={updatedAtColumn}
              title="Updated At"
              multiple={true}
            />
          )}
        </DataTableToolbar>
      </DataTable>
    </div>
  );
}

// Loading fallback component
function LoadingFallback() {
  return (
    <div className="p-4 lg:p-6">
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading completed assignments...</p>
        </div>
      </div>
    </div>
  );
}

// Main component wrapped in Suspense
export default function WriterCompletedOrdersPage() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <WriterCompletedOrdersContent />
    </Suspense>
  );
}
