import { NextResponse } from "next/server";
import  prisma  from "@/lib/prisma";
import { getServerSession } from "next-auth";
import { authConfig } from "@/auth";
import { apiSuccess, apiError } from "@/lib/api-utils";
import type { NewsletterStatsResponse, ApiResponse, ApiError } from "@/types/api";

export async function GET(): Promise<NextResponse<ApiResponse<NewsletterStatsResponse>> | NextResponse<ApiError>> {
  try {
    // Check if user is adminP
    const session = await getServerSession(authConfig);
    if (!session?.user || session.user.role !== "ADMIN") {
      return apiError("Unauthorized", 401);
    }

    // Get total subscribers
    const totalSubscribers = await prisma.newsletterSubscription.count({
      where: { isActive: true }
    });

    // Get total unsubscribed
    const totalUnsubscribed = await prisma.newsletterSubscription.count({
      where: { isActive: false }
    });

    // Get subscribers by source
    const subscribersBySource = await prisma.newsletterSubscription.groupBy({
      by: ['source'],
      where: { isActive: true },
      _count: {
        id: true
      }
    });

    // Get recent subscribers (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentSubscribers = await prisma.newsletterSubscription.count({
      where: {
        isActive: true,
        subscribedAt: {
          gte: thirtyDaysAgo
        }
      }
    });

    // Get daily subscription data for the last 7 days
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const dailySubscriptions = await prisma.newsletterSubscription.findMany({
      where: {
        subscribedAt: {
          gte: sevenDaysAgo
        }
      },
      select: {
        subscribedAt: true
      }
    });

    // Group by day
    const dailyStats = dailySubscriptions.reduce((acc: Record<string, number>, sub) => {
      const date = sub.subscribedAt.toISOString().split('T')[0];
      acc[date] = (acc[date] || 0) + 1;
      return acc;
    }, {});

    const statsData: NewsletterStatsResponse = {
      totalSubscribers,
      totalUnsubscribed,
      recentSubscribers,
      subscribersBySource: subscribersBySource.map(item => ({
        source: item.source || 'unknown',
        count: item._count.id
      })),
      dailyStats
    };

    return apiSuccess(statsData, "Newsletter statistics retrieved successfully");

  } catch (error) {
    console.error("Newsletter stats error:", error);
    return apiError("Internal server error", 500);
  }
}
