import { <PERSON>ada<PERSON> } from "next";
import { Suspense } from "react";
import { CreateOrderPage } from "@/components/create-order/create-order-page";
import { Skeleton } from "@/components/ui/skeleton";

export const metadata: Metadata = {
  title: "Create Order - Academic Writing Services | Professional Academic Help",
  description: "Submit your academic writing assignment with our easy-to-use order form. Get professional help from qualified writers for essays, research papers, and more.",
  keywords: "academic writing, essay help, research paper, assignment help, professional writers",
  openGraph: {
    title: "Create Order - Academic Writing Services",
    description: "Submit your academic writing assignment and get professional help from qualified writers.",
    type: "website",
  },
  robots: {
    index: true,
    follow: true,
  },
};

function CreateOrderSkeleton() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto space-y-8">
          {/* Header Skeleton */}
          <div className="text-center space-y-4">
            <Skeleton className="h-12 w-96 mx-auto" />
            <Skeleton className="h-6 w-[600px] mx-auto" />
          </div>

          {/* Progress Steps Skeleton */}
          <div className="flex justify-center">
            <div className="flex items-center space-x-4">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="flex items-center">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  {i < 4 && <Skeleton className="h-1 w-16 mx-2" />}
                </div>
              ))}
            </div>
          </div>

          {/* Main Content Skeleton */}
          <div className="grid lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2 space-y-6">
              <Skeleton className="h-[600px] w-full rounded-lg" />
            </div>
            <div className="space-y-6">
              <Skeleton className="h-[300px] w-full rounded-lg" />
              <Skeleton className="h-[200px] w-full rounded-lg" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function CreateOrder() {
  return (
    <Suspense fallback={<CreateOrderSkeleton />}>
      <CreateOrderPage />
    </Suspense>
  );
}