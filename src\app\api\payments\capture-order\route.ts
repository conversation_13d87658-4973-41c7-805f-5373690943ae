import { NextRequest, NextResponse } from "next/server";
import type { CaptureOrderRequest, CaptureOrderResponse, PayPalApiError } from "@/types/paypal";
import { updateAssignmentWithPayment } from "@/lib/payments";

export async function POST(req: NextRequest) {
  try {
    const { orderId, orderID }: CaptureOrderRequest = await req.json();

    // Get environment configuration (same as create-order)
    const environment = process.env.PAYPAL_ENVIRONMENT;
    const isSandbox = environment !== "production";

    const PAYPAL_CLIENT_ID = isSandbox
      ? process.env.PAYPAL_CLIENT_ID_SANDBOX
      : process.env.PAYPAL_CLIENT_ID_LIVE;
    const PAYPAL_SECRET = isSandbox
      ? process.env.PAYPAL_SECRET_SANDBOX
      : process.env.PAYPAL_SECRET_LIVE;
    const PAYPAL_API_URL = isSandbox
      ? "https://api-m.sandbox.paypal.com"
      : "https://api-m.paypal.com";

    // Get PayPal access token
    const basicAuth = Buffer.from(`${PAYPAL_CLIENT_ID}:${PAYPAL_SECRET}`).toString("base64");
    const tokenRes = await fetch(`${PAYPAL_API_URL}/v1/oauth2/token`, {
      method: "POST",
      headers: {
        "Authorization": `Basic ${basicAuth}`,
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: "grant_type=client_credentials",
    });
    const tokenData = await tokenRes.json();
    console.log("[PayPal Debug] Token response:", tokenData);

    if (!tokenRes.ok || !tokenData.access_token) {
      console.error("[PayPal Debug] PayPal token error", tokenData);
      return NextResponse.json<PayPalApiError>({ message: "Failed to get PayPal access token" }, { status: 500 });
    }

    // Capture PayPal order
    const captureRes = await fetch(`${PAYPAL_API_URL}/v2/checkout/orders/${orderID}/capture`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${tokenData.access_token}`,
      },
    });
    const captureData = await captureRes.json();
    console.log("[PayPal Debug] Capture response:", captureData);

    if (!captureRes.ok) {
      console.error("[PayPal Debug] PayPal capture error", captureData);
      return NextResponse.json<PayPalApiError>({ message: captureData.message || "Failed to capture PayPal order" }, { status: 500 });
    }

    // Update assignment with payment details and status
    console.log("[PayPal Debug] Updating assignment with payment details for:", orderId);
    await updateAssignmentWithPayment(orderId, {
      status: "COMPLETED",
      paymentStatus: "PAID",
      paypalOrderId: captureData.id,
      paypalPayerId: captureData.payer.payer_id,
      paypalPaymentId: captureData.purchase_units?.[0]?.payments?.captures?.[0]?.id || "",
    });

    const response = {
      id: captureData.id,
      payerID: captureData.payer.payer_id,
      paymentID: captureData.purchase_units?.[0]?.payments?.captures?.[0]?.id || "",
    };
    console.log("[PayPal Debug] Returning success response:", response);

    return NextResponse.json<CaptureOrderResponse>(response);
  } catch (error) {
    return NextResponse.json<PayPalApiError>({ message: error instanceof Error ? error.message : "Unknown error" }, { status: 500 });
  }
}
