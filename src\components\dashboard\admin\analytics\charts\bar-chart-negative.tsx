"use client";

import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { 
  Bar<PERSON>hart, 
  Bar, 
  XAxis, 
  <PERSON>Axis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ReferenceLine, 
  ResponsiveContainer, 
  Cell
} from "recharts";

interface BarChartNegativeProps {
  title: string;
  description?: string;
  data: Array<{
    name: string;
    value: number;
  }>;
  className?: string;
}

export function BarChartNegative({
  title,
  description,
  data,
  className,
}: BarChartNegativeProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent className="pl-2">
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={data}
              margin={{
                top: 5,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip 
                formatter={(value: number) => [`${value}`, 'Value']}
                labelFormatter={(label) => `${label}`}
              />
              <Legend />
              <ReferenceLine y={0} stroke="#000" />
             <Bar dataKey="value" radius={[4, 4, 0, 0]}>
               {data.map((entry, index) => (
                 <Cell
                   key={`cell-${index}`}
                   fill={entry.value >= 0 ? "hsl(var(--primary))" : "hsl(var(--destructive))"}
                 />
               ))}
             </Bar>
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
