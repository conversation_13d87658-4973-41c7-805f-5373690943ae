// src/components/dashboard/writer/dashboard-large-card.tsx
"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Mail,
  Phone,
  GraduationCap,
  Star,
  Edit3,
  Save,
  X,
  CheckCircle,
  AlertCircle,
  FileText,
  Briefcase,
  Award,
  Plus,
  // Trash2,
  Copy,
  Shield,
  Calendar,
  Loader2,
} from "lucide-react";
// import { useCurrentUserId } from "@/hooks/use-session-user-id";
import {
  useWriterDashboard,
  WriterDashboardUpdateData,
} from "@/hooks/user-writer-profile";
import { getRatingColorClass, getRatingLabel } from "@/lib/rating-utils";

interface WriterProfileProps {
  className?: string;
  avatarUrl?: string;
  joinDate?: string;
}

export default function WriterProfile({
  className = "",
  avatarUrl = "/avatars/shadcn.jpg",
}: WriterProfileProps) {
  const [editMode, setEditMode] = useState({
    profile: false,
    description: false,
    experience: false,
    competencies: false,
  });

  //   const { userId } = useCurrentUserId();
  const {
    writerData,
    loading,
    error,
    updateWriter,
    isUpdating,
    addCompetency,
    removeCompetency,
  } = useWriterDashboard();

  const [localData, setLocalData] = useState({
    name: "",
    email: "",
    phone: "",
    educationLevel: "",
    professionalSummary: "",
    experience: "",
    competencies: [] as string[],
  });

  const [newCompetency, setNewCompetency] = useState("");
  const [originalData, setOriginalData] = useState(localData);

  // Update local data when writer data changes
  useEffect(() => {
    if (writerData) {
      const updatedData = {
        name: writerData.name || "",
        email: writerData.email || "",
        phone: writerData.phone || "",
        educationLevel: writerData.educationLevel || "",
        professionalSummary: writerData.professionalSummary || "",
        experience: writerData.experience || "",
        competencies: writerData.competencies || [],
      };
      setLocalData(updatedData);
      setOriginalData(updatedData);
    }
  }, [writerData]);

  const toggleEdit = (section: keyof typeof editMode) => {
    if (!editMode[section]) {
      // Store original data when entering edit mode
      setOriginalData({ ...localData });
    }
    setEditMode((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const handleSave = async (section: keyof typeof editMode) => {
    try {
      // Initialize update data with all fields to ensure proper structure
      let updateData: WriterDashboardUpdateData = {
        name: undefined,
        email: undefined,
        phone: undefined,
        educationLevel: undefined,
        professionalSummary: undefined,
        experience: undefined,
        competencies: undefined,
      };

      switch (section) {
        case "profile":
          updateData = {
            ...updateData,
            name: localData.name || null,
            email: localData.email || "", // email cannot be null
            phone: localData.phone || null,
            educationLevel: localData.educationLevel || null,
          };
          break;
        case "description":
          updateData = {
            ...updateData,
            professionalSummary: localData.professionalSummary || null,
          };
          break;
        case "experience":
          updateData = {
            ...updateData,
            experience: localData.experience || null,
          };
          break;
        case "competencies":
          updateData = {
            ...updateData,
            competencies: localData.competencies || [], // Always send array, even if empty
          };
          break;
      }

      const success = await updateWriter(updateData);
      if (success) {
        setOriginalData({ ...localData });
        toggleEdit(section);
      } else {
        console.error("DASH POST ERROR:- Save failed for section:", section);
      }
    } catch (err) {
      console.error("DASH POST ERROR:- Save operation:", err);
    }
  };

  const handleCancel = (section: keyof typeof editMode) => {
    // Restore original data
    setLocalData(originalData);
    toggleEdit(section);
  };

  const handleAddCompetency = async () => {
    if (!newCompetency.trim() || isUpdating) {
      return;
    }

    try {
      const success = await addCompetency(newCompetency.trim());

      if (success) {
        setNewCompetency("");
      } else {
        console.error("DASH POST ERROR:- Failed to add competency");
      }
    } catch (err) {
      console.error("DASH POST ERROR:- Add competency:", err);
    }
  };

  const handleRemoveCompetency = async (competency: string) => {
    if (isUpdating) {
      console.log(
        "DASH POST:- Remove competency cancelled: update in progress"
      );
      return;
    }

    try {
      await removeCompetency(competency);
    } catch (err) {
      console.error("DASH POST ERROR:- Remove competency:", err);
    }
  };

  const copyAccountId = () => {
    if (writerData?.accountId) {
      navigator.clipboard.writeText(writerData.accountId);
    }
  };

  // Replace the existing getRatingColor function with our new utility
  const getRatingColor = (rating: number) => {
    return getRatingColorClass(rating);
  };

  // Remove this unused function:
  // const getRatingDescription = (rating: number): string => {
  //   return getRatingLabel(rating);
  // };

  const getProfileCompletion = () => {
    if (!writerData) return 0;

    let completed = 0;
    const total = 7;

    if (writerData.name) completed++;
    if (writerData.email) completed++;
    if (writerData.phone) completed++;
    if (writerData.educationLevel) completed++;
    if (writerData.professionalSummary) completed++;
    if (writerData.experience) completed++;
    if (writerData.competencies && writerData.competencies.length > 0)
      completed++;

    return Math.round((completed / total) * 100);
  };

  if (loading) {
    return (
      <div className={`h-full ${className}`}>
        <Card className="h-full">
          <CardContent className="p-6 h-full flex items-center justify-center">
            <div className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Loading writer profile...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`h-full ${className}`}>
        <Card className="h-full">
          <CardContent className="p-6 h-full flex items-center justify-center">
            <div className="text-center space-y-2">
              <AlertCircle className="h-8 w-8 text-destructive mx-auto" />
              <p className="text-destructive font-medium">
                Error loading profile
              </p>
              <p className="text-sm text-muted-foreground">{error}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!writerData) {
    return (
      <div className={`h-full ${className}`}>
        <Card className="h-full">
          <CardContent className="p-6 h-full flex items-center justify-center">
            <div className="text-center space-y-2">
              <AlertCircle className="h-8 w-8 text-muted-foreground mx-auto" />
              <p className="text-muted-foreground">No writer data found</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className={`h-full ${className}`}>
        <Card className="h-full">
          <CardContent className="p-4 sm:p-6 h-full overflow-y-auto">
            <div className="grid grid-cols-1 xl:grid-cols-5 gap-4 sm:gap-6 h-full">
              {/* Left Column - Profile Summary */}
              <div className="xl:col-span-2 space-y-4">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                  <div className="flex items-center gap-2">
                    <h2 className="text-base sm:text-lg font-semibold text-primary">
                      Writer Profile Summary
                    </h2>
                  </div>
                  <div className="flex gap-2 self-start sm:self-auto">
                    {editMode.profile ? (
                      <>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleCancel("profile")}
                          className="h-8 px-3"
                          disabled={isUpdating}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleSave("profile")}
                          className="h-8 px-3"
                          disabled={isUpdating}
                        >
                          {isUpdating ? (
                            <Loader2 className="h-3 w-3 animate-spin" />
                          ) : (
                            <Save className="h-3 w-3" />
                          )}
                        </Button>
                      </>
                    ) : (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => toggleEdit("profile")}
                        className="h-8 px-3"
                        disabled={isUpdating}
                      >
                        <Edit3 className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </div>

                <Card className="border-dashed border-primary/30">
                  <CardContent className="p-4 space-y-4">
                    {/* Profile Image */}
                    <div className="flex justify-center mb-4">
                      <Avatar className="w-20 h-20">
                        <AvatarImage
                          src={writerData.image || avatarUrl}
                          alt={writerData.name || "Writer"}
                          className="object-cover"
                        />
                        <AvatarFallback className="bg-primary/10 text-primary text-lg font-semibold">
                          {(writerData.name || "W")
                            .split(" ")
                            .map((n) => n[0])
                            .join("")
                            .toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                    </div>

                    {/* Profile Completion */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium">
                          Profile Completion:
                        </Label>
                        <span className="text-sm font-semibold text-primary">
                          {getProfileCompletion()}%
                        </span>
                      </div>
                      <Progress
                        value={getProfileCompletion()}
                        className="h-2"
                      />
                    </div>

                    <Separator />

                    {/* Writer ID */}
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-primary">
                        Account ID:
                      </Label>
                      <div className="flex items-center gap-2">
                        <div className="text-lg font-bold text-primary font-mono">
                          #{writerData.accountId || "N/A"}
                        </div>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={copyAccountId}
                              className="h-6 w-6 p-0"
                              disabled={!writerData.accountId}
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Copy Account ID</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                    </div>

                    {/* Name */}
                    <div className="space-y-2">
                      <Label htmlFor="name" className="text-sm font-medium">
                        Name:
                      </Label>
                      {editMode.profile ? (
                        <Input
                          id="name"
                          value={localData.name}
                          onChange={(e) =>
                            setLocalData((prev) => ({
                              ...prev,
                              name: e.target.value,
                            }))
                          }
                          className="h-8"
                          disabled={isUpdating}
                        />
                      ) : (
                        <div className="font-medium">
                          {writerData.name || "Not set"}
                        </div>
                      )}
                    </div>

                    {/* Email */}
                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-sm font-medium">
                        Email:
                      </Label>
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        {editMode.profile ? (
                          <Input
                            id="email"
                            type="email"
                            value={localData.email}
                            onChange={(e) =>
                              setLocalData((prev) => ({
                                ...prev,
                                email: e.target.value,
                              }))
                            }
                            className="h-8 flex-1"
                            disabled={isUpdating}
                          />
                        ) : (
                          <span className="flex-1 text-sm">
                            {writerData.email}
                          </span>
                        )}
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div>
                              {writerData.isEmailVerified ? (
                                <CheckCircle className="h-4 w-4 text-green-500" />
                              ) : (
                                <AlertCircle className="h-4 w-4 text-red-500" />
                              )}
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>
                              {writerData.isEmailVerified
                                ? "Email verified"
                                : "Email not verified"}
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                      <div className="flex items-center gap-1">
                        <Shield className="h-3 w-3 text-muted-foreground" />
                        <span className="text-xs text-muted-foreground">
                          {writerData.isEmailVerified
                            ? "Email verified"
                            : "Please verify your email"}
                        </span>
                      </div>
                    </div>

                    {/* Phone */}
                    <div className="space-y-2">
                      <Label htmlFor="phone" className="text-sm font-medium">
                        Phone:
                      </Label>
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        {editMode.profile ? (
                          <Input
                            id="phone"
                            value={localData.phone}
                            onChange={(e) =>
                              setLocalData((prev) => ({
                                ...prev,
                                phone: e.target.value,
                              }))
                            }
                            className="h-8 flex-1"
                            disabled={isUpdating}
                          />
                        ) : (
                          <span className="text-sm">
                            {writerData.phone || "Not set"}
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Education Level */}
                    <div className="space-y-2">
                      <Label
                        htmlFor="education"
                        className="text-sm font-medium"
                      >
                        Education Level:
                      </Label>
                      <div className="flex items-center gap-2">
                        <GraduationCap className="h-4 w-4 text-muted-foreground" />
                        {editMode.profile ? (
                          <Input
                            id="education"
                            value={localData.educationLevel}
                            onChange={(e) =>
                              setLocalData((prev) => ({
                                ...prev,
                                educationLevel: e.target.value,
                              }))
                            }
                            className="h-8 flex-1"
                            placeholder="Enter your education level"
                            disabled={isUpdating}
                          />
                        ) : (
                          <span className="text-sm flex-1">
                            {writerData.educationLevel || "Not specified"}
                          </span>
                        )}
                      </div>
                    </div>

                    <Separator />

                    {/* Stats Section */}
                    <div className="grid grid-cols-2 gap-4">
                      {/* Rating */}
                      <div className="space-y-1">
                        <Label className="text-xs font-medium text-muted-foreground">
                          Rating:
                        </Label>
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                          <span
                            className={`font-bold text-sm ${getRatingColor(
                              writerData.rating || 0
                            )}`}
                          >
                            {writerData.rating?.toFixed(1) || "0.0"}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            ({getRatingLabel(writerData.rating || 0)})
                          </span>
                        </div>
                      </div>
                      {/* Remove the extra closing div tag that was here */}

                      {/* Join Date */}
                      <div className="space-y-1">
                        <Label className="text-xs font-medium text-muted-foreground">
                          Member since:
                        </Label>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="text-xs">
                            {new Date(writerData.createdAt).toLocaleDateString(
                              "en-US",
                              {
                                month: "long",
                                year: "numeric",
                              }
                            )}
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Right Column - Sections */}
              <div className="xl:col-span-3 space-y-6">
                {/* Description Section */}
                <Card className="border-primary/20">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <FileText className="h-5 w-5 text-primary" />
                        <CardTitle className="text-lg text-primary">
                          Professional Summary
                        </CardTitle>
                      </div>
                      <div className="flex gap-2">
                        {editMode.description ? (
                          <>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleCancel("description")}
                              className="h-8 px-3"
                              disabled={isUpdating}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              onClick={() => handleSave("description")}
                              className="h-8 px-3"
                              disabled={isUpdating}
                            >
                              {isUpdating ? (
                                <Loader2 className="h-3 w-3 animate-spin" />
                              ) : (
                                <Save className="h-3 w-3" />
                              )}
                            </Button>
                          </>
                        ) : (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => toggleEdit("description")}
                            className="h-8 px-3"
                            disabled={isUpdating}
                          >
                            <Edit3 className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    {editMode.description ? (
                      <div className="space-y-2">
                        <Textarea
                          value={localData.professionalSummary}
                          onChange={(e) =>
                            setLocalData((prev) => ({
                              ...prev,
                              professionalSummary: e.target.value,
                            }))
                          }
                          className="min-h-[120px] resize-none"
                          placeholder="Describe yourself as a writer..."
                          disabled={isUpdating}
                        />
                        <div className="text-xs text-muted-foreground text-right">
                          {localData.professionalSummary.length}/500 characters
                        </div>
                      </div>
                    ) : (
                      <p className="text-sm leading-relaxed text-muted-foreground">
                        {writerData.professionalSummary ||
                          "No professional summary added yet."}
                      </p>
                    )}
                  </CardContent>
                </Card>

                {/* Experience Section */}
                <Card className="border-primary/20">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Briefcase className="h-5 w-5 text-primary" />
                        <CardTitle className="text-lg text-primary">
                          Experience & Background
                        </CardTitle>
                      </div>
                      <div className="flex gap-2">
                        {editMode.experience ? (
                          <>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleCancel("experience")}
                              className="h-8 px-3"
                              disabled={isUpdating}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              onClick={() => handleSave("experience")}
                              className="h-8 px-3"
                              disabled={isUpdating}
                            >
                              {isUpdating ? (
                                <Loader2 className="h-3 w-3 animate-spin" />
                              ) : (
                                <Save className="h-3 w-3" />
                              )}
                            </Button>
                          </>
                        ) : (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => toggleEdit("experience")}
                            className="h-8 px-3"
                            disabled={isUpdating}
                          >
                            <Edit3 className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    {editMode.experience ? (
                      <div className="space-y-2">
                        <Textarea
                          value={localData.experience}
                          onChange={(e) =>
                            setLocalData((prev) => ({
                              ...prev,
                              experience: e.target.value,
                            }))
                          }
                          className="min-h-[120px] resize-none"
                          placeholder="Share your writing experience..."
                          disabled={isUpdating}
                        />
                        <div className="text-xs text-muted-foreground text-right">
                          {localData.experience.length}/500 characters
                        </div>
                      </div>
                    ) : (
                      <p className="text-sm leading-relaxed text-muted-foreground">
                        {writerData.experience ||
                          "No experience information added yet."}
                      </p>
                    )}
                  </CardContent>
                </Card>

                {/* Competencies Section */}
                <Card className="border-primary/20">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Award className="h-5 w-5 text-primary" />
                        <CardTitle className="text-lg text-primary">
                          Skills & Competencies
                        </CardTitle>
                      </div>
                      <div className="flex gap-2">
                        {editMode.competencies ? (
                          <>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleCancel("competencies")}
                              className="h-8 px-3"
                              disabled={isUpdating}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              onClick={() => handleSave("competencies")}
                              className="h-8 px-3"
                              disabled={isUpdating}
                            >
                              {isUpdating ? (
                                <Loader2 className="h-3 w-3 animate-spin" />
                              ) : (
                                <Save className="h-3 w-3" />
                              )}
                            </Button>
                          </>
                        ) : (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => toggleEdit("competencies")}
                            className="h-8 px-3"
                            disabled={isUpdating}
                          >
                            <Edit3 className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    {editMode.competencies ? (
                      <div className="space-y-4">
                        <div className="flex gap-2">
                          <Input
                            placeholder="Add new skill..."
                            value={newCompetency}
                            onChange={(e) => setNewCompetency(e.target.value)}
                            onKeyPress={(e) =>
                              e.key === "Enter" && handleAddCompetency()
                            }
                            className="h-8 flex-1"
                            disabled={isUpdating}
                          />
                          <Button
                            size="sm"
                            onClick={handleAddCompetency}
                            disabled={!newCompetency.trim() || isUpdating}
                            className="h-8 px-3"
                          >
                            {isUpdating ? (
                              <Loader2 className="h-3 w-3 animate-spin" />
                            ) : (
                              <Plus className="h-3 w-3" />
                            )}
                          </Button>
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {writerData.competencies.map((competency, index) => (
                            <Badge
                              key={index}
                              variant="secondary"
                              className="text-xs flex items-center gap-1 pr-1"
                            >
                              {competency}
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() =>
                                  handleRemoveCompetency(competency)
                                }
                                className="h-4 w-4 p-0 ml-1 hover:bg-destructive hover:text-destructive-foreground"
                                disabled={isUpdating}
                              >
                                <X className="h-2 w-2" />
                              </Button>
                            </Badge>
                          ))}
                        </div>
                        {writerData.competencies.length === 0 && (
                          <p className="text-sm text-muted-foreground text-center py-4">
                            No skills added yet. Add your first skill above.
                          </p>
                        )}
                      </div>
                    ) : (
                      <div className="space-y-3">
                        <div className="flex flex-wrap gap-2">
                          {writerData.competencies.map((competency, index) => (
                            <Badge
                              key={index}
                              variant="secondary"
                              className="text-xs"
                            >
                              {competency}
                            </Badge>
                          ))}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {writerData.competencies.length} skill
                          {writerData.competencies.length !== 1 ? "s" : ""}{" "}
                          listed
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
  );
}

// This is the end of your WriterProfile component

/* 
// Example of how the rating display should be updated:
<div className="flex items-center gap-1.5">
  <Star className={`h-4 w-4 ${getRatingColor(writerData.rating || 0)}`} />
  <span className={`font-medium ${getRatingColor(writerData.rating || 0)}`}>
    {writerData.rating?.toFixed(1) || "0.0"}
  </span>
  <span className="text-xs text-muted-foreground">
    ({getRatingLabel(writerData.rating || 0)})
  </span>
</div>
*/
