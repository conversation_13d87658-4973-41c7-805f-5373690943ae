import { NextRequest, NextResponse } from "next/server";
import { apiSuccess, apiError, checkPermission } from "@/lib/api-utils";
import { pricingService } from "@/lib/pricing-service";
import { AcademicLevel, Priority, Spacing } from "@prisma/client";
import { pricingRuleUpdateSchema } from "@/lib/validations";

// Get all pricing rules
export async function GET(): Promise<NextResponse> {
  try {
    // Only admins can access pricing rules
    const permissionError = await checkPermission(["ADMIN"]);
    if (permissionError) return permissionError;

    const rules = await pricingService.getAllPricingRules();
    return apiSuccess(rules);
  } catch (error) {
    console.error("Error fetching pricing rules:", error);
    return apiError("Failed to fetch pricing rules", 500);
  }
}

// Update pricing rule
export async function PUT(req: NextRequest): Promise<NextResponse> {
  try {
    // Only admins can update pricing rules
    const permissionError = await checkPermission(["ADMIN"]);
    if (permissionError) return permissionError;

    const body = await req.json();
    const parsed = pricingRuleUpdateSchema.safeParse(body);
    
    if (!parsed.success) {
      return apiError("Invalid input data", 400, parsed.error.flatten().fieldErrors);
    }

    const { ruleType, value, academicLevel, priority, spacing } = parsed.data;

    const success = await pricingService.updatePricingRule(
      ruleType,
      value,
      academicLevel,
      priority,
      spacing
    );

    if (!success) {
      return apiError("Failed to update pricing rule", 500);
    }

    // Clear cache to trigger real-time updates
    pricingService.clearCache();

    return apiSuccess({ message: "Pricing rule updated successfully" });
  } catch (error) {
    console.error("Error updating pricing rule:", error);
    return apiError("Failed to update pricing rule", 500);
  }
}

// Calculate price preview
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Only admins can access price calculation
    const permissionError = await checkPermission(["ADMIN"]);
    if (permissionError) return permissionError;

    const body = await req.json();
    const { academicLevel, priority, spacing, pageCount } = body;

    if (!academicLevel || !priority || !spacing || !pageCount) {
      return apiError("Missing required parameters", 400);
    }

    const breakdown = await pricingService.calculatePrice({
      academicLevel: academicLevel as AcademicLevel,
      priority: priority as Priority,
      spacing: spacing as Spacing,
      pageCount: Number(pageCount),
    });

    const writerCompensation = await pricingService.calculateWriterCompensation(
      breakdown.finalPrice,
      Number(pageCount)
    );

    return apiSuccess({
      priceBreakdown: breakdown,
      writerCompensation,
    });
  } catch (error) {
    console.error("Error calculating price preview:", error);
    return apiError("Failed to calculate price preview", 500);
  }
}
