import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function main() {
  try {
    // Get all blog posts
    const blogs = await prisma.blog.findMany({
      select: {
        id: true,
        title: true,
      },
    });

    // Update each blog with default timestamps
    const now = new Date();

    for (const blog of blogs) {
      await prisma.blog.update({
        where: { id: blog.id },
        data: {
          updatedAt: now,
        },
      });
    }
  } catch (error) {
    console.error("Error fetching blogs:", error);
  }
}

main()
  .catch((e) => {
    console.error("Error updating blog posts:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
