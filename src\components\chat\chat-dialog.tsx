// src/components/chat/chat-dialog.tsx
"use client";

import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  useMemo,
} from "react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Separator } from "@/components/ui/separator";
import {
  MessageCircle,
  Send,
  Smile,
  User,
  Users,
  CheckCheck,
  Check,
  Wifi,
  <PERSON>if<PERSON><PERSON><PERSON>,
  <PERSON>f<PERSON><PERSON><PERSON>,
  <PERSON>,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useSSEChat } from "@/hooks/use-sse-chat";

const emojis = [
  "😀",
  "😃",
  "😄",
  "😁",
  "😆",
  "😅",
  "😂",
  "🤣",
  "😊",
  "😇",
  "🙂",
  "🙃",
  "😉",
  "😌",
  "😍",
  "🥰",
  "😘",
  "😗",
  "😙",
  "😚",
  "😋",
  "😛",
  "😝",
  "😜",
  "🤪",
  "🤨",
  "🧐",
  "🤓",
  "😎",
  "🤩",
  "🥳",
  "😏",
  "👍",
  "👎",
  "👌",
  "✌️",
  "🤞",
  "🤟",
  "🤘",
  "🤙",
  "👏",
  "🙌",
  "👐",
  "🤲",
  "🤝",
  "🙏",
  "✍️",
  "💪",
  "🎉",
  "🎊",
  "🎈",
  "🎁",
  "🏆",
  "🥇",
  "🥈",
  "🥉",
];

interface Message {
  id: string;
  chatId: string;
  senderId: string;
  content: string;
  type: "TEXT" | "EMOJI";
  isRead: boolean;
  createdAt: string;
  updatedAt: string;
  sender: {
    id: string;
    name: string;
    email: string;
    role: string;
    image?: string;
  };
}

interface ChatParticipant {
  id: string;
  chatId: string;
  userId: string;
  role: "CLIENT" | "WRITER" | "ADMIN";
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
    image?: string;
  };
}

interface ChatDialogProps {
  assignmentId: string;
  clientId: string;
  writerId?: string;
  userRole: "ADMIN" | "CLIENT" | "WRITER";
  assignmentStatus: string;
}

export function ChatDialog({
  assignmentId,
  clientId,
  writerId,
  userRole,
  assignmentStatus,
}: ChatDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<"client" | "writer">("client");
  const [messageInput, setMessageInput] = useState("");
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [currentChatType, setCurrentChatType] = useState<"client" | "writer">(
    "client"
  );

  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Determine if writer tab should be available
  const writerTabAvailable = useMemo(() => {
    if (userRole !== "ADMIN") return false;
    const allowedStatuses = ["ASSIGNED", "COMPLETED", "REVISION", "CANCELLED"];
    return allowedStatuses.includes(assignmentStatus) && writerId;
  }, [userRole, assignmentStatus, writerId]);

  // Use SSE Chat hook
  const {
    messages,
    participants,
    isConnected,
    isLoading,
    error,
    sendMessage,
    getUnreadCount,
    clientData,
    writerData,
    reconnect,
  } = useSSEChat({
    assignmentId,
    clientId,
    writerId,
    userRole,
    enabled: isOpen,
    chatType: currentChatType,
  });

  // Filter messages based on current chat type and user role
  const filteredMessages = useMemo(() => {
    if (userRole === "ADMIN") {
      // Admin sees messages based on active tab
      return messages.filter((msg) => {
        const senderRole = msg.sender.role;
        if (currentChatType === "client") {
          return senderRole === "CLIENT" || senderRole === "ADMIN";
        } else {
          return senderRole === "WRITER" || senderRole === "ADMIN";
        }
      });
    } else {
      // Client and Writer only see their conversation with admin
      return messages.filter((msg) => {
        const senderRole = msg.sender.role;
        return senderRole === userRole || senderRole === "ADMIN";
      });
    }
  }, [messages, userRole, currentChatType]);

  // Get total unread count for button badge
  const totalUnreadCount = useMemo(() => {
    if (userRole === "ADMIN") {
      return getUnreadCount("CLIENT") + getUnreadCount("WRITER");
    } else {
      return getUnreadCount();
    }
  }, [getUnreadCount, userRole]);

  // Get unread count for specific tab
  const getTabUnreadCount = useCallback(
    (tabType: "client" | "writer") => {
      if (userRole !== "ADMIN") return 0;
      return tabType === "client"
        ? getUnreadCount("CLIENT")
        : getUnreadCount("WRITER");
    },
    [getUnreadCount, userRole]
  );

  // Determine active participants for current chat type
  const activeParticipants = useMemo(() => {
    if (userRole === "ADMIN") {
      return participants.filter((participant) => {
        if (currentChatType === "client") {
          return participant.role === "CLIENT";
        } else {
          return participant.role === "WRITER";
        }
      });
    } else {
      // Non-admin users chat with admin
      return participants.filter((participant) => participant.role === "ADMIN");
    }
  }, [participants, userRole, currentChatType]);

  // Handle sending message
  const handleSendMessage = useCallback(async () => {
    if (!messageInput.trim()) return;

    try {
      // For admin users, pass the current chat type
      // For non-admin users, always use "client" since they only chat with admin
      const chatTypeToSend = userRole === "ADMIN" ? currentChatType : "client";

      await sendMessage(messageInput, "TEXT", chatTypeToSend);
      setMessageInput("");
      setShowEmojiPicker(false);
    } catch (error) {
      console.error("Failed to send message:", error);
      // Error handling is already done in the hook
    }
  }, [messageInput, sendMessage, userRole, currentChatType]);

  // Handle emoji selection
  const handleEmojiSelect = useCallback((emoji: string) => {
    setMessageInput((prev) => prev + emoji);
    setShowEmojiPicker(false);
    inputRef.current?.focus();
  }, []);

  // Handle key press in input
  const handleKeyPress = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        handleSendMessage();
      }
    },
    [handleSendMessage]
  );

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector(
        "[data-radix-scroll-area-viewport]"
      );
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight;
      }
    }
  }, [filteredMessages]);

  // Set current chat type based on active tab
  useEffect(() => {
    if (userRole === "ADMIN") {
      setCurrentChatType(activeTab);
    } else {
      setCurrentChatType("client"); // Non-admin users always use client type
    }
  }, [activeTab, userRole]);

  // Get chat partner info
  const getChatPartnerInfo = useCallback(() => {
    if (userRole === "ADMIN") {
      return currentChatType === "client"
        ? { name: clientData?.name || "Client", role: "CLIENT" }
        : { name: writerData?.name || "Writer", role: "WRITER" };
    } else {
      return { name: "Admin", role: "ADMIN" };
    }
  }, [userRole, currentChatType, clientData, writerData]);

  const chatPartner = getChatPartnerInfo();

  // Connection status component
  const ConnectionStatus = () => (
    <div className="flex items-center gap-2 text-xs">
      {isConnected ? (
        <>
          <Wifi className="h-3 w-3 text-green-500" />
          <span className="text-green-500">Connected</span>
        </>
      ) : (
        <>
          <WifiOff className="h-3 w-3 text-red-500" />
          <span className="text-red-500">Disconnected</span>
          <Button
            size="sm"
            variant="ghost"
            onClick={reconnect}
            className="h-6 px-2"
          >
            <RefreshCw className="h-3 w-3" />
          </Button>
        </>
      )}
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="relative">
          <MessageCircle className="h-4 w-4 mr-2" />
          Chat
          {totalUnreadCount > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
            >
              {totalUnreadCount > 99 ? "99+" : totalUnreadCount}
            </Badge>
          )}
        </Button>
      </DialogTrigger>

      <DialogContent className="max-w-2xl h-[600px] flex flex-col p-0">
        <DialogHeader className="p-4 pb-2">
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <MessageCircle className="h-5 w-5" />
              Assignment Chat
            </DialogTitle>
            <ConnectionStatus />
          </div>
        </DialogHeader>

        {error && (
          <div className="mx-4 p-2 bg-red-50 border border-red-200 rounded-md text-sm text-red-600">
            {error}
          </div>
        )}

        {/* Main chat area: flex column, messages area scrollable, input always visible */}
        <div className="flex flex-col flex-1 min-h-0">
          {/* Chat content (messages area) */}
          <div className="flex-1 min-h-0 overflow-hidden">
            {userRole === "ADMIN" ? (
              <Tabs
                value={activeTab}
                onValueChange={(value) =>
                  setActiveTab(value as "client" | "writer")
                }
                className="flex-1 flex flex-col h-full"
              >
                <div className="px-4">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="client" className="relative data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300">
                      <User className="h-4 w-4 mr-2" />
                      Client
                      {getTabUnreadCount("client") > 0 && (
                        <Badge
                          variant="destructive"
                          className="ml-2 h-4 w-4 rounded-full p-0 flex items-center justify-center text-xs"
                        >
                          {getTabUnreadCount("client")}
                        </Badge>
                      )}
                    </TabsTrigger>
                    <TabsTrigger
                      value="writer"
                      disabled={!writerTabAvailable}
                      className="relative data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300"
                    >
                      <Users className="h-4 w-4 mr-2" />
                      Writer
                      {getTabUnreadCount("writer") > 0 && (
                        <Badge
                          variant="destructive"
                          className="ml-2 h-4 w-4 rounded-full p-0 flex items-center justify-center text-xs"
                        >
                          {getTabUnreadCount("writer")}
                        </Badge>
                      )}
                    </TabsTrigger>
                  </TabsList>
                </div>

                <TabsContent
                  value="client"
                  className="flex-1 flex flex-col mt-2 min-h-0 overflow-hidden"
                >
                  <ChatContent
                    messages={filteredMessages}
                    chatPartner={chatPartner}
                    userRole={userRole}
                    isLoading={isLoading}
                    scrollAreaRef={scrollAreaRef}
                    participants={activeParticipants}
                  />
                </TabsContent>

                <TabsContent
                  value="writer"
                  className="flex-1 flex flex-col mt-2 min-h-0 overflow-hidden"
                >
                  <ChatContent
                    messages={filteredMessages}
                    chatPartner={chatPartner}
                    userRole={userRole}
                    isLoading={isLoading}
                    scrollAreaRef={scrollAreaRef}
                    participants={activeParticipants}
                  />
                </TabsContent>
              </Tabs>
            ) : (
              <div className="flex-1 flex flex-col h-full min-h-0 overflow-hidden">
                <div className="px-4 pb-2 shrink-0">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Avatar className="h-6 w-6">
                      <AvatarImage src="" />
                      <AvatarFallback className="text-xs">
                        {chatPartner.name.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <span>Chatting with {chatPartner.name}</span>
                  </div>
                </div>
                <div className="flex-1 min-h-0 overflow-hidden">
                  <ChatContent
                    messages={filteredMessages}
                    chatPartner={chatPartner}
                    userRole={userRole}
                    isLoading={isLoading}
                    scrollAreaRef={scrollAreaRef}
                    participants={activeParticipants}
                  />
                </div>
              </div>
            )}
          </div>
          {/* Message Input (always visible at the bottom) */}
          <div className="p-4 border-t shrink-0 bg-background border-border">
            <div className="flex gap-2 relative">
              <div className="flex-1 relative">
                <Input
                  ref={inputRef}
                  value={messageInput}
                  onChange={(e) => setMessageInput(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type your message..."
                  disabled={!isConnected}
                  className="pr-10 bg-background text-foreground border-border"
                />
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                  disabled={!isConnected}
                >
                  <Smile className="h-4 w-4" />
                </Button>

                {/* Emoji Picker */}
                {showEmojiPicker && (
                  <div className="absolute bottom-full right-0 mb-2 bg-background border border-border rounded-lg shadow-lg p-2 z-50 w-64">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-foreground">
                        Emojis
                      </span>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => setShowEmojiPicker(false)}
                        className="h-6 w-6 p-0"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                    <div className="grid grid-cols-8 gap-1 max-h-32 overflow-y-auto">
                      {emojis.map((emoji, index) => (
                        <Button
                          key={index}
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEmojiSelect(emoji)}
                          className="h-8 w-8 p-0 text-lg hover:bg-muted"
                        >
                          {emoji}
                        </Button>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <Button
                onClick={handleSendMessage}
                disabled={!messageInput.trim() || !isConnected}
                size="sm"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Chat Content Component
interface ChatContentProps {
  messages: Message[];
  chatPartner: { name: string; role: string };
  userRole: string;
  isLoading: boolean;
  scrollAreaRef: React.RefObject<HTMLDivElement | null>;
  participants: ChatParticipant[];
}

function ChatContent({
  messages,
  chatPartner,
  userRole,
  isLoading,
  scrollAreaRef,
  participants,
}: ChatContentProps) {
  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return "Today";
    } else if (date.toDateString() === yesterday.toDateString()) {
      return "Yesterday";
    } else {
      return date.toLocaleDateString();
    }
  };

  const groupMessagesByDate = (messages: Message[]) => {
    const groups: { [key: string]: Message[] } = {};

    messages.forEach((message) => {
      const dateKey = formatDate(message.createdAt);
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(message);
    });

    return groups;
  };

  const messageGroups = groupMessagesByDate(messages);

  // Use participants data to show additional info if needed
  const participantNames = participants.map((p) => p.user.name).join(", ");

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center h-full">
        <div className="text-center">
          <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
          <p className="text-sm text-muted-foreground">Loading chat...</p>
        </div>
      </div>
    );
  }

  if (messages.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center h-full">
        <div className="text-center">
          <MessageCircle className="h-12 w-12 mx-auto mb-4 text-muted-foreground/50" />
          <h3 className="font-medium mb-2">No messages yet</h3>
          <p className="text-sm text-muted-foreground">
            Start a conversation with {chatPartner.name}
          </p>
          {participants.length > 0 && (
            <p className="text-xs text-muted-foreground mt-1">
              Participants: {participantNames}
            </p>
          )}
        </div>
      </div>
    );
  }

  return (
    <ScrollArea
      ref={scrollAreaRef}
      className="flex-1 px-4 h-full overflow-y-auto"
    >
      <div className="space-y-4 pb-4">
        {Object.entries(messageGroups).map(([date, dateMessages]) => (
          <div key={date}>
            {/* Date Separator */}
            <div className="flex items-center gap-2 my-4">
              <Separator className="flex-1" />
              <span className="text-xs text-muted-foreground bg-background px-2">
                {date}
              </span>
              <Separator className="flex-1" />
            </div>

            {/* Messages for this date */}
            <div className="space-y-3">
              {dateMessages.map((message) => {
                const isOwnMessage = message.sender.role === userRole;

                return (
                  <div
                    key={message.id}
                    className={cn(
                      "flex gap-2 max-w-[80%]",
                      isOwnMessage ? "ml-auto flex-row-reverse" : ""
                    )}
                  >
                    <Avatar className="h-8 w-8 flex-shrink-0">
                      <AvatarImage src={message.sender.image} />
                      <AvatarFallback className="text-xs">
                        {message.sender.name.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>

                    <div
                      className={cn(
                        "flex flex-col gap-1",
                        isOwnMessage ? "items-end" : "items-start"
                      )}
                    >
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        {!isOwnMessage && (
                          <span className="font-medium">
                            {message.sender.name}
                          </span>
                        )}
                        <span>{formatTime(message.createdAt)}</span>
                      </div>

                      <div
                        className={cn(
                          "rounded-lg px-3 py-2 max-w-full break-words",
                          isOwnMessage
                            ? "bg-primary text-primary-foreground"
                            : "bg-muted text-foreground"
                        )}
                      >
                        <p className="text-sm whitespace-pre-wrap">
                          {message.content}
                        </p>
                      </div>

                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        {isOwnMessage && (
                          <>
                            {message.isRead ? (
                              <Tooltip>
                                <TooltipTrigger>
                                  <CheckCheck className="h-3 w-3 text-blue-500" />
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Read</p>
                                </TooltipContent>
                              </Tooltip>
                            ) : (
                              <Tooltip>
                                <TooltipTrigger>
                                  <Check className="h-3 w-3" />
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Delivered</p>
                                </TooltipContent>
                              </Tooltip>
                            )}
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        ))}
      </div>
    </ScrollArea>
  );
}
