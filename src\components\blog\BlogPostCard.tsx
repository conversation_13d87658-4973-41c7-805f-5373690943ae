import React from "react";
import Image from "next/image";
import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

export interface BlogPostCardProps {
  id: string;
  slug: string;
  category: string;
  title: string;
  date: string;
  readTime: string;
  imageUrl: string;
  imageAlt?: string;
  description: string;
}

export function BlogPostCard({ slug, category, title, date, readTime, imageUrl, imageAlt, description }: BlogPostCardProps) {
  return (
    <Card className="overflow-hidden group relative shadow-lg hover:shadow-xl transition-shadow h-full">
      <div className="relative w-full h-40">
        <Image
          src={imageUrl}
          alt={imageAlt || title}
          fill
          priority
          className="object-cover group-hover:scale-105 transition-transform duration-300"
          sizes="(min-width:1024px) 25vw, (min-width:768px) 33vw, 100vw"
        />
      </div>
      <CardContent className="p-4 flex flex-col gap-2">
        <span className="text-xs uppercase text-primary font-semibold">{category}</span>
        <h3 className="text-lg font-bold line-clamp-2 min-h-[48px]">{title}</h3>
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          <span>{date}</span>
          <span>&bull;</span>
          <span>{readTime} read</span>
        </div>
        <p className="text-sm text-muted-foreground line-clamp-4 min-h-[40px]">{description}</p>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button asChild variant="link" className="p-0 h-auto min-h-0 text-primary font-semibold">
                <Link href={`/blog/${slug}`} aria-label={`Read more about ${title}`}>Read more</Link>
              </Button>
            </TooltipTrigger>
            <TooltipContent>Read the full post</TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </CardContent>
    </Card>
  );
}
