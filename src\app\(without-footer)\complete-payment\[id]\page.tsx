"use client";

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import PayPalButton from "@/components/PayPalButton";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/dashboard/client/app-sidebar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import {
  Clock,
  FileText,
  DollarSign,
  CreditCard,
  CheckCircle2,
  Calendar,
  BookOpen,
  GraduationCap,
  Languages,
  FileSearch,
  Sparkles,
  Shield,
  // ArrowRight,
  ArrowDown,
} from "lucide-react";

interface Assignment {
  id: string;
  taskId: string;
  title: string;
  assignmentType: string;
  subject: string;
  service: string;
  pageCount: number;
  price: number;
  priority: string;
  academicLevel: string;
  spacing: string;
  languageStyle: string;
  formatStyle: string;
  numSources: number;
  status: string;
  paymentStatus: string;
  paypalOrderId?: string;
  paypalPayerId?: string;
  paypalPaymentId?: string;
  createdAt: string;
  assignedWriter?: {
    id: string;
    name: string;
    email: string;
    rating: number;
  };
}

export default function PaymentPageDashboard() {
  const params = useParams();
  const router = useRouter();
  const assignmentId = params.id as string;
  const [assignment, setAssignment] = useState<Assignment | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const breadcrumbs = [
    { label: "Dashboard", href: "/client/dashboard" },
    { label: "Payment", isCurrentPage: true },
  ];

  useEffect(() => {
    const fetchAssignment = async () => {
      try {
        const response = await fetch(`/api/assignments/${assignmentId}`);
        if (!response.ok) {
          throw new Error("Failed to fetch assignment");
        }
        const data = await response.json();
        setAssignment(data.data);
      } catch (err) {
        setError(err instanceof Error ? err.message : "An error occurred");
      } finally {
        setLoading(false);
      }
    };

    if (assignmentId) {
      fetchAssignment();
    }
  }, [assignmentId]);

  const formatAssignmentType = (type: string) => {
    return type
      .replace(/_/g, " ")
      .toLowerCase()
      .replace(/\b\w/g, (l) => l.toUpperCase());
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case "low":
        return "bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-900 dark:text-gray-300 dark:border-gray-700";
      case "medium":
        return "bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:border-blue-700";
      case "high":
        return "bg-red-50 text-red-700 border-red-200 dark:bg-red-900 dark:text-red-300 dark:border-red-700";
      default:
        return "bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-900 dark:text-gray-300 dark:border-gray-700";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
        return "bg-green-500/10 text-green-700 border-green-500/20";
      case "in_progress":
        return "bg-blue-500/10 text-blue-700 border-blue-500/20";
      case "posted":
        return "bg-yellow-500/10 text-yellow-700 border-yellow-500/20";
      default:
        return "bg-gray-500/10 text-gray-700 border-gray-500/20";
    }
  };

  if (loading) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <div className="flex flex-1 flex-col">
            <div className="@container/main flex flex-1 flex-col gap-2">
              <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
                <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
                  <div className="flex items-center gap-2 px-4">
                    <SidebarTrigger className="-ml-1" />
                    <Separator
                      orientation="vertical"
                      className="mr-2 data-[orientation=vertical]:h-4"
                    />
                    <Breadcrumb>
                      <BreadcrumbList>
                        {breadcrumbs.map((item, index) => (
                          <div key={index} className="flex items-center">
                            {index > 0 && (
                              <BreadcrumbSeparator className="hidden md:block" />
                            )}
                            <BreadcrumbItem className="hidden md:block">
                              {item.isCurrentPage ? (
                                <BreadcrumbPage>{item.label}</BreadcrumbPage>
                              ) : (
                                <BreadcrumbLink href={item.href || "#"}>
                                  {item.label}
                                </BreadcrumbLink>
                              )}
                            </BreadcrumbItem>
                          </div>
                        ))}
                      </BreadcrumbList>
                    </Breadcrumb>
                  </div>
                </header>

                <div className="w-full max-w-7xl mx-auto px-6 pb-6">
                  <div className="space-y-8">
                    <Skeleton className="h-12 w-80" />
                    <div className="grid gap-8 lg:grid-cols-2">
                      <Card className="h-[600px]">
                        <CardHeader>
                          <Skeleton className="h-8 w-48" />
                        </CardHeader>
                        <CardContent className="space-y-4">
                          {Array(8)
                            .fill(0)
                            .map((_, i) => (
                              <Skeleton key={i} className="h-16 w-full" />
                            ))}
                        </CardContent>
                      </Card>
                      <Card className="h-[600px]">
                        <CardHeader>
                          <Skeleton className="h-8 w-48" />
                        </CardHeader>
                        <CardContent>
                          <Skeleton className="h-80 w-full" />
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  if (error) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <div className="flex flex-1 flex-col">
            <div className="@container/main flex flex-1 flex-col gap-2">
              <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
                <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
                  <div className="flex items-center gap-2 px-4">
                    <SidebarTrigger className="-ml-1" />
                    <Separator
                      orientation="vertical"
                      className="mr-2 data-[orientation=vertical]:h-4"
                    />
                    <Breadcrumb>
                      <BreadcrumbList>
                        {breadcrumbs.map((item, index) => (
                          <div key={index} className="flex items-center">
                            {index > 0 && (
                              <BreadcrumbSeparator className="hidden md:block" />
                            )}
                            <BreadcrumbItem className="hidden md:block">
                              {item.isCurrentPage ? (
                                <BreadcrumbPage>{item.label}</BreadcrumbPage>
                              ) : (
                                <BreadcrumbLink href={item.href || "#"}>
                                  {item.label}
                                </BreadcrumbLink>
                              )}
                            </BreadcrumbItem>
                          </div>
                        ))}
                      </BreadcrumbList>
                    </Breadcrumb>
                  </div>
                </header>

                <div className="w-full max-w-7xl mx-auto px-6 pb-6">
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                </div>
              </div>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  if (!assignment) return null;

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
              {/* Header with Sidebar Trigger and Breadcrumbs */}
              <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
                <div className="flex items-center gap-2 px-4">
                  <SidebarTrigger className="-ml-1" />
                  <Separator
                    orientation="vertical"
                    className="mr-2 data-[orientation=vertical]:h-4"
                  />
                  <Breadcrumb>
                    <BreadcrumbList>
                      {breadcrumbs.map((item, index) => (
                        <div key={index} className="flex items-center">
                          {index > 0 && (
                            <BreadcrumbSeparator className="hidden md:block" />
                          )}
                          <BreadcrumbItem className="hidden md:block">
                            {item.isCurrentPage ? (
                              <BreadcrumbPage>{item.label}</BreadcrumbPage>
                            ) : (
                              <BreadcrumbLink href={item.href || "#"}>
                                {item.label}
                              </BreadcrumbLink>
                            )}
                          </BreadcrumbItem>
                        </div>
                      ))}
                    </BreadcrumbList>
                  </Breadcrumb>
                </div>
              </header>

              {/* Main Content Area */}
              <div className="w-full max-w-7xl mx-auto px-6 pb-6">
                <div className="space-y-8">
                  {/* Page Header */}
                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 rounded-2xl blur-3xl" />
                    <div className="relative bg-gradient-to-r from-blue-50 via-purple-50 to-pink-50 dark:from-blue-950/50 dark:via-purple-950/50 dark:to-pink-950/50 rounded-2xl border border-blue-200/50 dark:border-blue-800/50 p-8">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg">
                          <CreditCard className="h-8 w-8 text-white" />
                        </div>
                        <div>
                          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                            Completing Payment
                          </h1>
                          <p className="text-lg text-muted-foreground mt-2">
                            Review your assignment details and complete the
                            payment process
                          </p>
                        </div>
                      </div>
                      <Progress value={90} className="mt-6 h-2" />
                      <p className="text-sm text-muted-foreground mt-2">
                        Almost there! Just one final step.
                      </p>
                    </div>
                  </div>

                  {/* Main Content Grid */}
                  <div className="grid gap-8 lg:grid-cols-2">
                    {/* Assignment Summary Card */}
                    <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-white via-blue-50/30 to-purple-50/30 dark:from-gray-900 dark:via-blue-950/30 dark:to-purple-950/30 shadow-2xl">
                      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-pink-500/5" />
                      <CardHeader className="relative pb-4">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg">
                            <FileText className="h-6 w-6 text-white" />
                          </div>
                          <div>
                            <CardTitle className="text-2xl font-bold">
                              Assignment Summary
                            </CardTitle>
                            <CardDescription className="text-base">
                              Task ID:{" "}
                              <span className="font-mono font-semibold">
                                {assignment.taskId}
                              </span>
                            </CardDescription>
                          </div>
                        </div>
                      </CardHeader>

                      <CardContent className="relative space-y-6">
                        {/* Title Section */}
                        <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-xl p-6 border border-blue-200/20 dark:border-blue-800/20">
                          <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                            {assignment.title}
                          </h3>
                          <div className="flex flex-wrap gap-2">
                            <Badge
                              variant="outline"
                              className={`${getStatusColor(
                                assignment.status
                              )} font-semibold`}
                            >
                              <CheckCircle2 className="h-3 w-3 mr-1" />
                              {formatAssignmentType(assignment.status)}
                            </Badge>
                            <Badge
                              variant="outline"
                              className={`${getPriorityColor(
                                assignment.priority
                              )}`}
                            >
                              <Clock className="h-3 w-3 mr-1" />
                              {formatAssignmentType(assignment.priority)}
                              Priority
                            </Badge>
                          </div>
                        </div>

                        {/* Assignment Details Grid */}
                        <div className="grid gap-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 dark:border-gray-700/50">
                              <div className="flex items-center gap-2 mb-2">
                                <BookOpen className="h-4 w-4 text-blue-600" />
                                <span className="text-sm font-medium text-muted-foreground">
                                  Type
                                </span>
                              </div>
                              <p className="font-semibold">
                                {formatAssignmentType(
                                  assignment.assignmentType
                                )}
                              </p>
                            </div>

                            <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 dark:border-gray-700/50">
                              <div className="flex items-center gap-2 mb-2">
                                <GraduationCap className="h-4 w-4 text-purple-600" />
                                <span className="text-sm font-medium text-muted-foreground">
                                  Level
                                </span>
                              </div>
                              <p className="font-semibold">
                                {formatAssignmentType(assignment.academicLevel)}
                              </p>
                            </div>
                          </div>

                          <div className="grid grid-cols-2 gap-4">
                            <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 dark:border-gray-700/50">
                              <div className="flex items-center gap-2 mb-2">
                                <FileText className="h-4 w-4 text-green-600" />
                                <span className="text-sm font-medium text-muted-foreground">
                                  Pages
                                </span>
                              </div>
                              <p className="font-semibold">
                                {assignment.pageCount} pages
                              </p>
                            </div>

                            <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 dark:border-gray-700/50">
                              <div className="flex items-center gap-2 mb-2">
                                <FileSearch className="h-4 w-4 text-orange-600" />
                                <span className="text-sm font-medium text-muted-foreground">
                                  Sources
                                </span>
                              </div>
                              <p className="font-semibold">
                                {assignment.numSources} sources
                              </p>
                            </div>
                          </div>

                          <div className="grid grid-cols-2 gap-4">
                            <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 dark:border-gray-700/50">
                              <div className="flex items-center gap-2 mb-2">
                                <Languages className="h-4 w-4 text-pink-600" />
                                <span className="text-sm font-medium text-muted-foreground">
                                  Style
                                </span>
                              </div>
                              <p className="font-semibold">
                                {assignment.formatStyle} •{" "}
                                {formatAssignmentType(assignment.languageStyle)}
                              </p>
                            </div>

                            <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 dark:border-gray-700/50">
                              <div className="flex items-center gap-2 mb-2">
                                <Calendar className="h-4 w-4 text-indigo-600" />
                                <span className="text-sm font-medium text-muted-foreground">
                                  Created
                                </span>
                              </div>
                              <p className="font-semibold">
                                {formatDate(assignment.createdAt)}
                              </p>
                            </div>
                          </div>

                          <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 dark:border-gray-700/50">
                            <div className="flex items-center gap-2 mb-2">
                              <span className="text-sm font-medium text-muted-foreground">
                                Subject & Service
                              </span>
                            </div>
                            <p className="font-semibold">
                              {assignment.subject} • {assignment.service}
                            </p>
                          </div>

                         
                        </div>
                      </CardContent>
                    </Card>

                    {/* Payment Card */}
                    <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-white via-green-50/30 to-blue-50/30 dark:from-gray-900 dark:via-green-950/30 dark:to-blue-950/30 shadow-2xl">
                      <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 via-blue-500/5 to-purple-500/5" />
                      <CardHeader className="relative pb-4">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-green-500 to-blue-600 rounded-xl shadow-lg">
                            <DollarSign className="h-6 w-6 text-white" />
                          </div>
                          <div>
                            <CardTitle className="text-2xl font-bold">
                              Complete Payment
                            </CardTitle>
                            <CardDescription className="text-base">
                              Secure payment powered by PayPal
                            </CardDescription>
                          </div>
                        </div>
                      </CardHeader>

                      <CardContent className="relative space-y-6">
                        {/* Price Display */}
                        <div className="bg-gradient-to-r from-green-100 to-blue-100 dark:from-green-900/30 dark:to-blue-900/30 rounded-2xl p-8 text-center border border-green-200/50 dark:border-green-800/50">
                          <div className="flex items-center justify-center gap-2 mb-2">
                            <Sparkles className="h-6 w-6 text-green-600" />
                            <span className="text-lg font-medium text-muted-foreground">
                              Total Amount
                            </span>
                          </div>
                          <div className="text-5xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-4">
                            ${assignment.price.toFixed(2)}
                          </div>
                          <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
                            <Shield className="h-4 w-4" />
                            <span>Secure payment with buyer protection</span>
                          </div>
                        </div>

                        {/* Payment Details */}
                        <div className="space-y-4">
                          <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 dark:border-gray-700/50">
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-sm font-medium text-muted-foreground">
                                Assignment Cost
                              </span>
                              <span className="font-semibold">
                                ${assignment.price.toFixed(2)}
                              </span>
                            </div>
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-sm font-medium text-muted-foreground">
                                Processing Fee
                              </span>
                              <span className="font-semibold">$0.00</span>
                            </div>
                            <Separator className="my-3" />
                            <div className="flex justify-between items-center">
                              <span className="font-bold">Total</span>
                              <span className="text-2xl font-bold text-green-600">
                                ${assignment.price.toFixed(2)}
                              </span>
                            </div>
                          </div>

                          {/* PayPal Button Section */}
                          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30 rounded-lg p-6 border border-blue-200/50 dark:border-blue-800/50">
                            <div className="text-center mb-6">
                              <h3 className="text-lg font-semibold mb-2">
                                Ready to Pay?
                              </h3>
                              <p className="text-sm text-muted-foreground">
                                Click below to complete your payment securely
                                with PayPal
                              </p>
                            </div>

                            {/* PayPal Button Placeholder */}
                            <div className="space-y-4">
                              <Button
                                size="lg"
                                className="w-full h-14 text-lg font-semibold bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
                              >
                                <div className="flex items-center gap-3">
                                  <CreditCard className="h-6 w-6" />
                                  <span>Pay with PayPal Below</span>
                                  <ArrowDown className="h-5 w-5" />
                                </div>
                              </Button>

                              <PayPalButton
  orderId={assignment.id}
  amount={assignment.price}
  onSuccess={(details) => {
    console.log("Payment successful!", details);
    toast.success("Payment completed successfully! 🎉", {
      description: `Payment ID: ${details.paymentID}`,
      duration: 5000,
    });
    // Redirect to order page after successful payment
    setTimeout(() => {
      router.push(`/order/${assignment.id}`);
    }, 2000);
  }}
  onError={(error) => {
    console.error("Payment failed!", error);
    toast.error("Payment failed", {
      description: error.message || "Please try again or contact support.",
      duration: 5000,
    });
  }}
  onCancel={() => {
    toast.info("Payment cancelled", {
      description: "You can complete the payment anytime.",
      duration: 3000,
    });
  }}
/>

                              <div className="text-center">
                                <p className="text-xs text-muted-foreground">
                                  By proceeding, you agree to our terms and
                                  conditions
                                </p>
                              </div>
                            </div>
                          </div>

                          {/* Security Features */}
                          <div className="bg-green-50 dark:bg-green-950/30 rounded-lg p-4 border border-green-200/50 dark:border-green-800/50">
                            <div className="flex items-center gap-2 mb-2">
                              <Shield className="h-5 w-5 text-green-600" />
                              <span className="font-semibold text-green-900 dark:text-green-100">
                                Secure Payment Guarantee
                              </span>
                            </div>
                            <ul className="text-sm text-green-700 dark:text-green-300 space-y-1">
                              <li>• SSL encrypted transaction</li>
                              <li>• PayPal buyer protection</li>
                              <li>• No payment information stored</li>
                              <li>• Full refund policy available</li>
                            </ul>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
