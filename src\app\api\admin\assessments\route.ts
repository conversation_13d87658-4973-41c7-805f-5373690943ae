// src/app/api/admin/assessments/route.ts
import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authConfig as authOptions } from "../../../../auth";

import prisma from "@/lib/prisma";

// Types for Assessment
interface MultipleChoiceQuestion {
  question: string;
  options: string[];
  correctAnswer: string;
}

interface EssayExam {
  topic: string;
  rubrics: string;
}

export interface AssessmentPayload {
  title: string;
  multipleChoiceQuiz: MultipleChoiceQuestion[];
  essayExam: EssayExam;
}

// Helper for role and access check
async function checkAccess(): Promise<{ allowed: boolean; error?: string }> {
  const session = await getServerSession(authOptions);

  if (!session?.user) {
    return { allowed: false, error: "Authentication required" };
  }

  // Allow access for admins
  if (session.user.role === "ADMIN") {
    return { allowed: true };
  }

  // For writers, check if they're not approved yet
  if (session.user.role === "WRITER") {
    try {
      const writer = await prisma.user.findUnique({
        where: { id: session.user.id },
        select: { isApproved: true },
      });

      // Only non-approved writers can access assessments
      if (writer && !writer.isApproved) {
        return { allowed: true };
      }

      return {
        allowed: false,
        error: "Only non-approved writers can access assessments",
      };
    } catch (error) {
      console.error("Error checking writer status:", error);
      return { allowed: false, error: "Failed to verify writer status" };
    }
  }

  return { allowed: false, error: "Access denied" };
}

// GET: List assessments
export async function GET(): Promise<NextResponse> {
  const { allowed, error } = await checkAccess();

  if (!allowed) {
    return NextResponse.json(
      { error: error || "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    const session = await getServerSession(authOptions);

    // For writers, return only active assessments
    if (session?.user?.role === "WRITER") {
      const activeAssessments = await prisma.assessment.findMany({
        where: { isActive: true },
        orderBy: { createdAt: "desc" },
      });
      return NextResponse.json(activeAssessments);
    }

    // For admins, return all assessments
    const assessments = await prisma.assessment.findMany({
      orderBy: { createdAt: "desc" },
    });
    return NextResponse.json(assessments);
  } catch (error) {
    console.error("Error fetching assessments:", error);
    return NextResponse.json(
      { error: "Failed to fetch assessments" },
      { status: 500 }
    );
  }
}

// POST: Create assessment (admin only)
export async function POST(req: NextRequest): Promise<NextResponse> {
  const session = await getServerSession(authOptions);
  if (session?.user?.role !== "ADMIN") {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const body = await req.json();
  try {
    // If creating an active assessment, deactivate all others first
    if (body.isActive) {
      await prisma.assessment.updateMany({
        where: { isActive: true },
        data: { isActive: false },
      });
    }

    const assessment = await prisma.assessment.create({
      data: {
        title: body.title,
        multipleChoiceQuiz: body.multipleChoiceQuiz,
        essayExam: body.essayExam,
        isActive: body.isActive || false,
      },
    });
    return NextResponse.json(assessment);
  } catch (error) {
    return NextResponse.json(
      { error: "Failed to create assessment", details: String(error) },
      { status: 400 }
    );
  }
}
