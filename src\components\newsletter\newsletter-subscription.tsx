"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Mail, Loader2 } from 'lucide-react';
import { useNewsletter } from '@/hooks/use-newsletter';

interface NewsletterSubscriptionProps {
  source?: string;
  placeholder?: string;
  buttonText?: string;
  className?: string;
  variant?: 'default' | 'compact' | 'inline';
  showIcon?: boolean;
}

export function NewsletterSubscription({
  source = 'unknown',
  placeholder = 'Enter your email address',
  buttonText = 'Subscribe',
  className = '',
  variant = 'default',
  showIcon = true,
}: NewsletterSubscriptionProps) {
  const [email, setEmail] = useState('');
  const { isLoading, subscribe } = useNewsletter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const success = await subscribe(email, source);
    if (success) {
      setEmail(''); // Clear the input on success
    }
  };

  if (variant === 'compact') {
    return (
      <form onSubmit={handleSubmit} className={`flex gap-2 ${className}`}>
        <Input
          type="email"
          placeholder={placeholder}
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          className="flex-1"
          disabled={isLoading}
        />
        <Button 
          type="submit" 
          disabled={isLoading || !email}
          size="sm"
        >
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : showIcon ? (
            <Mail className="h-4 w-4" />
          ) : (
            buttonText
          )}
        </Button>
      </form>
    );
  }

  if (variant === 'inline') {
    return (
      <form onSubmit={handleSubmit} className={`flex flex-col sm:flex-row gap-2 ${className}`}>
        <Input
          type="email"
          placeholder={placeholder}
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          className="flex-1"
          disabled={isLoading}
        />
        <Button 
          type="submit" 
          disabled={isLoading || !email}
          className="whitespace-nowrap"
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Subscribing...
            </>
          ) : (
            <>
              {showIcon && <Mail className="h-4 w-4 mr-2" />}
              {buttonText}
            </>
          )}
        </Button>
      </form>
    );
  }

  // Default variant
  return (
    <div className={`space-y-4 ${className}`}>
      <div className="text-center space-y-2">
        {showIcon && (
          <div className="flex justify-center">
            <div className="p-3 bg-primary/10 rounded-full">
              <Mail className="h-6 w-6 text-primary" />
            </div>
          </div>
        )}
        <h3 className="text-lg font-semibold">Subscribe to Our Newsletter</h3>
        <p className="text-sm text-muted-foreground">
          Get the latest updates, tips, and exclusive content delivered to your inbox.
        </p>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-3">
        <Input
          type="email"
          placeholder={placeholder}
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          disabled={isLoading}
        />
        <Button 
          type="submit" 
          disabled={isLoading || !email}
          className="w-full"
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Subscribing...
            </>
          ) : (
            <>
              <Mail className="h-4 w-4 mr-2" />
              {buttonText}
            </>
          )}
        </Button>
      </form>
      
      <p className="text-xs text-muted-foreground text-center">
        We respect your privacy. Unsubscribe at any time.
      </p>
    </div>
  );
}
