import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

interface LoadingSkeletonProps extends React.ComponentProps<typeof Skeleton> {
  count?: number;
  className?: string;
}

/**
 * LoadingSkeleton component that extends the Shadcn UI Skeleton
 * Allows for multiple skeletons and additional styling options
 */
export default function LoadingSkeleton({ 
  count = 1, 
  className,
  ...props 
}: LoadingSkeletonProps) {
  return (
    <div className="space-y-2">
      {Array.from({ length: count }).map((_, i) => (
        <Skeleton 
          key={i} 
          className={cn("w-full", className)}
          {...props}
        />
      ))}
    </div>
  );
}
