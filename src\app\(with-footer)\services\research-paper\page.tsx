//src/app/(with-footer)/services/research-paper/page.tsx
import React from "react";
import {
  Card,
  CardContent,
  //   CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  CheckCircle,
  Search,
  BarChart3,
  FileSearch,
  Microscope,
  Database,
} from "lucide-react";

const ResearchPaperWritingService: React.FC = () => {
  const researchTypes = [
    {
      icon: <Search className="h-6 w-6 text-primary" />,
      title: "Qualitative Research",
      description:
        "In-depth analysis of non-numerical data, interviews, case studies, and observational research",
    },
    {
      icon: <BarChart3 className="h-6 w-6 text-primary" />,
      title: "Quantitative Research",
      description:
        "Statistical analysis, surveys, experiments, and numerical data interpretation",
    },
    {
      icon: <FileSearch className="h-6 w-6 text-primary" />,
      title: "Mixed Methods",
      description:
        "Combination of qualitative and quantitative approaches for comprehensive analysis",
    },
    {
      icon: <Microscope className="h-6 w-6 text-primary" />,
      title: "Experimental Research",
      description:
        "Controlled studies, hypothesis testing, and scientific methodology",
    },
  ];

  const benefits = [
    "Access to premium academic databases and journals",
    "Expert writers with advanced degrees in your field",
    "Rigorous peer-review process before delivery",
    "Proper citation and reference management",
    "Statistical analysis and data interpretation",
    "Plagiarism-free guarantee with detailed reports",
  ];

  const disciplines = [
    "Psychology",
    "Sociology",
    "Business",
    "Medicine",
    "Engineering",
    "Computer Science",
    "Biology",
    "Chemistry",
    "Physics",
    "Economics",
    "Political Science",
    "Literature",
    "History",
    "Education",
    "Nursing",
  ];

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-12 max-w-6xl">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <Badge variant="secondary" className="mb-4">
            <Database className="h-4 w-4 mr-2" />
            Research & Analysis
          </Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
            Expert Research Paper Writing Service
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            Transform your ideas into compelling research papers backed by
            rigorous methodology, comprehensive literature reviews, and expert
            analysis. Our PhD-level writers deliver publication-quality research
            across all academic disciplines.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg">Start Your Research Project</Button>
            <Button size="lg" variant="outline">
              View Research Samples
            </Button>
          </div>
        </div>

        {/* What is a Research Paper */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle className="flex items-center text-2xl">
              <FileSearch className="h-6 w-6 mr-3 text-primary" />
              Understanding Research Papers
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="definition" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="definition" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300">Definition</TabsTrigger>
                <TabsTrigger value="structure" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300">Structure</TabsTrigger>
                <TabsTrigger value="importance" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300">Importance</TabsTrigger>
              </TabsList>

              <TabsContent value="definition" className="space-y-4">
                <p className="text-muted-foreground leading-relaxed">
                  A research paper is an academic document that presents
                  original research, analysis, or interpretation on a specific
                  topic. Unlike other academic assignments, research papers
                  require you to investigate a question, collect and analyze
                  data, and present findings that contribute new knowledge to
                  your field.
                </p>
                <p className="text-muted-foreground leading-relaxed">
                  Research papers can be empirical (based on original data
                  collection) or theoretical (based on analysis of existing
                  literature). They typically range from 10-50 pages and follow
                  strict academic formatting guidelines while maintaining
                  scholarly rigor throughout.
                </p>
              </TabsContent>

              <TabsContent value="structure" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-muted/50 p-4 rounded-lg">
                    <h4 className="font-semibold mb-3 text-foreground">
                      Research Paper Components:
                    </h4>
                    <ul className="space-y-2 text-muted-foreground">
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 mr-2 text-primary" />
                        Abstract & Keywords
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 mr-2 text-primary" />
                        Introduction & Problem Statement
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 mr-2 text-primary" />
                        Literature Review
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 mr-2 text-primary" />
                        Methodology
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 mr-2 text-primary" />
                        Results & Analysis
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 mr-2 text-primary" />
                        Discussion & Conclusion
                      </li>
                    </ul>
                  </div>
                  <div className="bg-muted/50 p-4 rounded-lg">
                    <h4 className="font-semibold mb-3 text-foreground">
                      Research Methods:
                    </h4>
                    <ul className="space-y-2 text-muted-foreground">
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 mr-2 text-primary" />
                        Primary Data Collection
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 mr-2 text-primary" />
                        Secondary Data Analysis
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 mr-2 text-primary" />
                        Statistical Testing
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 mr-2 text-primary" />
                        Qualitative Analysis
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 mr-2 text-primary" />
                        Systematic Reviews
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 mr-2 text-primary" />
                        Meta-Analysis
                      </li>
                    </ul>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="importance" className="space-y-4">
                <p className="text-muted-foreground leading-relaxed">
                  Research papers are fundamental to academic and professional
                  advancement. They demonstrate your ability to think
                  critically, conduct independent research, and contribute
                  meaningful insights to your field. Strong research papers can
                  lead to publication opportunities, conference presentations,
                  and career advancement.
                </p>
                <div className="bg-primary/5 p-6 rounded-lg border border-primary/20">
                  <h4 className="font-semibold mb-3 text-foreground">
                    Why Research Papers Matter:
                  </h4>
                  <ul className="grid grid-cols-1 md:grid-cols-2 gap-2 text-muted-foreground">
                    <li>• Advance knowledge in your field</li>
                    <li>• Develop critical thinking skills</li>
                    <li>• Build research methodology expertise</li>
                    <li>• Enhance academic credentials</li>
                    <li>• Create publication opportunities</li>
                    <li>• Support evidence-based decision making</li>
                  </ul>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Research Types */}
        <div className="mb-12">
          <h2 className="text-3xl font-bold text-center mb-8 text-foreground">
            Research Methodologies We Specialize In
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {researchTypes.map((type, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">{type.icon}</div>
                    <div>
                      <h3 className="font-semibold text-foreground mb-2">
                        {type.title}
                      </h3>
                      <p className="text-muted-foreground">
                        {type.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Benefits & Disciplines */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">
                Why Choose Our Research Writing Service?
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                    <span className="text-muted-foreground">{benefit}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-xl">
                Academic Disciplines We Cover
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {disciplines.map((discipline, index) => (
                  <Badge key={index} variant="secondary" className="text-sm">
                    {discipline}
                  </Badge>
                ))}
              </div>
              <p className="text-muted-foreground mt-4 text-sm">
                Don&apos;t see your field? We work with specialists across all
                academic disciplines. Contact us to discuss your specific
                research needs.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* CTA Section */}
        <Card className="bg-gradient-to-r from-primary/10 to-primary/5 border-primary/20">
          <CardContent className="p-8 text-center">
            <h3 className="text-2xl font-bold text-foreground mb-4">
              Ready to Advance Your Research?
            </h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Whether you need help with methodology design, data analysis, or
              complete research paper writing, our expert researchers are ready
              to support your academic journey with rigorous, original research.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg">Submit Research Requirements</Button>
              <Button size="lg" variant="outline">
                Consult with Research Expert
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ResearchPaperWritingService;
