"use client";

import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  ArrowRight, 
  CheckCircle, 
  Star, 
  Users, 
  MessageSquare, 
  Shield,
  Clock,
  Award,
  Sparkles,
  Mail
} from "lucide-react";
import Link from "next/link";

const TestimonialCTA = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [email, setEmail] = useState("");
  const [isSubscribed, setIsSubscribed] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const handleSubscribe = (e: React.FormEvent) => {
    e.preventDefault();
    if (email) {
      setIsSubscribed(true);
      setEmail("");
      setTimeout(() => setIsSubscribed(false), 3000);
    }
  };

  const features = [
    {
      icon: CheckCircle,
      title: "Quality Guarantee",
      description: "100% original, plagiarism-free content"
    },
    {
      icon: Clock,
      title: "On-Time Delivery",
      description: "Meet your deadlines, every time"
    },
    {
      icon: Shield,
      title: "Secure & Confidential",
      description: "Your privacy is our priority"
    },
    {
      icon: Award,
      title: "Expert Writers",
      description: "PhD and Master's qualified professionals"
    }
  ];

  const testimonialHighlights = [
    {
      text: "Exceptional quality and timely delivery!",
      author: "Sarah M.",
      rating: 5
    },
    {
      text: "Best academic writing service I've used.",
      author: "Michael T.",
      rating: 5
    },
    {
      text: "Helped me achieve my academic goals.",
      author: "Emily R.",
      rating: 5
    }
  ];

  return (
    <section 
      ref={sectionRef}
      className="py-20 bg-gradient-to-br from-background via-muted/10 to-primary/5 relative overflow-hidden"
    >
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-primary/5 rounded-full blur-3xl animate-pulse-slow" />
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-chart-1/5 rounded-full blur-3xl animate-pulse-slow delay-1000" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-chart-2/3 rounded-full blur-3xl opacity-10 animate-spin-slow" />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Main CTA Section */}
        <div className={`text-center mb-16 transform transition-all duration-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
          <Badge variant="outline" className="mb-6 px-6 py-2 text-sm font-medium border-primary/20 bg-primary/5 text-primary">
            <Sparkles className="w-4 h-4 mr-2" />
            Ready to Start Your Success Story?
          </Badge>
          
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6 leading-tight">
            Join <span className="text-primary">150,000+</span> Students Who
            <br />
            <span className="bg-gradient-to-r from-primary to-chart-1 bg-clip-text text-transparent">
              Achieved Academic Excellence
            </span>
          </h2>
          
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8 leading-relaxed">
            Don&quot;t let academic stress hold you back. Get expert help from our qualified writers 
            and experience the same success as thousands of students before you.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Button asChild size="lg" className="px-8 py-4 text-lg font-semibold group">
              <Link href="/register/client">
                Get Started Now
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="px-8 py-4 text-lg font-semibold">
              <Link href="/services">
                View Our Services
              </Link>
            </Button>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <Card 
                key={index}
                className={`group bg-card/80 backdrop-blur-sm border-border/50 hover:border-primary/20 transition-all duration-500 hover:shadow-lg hover:shadow-primary/5 transform ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}
                style={{ transitionDelay: `${200 + index * 100}ms` }}
              >
                <CardContent className="p-6 text-center">
                  <div className="w-12 h-12 rounded-xl bg-primary/10 flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300">
                    <Icon className="w-6 h-6 text-primary" />
                  </div>
                  <h3 className="font-semibold text-foreground mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Newsletter Signup */}
        <div className={`transform transition-all duration-1000 delay-600 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
          <Card className="max-w-2xl mx-auto bg-card/80 backdrop-blur-sm border-border/50 mb-16">
            <CardContent className="p-8 text-center">
              <div className="flex items-center justify-center mb-4">
                <Mail className="w-8 h-8 text-primary mr-3" />
                <h3 className="text-2xl font-bold text-foreground">
                  Stay Updated
                </h3>
              </div>
              <p className="text-muted-foreground mb-6">
                Get academic tips, writing guides, and exclusive offers delivered to your inbox
              </p>
              
              {!isSubscribed ? (
                <form onSubmit={handleSubscribe} className="flex flex-col sm:flex-row gap-3">
                  <Input
                    type="email"
                    placeholder="Enter your email address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="flex-1"
                    required
                  />
                  <Button type="submit" className="px-6">
                    Subscribe
                  </Button>
                </form>
              ) : (
                <div className="flex items-center justify-center text-green-600">
                  <CheckCircle className="w-5 h-5 mr-2" />
                  <span className="font-medium">Thank you for subscribing!</span>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Quick Testimonial Highlights */}
        <div className={`transform transition-all duration-1000 delay-800 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16">
            {testimonialHighlights.map((testimonial, index) => (
              <Card key={index} className="bg-card/60 backdrop-blur-sm border-border/30">
                <CardContent className="p-6 text-center">
                  <div className="flex justify-center mb-3">
                    {Array.from({ length: testimonial.rating }).map((_, i) => (
                      <Star key={i} className="w-4 h-4 text-yellow-500 fill-yellow-500" />
                    ))}
                  </div>
                  <blockquote className="text-sm text-muted-foreground italic mb-3">
                  &quot;{testimonial.text}&quot;
                  </blockquote>
                  <cite className="text-xs font-medium text-foreground">
                    - {testimonial.author}
                  </cite>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Final Stats Banner */}
        <div className={`transform transition-all duration-1000 delay-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
          <Card className="bg-gradient-to-r from-primary/10 to-chart-1/10 border-primary/20">
            <CardContent className="p-8">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                <div className="flex items-center justify-center gap-3">
                  <Users className="w-8 h-8 text-primary" />
                  <div>
                    <div className="text-2xl font-bold text-foreground">150K+</div>
                    <div className="text-sm text-muted-foreground">Happy Students</div>
                  </div>
                </div>
                <div className="flex items-center justify-center gap-3">
                  <Star className="w-8 h-8 text-yellow-500" />
                  <div>
                    <div className="text-2xl font-bold text-foreground">4.9/5</div>
                    <div className="text-sm text-muted-foreground">Average Rating</div>
                  </div>
                </div>
                <div className="flex items-center justify-center gap-3">
                  <MessageSquare className="w-8 h-8 text-green-500" />
                  <div>
                    <div className="text-2xl font-bold text-foreground">98%</div>
                    <div className="text-sm text-muted-foreground">Satisfaction Rate</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default TestimonialCTA;
