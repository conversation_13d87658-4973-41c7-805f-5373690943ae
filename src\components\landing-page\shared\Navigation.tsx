// src/components/landing-page/shared/Navigation.tsx
"use client"
import React, { useState } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Menu, X } from "lucide-react";

export const Navigation = () => {
  const [isScrolled, setIsScrolled] = useState(false);

  React.useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      setIsScrolled(scrollPosition > 10);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const navLinks = [
    { title: "Home", href: "#hero" },
    { title: "About", href: "#about" },
    { title: "Services", href: "#services" },
    { title: "Why Us", href: "#why-choose-us" },
    { title: "Testimonials", href: "#testimonials" },
    { title: "Contact", href: "#contact" },
  ];

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled
          ? "bg-white shadow-md py-2"
          : "bg-transparent py-4 text-white"
      }`}
    >
      <div className="container mx-auto px-6 z-1050">
        <div className="flex justify-between items-center">
          <Link
            href="#"
            className="text-xl md:text-2xl font-bold flex items-center gap-2"
          >
            <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center text-white">
              A
            </div>
            <span className={isScrolled ? "text-blue-700" : ""}>
              AcademicWriters
            </span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center gap-1 lg:gap-2">
            {navLinks.map((link, index) => (
              <Link
                key={index}
                href={link.href}
                className={`px-3 py-2 rounded-md hover:bg-white/10 text-sm lg:text-base font-medium transition-colors ${
                  isScrolled ? "text-gray-700 hover:bg-gray-100" : ""
                }`}
              >
                {link.title}
              </Link>
            ))}
          </nav>

          <div className="hidden md:flex items-center gap-4">
            <Button
              variant="ghost"
              className={`font-medium ${
                isScrolled ? "text-gray-700" : "text-white"
              }`}
            >
              Log In
            </Button>
            <Button
              className={`${
                isScrolled
                  ? "bg-blue-600 hover:bg-blue-700"
                  : "bg-white text-blue-700 hover:bg-blue-50"
              }`}
            >
              Get Started
            </Button>
          </div>

          {/* Mobile Navigation */}
          <Sheet>
            <SheetTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="md:hidden"
                aria-label="Menu"
              >
                <Menu
                  className={`h-6 w-6 ${isScrolled ? "text-gray-700" : ""}`}
                />
              </Button>
            </SheetTrigger>
            <SheetContent side="right">
              <div className="flex flex-col h-full">
                <div className="flex justify-between items-center mb-8 pt-2">
                  <Link
                    href="#"
                    className="text-xl font-bold flex items-center gap-2"
                  >
                    <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center text-white">
                      A
                    </div>
                    <span className="text-blue-700">AcademicWriters</span>
                  </Link>
                  <SheetClose>
                    <Button variant="ghost" size="icon">
                      <X className="h-6 w-6" />
                    </Button>
                  </SheetClose>
                </div>

                <nav className="flex flex-col gap-1">
                  {navLinks.map((link, index) => (
                    <SheetClose asChild key={index}>
                      <Link
                        href={link.href}
                        className="px-3 py-4 border-b text-gray-700 hover:text-blue-700 font-medium transition-colors"
                      >
                        {link.title}
                      </Link>
                    </SheetClose>
                  ))}
                </nav>

                <div className="mt-auto pt-8 pb-4 grid grid-cols-2 gap-4">
                  <Button variant="outline">Log In</Button>
                  <Button>Sign Up</Button>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
};

export default Navigation;