// src/components/dashboard/admin/ViewWriterDialog.tsx

/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Eye,
  User,
  Mail,
  Phone,
  Calendar,
  Award,
  Briefcase,
  GraduationCap,
  Star,
  FileText,
  Hash,
  Loader2,
} from "lucide-react";
import { cn } from "@/lib/utils";

// Extended Writer interface to match API response
interface Writer {
  id: string;
  accountId: string | null;
  name: string;
  email: string;
  phone: string;
  role: string;
  isApproved: boolean;
  emailVerified: boolean;
  professionalSummary: string | null;
  experience: string | null;
  competencies: string[];
  educationLevel: string | null;
  rating: number | null;
  createdAt: string;
  updatedAt: string;
  image?: string;
  bidCount?: number;
  jobCount?: number;
  recentBids?: any[];
  recentJobs?: any[];
}

interface ViewWriterDialogProps {
  writerId: string;
  trigger?: React.ReactNode;
}

export default function ViewWriterDialog({ writerId, trigger }: ViewWriterDialogProps) {
  const [open, setOpen] = useState(false);
  const [writer, setWriter] = useState<Writer | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch writer data when dialog opens
  const fetchWriter = async () => {
    if (!writerId) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/users/writers/${writerId}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch writer: ${response.status}`);
      }
      
      const result = await response.json();
      setWriter(result.data);
    } catch (err) {
      console.error("Error fetching writer:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch writer");
    } finally {
      setLoading(false);
    }
  };

  // Fetch data when dialog opens
  useEffect(() => {
    if (open && writerId) {
      fetchWriter();
    }
  }, [open, writerId]);

  // Reset state when dialog closes
  useEffect(() => {
    if (!open) {
      setWriter(null);
      setError(null);
    }
  }, [open]);

  // Format date helper
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Get approval status badge
  const getApprovalBadge = (isApproved: boolean) => {
    return (
      <Badge
        variant={isApproved ? "default" : "secondary"}
        className={cn(
          isApproved 
            ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200" 
            : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
        )}
      >
        {isApproved ? "Approved" : "Pending Approval"}
      </Badge>
    );
  };

  // Render rating stars
  const renderRating = (rating: number | null) => {
    if (!rating) return <span className="text-muted-foreground">No rating yet</span>;
    
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    
    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
        );
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <Star key={i} className="h-4 w-4 fill-yellow-200 text-yellow-400" />
        );
      } else {
        stars.push(
          <Star key={i} className="h-4 w-4 text-gray-300" />
        );
      }
    }
    
    return (
      <div className="flex items-center gap-1">
        {stars}
        <span className="ml-2 text-sm font-medium">{rating.toFixed(1)}</span>
      </div>
    );
  };

  const defaultTrigger = (
    <Button variant="ghost" size="sm" className="justify-start">
      <Eye className="mr-2 h-4 w-4" />
      View
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
              <User className="h-5 w-5 text-primary" />
            </div>
            <div className="flex flex-col">
              <span className="text-xl">
                {loading ? "Loading..." : writer?.name || "Writer Profile"}
              </span>
              <span className="text-sm text-muted-foreground font-normal">
                Writer Profile
              </span>
            </div>
          </DialogTitle>
          <DialogDescription>
            Complete writer information and profile details
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="max-h-[calc(90vh-120px)]">
          <div className="space-y-6 pr-4">
            {loading && (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="h-8 w-8 animate-spin mr-2" />
                <span>Loading writer details...</span>
              </div>
            )}

            {error && (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <p className="text-destructive mb-4">{error}</p>
                  <Button onClick={fetchWriter} size="sm">
                    Try Again
                  </Button>
                </div>
              </div>
            )}

            {writer && !loading && !error && (
              <>
                {/* Basic Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <User className="h-5 w-5" />
                      Basic Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-sm">
                          <Hash className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">Account ID:</span>
                          <span className="font-mono bg-muted px-2 py-1 rounded">
                            {writer.accountId || "Not assigned"}
                          </span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">Email:</span>
                          <span>{writer.email}</span>
                          {writer.emailVerified && (
                            <Badge variant="outline" className="text-xs">Verified</Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">Phone:</span>
                          <span>{writer.phone}</span>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-sm">
                          <Award className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">Status:</span>
                          {getApprovalBadge(writer.isApproved)}
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">Joined:</span>
                          <span>{formatDate(writer.createdAt)}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <Star className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">Rating:</span>
                          {renderRating(writer.rating)}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Activity Stats */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm">Total Bids</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-blue-600">
                        {writer.bidCount || 0}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Proposals submitted
                      </p>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm">Assignments</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-green-600">
                        {writer.jobCount || 0}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Projects completed
                      </p>
                    </CardContent>
                  </Card>
                </div>

                {/* Professional Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Briefcase className="h-5 w-5" />
                      Professional Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Professional Summary */}
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <FileText className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium text-sm">Professional Summary</span>
                      </div>
                      <div className="bg-muted/50 p-3 rounded-lg">
                        {writer.professionalSummary ? (
                          <p className="text-sm leading-relaxed">{writer.professionalSummary}</p>
                        ) : (
                          <p className="text-sm text-muted-foreground italic">
                            No professional summary provided yet
                          </p>
                        )}
                      </div>
                    </div>

                    <Separator />

                    {/* Experience */}
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <Briefcase className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium text-sm">Experience</span>
                      </div>
                      <div className="bg-muted/50 p-3 rounded-lg">
                        {writer.experience ? (
                          <p className="text-sm leading-relaxed">{writer.experience}</p>
                        ) : (
                          <p className="text-sm text-muted-foreground italic">
                            No experience details provided yet
                          </p>
                        )}
                      </div>
                    </div>

                    <Separator />

                    {/* Education Level */}
                    <div className="flex items-center gap-2">
                      <GraduationCap className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium text-sm">Education Level:</span>
                      {writer.educationLevel ? (
                        <Badge variant="outline" className="ml-2">
                          {writer.educationLevel}
                        </Badge>
                      ) : (
                        <span className="text-sm text-muted-foreground italic">
                          Not specified
                        </span>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Competencies */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Award className="h-5 w-5" />
                      Skills & Competencies
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {writer.competencies && writer.competencies.length > 0 ? (
                      <div className="flex flex-wrap gap-2">
                        {writer.competencies.map((competency, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {competency}
                          </Badge>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground italic">
                        No skills or competencies listed yet
                      </p>
                    )}
                  </CardContent>
                </Card>

                {/* Recent Activity */}
                {(writer.recentBids?.length || writer.recentJobs?.length) && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Recent Bids */}
                    {writer.recentBids && writer.recentBids.length > 0 && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-sm">Recent Bids</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2">
                            {writer.recentBids.slice(0, 3).map((bid: any) => (
                              <div key={bid.id} className="flex items-center justify-between text-xs">
                                <span className="font-mono">{bid.id.slice(0, 8)}...</span>
                                <Badge variant="outline" className="text-xs">
                                  {bid.status}
                                </Badge>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {/* Recent Jobs */}
                    {writer.recentJobs && writer.recentJobs.length > 0 && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-sm">Recent Jobs</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2">
                            {writer.recentJobs.slice(0, 3).map((job: any) => (
                              <div key={job.id} className="flex items-center justify-between text-xs">
                                <span className="font-mono">{job.id.slice(0, 8)}...</span>
                                <Badge variant="outline" className="text-xs">
                                  {job.status}
                                </Badge>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                )}

                {/* Last Updated */}
                <div className="text-xs text-muted-foreground text-center pt-4">
                  Profile last updated: {formatDate(writer.updatedAt)}
                </div>
              </>
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}