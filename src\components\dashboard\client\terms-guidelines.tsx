"use client";
import React, { useState } from "react";
import { useCompanyInfo } from "@/hooks/use-company-info";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
// import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import {
  FileText,
  Shield,
  Users,
  CreditCard,
  AlertTriangle,
  UserX,
  Lock,
  Eye,
  Database,
  Share2,
  CheckCircle,
  XCircle,
  Clock,
  Mail,
  Globe,
  Key,
  Settings,
  Bell,
  Download,
  ChevronRight,
  Star,
  TrendingUp,
  Zap,
  Heart,
  BookOpen,
  Target,
  Award,
  Phone,
  MapPin,
  ExternalLink,
  Scale,
} from "lucide-react";

export default function TermsGuidelines() {
  const [activeSection, setActiveSection] = useState("terms");
  const { companyInfo } = useCompanyInfo();

  const writerResponsibilities = [
    {
      icon: FileText,
      text: "Deliver original, plagiarism-free content for all assignments",
      color: "blue",
      highlight: "Zero Tolerance",
      description:
        "Every piece of work must be 100% original and written specifically for each client",
    },
    {
      icon: Clock,
      text: "Meet all specified deadlines and requirements",
      color: "blue",
      highlight: "On-Time Delivery",
      description:
        "Punctuality is crucial for maintaining client trust and platform reputation",
    },
    {
      icon: Users,
      text: "Maintain professional communication with clients",
      color: "blue",
      highlight: "Professional Standards",
      description:
        "Clear, respectful, and timely communication throughout the project lifecycle",
    },
    {
      icon: CheckCircle,
      text: "Follow academic writing standards and citation guidelines",
      color: "blue",
      highlight: "Academic Excellence",
      description:
        "Adherence to proper formatting, citations, and academic integrity standards",
    },
  ];

  const qualityStandards = [
    {
      icon: Shield,
      text: "All work must be original and written specifically for each order",
      color: "green",
      metric: "100%",
      description:
        "Unique content verification through advanced plagiarism detection",
    },
    {
      icon: CheckCircle,
      text: "Proper grammar, spelling, and formatting are required",
      color: "green",
      metric: "A+ Grade",
      description: "Professional editing and proofreading standards maintained",
    },
    {
      icon: FileText,
      text: "Citations must follow the specified academic style guide",
      color: "green",
      metric: "Style Perfect",
      description:
        "APA, MLA, Chicago, Harvard - all formats supported with precision",
    },
    {
      icon: Eye,
      text: "Content must be relevant and meet assignment objectives",
      color: "green",
      metric: "Goal Aligned",
      description:
        "Every deliverable carefully reviewed against project requirements",
    },
  ];

  // const paymentTerms = [
  //   {
  //     icon: CreditCard,
  //     text: "Payment is released upon successful completion and client approval",
  //     color: "purple",
  //     timeframe: "24-48 hours",
  //     security: "Escrow Protected",
  //   },
  //   {
  //     icon: Settings,
  //     text: "Rates are agreed upon before starting any assignment",
  //     color: "purple",
  //     timeframe: "Pre-Project",
  //     security: "Contract Bound",
  //   },
  //   {
  //     icon: FileText,
  //     text: "Revisions may be required without additional compensation",
  //     color: "purple",
  //     timeframe: "Up to 3 rounds",
  //     security: "Quality Assured",
  //   },
  //   {
  //     icon: TrendingUp,
  //     text: "Performance bonuses available for exceptional work",
  //     color: "purple",
  //     timeframe: "Monthly",
  //     security: "Merit Based",
  //   },
  // ];

  const prohibitedActivities = [
    {
      icon: XCircle,
      text: "Plagiarism or submission of non-original content",
      color: "red",
      consequence: "Immediate Termination",
      severity: "Critical",
    },
    {
      icon: Share2,
      text: "Sharing or reselling completed assignments",
      color: "red",
      consequence: "Account Suspension",
      severity: "High",
    },
    {
      icon: Users,
      text: "Direct contact with clients outside the platform",
      color: "red",
      consequence: "Warning → Suspension",
      severity: "Medium",
    },
    {
      icon: AlertTriangle,
      text: "Violation of academic integrity policies",
      color: "red",
      consequence: "Case Review",
      severity: "Variable",
    },
  ];

  const dataCollected = {
    direct: [
      { item: "Names and contact information", icon: Users, sensitive: false },
      {
        item: "Email addresses and phone numbers",
        icon: Mail,
        sensitive: false,
      },
      {
        item: "Payment details and billing information",
        icon: CreditCard,
        sensitive: true,
      },
      {
        item: "Academic credentials and institutional affiliations",
        icon: Award,
        sensitive: true,
      },
    ],
    indirect: [
      {
        item: "IP addresses and device information",
        icon: Globe,
        sensitive: false,
      },
      {
        item: "Browser cookies and session data",
        icon: Settings,
        sensitive: false,
      },
      {
        item: "Usage analytics and behavior patterns",
        icon: TrendingUp,
        sensitive: false,
      },
      {
        item: "Location data (when permitted)",
        icon: MapPin,
        sensitive: false,
      },
    ],
    sensitive: [
      {
        item: "Bank account details for payment processing",
        icon: CreditCard,
        sensitive: true,
      },
      {
        item: "Institutional affiliations and academic records",
        icon: BookOpen,
        sensitive: true,
      },
      {
        item: "Government-issued identification (when required)",
        icon: Shield,
        sensitive: true,
      },
      {
        item: "Communication logs and support tickets",
        icon: Mail,
        sensitive: true,
      },
    ],
  };

  const collectionMethods = [
    {
      icon: FileText,
      title: "Registration Forms",
      description: "Account creation and profile setup",
      details:
        "Initial onboarding process where users provide basic information to create their accounts",
    },
    {
      icon: CreditCard,
      title: "Order Placement",
      description: "Assignment details and payment information",
      details:
        "Transaction data collected during the project commissioning process",
    },
    {
      icon: Settings,
      title: "Automated Tools",
      description: "Cookies, Google Analytics, and tracking pixels",
      details:
        "Technical data gathered through various tracking mechanisms for service improvement",
    },
    {
      icon: Users,
      title: "Communication",
      description: "Support tickets and platform interactions",
      details:
        "All communication data to provide better customer service and platform experience",
    },
  ];

  const securityMeasures = [
    {
      icon: Lock,
      title: "Encryption",
      description: "AES-256 encryption and SSL/TLS protocols",
      grade: "Military Grade",
      status: "Active",
    },
    {
      icon: Key,
      title: "Authentication",
      description: "Multi-factor authentication and secure login",
      grade: "Enterprise Level",
      status: "Mandatory",
    },
    {
      icon: Shield,
      title: "Testing",
      description: "Regular penetration testing and security audits",
      grade: "Professional",
      status: "Quarterly",
    },
    {
      icon: Users,
      title: "Training",
      description: "Staff training on GDPR/CCPA compliance",
      grade: "Certified",
      status: "Ongoing",
    },
  ];

  const userRights = [
    {
      icon: Eye,
      title: "Access",
      description: "View all personal data we hold about you",
      action: "Request Data Export",
      timeframe: "Within 30 days",
    },
    {
      icon: Settings,
      title: "Correction",
      description: "Update or correct your personal information",
      action: "Update Profile",
      timeframe: "Immediate",
    },
    {
      icon: XCircle,
      title: "Deletion",
      description: "Request removal of your account and data",
      action: "Delete Account",
      timeframe: "Within 30 days",
    },
    {
      icon: Download,
      title: "Portability",
      description: "Export your data in a portable format",
      action: "Download Data",
      timeframe: "Within 7 days",
    },
    {
      icon: Mail,
      title: "Opt-out",
      description: "Unsubscribe from marketing communications",
      action: "Manage Preferences",
      timeframe: "Immediate",
    },
    {
      icon: Globe,
      title: "Cookie Control",
      description: "Manage cookie preferences and tracking",
      action: "Cookie Settings",
      timeframe: "Immediate",
    },
  ];

  const stats = [
    { label: "Data Protection", value: "99.9%", icon: Shield },
    { label: "Uptime Guarantee", value: "99.5%", icon: Zap },
    { label: "Client Satisfaction", value: "4.8/5", icon: Star },
    { label: "Response Time", value: "<2hrs", icon: Clock },
  ];

  const contactMethods = [
    {
      icon: Phone,
      title: "Phone Support",
      description: "24/7 support line available",
      contact: companyInfo?.phone || "+****************",
    },
    {
      icon: Bell,
      title: "Notifications",
      description: "Instant alerts for updates",
      contact: "Preferences",
    },
    {
      icon: Target,
      title: "Targeted Support",
      description: "Specialized assistance",
      contact: "Priority Support",
    },
    {
      icon: ChevronRight,
      title: "Quick Access",
      description: "Fast-track support options",
      contact: "Premium Support",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="max-w-7xl mx-auto p-6 space-y-8">
        {/* Header Section */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-3xl blur-3xl"></div>
          <div className="relative text-center space-y-6 py-12">
            <div className="inline-flex items-center gap-2 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 px-6 py-3 rounded-full text-sm font-medium border border-blue-200 dark:border-blue-700">
              <Shield className="w-4 h-4" />
              Legal & Privacy Information
              <Badge variant="secondary" className="ml-2">
                Updated Today
              </Badge>
            </div>

            <h1 className="text-5xl font-bold bg-gradient-to-r from-slate-700 to-blue-600 dark:from-slate-200 dark:to-blue-400 bg-clip-text text-transparent">
              Terms & Privacy Policy
            </h1>

            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto leading-relaxed">
              Comprehensive terms of service and privacy policy designed to
              ensure transparency, GDPR/CCPA compliance, and build lasting trust
              with our platform community.
            </p>

            {/* Stats Section */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
              {stats.map((stat, index) => (
                <Card
                  key={index}
                  className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-0 shadow-lg"
                >
                  <CardContent className="p-4 text-center">
                    <div className="flex items-center justify-center mb-2">
                      <stat.icon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">
                      {stat.value}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">
                      {stat.label}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <Tabs
          value={activeSection}
          onValueChange={setActiveSection}
          className="w-full"
        >
          <TabsList className="grid w-full grid-cols-2 mb-8 h-14 bg-white dark:bg-gray-800 shadow-lg rounded-xl">
            <TabsTrigger
              value="terms"
              className="flex items-center gap-3 text-base font-medium data-[state=active]:bg-blue-500 data-[state=active]:text-white"
            >
              <FileText className="w-5 h-5" />
              Terms of Service
            </TabsTrigger>
            <TabsTrigger
              value="privacy"
              className="flex items-center gap-3 text-base font-medium data-[state=active]:bg-purple-500 data-[state=active]:text-white"
            >
              <Shield className="w-5 h-5" />
              Privacy Policy
            </TabsTrigger>
          </TabsList>

          {/* Terms of Service Tab */}
          <TabsContent value="terms" className="space-y-8">
            <Alert className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-blue-200 dark:border-blue-700">
              <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              <AlertDescription className="text-blue-800 dark:text-blue-200 text-base">
                <strong>Welcome to our platform!</strong> These terms ensure a
                fair, transparent, and professional environment for all users.
                Please read carefully as they form a binding agreement.
              </AlertDescription>
            </Alert>

            <Accordion
              type="single"
              collapsible
              className="w-full space-y-6"
              defaultValue="responsibilities"
            >
              {/* Writer Responsibilities */}
              <AccordionItem
                value="responsibilities"
                className="border-0 bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden"
              >
                <AccordionTrigger className="text-xl font-semibold text-slate-800 dark:text-slate-200 hover:text-blue-600 dark:hover:text-blue-400 px-8 py-6 hover:no-underline">
                  <div className="flex items-center gap-4">
                    <div className="p-3 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900 dark:to-blue-800 rounded-xl">
                      <Users className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="text-left">
                      <div className="text-xl font-bold">
                        1. Writer Responsibilities
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400 font-normal">
                        Core obligations and professional standards
                      </div>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-8 pb-8">
                  <div className="grid gap-6 md:grid-cols-2">
                    {writerResponsibilities.map((item, index) => (
                      <Card
                        key={index}
                        className="group hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30"
                      >
                        <CardContent className="p-6">
                          <div className="flex items-start gap-4">
                            <div className="p-3 bg-blue-100 dark:bg-blue-800 rounded-lg group-hover:scale-110 transition-transform">
                              <item.icon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <Badge variant="secondary" className="text-xs">
                                  {item.highlight}
                                </Badge>
                              </div>
                              <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                                {item.text}
                              </h4>
                              <p className="text-sm text-blue-700 dark:text-blue-300">
                                {item.description}
                              </p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>

              {/* Quality Standards */}
              <AccordionItem
                value="quality"
                className="border-0 bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden"
              >
                <AccordionTrigger className="text-xl font-semibold text-slate-800 dark:text-slate-200 hover:text-green-600 dark:hover:text-green-400 px-8 py-6 hover:no-underline">
                  <div className="flex items-center gap-4">
                    <div className="p-3 bg-gradient-to-br from-green-100 to-emerald-200 dark:from-green-900 dark:to-emerald-800 rounded-xl">
                      <Shield className="w-6 h-6 text-green-600 dark:text-green-400" />
                    </div>
                    <div className="text-left">
                      <div className="text-xl font-bold">
                        2. Quality Standards
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400 font-normal">
                        Excellence benchmarks and requirements
                      </div>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-8 pb-8">
                  <div className="grid gap-6 md:grid-cols-2">
                    {qualityStandards.map((item, index) => (
                      <Card
                        key={index}
                        className="group hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/30 dark:to-emerald-900/30"
                      >
                        <CardContent className="p-6">
                          <div className="flex items-start gap-4">
                            <div className="p-3 bg-green-100 dark:bg-green-800 rounded-lg group-hover:scale-110 transition-transform">
                              <item.icon className="w-6 h-6 text-green-600 dark:text-green-400" />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <Badge className="bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100 text-xs">
                                  {item.metric}
                                </Badge>
                              </div>
                              <h4 className="font-semibold text-green-900 dark:text-green-100 mb-2">
                                {item.text}
                              </h4>
                              <p className="text-sm text-green-700 dark:text-green-300">
                                {item.description}
                              </p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>

              {/* Payment Terms
              <AccordionItem
                value="payment"
                className="border-0 bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden"
              >
                <AccordionTrigger className="text-xl font-semibold text-slate-800 dark:text-slate-200 hover:text-purple-600 dark:hover:text-purple-400 px-8 py-6 hover:no-underline">
                  <div className="flex items-center gap-4">
                    <div className="p-3 bg-gradient-to-br from-purple-100 to-pink-200 dark:from-purple-900 dark:to-pink-800 rounded-xl">
                      <CreditCard className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div className="text-left">
                      <div className="text-xl font-bold">3. Payment Terms</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400 font-normal">
                        Compensation structure and policies
                      </div>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-8 pb-8">
                  <div className="grid gap-6 md:grid-cols-2">
                    {paymentTerms.map((item, index) => (
                      <Card
                        key={index}
                        className="group hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/30 dark:to-pink-900/30"
                      >
                        <CardContent className="p-6">
                          <div className="flex items-start gap-4">
                            <div className="p-3 bg-purple-100 dark:bg-purple-800 rounded-lg group-hover:scale-110 transition-transform">
                              <item.icon className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <Badge className="bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100 text-xs">
                                  {item.timeframe}
                                </Badge>
                                <Badge variant="outline" className="text-xs">
                                  {item.security}
                                </Badge>
                              </div>
                              <h4 className="font-semibold text-purple-900 dark:text-purple-100 mb-2">
                                {item.text}
                              </h4>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem> */}

              {/* Prohibited Activities */}
              <AccordionItem
                value="prohibited"
                className="border-0 bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden"
              >
                <AccordionTrigger className="text-xl font-semibold text-slate-800 dark:text-slate-200 hover:text-red-600 dark:hover:text-red-400 px-8 py-6 hover:no-underline">
                  <div className="flex items-center gap-4">
                    <div className="p-3 bg-gradient-to-br from-red-100 to-red-200 dark:from-red-900 dark:to-red-800 rounded-xl">
                      <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
                    </div>
                    <div className="text-left">
                      <div className="text-xl font-bold">
                        4. Prohibited Activities
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400 font-normal">
                        Violations and enforcement policies
                      </div>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-8 pb-8">
                  <Alert className="bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 border-red-200 dark:border-red-700 mb-6">
                    <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
                    <AlertDescription className="text-red-800 dark:text-red-200">
                      <strong>Zero Tolerance Policy:</strong> Violation of these
                      policies may result in immediate account termination and
                      legal action where applicable.
                    </AlertDescription>
                  </Alert>
                  <div className="grid gap-6 md:grid-cols-2">
                    {prohibitedActivities.map((item, index) => (
                      <Card
                        key={index}
                        className="group hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-red-50 to-orange-50 dark:from-red-900/30 dark:to-orange-900/30 border-red-100 dark:border-red-800"
                      >
                        <CardContent className="p-6">
                          <div className="flex items-start gap-4">
                            <div className="p-3 bg-red-100 dark:bg-red-800 rounded-lg group-hover:scale-110 transition-transform">
                              <item.icon className="w-6 h-6 text-red-600 dark:text-red-400" />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <Badge
                                  variant="destructive"
                                  className="text-xs"
                                >
                                  {item.severity}
                                </Badge>
                                <Badge
                                  variant="outline"
                                  className="text-xs border-red-200 dark:border-red-700"
                                >
                                  {item.consequence}
                                </Badge>
                              </div>
                              <h4 className="font-semibold text-red-900 dark:text-red-100 mb-2">
                                {item.text}
                              </h4>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>

              {/* Account Termination */}
              <AccordionItem
                value="termination"
                className="border-0 bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden"
              >
                <AccordionTrigger className="text-xl font-semibold text-slate-800 dark:text-slate-200 hover:text-orange-600 dark:hover:text-orange-400 px-8 py-6 hover:no-underline">
                  <div className="flex items-center gap-4">
                    <div className="p-3 bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-900 dark:to-orange-800 rounded-xl">
                      <UserX className="w-6 h-6 text-orange-600 dark:text-orange-400" />
                    </div>
                    <div className="text-left">
                      <div className="text-xl font-bold">
                        5. Account Termination
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400 font-normal">
                        Enforcement procedures and appeals
                      </div>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-8 pb-8">
                  <Card className="bg-gradient-to-r from-orange-50 via-red-50 to-pink-50 dark:from-orange-900/30 dark:via-red-900/30 dark:to-pink-900/30 border-orange-200 dark:border-orange-700">
                    <CardContent className="p-8">
                      <div className="flex items-start gap-4 mb-6">
                        <div className="p-3 bg-orange-100 dark:bg-orange-800 rounded-lg">
                          <AlertTriangle className="w-6 h-6 text-orange-600 dark:text-orange-400" />
                        </div>
                        <div>
                          <h4 className="text-lg font-semibold text-orange-900 dark:text-orange-100 mb-2">
                            Termination Policy
                          </h4>
                          <p className="text-orange-800 dark:text-orange-200 leading-relaxed">
                            Violation of these terms may result in account
                            suspension or termination. We reserve the right to
                            review all submissions and take appropriate action
                            for any violations of our quality standards or terms
                            of service.
                          </p>
                        </div>
                      </div>

                      <div className="grid gap-4 md:grid-cols-2">
                        <div className="p-6 bg-white/80 dark:bg-gray-800/80 rounded-xl">
                          <div className="flex items-center gap-2 mb-3">
                            <Heart className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                            <h5 className="font-semibold text-orange-900 dark:text-orange-100">
                              Fair Process
                            </h5>
                          </div>
                          <p className="text-sm text-orange-700 dark:text-orange-300">
                            All termination decisions go through a thorough
                            review process with multiple checkpoints to ensure
                            fairness.
                          </p>
                        </div>

                        <div className="p-6 bg-white/80 dark:bg-gray-800/80 rounded-xl">
                          <div className="flex items-center gap-2 mb-3">
                            <Scale className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                            <h5 className="font-semibold text-orange-900 dark:text-orange-100">
                              Appeal Rights
                            </h5>
                          </div>
                          <p className="text-sm text-orange-700 dark:text-orange-300">
                            Users may appeal termination decisions within 30
                            days by contacting our support team with relevant
                            documentation.
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </TabsContent>

          {/* Privacy Policy Tab */}
          <TabsContent value="privacy" className="space-y-8">
            <Alert className="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 border-purple-200 dark:border-purple-700">
              <Shield className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              <AlertDescription className="text-purple-800 dark:text-purple-200 text-base">
                <strong>Privacy First:</strong> We ensure GDPR/CCPA compliance,
                build trust, and maintain transparency in all our data handling
                practices. Your privacy is our priority.
              </AlertDescription>
            </Alert>

            <Accordion
              type="single"
              collapsible
              className="w-full space-y-6"
              defaultValue="data-collection"
            >
              {/* Data Collection Section */}
              <AccordionItem
                value="data-collection"
                className="border-0 bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden"
              >
                <AccordionTrigger className="text-xl font-semibold px-8 py-6 hover:no-underline">
                  <div className="flex items-center gap-4">
                    <div className="p-3 bg-gradient-to-br from-purple-100 to-blue-200 dark:from-purple-900 dark:to-blue-800 rounded-xl">
                      <Database className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div className="text-left">
                      <div className="text-xl font-bold">
                        1. Data Collection
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400 font-normal">
                        Information we collect and process
                      </div>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-8 pb-8">
                  <div className="grid gap-6 md:grid-cols-3">
                    {/* Direct Data */}
                    <Card className="bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900/30 dark:to-blue-900/30">
                      <CardHeader>
                        <CardTitle className="text-lg flex items-center gap-2">
                          <Users className="w-5 h-5 text-purple-600" />
                          Direct Collection
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-3">
                          {dataCollected.direct.map((item, index) => (
                            <li key={index} className="flex items-center gap-2">
                              <item.icon className="w-4 h-4 text-purple-600" />
                              <span className="text-sm">{item.item}</span>
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>

                    {/* Indirect Data */}
                    <Card className="bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900/30 dark:to-blue-900/30">
                      <CardHeader>
                        <CardTitle className="text-lg flex items-center gap-2">
                          <Globe className="w-5 h-5 text-purple-600" />
                          Indirect Collection
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-3">
                          {dataCollected.indirect.map((item, index) => (
                            <li key={index} className="flex items-center gap-2">
                              <item.icon className="w-4 h-4 text-purple-600" />
                              <span className="text-sm">{item.item}</span>
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>

                    {/* Sensitive Data */}
                    <Card className="bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900/30 dark:to-blue-900/30">
                      <CardHeader>
                        <CardTitle className="text-lg flex items-center gap-2">
                          <Shield className="w-5 h-5 text-purple-600" />
                          Sensitive Data
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-3">
                          {dataCollected.sensitive.map((item, index) => (
                            <li key={index} className="flex items-center gap-2">
                              <item.icon className="w-4 h-4 text-purple-600" />
                              <span className="text-sm">{item.item}</span>
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  </div>

                  <div className="mt-8">
                    <h3 className="text-lg font-semibold mb-4">
                      Collection Methods
                    </h3>
                    <div className="grid gap-4 md:grid-cols-2">
                      {collectionMethods.map((method, index) => (
                        <Card key={index}>
                          <CardContent className="p-4">
                            <div className="flex items-start gap-3">
                              <method.icon className="w-5 h-5 text-purple-600" />
                              <div>
                                <h4 className="font-medium">{method.title}</h4>
                                <p className="text-sm text-gray-600">
                                  {method.description}
                                </p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>

              {/* Security Measures Section */}
              <AccordionItem
                value="security"
                className="border-0 bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden"
              >
                <AccordionTrigger className="text-xl font-semibold px-8 py-6 hover:no-underline">
                  <div className="flex items-center gap-4">
                    <div className="p-3 bg-gradient-to-br from-green-100 to-emerald-200 dark:from-green-900 dark:to-emerald-800 rounded-xl">
                      <Lock className="w-6 h-6 text-green-600 dark:text-green-400" />
                    </div>
                    <div className="text-left">
                      <div className="text-xl font-bold">
                        2. Security Measures
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400 font-normal">
                        How we protect your data
                      </div>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-8 pb-8">
                  <div className="grid gap-6 md:grid-cols-2">
                    {securityMeasures.map((measure, index) => (
                      <Card
                        key={index}
                        className="group hover:shadow-xl transition-all duration-300"
                      >
                        <CardContent className="p-6">
                          <div className="flex items-start gap-4">
                            <div className="p-3 bg-green-100 dark:bg-green-800 rounded-lg group-hover:scale-110 transition-transform">
                              <measure.icon className="w-6 h-6 text-green-600 dark:text-green-400" />
                            </div>
                            <div>
                              <h4 className="font-semibold mb-2">
                                {measure.title}
                              </h4>
                              <p className="text-sm text-gray-600 dark:text-gray-300">
                                {measure.description}
                              </p>
                              <div className="flex items-center gap-2 mt-2">
                                <Badge variant="secondary">
                                  {measure.grade}
                                </Badge>
                                <Badge variant="outline">
                                  {measure.status}
                                </Badge>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>

              {/* User Rights Section */}
              <AccordionItem
                value="rights"
                className="border-0 bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden"
              >
                <AccordionTrigger className="text-xl font-semibold px-8 py-6 hover:no-underline">
                  <div className="flex items-center gap-4">
                    <div className="p-3 bg-gradient-to-br from-blue-100 to-indigo-200 dark:from-blue-900 dark:to-indigo-800 rounded-xl">
                      <Users className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="text-left">
                      <div className="text-xl font-bold">3. User Rights</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400 font-normal">
                        Your rights regarding personal data
                      </div>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-8 pb-8">
                  <div className="grid gap-6 md:grid-cols-2">
                    {userRights.map((right, index) => (
                      <Card
                        key={index}
                        className="group hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30"
                      >
                        <CardContent className="p-6">
                          <div className="flex items-start gap-4">
                            <div className="p-3 bg-blue-100 dark:bg-blue-800 rounded-lg group-hover:scale-110 transition-transform">
                              <right.icon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div className="flex-1">
                              <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                                {right.title}
                              </h4>
                              <p className="text-sm text-blue-700 dark:text-blue-300">
                                {right.description}
                              </p>
                              <div className="flex items-center gap-2 mt-2">
                                <Badge variant="secondary" className="text-xs">
                                  {right.timeframe}
                                </Badge>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>

              {/* Contact Options Section */}
              <AccordionItem
                value="contact"
                className="border-0 bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden"
              >
                <AccordionTrigger className="text-xl font-semibold px-8 py-6 hover:no-underline">
                  <div className="flex items-center gap-4">
                    <div className="p-3 bg-gradient-to-br from-blue-100 to-indigo-200 dark:from-blue-900 dark:to-indigo-800 rounded-xl">
                      <Phone className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="text-left">
                      <div className="text-xl font-bold">
                        4. Contact Options
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400 font-normal">
                        Ways to reach our support team
                      </div>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-8 pb-8">
                  <div className="grid gap-6 md:grid-cols-2">
                    {contactMethods.map((method, index) => (
                      <Card
                        key={index}
                        className="group hover:shadow-xl transition-all duration-300"
                      >
                        <CardContent className="p-6">
                          <div className="flex items-start gap-4">
                            <div className="p-3 bg-blue-100 dark:bg-blue-800 rounded-lg group-hover:scale-110 transition-transform">
                              <method.icon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div>
                              <h4 className="font-semibold mb-2">
                                {method.title}
                              </h4>
                              <p className="text-sm text-gray-600 dark:text-gray-300">
                                {method.description}
                              </p>
                              <Badge variant="outline" className="mt-2">
                                {method.contact}
                              </Badge>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </TabsContent>
        </Tabs>

        {/* Footer Section */}
        <div className="mt-12 text-center space-y-6">
          <div className="flex items-center justify-center gap-4">
            <Button variant="outline" className="gap-2">
              <Download className="w-4 h-4" />
              Download PDF Version
            </Button>
            <Button variant="outline" className="gap-2">
              <ExternalLink className="w-4 h-4" />
              View Full Legal Document
            </Button>
          </div>

          <p className="text-sm text-gray-500 dark:text-gray-400">
            Last updated: {new Date().toLocaleDateString()}
          </p>
        </div>
      </div>
    </div>
  );
}
