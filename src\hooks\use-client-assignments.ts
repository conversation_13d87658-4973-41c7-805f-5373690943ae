// src/hooks/use-client-assignments.ts
"use client";

import { useState, useEffect, useCallback } from "react";
import { useCurrentUserId } from "./use-session-user-id";

interface Assignment {
  id: string;
  taskId: string;
  title: string;
  status: string;
  clientId: string;
  createdAt: string;
  updatedAt: string;
}

interface AssignmentsApiResponse {
  success: boolean;
  message: string;
  data: {
    assignments: Assignment[];
    totalCount: number;
    pagination: {
      total: number;
      page: number;
      limit: number;
      pages: number;
    };
  };
}

interface MonthlyData {
  month: string;
  orders: number;
}

interface UseClientAssignmentsReturn {
  assignments: Assignment[];
  totalOrdersData: MonthlyData[];
  completedOrdersData: MonthlyData[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  totalOrders: number;
  completedOrders: number;
}

export function useClientAssignments(): UseClientAssignmentsReturn {
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [totalOrdersData, setTotalOrdersData] = useState<MonthlyData[]>([]);
  const [completedOrdersData, setCompletedOrdersData] = useState<MonthlyData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalOrders, setTotalOrders] = useState(0);
  const [completedOrders, setCompletedOrders] = useState(0);

  const { userId } = useCurrentUserId();

  const processMonthlyData = useCallback((assignments: Assignment[], filterCompleted = false) => {
    const monthlyMap = new Map<string, number>();
    
    // Initialize last 12 months
    const months = [];
    for (let i = 11; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthKey = date.toISOString().slice(0, 7); // YYYY-MM format
      const monthName = date.toLocaleDateString('en-US', { month: 'short' });
      months.push({ key: monthKey, name: monthName });
      monthlyMap.set(monthKey, 0);
    }

    // Filter assignments if needed
    const filteredAssignments = filterCompleted 
      ? assignments.filter(assignment => 
          assignment.status === "COMPLETED" || assignment.status === "CANCELLED"
        )
      : assignments;

    // Count assignments by month
    filteredAssignments.forEach((assignment) => {
      const monthKey = assignment.createdAt.slice(0, 7); // YYYY-MM format
      if (monthlyMap.has(monthKey)) {
        monthlyMap.set(monthKey, monthlyMap.get(monthKey)! + 1);
      }
    });

    // Convert to chart data format
    return months.map(({ key, name }) => ({
      month: name,
      orders: monthlyMap.get(key) || 0,
    }));
  }, []);

  const fetchAssignments = useCallback(async () => {
    if (!userId) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Fetch all assignments with a high limit to get complete data
      const response = await fetch("/api/assignments?limit=10000");
      const data: AssignmentsApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to fetch assignments");
      }

      if (data.success && data.data) {
        // Filter assignments for the current client
        const clientAssignments = data.data.assignments.filter(
          (assignment) => assignment.clientId === userId
        );

        // Filter completed assignments
        const completedAssignments = clientAssignments.filter(
          (assignment) => assignment.status === "COMPLETED" || assignment.status === "CANCELLED"
        );

        setAssignments(clientAssignments);
        setTotalOrders(clientAssignments.length);
        setCompletedOrders(completedAssignments.length);
        setTotalOrdersData(processMonthlyData(clientAssignments, false));
        setCompletedOrdersData(processMonthlyData(clientAssignments, true));
      } else {
        throw new Error(data.message || "Invalid response format");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch assignments";
      setError(errorMessage);
      console.error("Error fetching client assignments:", err);
    } finally {
      setLoading(false);
    }
  }, [userId, processMonthlyData]);

  const refetch = useCallback(async () => {
    await fetchAssignments();
  }, [fetchAssignments]);

  // Initial fetch
  useEffect(() => {
    fetchAssignments();
  }, [fetchAssignments]);

  return {
    assignments,
    totalOrdersData,
    completedOrdersData,
    loading,
    error,
    refetch,
    totalOrders,
    completedOrders,
  };
}
