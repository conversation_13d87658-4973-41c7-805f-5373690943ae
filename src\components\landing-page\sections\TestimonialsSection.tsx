// src/components/landing-page/sections/TestimonialsSection.tsx
import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback} from "@/components/ui/avatar";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { TestimonialType } from "../types";

export const TestimonialsSection = () => {
  const testimonials: TestimonialType[] = [
    {
      id: 1,
      name: "<PERSON>",
      role: "PhD Student, Psychology",
      content: "The quality of research and analysis in my dissertation literature review was exceptional. My writer understood the complex theoretical framework I needed and delivered beyond my expectations. I've recommended this service to everyone in my cohort.",
      rating: 5
    },
    {
      id: 2,
      name: "<PERSON>",
      role: "Undergraduate, Business Administration",
      content: "I was struggling with my term paper while working full-time. The writer assigned to my project was knowledgeable and responsive. The paper was delivered ahead of schedule with perfect formatting and citations. Definitely worth every penny.",
           rating: 5
    },
    {
      id: 3,
      name: "<PERSON>",
      role: "Master's Student, Engineering",
      content: "I needed help with a complex technical report that involved statistical analysis. The expert who worked on my project had the exact specialization I needed. The work was impeccable, and I received an A. Will definitely use again for future projects.",
            rating: 5
    },
    {
      id: 4,
      name: "David R.",
      role: "Law Student",
      content: "The legal brief I received was thoroughly researched and professionally written. The writer demonstrated an excellent understanding of case law and legal principles. The 24/7 support team was also incredibly helpful throughout the process.",
            rating: 4
    },
    {
      id: 5,
      name: "Amanda P.",
      role: "Medical Student",
      content: "As a medical student with limited time, I needed help with a research paper. My assigned writer had a background in healthcare and delivered a well-structured, evidence-based paper that impressed my professor. The revisions were handled promptly too.",
            rating: 5
    }
  ];

  const renderStars = (rating: number) => {
    return (
      <div className="flex">
        {[...Array(5)].map((_, i) => (
          <svg
            key={i}
            className={`w-4 h-4 ${i < rating ? "text-amber-500" : "text-muted"}`}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        ))}
      </div>
    );
  };

  return (
    <section id="testimonials" className="py-8 bg-gradient-to-b from-background to-muted">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4 px-4 py-1 border-amber-200 text-amber-700 dark:text-amber-400 bg-amber-50/50 dark:bg-amber-500/10">
            Success Stories
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            What Our Students Say
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto text-lg">
            Real feedback from students who achieved academic success with our writing services
          </p>
        </div>

        <div className="max-w-5xl mx-auto">
          <Carousel
            opts={{
              align: "start",
              loop: true,
            }}
            className="w-full"
          >
            <CarouselContent>
              {testimonials.map((testimonial) => (
                <CarouselItem key={testimonial.id} className="md:basis-1/2 lg:basis-1/3 p-2">
                  <Card className="h-full border-border shadow-md">
                    <CardContent className="p-6">
                      <div className="flex items-start gap-2 mb-4">
                        <Avatar className="h-10 w-10 border-2 border-background shadow-sm">
                          <AvatarFallback>{testimonial.name.charAt(0)}</AvatarFallback>
                                                  </Avatar>
                        <div>
                          <h4 className="font-semibold text-foreground">{testimonial.name}</h4>
                          <p className="text-xs text-muted-foreground">{testimonial.role}</p>
                        </div>
                      </div>
                      
                      <blockquote className="text-muted-foreground relative pl-4 border-l-2 border-amber-500/50">
                        <p className="italic text-sm">{testimonial.content}</p>
                      </blockquote>
                    </CardContent>
                    <CardFooter className="px-6 pt-0 pb-6 flex justify-between items-center">
                      {renderStars(testimonial.rating)}
                      <Badge variant="secondary" className="text-amber-700 dark:text-amber-400">Verified Order</Badge>
                    </CardFooter>
                  </Card>
                </CarouselItem>
              ))}
            </CarouselContent>
            <div className="hidden md:flex justify-end gap-2 mt-8">
              <CarouselPrevious />
              <CarouselNext />
            </div>
          </Carousel>
        </div>

        <div className="mt-16 max-w-3xl mx-auto bg-amber-50/50 dark:bg-amber-500/5 rounded-xl p-6 md:p-8 shadow-sm border border-amber-200/50 dark:border-amber-500/10">
          <div className="flex flex-col md:flex-row gap-6 items-center">
            <div className="md:w-1/4 flex justify-center">
              <div className="w-24 h-24 rounded-full bg-amber-500 dark:bg-amber-500/80 flex items-center justify-center text-white">
                <span className="text-4xl font-bold">98%</span>
              </div>
            </div>
            <div className="md:w-3/4 text-center md:text-left">
              <h3 className="text-xl font-bold text-foreground mb-2">Satisfaction Rate</h3>
              <p className="text-muted-foreground">
                98% of our customers report better grades and improved academic performance after using our services. Our commitment to quality ensures your success.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;