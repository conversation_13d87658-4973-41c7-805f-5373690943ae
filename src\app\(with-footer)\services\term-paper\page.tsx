import React from "react";
import {
  Card,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle,
  Clock,
  Users,
  Award,
  BookOpen,
  FileText,
} from "lucide-react";

const TermPaperWritingService: React.FC = () => {
  const features = [
    {
      icon: <CheckCircle className="h-5 w-5 text-primary" />,
      title: "Original Research",
      description:
        "Every term paper is crafted from scratch with thorough research and analysis",
    },
    {
      icon: <Clock className="h-5 w-5 text-primary" />,
      title: "Timely Delivery",
      description:
        "We guarantee on-time submission to meet your academic deadlines",
    },
    {
      icon: <Users className="h-5 w-5 text-primary" />,
      title: "Expert Writers",
      description:
        "PhD and Master&apos;s level writers with subject matter expertise",
    },
    {
      icon: <Award className="h-5 w-5 text-primary" />,
      title: "Quality Assurance",
      description: "Multiple rounds of editing and plagiarism checking",
    },
  ];

  const steps = [
    "Submit your term paper requirements and deadline",
    "Get matched with qualified writers who bid on your project",
    "Choose your preferred writer based on credentials and price",
    "Collaborate throughout the writing process",
    "Receive your completed, polished term paper",
  ];

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-12 max-w-6xl">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <Badge variant="secondary" className="mb-4">
            <BookOpen className="h-4 w-4 mr-2" />
            Academic Writing Services
          </Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
            Professional Term Paper Writing Service
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            Get expertly written term papers that demonstrate deep understanding
            of your subject matter. Our qualified academic writers deliver
            original, well-researched papers tailored to your specific
            requirements.
          </p>
          <Button size="lg" className="mr-4">
            Order Your Term Paper
          </Button>
          <Button size="lg" variant="outline">
            View Sample Papers
          </Button>
        </div>

        {/* What is a Term Paper Section */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle className="flex items-center text-2xl">
              <FileText className="h-6 w-6 mr-3 text-primary" />
              What is a Term Paper?
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground leading-relaxed">
              A term paper is a comprehensive research assignment that students
              complete over the course of an academic term or semester. Unlike
              regular essays, term papers require extensive research, critical
              analysis, and in-depth exploration of a specific topic within your
              field of study.
            </p>
            <p className="text-muted-foreground leading-relaxed">
              Term papers typically range from 8-20 pages and require multiple
              credible sources, proper academic formatting (APA, MLA, Chicago),
              and demonstrate your ability to synthesize information, present
              arguments, and draw meaningful conclusions. They often contribute
              significantly to your final course grade and showcase your mastery
              of course material.
            </p>
            <div className="bg-muted/50 p-6 rounded-lg">
              <h4 className="font-semibold mb-3 text-foreground">
                Key Components of a Term Paper:
              </h4>
              <ul className="grid grid-cols-1 md:grid-cols-2 gap-2 text-muted-foreground">
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 mr-2 text-primary" />
                  Title Page & Abstract
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 mr-2 text-primary" />
                  Introduction & Thesis Statement
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 mr-2 text-primary" />
                  Literature Review
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 mr-2 text-primary" />
                  Methodology & Analysis
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 mr-2 text-primary" />
                  Results & Discussion
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 mr-2 text-primary" />
                  Conclusion & References
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Why Choose Us Section */}
        <div className="mb-12">
          <h2 className="text-3xl font-bold text-center mb-8 text-foreground">
            Why Students Choose Our Term Paper Writing Service
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {features.map((feature, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">{feature.icon}</div>
                    <div>
                      <h3 className="font-semibold text-foreground mb-2">
                        {feature.title}
                      </h3>
                      <p className="text-muted-foreground">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* How It Works Section */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle className="text-2xl text-center">
              How Our Term Paper Service Works
            </CardTitle>
            <CardDescription className="text-center">
              Simple steps to get your professionally written term paper
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {steps.map((step, index) => (
                <div key={index} className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground font-semibold text-sm">
                    {index + 1}
                  </div>
                  <p className="text-muted-foreground pt-1">{step}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* CTA Section */}
        <Card className="bg-primary/5 border-primary/20">
          <CardContent className="p-8 text-center">
            <h3 className="text-2xl font-bold text-foreground mb-4">
              Ready to Excel in Your Term Paper?
            </h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Don&apos;t let a challenging term paper overwhelm you. Our expert
              writers are here to help you succeed with high-quality, original
              research papers that meet all academic standards.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg">Get Started Today</Button>
              <Button size="lg" variant="outline">
                Chat with Support
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TermPaperWritingService;
