"use client";

import { motion } from "framer-motion";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  FileText, 
  Users, 
  CheckCircle, 
  Clock,
  ArrowRight,
  Play,
  Star
} from "lucide-react";

export function HowItWorksHero() {
  const quickStats = [
    { icon: FileText, text: "4 Simple Steps", value: "Easy Process" },
    { icon: Clock, text: "Quick Turnaround", value: "Fast Delivery" },
    { icon: Users, text: "Expert Writers", value: "500+ Professionals" },
    { icon: Star, text: "Quality Assured", value: "98% Satisfaction" },
  ];

  return (
    <section className="relative py-20 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5" />
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          {/* Badge */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-6"
          >
            <Badge 
              variant="secondary" 
              className="px-4 py-2 text-sm font-medium bg-primary/10 text-primary border-primary/20"
            >
              <Play className="w-4 h-4 mr-2" />
              Simple & Transparent Process
            </Badge>
          </motion.div>

          {/* Main Heading */}
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.1 }}
            className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-primary via-primary/80 to-primary/60 bg-clip-text text-transparent"
          >
            How It Works
            <br />
            <span className="text-foreground">Made Simple</span>
          </motion.h1>

          {/* Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-lg md:text-xl text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed"
          >
            Getting academic help has never been easier. Our streamlined 4-step process ensures 
            you receive high-quality work quickly and efficiently, with complete transparency every step of the way.
          </motion.p>

          {/* Quick Stats */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-10"
          >
            {quickStats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
                className="flex flex-col items-center p-4 rounded-lg bg-card/50 border border-border/50 hover:border-primary/30 transition-colors"
              >
                <stat.icon className="w-8 h-8 text-primary mb-2" />
                <span className="text-sm font-medium text-center mb-1">{stat.text}</span>
                <span className="text-xs text-muted-foreground text-center">{stat.value}</span>
              </motion.div>
            ))}
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <Link href="/create-order">
              <Button 
                size="lg" 
                className="bg-primary text-primary-foreground hover:bg-primary/90 px-8 py-3 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 group"
              >
                <FileText className="w-5 h-5 mr-2" />
                Start Your Order
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </Button>
            </Link>
            
            <Link href="#steps">
              <Button 
                variant="outline" 
                size="lg"
                className="px-8 py-3 text-lg font-semibold border-2 hover:border-primary hover:text-primary transition-all duration-300"
              >
                <Play className="w-5 h-5 mr-2" />
                See How It Works
              </Button>
            </Link>
          </motion.div>

          {/* Process Preview */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="mt-12 pt-8 border-t border-border/50"
          >
            <p className="text-sm text-muted-foreground mb-6">
              Our proven process has helped thousands of students achieve academic success
            </p>
            
            {/* Mini Process Steps */}
            <div className="flex justify-center items-center gap-4 md:gap-8">
              <div className="flex items-center text-sm">
                <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-semibold mr-2">
                  1
                </div>
                <span className="text-muted-foreground">Place Order</span>
              </div>
              
              <ArrowRight className="w-4 h-4 text-muted-foreground" />
              
              <div className="flex items-center text-sm">
                <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-semibold mr-2">
                  2
                </div>
                <span className="text-muted-foreground">Writer Assigned</span>
              </div>
              
              <ArrowRight className="w-4 h-4 text-muted-foreground hidden md:block" />
              
              <div className="flex items-center text-sm hidden md:flex">
                <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-semibold mr-2">
                  3
                </div>
                <span className="text-muted-foreground">Work Completed</span>
              </div>
              
              <ArrowRight className="w-4 h-4 text-muted-foreground hidden md:block" />
              
              <div className="flex items-center text-sm hidden md:flex">
                <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-semibold mr-2">
                  4
                </div>
                <span className="text-muted-foreground">Delivered</span>
              </div>
            </div>

            {/* Trust Indicators */}
            <div className="mt-8 flex justify-center items-center gap-8 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>Secure Process</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-blue-500" />
                <span>On-Time Delivery</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="w-4 h-4 text-yellow-500" />
                <span>Quality Guaranteed</span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
