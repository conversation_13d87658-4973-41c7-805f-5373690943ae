// src/app/api/todos/route.ts
import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import {
  apiSuccess,
  apiError,
  getCurrentUserId,
  safeJsonDate,
  formatZodErrors,
} from "@/lib/api-utils";
import { todoCreateSchema, paginationSchema } from "@/lib/validations";
import { TodoResponse } from "@/types/api";
import { Prisma } from "@prisma/client";

export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    const currentUserId = await getCurrentUserId();
    if (!currentUserId) {
      return apiError("Authentication required", 401);
    }

    const url = new URL(req.url);
    const pageParam = url.searchParams.get("page") ?? "1";
    const limitParam = url.searchParams.get("limit") ?? "50";
    const isCompleted = url.searchParams.get("isCompleted");

    const { page, limit } = paginationSchema.parse({
      page: parseInt(pageParam, 10),
      limit: parseInt(limitParam, 10),
    });

    const skip = (page - 1) * limit;

    // Build where clause
    const where: Prisma.TodoWhereInput = {
      userId: currentUserId,
    };

    if (isCompleted !== null) {
      where.isCompleted = isCompleted === "true";
    }

    // Fetch todos with pagination
    const [todos, totalCount] = await Promise.all([
      prisma.todo.findMany({
        where,
        orderBy: [
          { isCompleted: "asc" }, // Incomplete todos first
          { createdAt: "desc" }, // Most recent first
        ],
        skip,
        take: limit,
      }),
      prisma.todo.count({ where }),
    ]);

    return apiSuccess({
      todos: safeJsonDate(todos) as TodoResponse[],
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching todos:", error);
    return apiError("Failed to fetch todos", 500);
  }
}

export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    const currentUserId = await getCurrentUserId();
    if (!currentUserId) {
      return apiError("Authentication required", 401);
    }

    // Get raw body for debugging
    const rawBody = await req.text();
    console.log("Raw request body:", rawBody);

    // Parse JSON manually for debugging
    let requestData;
    try {
      requestData = JSON.parse(rawBody);
      console.log("Parsed request data:", requestData);
    } catch (parseError) {
      console.error("JSON parse error:", parseError);
      return apiError("Invalid JSON in request body", 400);
    }

    // Validate using schema
    const validationResult = todoCreateSchema.safeParse(requestData);
    if (!validationResult.success) {
      console.error("Validation errors:", validationResult.error.errors);
      return apiError("Validation error", 400, formatZodErrors(validationResult.error.errors));
    }

    const todoData = validationResult.data;
    console.log("Validated todo data:", todoData);

    // Ensure the todo is created for the current user
    const createData = {
      ...todoData,
      userId: currentUserId,
      dueDate: todoData.dueDate ? new Date(todoData.dueDate) : null,
    };

    console.log("Final create data:", createData);

    const todo = await prisma.todo.create({
      data: createData,
    });

    return apiSuccess(
      safeJsonDate(todo) as TodoResponse,
      "Todo created successfully"
    );
  } catch (error) {
    console.error("Error creating todo:", error);
    return apiError("Failed to create todo", 500);
  }
}
