# Supabase File Upload Implementation

## Overview
This implementation adds comprehensive file upload functionality using Supabase Storage for academic documents (PDF, DOC, DOCX, PPT, PPTX, TXT, RTF, XLS, XLSX) while preserving original file names for academic submissions.

## Supabase Storage Setup
**Bucket Name:** `academic-files`

### Required Supabase Configuration
1. Create a bucket named `academic-files` in your Supabase Storage
2. Set appropriate permissions for authenticated users
3. Ensure your environment variables are set:
   - `NEXT_PUBLIC_SUPABASE_URL`
   - `NEXT_PUBLIC_SUPABASE_ANON_KEY`

## Implementation Details

### 1. Database Schema Updates
- Added `FileAttachment` model to Prisma schema
- Added relation to `Assignment` model
- Fields: id, assignmentId, fileName, originalName, fileUrl, fileSize, fileType, uploadedAt

### 2. Core Components

#### `useSupabaseFileUpload` Hook
- Location: `src/hooks/useSupabaseFileUpload.ts`
- Handles direct Supabase uploads
- Validates file types and sizes
- Preserves original file names with unique identifiers

#### `FileUpload` Component
- Location: `src/components/file-upload.tsx`
- Drag & drop interface
- Progress tracking
- File preview and download
- Assignment-specific uploads when `assignmentId` is provided

### 3. API Routes

#### File Upload API
- Route: `/api/files/upload`
- Method: POST
- Handles assignment-specific file uploads
- Validates permissions and file types
- Stores metadata in database

#### File Management API
- Route: `/api/assignments/[id]/files`
- Methods: GET, DELETE
- Retrieves and manages files for specific assignments
- Proper authorization checks

### 4. Integration Points

#### Quick Create Assignment (Admin)
- Location: `src/components/dashboard/admin/quick-create-assignment.tsx`
- Integrated FileUpload component
- Supports multiple file uploads during assignment creation

#### Client Quick Create Assignment
- Location: `src/components/dashboard/client/client-quick-create-assignment.tsx`
- Same functionality as admin version
- Client-specific file uploads

#### Order Details Page
- Location: `src/app/(without-footer)/order/[id]/page.tsx`
- Displays uploaded files with download links
- Allows additional file uploads
- Real-time file management

### 5. File Type Support
- PDF documents
- Microsoft Word (DOC, DOCX)
- PowerPoint presentations (PPT, PPTX)
- Plain text files (TXT)
- Rich Text Format (RTF)
- Excel spreadsheets (XLS, XLSX)

### 6. Security Features
- File type validation
- File size limits (50MB max)
- User authorization checks
- Assignment-specific access control
- Filename sanitization for Supabase Storage compatibility
- Preserved original file names for academic integrity (stored in database)

### 7. Key Features
- **Drag & Drop Interface**: Modern file upload experience
- **Progress Tracking**: Real-time upload progress
- **File Preview**: Visual file list with metadata
- **Download Functionality**: Direct download links for all files
- **Permission-based Access**: Only authorized users can upload/view files
- **Original Name Preservation**: Important for academic submissions
- **Responsive Design**: Works on all device sizes

## Usage Examples

### Basic File Upload
```tsx
<FileUpload
  onFileUpload={handleFileUpload}
  onFileRemove={handleFileRemove}
  uploadedFiles={uploadedFiles}
  folder="assignments"
  multiple={true}
  maxFiles={10}
/>
```

### Assignment-Specific Upload
```tsx
<FileUpload
  onFileUpload={handleFileUpload}
  onFileRemove={handleFileRemove}
  uploadedFiles={uploadedFiles}
  folder="assignments"
  multiple={true}
  maxFiles={20}
  assignmentId={assignmentId}
/>
```

## File Structure
```
src/
├── components/
│   └── file-upload.tsx
├── hooks/
│   └── useSupabaseFileUpload.ts
├── lib/
│   └── supabase.ts
├── types/
│   └── upload.ts (updated)
├── app/api/
│   ├── files/upload/route.ts
│   └── assignments/[id]/files/route.ts
└── components/dashboard/
    ├── admin/quick-create-assignment.tsx (updated)
    └── client/client-quick-create-assignment.tsx (updated)
```

## Next Steps
1. Create the `academic-files` bucket in Supabase Storage
2. Set appropriate bucket policies
3. Test file uploads in development
4. Configure production environment variables
5. Implement file cleanup jobs (optional)

## Notes
- Files are not automatically deleted from Supabase Storage when removed from database (prevents data loss)
- Consider implementing a cleanup job for orphaned files
- File URLs are public but require knowledge of the exact path
- All file operations are logged for audit purposes
- Filenames are sanitized to remove invalid characters (quotes, special chars) and spaces are replaced with underscores
- Original filenames are preserved in the database for display purposes
