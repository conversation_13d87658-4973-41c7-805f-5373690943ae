import * as React from 'react';
import { useCallback, useRef, useState } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { X, Upload, Image as ImageIcon } from 'lucide-react';
import { useCloudinaryUpload } from '@/hooks/useCloudinaryUpload';
import { toast } from 'sonner';

interface ImageUploadProps {
  onImageUpload: (url: string) => void;
  onAltTextChange?: (altText: string) => void;
  currentImageUrl?: string;
  currentAltText?: string;
  className?: string;
  showAltTextInput?: boolean;
}

export const ImageUpload: React.FC<ImageUploadProps> = ({
  onImageUpload,
  onAltTextChange,
  currentImageUrl,
  currentAltText = '',
  className = '',
  showAltTextInput = false
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragActive, setDragActive] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentImageUrl || null);
  const [altText, setAltText] = useState<string>(currentAltText);

  const { uploadImage, isUploading, uploadProgress, error, resetError } = useCloudinaryUpload();

  // Handle alt text changes
  const handleAltTextChange = useCallback((value: string) => {
    setAltText(value);
    if (onAltTextChange) {
      onAltTextChange(value);
    }
  }, [onAltTextChange]);

  const handleFileSelect = useCallback(async (file: File) => {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Invalid file type. Only JPEG, PNG, WebP, and GIF are allowed.');
      return;
    }

    // Validate file size (10MB max)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      toast.error('File size too large. Maximum size is 10MB.');
      return;
    }

    // Create preview
    const objectUrl = URL.createObjectURL(file);
    setPreviewUrl(objectUrl);

    try {
      resetError();
      const result = await uploadImage(file);
      onImageUpload(result.url);
      toast.success('Image uploaded successfully!');
      
      // Clean up object URL
      URL.revokeObjectURL(objectUrl);
    } catch (uploadError) {
      // Clean up object URL on error
      URL.revokeObjectURL(objectUrl);
      setPreviewUrl(currentImageUrl || null);
      
      const errorMessage = uploadError instanceof Error ? uploadError.message : 'Upload failed';
      toast.error(errorMessage);
    }
  }, [uploadImage, onImageUpload, currentImageUrl, resetError]);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  }, [handleFileSelect]);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const file = e.dataTransfer.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  }, [handleFileSelect]);

  const handleButtonClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const handleRemoveImage = useCallback(() => {
    setPreviewUrl(null);
    onImageUpload('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [onImageUpload]);

  return (
    <div className={`space-y-4 ${className}`}>
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileInputChange}
        className="hidden"
        disabled={isUploading}
      />

      {previewUrl ? (
        <div className="relative">
          <div className="relative w-full h-48 bg-gray-100 rounded-lg overflow-hidden border-2 border-dashed border-gray-300">
            <Image
              src={previewUrl}
              alt="Preview"
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              unoptimized={previewUrl.startsWith('blob:')}
            />
            <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity z-10">
              <Button
                type="button"
                variant="secondary"
                size="sm"
                onClick={handleButtonClick}
                disabled={isUploading}
                className="mr-2"
              >
                <Upload className="w-4 h-4 mr-1" />
                Replace
              </Button>
              <Button
                type="button"
                variant="destructive"
                size="sm"
                onClick={handleRemoveImage}
                disabled={isUploading}
              >
                <X className="w-4 h-4 mr-1" />
                Remove
              </Button>
            </div>
          </div>
        </div>
      ) : (
        <div
          className={`relative w-full h-48 border-2 border-dashed rounded-lg transition-colors cursor-pointer ${
            dragActive
              ? 'border-blue-500 bg-blue-50'
              : 'border-gray-300 hover:border-gray-400'
          } ${isUploading ? 'pointer-events-none opacity-50' : ''}`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
          onClick={handleButtonClick}
        >
          <div className="flex flex-col items-center justify-center h-full text-gray-500">
            <ImageIcon className="w-12 h-12 mb-4" />
            <p className="text-lg font-medium mb-2">
              {isUploading ? 'Uploading...' : 'Click to upload or drag and drop'}
            </p>
            <p className="text-sm">PNG, JPG, WebP or GIF (max. 10MB)</p>
          </div>
        </div>
      )}

      {isUploading && (
        <div className="space-y-2">
          <Progress value={uploadProgress} className="w-full" />
          <p className="text-sm text-gray-600 text-center">
            Uploading... {uploadProgress}%
          </p>
        </div>
      )}

      {error && (
        <div className="text-red-600 text-sm bg-red-50 p-2 rounded">
          {error}
        </div>
      )}

      {showAltTextInput && (
        <div className="space-y-2">
          <Label htmlFor="image-alt-text" className="text-sm font-medium">
            Alt Text <span className="text-red-500">*</span>
            <span className="text-xs text-muted-foreground ml-1">
              (Important for SEO and accessibility)
            </span>
          </Label>
          <Input
            id="image-alt-text"
            type="text"
            value={altText}
            onChange={(e) => handleAltTextChange(e.target.value)}
            placeholder="Describe the image for screen readers and SEO..."
            maxLength={125}
            className="w-full"
            disabled={isUploading}
          />
          <p className="text-xs text-muted-foreground">
            {altText.length}/125 characters. Keep it descriptive but concise.
          </p>
        </div>
      )}
    </div>
  );
};