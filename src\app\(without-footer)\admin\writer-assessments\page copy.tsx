// "use client";

// import React, { useState, useEffect } from "react";
// import {
//   Card,
//   CardContent,
//   CardDescription,
//   <PERSON>Header,
//   CardTitle,
// } from "@/components/ui/card";
// import {
//   Table,
//   TableBody,
//   TableCell,
//   TableHead,
//   TableHeader,
//   TableRow,
// } from "@/components/ui/table";
// import { Button } from "@/components/ui/button";
// import { Badge } from "@/components/ui/badge";
// import { FileText, Eye, Trash2, Loader2 } from "lucide-react";
// // import { WriterAssessmentAnswers } from "./components/WriterAssessmentAnswers";
// import { toast } from "sonner";
// import { WriterAssessmentAnswers } from "@/components/dashboard/admin/writers-assessment-answers";

// // Types based on your schema
// interface WriterAnswer {
//   writerId: string;
//   multipleChoiceAnswers: string[];
//   essayText: string;
//   status?: "Passed" | "Failed";
// }

// interface Assessment {
//   id: string;
//   title: string;
//   multipleChoiceQuiz: Array<{
//     question: string;
//     options: string[];
//     correctAnswer: string;
//   }>;
//   essayExam: {
//     topic: string;
//     rubrics: string;
//   };
//   writersAnswers: WriterAnswer[];
//   createdAt: string;
//   updatedAt: string;
// }

// interface TableRow {
//   writerId: string;
//   assessmentTitle: string;
//   status: "Passed" | "Failed" | "Pending";
//   assessmentId: string;
//   writerAnswer: WriterAnswer;
// }

// export default function WriterAssessmentsPage() {
//   const [assessments, setAssessments] = useState<Assessment[]>([]);
//   const [loading, setLoading] = useState(true);
//   const [error, setError] = useState<string | null>(null);
//   const [selectedWriter, setSelectedWriter] = useState<{
//     writerId: string;
//     assessment: Assessment;
//     writerAnswer: WriterAnswer;
//   } | null>(null);
//   const [isDialogOpen, setIsDialogOpen] = useState(false);

//   // Fetch assessments data
//   useEffect(() => {
//     const fetchAssessments = async () => {
//       try {
//         console.log("🔄 [WriterAssessments] Starting to fetch assessments...");
//         const response = await fetch("/api/admin/assessments", {
//           method: "GET",
//           headers: {
//             "Content-Type": "application/json",
//           },
//           credentials: "include",
//         });

//         console.log("📝 [WriterAssessments] Response status:", response.status);

//         if (!response.ok) {
//           const errorData = await response.json();
//           console.error("❌ [WriterAssessments] Error response:", errorData);
//           throw new Error(
//             `HTTP error! status: ${response.status} - ${errorData.error || "Unknown error"}`
//           );
//         }

//         const assessmentsData = await response.json();
//         console.log(
//           "✅ [WriterAssessments] Fetched assessments:",
//           assessmentsData
//         );

//         setAssessments(assessmentsData);
//       } catch (err) {
//         console.error(
//           "❌ [WriterAssessments] Error fetching assessments:",
//           err
//         );
//         setError("Failed to load assessments data. Please try again later.");
//         toast.error("Failed to load assessments");
//       } finally {
//         setLoading(false);
//       }
//     };

//     fetchAssessments();
//   }, []);

//   // Transform assessments data into table rows
//   const tableData: TableRow[] = assessments.flatMap((assessment) =>
//     assessment.writersAnswers.map((writerAnswer) => ({
//       writerId: writerAnswer.writerId,
//       assessmentTitle: assessment.title,
//       status: writerAnswer.status || "Pending",
//       assessmentId: assessment.id,
//       writerAnswer,
//     }))
//   );

//   const handleView = (writerId: string, assessmentId: string) => {
//     const assessment = assessments.find((a) => a.id === assessmentId);
//     const writerAnswer = assessment?.writersAnswers.find(
//       (wa) => wa.writerId === writerId
//     );

//     if (assessment && writerAnswer) {
//       setSelectedWriter({ writerId, assessment, writerAnswer });
//       setIsDialogOpen(true);
//     }
//   };

//   const handleDelete = async (writerId: string, assessmentId: string) => {
//     try {
//       const response = await fetch(
//         `/api/admin/assessments/${assessmentId}/writers/${writerId}`,
//         {
//           method: "DELETE",
//           credentials: "include",
//         }
//       );

//       if (!response.ok) {
//         throw new Error("Failed to delete writer answer");
//       }

//       // Update local state
//       setAssessments((prev) =>
//         prev.map((assessment) =>
//           assessment.id === assessmentId
//             ? {
//                 ...assessment,
//                 writersAnswers: assessment.writersAnswers.filter(
//                   (wa) => wa.writerId !== writerId
//                 ),
//               }
//             : assessment
//         )
//       );

//       toast.success("Writer answer deleted successfully");
//     } catch (err) {
//       console.error("Error deleting writer answer:", err);
//       toast.error("Failed to delete writer answer");
//     }
//   };

//   const handleStatusUpdate = (
//     writerId: string,
//     assessmentId: string,
//     newStatus: "Passed" | "Failed"
//   ) => {
//     setAssessments((prev) =>
//       prev.map((assessment) =>
//         assessment.id === assessmentId
//           ? {
//               ...assessment,
//               writersAnswers: assessment.writersAnswers.map((wa) =>
//                 wa.writerId === writerId ? { ...wa, status: newStatus } : wa
//               ),
//             }
//           : assessment
//       )
//     );
//     setIsDialogOpen(false);
//     toast.success(`Writer status updated to ${newStatus}`);
//   };

//   const getStatusColor = (status: string) => {
//     switch (status) {
//       case "Passed":
//         return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
//       case "Failed":
//         return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
//       default:
//         return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
//     }
//   };

//   if (loading) {
//     return (
//       <div className="container mx-auto py-8 px-4">
//         <div className="flex items-center justify-center py-12">
//           <Loader2 className="h-8 w-8 animate-spin" />
//           <span className="ml-2">Loading assessments...</span>
//         </div>
//       </div>
//     );
//   }

//   if (error) {
//     return (
//       <div className="container mx-auto py-8 px-4">
//         <Card>
//           <CardContent className="py-12">
//             <div className="text-center text-destructive">
//               <p>{error}</p>
//             </div>
//           </CardContent>
//         </Card>
//       </div>
//     );
//   }

//   return (
//     <div className="container mx-auto py-8 px-4">
//       <div className="mb-8">
//         <div className="flex items-center gap-3 mb-4">
//           <div className="p-2 bg-primary/10 rounded-lg">
//             <FileText className="h-6 w-6 text-primary" />
//           </div>
//           <div>
//             <h1 className="text-3xl font-bold tracking-tight">
//               Writers Assessment Answers
//             </h1>
//             <p className="text-muted-foreground">
//               Review submitted assessment answers from writers
//             </p>
//           </div>
//         </div>

//         <Card>
//           <CardHeader>
//             <CardTitle>Assessment Submissions</CardTitle>
//             <CardDescription>
//               List of all writer submissions for assessments ({tableData.length}{" "}
//               total)
//             </CardDescription>
//           </CardHeader>
//           <CardContent>
//             {tableData.length === 0 ? (
//               <div className="text-muted-foreground text-center py-8">
//                 No writer submissions found
//               </div>
//             ) : (
//               <div className="rounded-md border">
//                 <Table>
//                   <TableHeader>
//                     <TableRow>
//                       <TableHead>Writer ID</TableHead>
//                       <TableHead>Assessment Title</TableHead>
//                       <TableHead>Status</TableHead>
//                       <TableHead className="text-right">Actions</TableHead>
//                     </TableRow>
//                   </TableHeader>
//                   <TableBody>
//                     {tableData.map((row, index) => (
//                       <TableRow
//                         key={`${row.writerId}-${row.assessmentId}-${index}`}
//                       >
//                         <TableCell className="font-mono text-sm">
//                           {row.writerId.slice(0, 12)}...
//                         </TableCell>
//                         <TableCell className="font-medium">
//                           {row.assessmentTitle}
//                         </TableCell>
//                         <TableCell>
//                           <Badge
//                             variant="secondary"
//                             className={getStatusColor(row.status)}
//                           >
//                             {row.status}
//                           </Badge>
//                         </TableCell>
//                         <TableCell className="text-right">
//                           <div className="flex items-center justify-end gap-2">
//                             <Button
//                               variant="outline"
//                               size="sm"
//                               onClick={() =>
//                                 handleView(row.writerId, row.assessmentId)
//                               }
//                               className="h-8 w-8 p-0"
//                             >
//                               <Eye className="h-4 w-4" />
//                             </Button>
//                             <Button
//                               variant="outline"
//                               size="sm"
//                               onClick={() =>
//                                 handleDelete(row.writerId, row.assessmentId)
//                               }
//                               className="h-8 w-8 p-0 text-destructive hover:text-destructive"
//                             >
//                               <Trash2 className="h-4 w-4" />
//                             </Button>
//                           </div>
//                         </TableCell>
//                       </TableRow>
//                     ))}
//                   </TableBody>
//                 </Table>
//               </div>
//             )}
//           </CardContent>
//         </Card>
//       </div>

//       {/* Writer Assessment Answers Dialog */}
//       {selectedWriter && (
//         <WriterAssessmentAnswers
//           isOpen={isDialogOpen}
//           onOpenChange={setIsDialogOpen}
//           writerId={selectedWriter.writerId}
//           assessment={selectedWriter.assessment}
//           writerAnswer={selectedWriter.writerAnswer}
//           onStatusUpdate={(status) =>
//             handleStatusUpdate(
//               selectedWriter.writerId,
//               selectedWriter.assessment.id,
//               status
//             )
//           }
//         />
//       )}
//     </div>
//   );
// }
