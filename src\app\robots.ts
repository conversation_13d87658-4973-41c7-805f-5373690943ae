import { MetadataRoute } from 'next';

export default function robots(): MetadataRoute.Robots {
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  
  return {
    rules: {
      userAgent: '*',
      allow: '/',
      disallow: [
        '/admin/',        // Admin sections
        '/api/',          // API routes
        '/login/',        // Login pages
        '/register/',     // Registration pages
        '/unauthorized/', // Unauthorized pages
        '/client/',       // Client dashboard
        '/writer/',       // Writer dashboard
        '/order/',        // Order processing pages
        '/complete-payment/', // Payment pages
      ],
    },
    sitemap: `${baseUrl}/sitemap.xml`,
    host: baseUrl,
  };
}
