"use client";

import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import { AppSidebar as AdminSidebar } from "@/components/dashboard/admin/app-sidebar";
import { SiteHeader as AdminHeader } from "@/components/dashboard/admin/site-header";
import { AppSidebar as ClientSidebar } from "@/components/dashboard/client/app-sidebar";
import { SiteHeader as ClientHeader } from "@/components/dashboard/admin/site-header";
import { AppSidebar as WriterSidebar } from "@/components/dashboard/writer/app-sidebar";
import { SiteHeader as WriterHeader } from "@/components/dashboard/admin/site-header";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { Skeleton } from "@/components/ui/skeleton";

type UserRole = "ADMIN" | "CLIENT" | "WRITER";

// Extend the NextAuth User type to include role
interface ExtendedUser {
  id?: string;
  name?: string | null;
  email?: string | null;
  image?: string | null;
  role?: UserRole;
}

// Define proper sidebar props type
interface SidebarProps {
  variant?: "inset" | "sidebar" | "floating";
  side?: "left" | "right";
  collapsible?: "none" | "icon" | "offcanvas";
}

interface LayoutComponents {
  Sidebar: React.ComponentType<SidebarProps>;
  Header: React.ComponentType;
}

const layoutComponents: Record<UserRole, LayoutComponents> = {
  ADMIN: {
    Sidebar: AdminSidebar,
    Header: AdminHeader,
  },
  CLIENT: {
    Sidebar: ClientSidebar,
    Header: ClientHeader,
  },
  WRITER: {
    Sidebar: WriterSidebar,
    Header: WriterHeader,
  },
};

// Loading skeleton component
function LayoutSkeleton({ children }: { children: React.ReactNode }) {
  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <div className="w-72 h-screen border-r bg-sidebar">
        <div className="p-4 space-y-4">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-4 w-24" />
          <div className="space-y-2">
            {Array.from({ length: 6 }).map((_, i) => (
              <Skeleton key={i} className="h-8 w-full" />
            ))}
          </div>
        </div>
      </div>
      <SidebarInset>
        <div className="h-12 border-b bg-background px-4 flex items-center">
          <Skeleton className="h-6 w-48" />
        </div>
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
              {children}
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}

// Error fallback component
function LayoutError({ children }: { children: React.ReactNode }) {
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto py-8">
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-destructive mb-2">
            Access Error
          </h1>
          <p className="text-muted-foreground">
            Unable to determine user role. Please try logging in again.
          </p>
        </div>
        {children}
      </div>
    </div>
  );
}

export default function DynamicLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { data: session, status } = useSession();
  const [userRole, setUserRole] = useState<UserRole | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (status === "loading") {
      setIsLoading(true);
      return;
    }

    if (status === "unauthenticated") {
      setIsLoading(false);
      setUserRole(null);
      return;
    }

    if (session?.user) {
      // Extract role from session with proper typing
      const user = session.user as ExtendedUser;
      const role = user.role;

      if (role && (["ADMIN", "CLIENT", "WRITER"] as const).includes(role)) {
        setUserRole(role);
      } else {
        console.warn("Invalid or missing user role:", role);
        setUserRole(null);
      }
    }

    setIsLoading(false);
  }, [session, status]);

  // Show loading skeleton while determining role
  if (isLoading || status === "loading") {
    return <LayoutSkeleton>{children}</LayoutSkeleton>;
  }

  // Show error if no valid role found
  if (!userRole || !session) {
    return <LayoutError>{children}</LayoutError>;
  }

  // Get the appropriate components for the user's role
  const { Sidebar, Header } = layoutComponents[userRole];

  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <Sidebar variant="inset" />
      <SidebarInset>
        <Header />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
              {children}
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
