// src/components/dashboard/get-started-guidelines

"use client"

import React, { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  CheckCircle,
  User,
  Search,
  Send,
  Award,
  BookOpen,
  Shield,
  Target,
  Lightbulb,
  Quote,
  ArrowRight,
  Star,
  Clock,
  FileText,
  Users,
} from "lucide-react";

type ColorVariant = "blue" | "green" | "purple" | "orange";

interface Step {
  id: number;
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  description: string;
  color: ColorVariant;
  estimatedTime: string;
}

export default function GetStartedGuidelines() {
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);

  const toggleStep = (stepId: number): void => {
    setCompletedSteps((prev) =>
      prev.includes(stepId)
        ? prev.filter((id) => id !== stepId)
        : [...prev, stepId]
    );
  };

  const progressPercentage = (completedSteps.length / 4) * 100;

  const mainSteps: Step[] = [
    {
      id: 1,
      icon: User,
      title: "Complete Your Profile",
      description:
        "Fill out your writer profile with your qualifications, expertise areas, and writing samples. A complete profile helps you get matched with suitable assignments.",
      color: "blue",
      estimatedTime: "15-20 min",
    },
    {
      id: 2,
      icon: Search,
      title: "Browse Available Orders",
      description:
        "Navigate to the Orders section to view available assignments. Filter by subject, deadline, and complexity to find orders that match your expertise.",
      color: "green",
      estimatedTime: "5-10 min",
    },
    {
      id: 3,
      icon: Send,
      title: "Submit Your First Bid",
      description:
        "When you find an interesting order, submit a competitive bid with a clear proposal outlining your approach and timeline for completion.",
      color: "purple",
      estimatedTime: "10-15 min",
    },
    {
      id: 4,
      icon: Award,
      title: "Deliver Quality Work",
      description:
        "Once assigned, focus on delivering high-quality, original content that meets all requirements. Use proper citations and follow academic writing standards.",
      color: "orange",
      estimatedTime: "Varies",
    },
  ];

  const getColorClasses = (color: ColorVariant) => {
    const colorMap = {
      blue: {
        bg: "bg-blue-50 dark:bg-blue-950/20",
        border: "border-blue-200 dark:border-blue-800",
        text: "text-blue-900 dark:text-blue-100",
        accent: "text-blue-800 dark:text-blue-200",
        button: "bg-blue-500 hover:bg-blue-600",
        badge: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
      },
      green: {
        bg: "bg-green-50 dark:bg-green-950/20",
        border: "border-green-200 dark:border-green-800",
        text: "text-green-900 dark:text-green-100",
        accent: "text-green-800 dark:text-green-200",
        button: "bg-green-500 hover:bg-green-600",
        badge:
          "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      },
      purple: {
        bg: "bg-purple-50 dark:bg-purple-950/20",
        border: "border-purple-200 dark:border-purple-800",
        text: "text-purple-900 dark:text-purple-100",
        accent: "text-purple-800 dark:text-purple-200",
        button: "bg-purple-500 hover:bg-purple-600",
        badge:
          "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
      },
      orange: {
        bg: "bg-orange-50 dark:bg-orange-950/20",
        border: "border-orange-200 dark:border-orange-800",
        text: "text-orange-900 dark:text-orange-100",
        accent: "text-orange-800 dark:text-orange-200",
        button: "bg-orange-500 hover:bg-orange-600",
        badge:
          "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
      },
    };
    return colorMap[color];
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      <div className="space-y-8 p-6 max-w-7xl mx-auto">
        {/* Hero Section */}
        <div className="text-center space-y-6 py-12">
          <div className="inline-flex items-center space-x-2 bg-blue-100 dark:bg-blue-900/30 px-4 py-2 rounded-full">
            <Lightbulb className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
              Getting Started Guide
            </span>
          </div>

          <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 dark:from-white dark:via-blue-200 dark:to-purple-200 bg-clip-text text-transparent leading-tight">
            Begin Your Writer Journey
          </h1>

          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Transform your expertise into exceptional academic content. Follow
            our comprehensive guide to establish yourself as a trusted writer on
            our platform.
          </p>

          {/* Progress Tracker */}
          <div className="max-w-md mx-auto space-y-3">
            <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
              <span>Progress</span>
              <span>{completedSteps.length}/4 completed</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>
        </div>

        {/* Main Steps Section */}
        <div className="space-y-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Your Path to Success
            </h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Complete these essential steps to unlock your potential and start
              earning as a professional writer.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2">
            {mainSteps.map((step) => {
              const IconComponent = step.icon;
              const colors = getColorClasses(step.color);
              const isCompleted = completedSteps.includes(step.id);

              return (
                <Card
                  key={step.id}
                  className={`relative overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1 cursor-pointer group ${
                    isCompleted ? "ring-2 ring-green-500" : ""
                  }`}
                  onClick={() => toggleStep(step.id)}
                >
                  <div className={`absolute inset-0 ${colors.bg} opacity-50`} />
                  <div
                    className={`absolute top-0 left-0 w-full h-1 ${colors.button}`}
                  />

                  <CardHeader className="relative">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-4">
                        <div
                          className={`p-3 rounded-xl ${colors.button} text-white shadow-lg group-hover:scale-110 transition-transform duration-300`}
                        >
                          <IconComponent className="w-6 h-6" />
                        </div>
                        <div>
                          <Badge variant="outline" className={colors.badge}>
                            Step {step.id}
                          </Badge>
                          <CardTitle
                            className={`text-xl font-bold ${colors.text} mt-2`}
                          >
                            {step.title}
                          </CardTitle>
                        </div>
                      </div>

                      {isCompleted && (
                        <CheckCircle className="w-6 h-6 text-green-500 animate-pulse" />
                      )}
                    </div>
                  </CardHeader>

                  <CardContent className="relative space-y-4">
                    <p className={`${colors.accent} leading-relaxed`}>
                      {step.description}
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Clock className="w-4 h-4 text-gray-500" />
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          {step.estimatedTime}
                        </span>
                      </div>

                      <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-gray-600 group-hover:translate-x-1 transition-all duration-300" />
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* Detailed Guidelines Section */}
        <div className="mt-16">
          <Tabs defaultValue="guidelines" className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-8">
              <TabsTrigger
                value="guidelines"
                className="flex items-center space-x-2"
              >
                <BookOpen className="w-4 h-4" />
                <span>Guidelines</span>
              </TabsTrigger>
              <TabsTrigger
                value="best-practices"
                className="flex items-center space-x-2"
              >
                <Target className="w-4 h-4" />
                <span>Best Practices</span>
              </TabsTrigger>
              <TabsTrigger
                value="success-tips"
                className="flex items-center space-x-2"
              >
                <Star className="w-4 h-4" />
                <span>Success Tips</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="guidelines" className="space-y-6">
              <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900">
                <CardHeader>
                  <CardTitle className="text-2xl font-bold flex items-center space-x-3">
                    <Shield className="w-7 h-7 text-blue-600" />
                    <span>Registration & Project Guidelines</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-8">
                  <div className="grid md:grid-cols-3 gap-6">
                    <Card className="bg-blue-50 dark:bg-blue-950/30 border-blue-200 dark:border-blue-800">
                      <CardHeader>
                        <CardTitle className="text-lg text-blue-900 dark:text-blue-100">
                          Registration & Verification
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <ul className="space-y-2 text-sm text-blue-800 dark:text-blue-200">
                          <li className="flex items-start space-x-2">
                            <CheckCircle className="w-4 h-4 mt-0.5 text-blue-600" />
                            <span>
                              Submit academic credentials (degrees,
                              certifications)
                            </span>
                          </li>
                          <li className="flex items-start space-x-2">
                            <CheckCircle className="w-4 h-4 mt-0.5 text-blue-600" />
                            <span>Provide writing samples for portfolio</span>
                          </li>
                          <li className="flex items-start space-x-2">
                            <CheckCircle className="w-4 h-4 mt-0.5 text-blue-600" />
                            <span>Complete AI-driven skill assessments</span>
                          </li>
                        </ul>
                      </CardContent>
                    </Card>

                    <Card className="bg-green-50 dark:bg-green-950/30 border-green-200 dark:border-green-800">
                      <CardHeader>
                        <CardTitle className="text-lg text-green-900 dark:text-green-100">
                          Project Guidelines
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <ul className="space-y-2 text-sm text-green-800 dark:text-green-200">
                          <li className="flex items-start space-x-2">
                            <CheckCircle className="w-4 h-4 mt-0.5 text-green-600" />
                            <span>
                              Maintain clarity, coherence, and formal tone
                            </span>
                          </li>
                          <li className="flex items-start space-x-2">
                            <CheckCircle className="w-4 h-4 mt-0.5 text-green-600" />
                            <span>Use provided structural templates</span>
                          </li>
                          <li className="flex items-start space-x-2">
                            <CheckCircle className="w-4 h-4 mt-0.5 text-green-600" />
                            <span>Follow funnel-shaped introductions</span>
                          </li>
                        </ul>
                      </CardContent>
                    </Card>

                    <Card className="bg-purple-50 dark:bg-purple-950/30 border-purple-200 dark:border-purple-800">
                      <CardHeader>
                        <CardTitle className="text-lg text-purple-900 dark:text-purple-100">
                          Quality Assurance
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <ul className="space-y-2 text-sm text-purple-800 dark:text-purple-200">
                          <li className="flex items-start space-x-2">
                            <CheckCircle className="w-4 h-4 mt-0.5 text-purple-600" />
                            <span>Mandatory Turnitin plagiarism reports</span>
                          </li>
                          <li className="flex items-start space-x-2">
                            <CheckCircle className="w-4 h-4 mt-0.5 text-purple-600" />
                            <span>Tiered review system (peer + expert)</span>
                          </li>
                          <li className="flex items-start space-x-2">
                            <CheckCircle className="w-4 h-4 mt-0.5 text-purple-600" />
                            <span>Continuous quality monitoring</span>
                          </li>
                        </ul>
                      </CardContent>
                    </Card>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="best-practices" className="space-y-6">
              <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-green-50 dark:from-slate-800 dark:to-green-950">
                <CardHeader>
                  <CardTitle className="text-2xl font-bold flex items-center space-x-3">
                    <Target className="w-7 h-7 text-green-600" />
                    <span>Academic Excellence Standards</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid md:grid-cols-3 gap-6">
                    <Card className="bg-white dark:bg-slate-800 shadow-md">
                      <CardHeader>
                        <CardTitle className="text-lg flex items-center space-x-2">
                          <FileText className="w-5 h-5 text-blue-600" />
                          <span>Research & Referencing</span>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="text-sm space-y-2">
                        <p>
                          Use credible sources from peer-reviewed journals and
                          academic databases. Maintain proper citation formats
                          (APA, MLA, Chicago, etc.)
                        </p>
                      </CardContent>
                    </Card>

                    <Card className="bg-white dark:bg-slate-800 shadow-md">
                      <CardHeader>
                        <CardTitle className="text-lg flex items-center space-x-2">
                          <Users className="w-5 h-5 text-green-600" />
                          <span>Audience Awareness</span>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="text-sm space-y-2">
                        <p>
                          Tailor content specifically for academic readers
                          including professors, researchers, and scholarly
                          communities.
                        </p>
                      </CardContent>
                    </Card>

                    <Card className="bg-white dark:bg-slate-800 shadow-md">
                      <CardHeader>
                        <CardTitle className="text-lg flex items-center space-x-2">
                          <Search className="w-5 h-5 text-purple-600" />
                          <span>SEO Writing</span>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="text-sm space-y-2">
                        <p>
                          Optimize metadata for discoverability while
                          maintaining academic rigor and scholarly integrity.
                        </p>
                      </CardContent>
                    </Card>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="success-tips" className="space-y-6">
              <Alert className="border-amber-200 bg-amber-50 dark:bg-amber-950/30 dark:border-amber-800">
                <Lightbulb className="h-4 w-4 text-amber-600" />
                <AlertDescription className="text-amber-800 dark:text-amber-200">
                  <strong>Pro Tip:</strong> Writers who follow these guidelines
                  consistently earn 40% more and receive higher client ratings.
                </AlertDescription>
              </Alert>

              <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-amber-50 dark:from-slate-800 dark:to-amber-950">
                <CardHeader>
                  <CardTitle className="text-2xl font-bold flex items-center space-x-3">
                    <Star className="w-7 h-7 text-amber-600" />
                    <span>Tips for Exceptional Success</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h4 className="font-semibold text-lg text-gray-900 dark:text-white">
                        Communication Excellence
                      </h4>
                      <ul className="space-y-3">
                        <li className="flex items-start space-x-3">
                          <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                          <span className="text-gray-700 dark:text-gray-300">
                            Always read assignment instructions carefully and
                            thoroughly
                          </span>
                        </li>
                        <li className="flex items-start space-x-3">
                          <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                          <span className="text-gray-700 dark:text-gray-300">
                            Communicate promptly with clients when clarification
                            is needed
                          </span>
                        </li>
                        <li className="flex items-start space-x-3">
                          <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                          <span className="text-gray-700 dark:text-gray-300">
                            Maintain professional communication at all times
                          </span>
                        </li>
                      </ul>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-semibold text-lg text-gray-900 dark:text-white">
                        Delivery Excellence
                      </h4>
                      <ul className="space-y-3">
                        <li className="flex items-start space-x-3">
                          <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                          <span className="text-gray-700 dark:text-gray-300">
                            Submit work before the deadline consistently
                          </span>
                        </li>
                        <li className="flex items-start space-x-3">
                          <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                          <span className="text-gray-700 dark:text-gray-300">
                            Ask for clarification if requirements are unclear
                          </span>
                        </li>
                        <li className="flex items-start space-x-3">
                          <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                          <span className="text-gray-700 dark:text-gray-300">
                            Exceed expectations with additional value-added
                            insights
                          </span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Inspirational Quote Section */}
        <div className="mt-16">
          <Card className="border-0 shadow-2xl bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 text-white overflow-hidden relative">
            <div className="absolute inset-0 bg-black/20" />
            <div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-32 translate-x-32" />
            <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24" />

            <CardContent className="relative py-12 px-8 text-center">
              <Quote className="w-12 h-12 mx-auto mb-6 text-white/80" />
              <blockquote className="text-xl md:text-2xl font-medium leading-relaxed max-w-4xl mx-auto mb-6">
                &quot;Our platform empowers writers to produce ethically sound,
                rigorously researched academic content. Whether drafting a STEM
                lab report or a humanities thesis, prioritize clarity,
                evidence-based arguments, and adherence to institutional
                guidelines.&quot;
              </blockquote>
              <div className="w-24 h-1 bg-white/50 mx-auto" />
            </CardContent>
          </Card>
        </div>

        {/* Call to Action */}
        <div className="text-center py-12">
          <Button
            size="lg"
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300"
          >
            Start Your Writer Journey
            <ArrowRight className="w-5 h-5 ml-2" />
          </Button>
        </div>
      </div>
    </div>
  );
}
