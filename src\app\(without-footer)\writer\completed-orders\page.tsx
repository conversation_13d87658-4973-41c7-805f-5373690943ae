import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import WriterCompletedOrdersPage from "@/components/dashboard/writer/completed-orders";
import WriterRejectedOrdersPage from "@/components/dashboard/writer/rejected-orders";

export default function OrdersDashboard() {
  const breadcrumbs = [
    { label: "Dashboard", href: "/writer/dashboard" },
    { label: "Orders", isCurrentPage: true },
  ];

  return (
    <>
      {/* Header with Sidebar Trigger and Breadcrumbs */}
      <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <Breadcrumb>
            <BreadcrumbList>
              {breadcrumbs.map((item, index) => (
                <div key={index} className="flex items-center">
                  {index > 0 && (
                    <BreadcrumbSeparator className="hidden md:block" />
                  )}
                  <BreadcrumbItem className="hidden md:block">
                    {item.isCurrentPage ? (
                      <BreadcrumbPage>{item.label}</BreadcrumbPage>
                    ) : (
                      <BreadcrumbLink href={item.href || "#"}>
                        {item.label}
                      </BreadcrumbLink>
                    )}
                  </BreadcrumbItem>
                </div>
              ))}
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>

      {/* Main Content Area */}
      <div className="w-full max-w-6xl mx-auto pt-2 px-6 pb-6">
        <Tabs defaultValue="completed" className="w-full">
          <div className="flex justify-center w-full">
            <TabsList className="inline-flex w-full max-w-[400px] justify-center rounded-full bg-gray-100 dark:bg-gray-800 p-1 shadow-sm">
              <TabsTrigger
                value="completed"
                className="data-[state=active]:bg-green-500 dark:data-[state=active]:bg-green-600 data-[state=active]:text-white
                    text-sm font-medium px-4 py-2 rounded-full transition-all duration-300
                    text-gray-700 dark:text-gray-300 hover:bg-green-100 dark:hover:bg-green-900/50 hover:text-green-700 dark:hover:text-green-400"
              >
                Completed
              </TabsTrigger>
              <TabsTrigger
                value="rejected"
                className="data-[state=active]:bg-red-500 dark:data-[state=active]:bg-red-600 data-[state=active]:text-white
                    text-sm font-medium px-4 py-2 rounded-full transition-all duration-300
                    text-gray-700 dark:text-gray-300 hover:bg-red-100 dark:hover:bg-red-900/50 hover:text-red-700 dark:hover:text-red-400"
              >
                Rejected
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="completed">
            <WriterCompletedOrdersPage />
          </TabsContent>

          <TabsContent value="rejected">
            <WriterRejectedOrdersPage />
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
}
