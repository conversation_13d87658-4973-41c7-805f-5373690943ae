"use client"

import { IconTrendingDown, IconTrendingUp } from "@tabler/icons-react"
import { useEffect, useState } from "react"

import { Badge } from "@/components/ui/badge"
import {
  Card,
  CardAction,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { useRevenue } from "@/hooks/useRevenue"



interface User {
  id: string
  email: string
  name?: string | null
  role: string
  createdAt: string
}

interface UsersApiResponse {
  success: boolean
  message: string
  data: {
    users: User[]
    pagination: {
      total: number
      page: number
      limit: number
      pages: number
    }
  }
}

export function SectionCards() {
  const { revenueData, loading: revenueLoading } = useRevenue()
  const [userAnalytics, setUserAnalytics] = useState({
    newCustomers: 0,
    customerGrowth: 0,
    activeAccounts: 0,
    accountGrowth: 0,
  })
  const [userLoading, setUserLoading] = useState(true)

  useEffect(() => {
    const fetchUserAnalytics = async () => {
      try {
        // Fetch growth data
        const growthResponse = await fetch('/api/analytics/growth')
        const growthData = await growthResponse.json()

        // Calculate new customers (monthly basis)
        const currentDate = new Date()
        const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)

        const usersResponse = await fetch('/api/users?limit=1000') // Get more users for accurate calculation
        const usersData: UsersApiResponse = await usersResponse.json()

        if (!usersData.success || !usersData.data?.users) {
          throw new Error('Failed to fetch users data')
        }

        const users = usersData.data.users

        const newCustomersThisMonth = users.filter((user: User) =>
          new Date(user.createdAt) >= startOfMonth
        ).length

        // Calculate previous month for growth comparison
        const startOfPrevMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1)
        const endOfPrevMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 0)

        const newCustomersPrevMonth = users.filter((user: User) => {
          const userDate = new Date(user.createdAt)
          return userDate >= startOfPrevMonth && userDate <= endOfPrevMonth
        }).length

        const customerGrowth = newCustomersPrevMonth > 0
          ? ((newCustomersThisMonth - newCustomersPrevMonth) / newCustomersPrevMonth) * 100
          : 100

        // Calculate active accounts (total clients and writers)
        const activeAccounts = users.filter((user: User) =>
          user.role === 'CLIENT' || user.role === 'WRITER'
        ).length

        setUserAnalytics({
          newCustomers: newCustomersThisMonth,
          customerGrowth,
          activeAccounts,
          accountGrowth: growthData.data?.growthRate || 0,
        })
      } catch (error) {
        console.error('Error fetching user analytics:', error)
      } finally {
        setUserLoading(false)
      }
    }

    fetchUserAnalytics()
  }, [])

  const loading = revenueLoading || userLoading

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num)
  }

  const formatPercentage = (num: number) => {
    return `${num >= 0 ? '+' : ''}${num.toFixed(1)}%`
  }

  if (loading) {
    return (
      <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 px-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs lg:px-6 @xl/main:grid-cols-2 @5xl/main:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="@container/card animate-pulse">
            <CardHeader>
              <div className="h-4 bg-muted rounded w-24"></div>
              <div className="h-8 bg-muted rounded w-32"></div>
            </CardHeader>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 px-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs lg:px-6 @xl/main:grid-cols-2 @5xl/main:grid-cols-4">
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Total Revenue</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
            {formatCurrency(revenueData.totalRevenue)}
          </CardTitle>
          <CardAction>
            <Badge variant="outline">
              {revenueData.revenueGrowth >= 0 ? <IconTrendingUp /> : <IconTrendingDown />}
              {formatPercentage(revenueData.revenueGrowth)}
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            {revenueData.revenueGrowth >= 0 ? 'Trending up' : 'Trending down'} this month{" "}
            {revenueData.revenueGrowth >= 0 ? <IconTrendingUp className="size-4" /> : <IconTrendingDown className="size-4" />}
          </div>
          <div className="text-muted-foreground">
            Monthly revenue: {formatCurrency(revenueData.monthlyRevenue)}
          </div>
        </CardFooter>
      </Card>
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>New Customers</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
            {formatNumber(userAnalytics.newCustomers)}
          </CardTitle>
          <CardAction>
            <Badge variant="outline">
              {userAnalytics.customerGrowth >= 0 ? <IconTrendingUp /> : <IconTrendingDown />}
              {formatPercentage(userAnalytics.customerGrowth)}
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            {userAnalytics.customerGrowth >= 0 ? 'Growing' : 'Declining'} this month{" "}
            {userAnalytics.customerGrowth >= 0 ? <IconTrendingUp className="size-4" /> : <IconTrendingDown className="size-4" />}
          </div>
          <div className="text-muted-foreground">
            New registrations this month
          </div>
        </CardFooter>
      </Card>
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Active Accounts</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
            {formatNumber(userAnalytics.activeAccounts)}
          </CardTitle>
          <CardAction>
            <Badge variant="outline">
              {userAnalytics.accountGrowth >= 0 ? <IconTrendingUp /> : <IconTrendingDown />}
              {formatPercentage(userAnalytics.accountGrowth)}
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            {userAnalytics.accountGrowth >= 0 ? 'Strong user retention' : 'User retention declining'}{" "}
            {userAnalytics.accountGrowth >= 0 ? <IconTrendingUp className="size-4" /> : <IconTrendingDown className="size-4" />}
          </div>
          <div className="text-muted-foreground">Total clients and writers</div>
        </CardFooter>
      </Card>
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Growth Rate</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
            {formatPercentage(revenueData.revenueGrowth)}
          </CardTitle>
          <CardAction>
            <Badge variant="outline">
              {revenueData.revenueGrowth >= 0 ? <IconTrendingUp /> : <IconTrendingDown />}
              {formatPercentage(revenueData.revenueGrowth)}
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            {revenueData.revenueGrowth >= 0 ? 'Steady performance increase' : 'Performance declining'}{" "}
            {revenueData.revenueGrowth >= 0 ? <IconTrendingUp className="size-4" /> : <IconTrendingDown className="size-4" />}
          </div>
          <div className="text-muted-foreground">Revenue growth rate</div>
        </CardFooter>
      </Card>
    </div>
  )
}
