//src/hooks/use-data-table.tsx

"use client";

import {
  type ColumnFiltersState,
  type PaginationState,
  type RowSelectionState,
  type SortingState,
  type TableOptions,
  type TableState,
  type Updater,
  type VisibilityState,
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  type Parser,
  type UseQueryStateOptions,
  parseAsArrayOf,
  parseAsInteger,
  parseAsString,
  useQueryState,
  useQueryStates,
} from "nuqs";
import * as React from "react";

import { useDebouncedCallback } from "@/hooks/use-debounced-callback";
import { getSortingStateParser } from "@/lib/parsers";
import type { ExtendedColumnSort } from "@/types/data-table";

const PAGE_KEY = "page";
const PER_PAGE_KEY = "perPage";
const SORT_KEY = "sort";
const ARRAY_SEPARATOR = ",";
const DEBOUNCE_MS = 300;
const THROTTLE_MS = 50;

interface UseDataTableProps<TData>
  extends Omit<TableOptions<TData>, "state" | "pageCount" | "getCoreRowModel">,
    Required<Pick<TableOptions<TData>, "pageCount">> {
  // MGX EDGE:- Added properties for server-side pagination
  manualPagination?: boolean;
  manualFiltering?: boolean;
  manualSorting?: boolean;
  // MGX EDGE:- Added apiUrl for server-side data fetching
  apiUrl?: string;
  // MGX EDGE:- Added onDataChange callback for parent components to handle data updates
  onDataChange?: (data: TData[], totalCount: number) => void;
  initialState?: Omit<Partial<TableState>, "sorting"> & {
    sorting?: ExtendedColumnSort<TData>[];
  };
  history?: "push" | "replace";
  debounceMs?: number;
  throttleMs?: number;
  clearOnDefault?: boolean;
  enableAdvancedFilter?: boolean;
  scroll?: boolean;
  shallow?: boolean;
  startTransition?: React.TransitionStartFunction;
}

export function useDataTable<TData>(props: UseDataTableProps<TData>) {
  const {
    columns,
    pageCount = -1,
    initialState,
    history = "replace",
    debounceMs = DEBOUNCE_MS,
    throttleMs = THROTTLE_MS,
    clearOnDefault = false,
    enableAdvancedFilter = false,
    scroll = false,
    shallow = true,
    startTransition,
    apiUrl,
    onDataChange,
    manualPagination = false,
    manualFiltering = false,
    manualSorting = false,
    ...tableProps
  } = props;

  // MGX EDGE:- Added states for server-side data fetching
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [serverPageCount, setServerPageCount] = React.useState(pageCount);

  const queryStateOptions = React.useMemo<
    Omit<UseQueryStateOptions<string>, "parse">
  >(
    () => ({
      history,
      scroll,
      shallow,
      throttleMs,
      debounceMs,
      clearOnDefault,
      startTransition,
    }),
    [
      history,
      scroll,
      shallow,
      throttleMs,
      debounceMs,
      clearOnDefault,
      startTransition,
    ]
  );

  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>(
    initialState?.rowSelection ?? {}
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>(initialState?.columnVisibility ?? {});

  const [page, setPage] = useQueryState(
    PAGE_KEY,
    parseAsInteger.withOptions(queryStateOptions).withDefault(1)
  );
  const [perPage, setPerPage] = useQueryState(
    PER_PAGE_KEY,
    parseAsInteger
      .withOptions(queryStateOptions)
      .withDefault(initialState?.pagination?.pageSize ?? 10)
  );

  const pagination: PaginationState = React.useMemo(() => {
    return {
      pageIndex: page - 1, // zero-based index -> one-based index
      pageSize: perPage,
    };
  }, [page, perPage]);

  // ROWS PER PAGE:- Updated pagination change handler to properly sync URL state with table state
  const onPaginationChange = React.useCallback(
    (updaterOrValue: Updater<PaginationState>) => {
      if (typeof updaterOrValue === "function") {
        const newPagination = updaterOrValue(pagination);

        // ROWS PER PAGE:- Update URL query parameters which will trigger data refetch
        void setPage(newPagination.pageIndex + 1);
        void setPerPage(newPagination.pageSize);
      } else {
        // ROWS PER PAGE:- Update URL query parameters which will trigger data refetch
        void setPage(updaterOrValue.pageIndex + 1);
        void setPerPage(updaterOrValue.pageSize);
      }
    },
    [pagination, setPage, setPerPage]
  );

  const columnIds = React.useMemo(() => {
    return new Set(
      columns.map((column) => column.id).filter(Boolean) as string[]
    );
  }, [columns]);

  const [sorting, setSorting] = useQueryState(
    SORT_KEY,
    getSortingStateParser<TData>(columnIds)
      .withOptions(queryStateOptions)
      .withDefault(initialState?.sorting ?? [])
  );

  const onSortingChange = React.useCallback(
    (updaterOrValue: Updater<SortingState>) => {
      if (typeof updaterOrValue === "function") {
        const newSorting = updaterOrValue(sorting);
        setSorting(newSorting as ExtendedColumnSort<TData>[]);
      } else {
        setSorting(updaterOrValue as ExtendedColumnSort<TData>[]);
      }
    },
    [sorting, setSorting]
  );

  const filterableColumns = React.useMemo(() => {
    if (enableAdvancedFilter) return [];

    return columns.filter((column) => column.enableColumnFilter !== false);
  }, [columns, enableAdvancedFilter]);

  const filterParsers = React.useMemo(() => {
    if (enableAdvancedFilter) return {};

    return filterableColumns.reduce<
      Record<string, Parser<string> | Parser<string[]>>
    >((acc, column) => {
      if (column.meta?.options) {
        acc[column.id ?? ""] = parseAsArrayOf(
          parseAsString,
          ARRAY_SEPARATOR
        ).withOptions(queryStateOptions);
      } else {
        acc[column.id ?? ""] = parseAsString.withOptions(queryStateOptions);
      }
      return acc;
    }, {});
  }, [filterableColumns, queryStateOptions, enableAdvancedFilter]);

  const [filterValues, setFilterValues] = useQueryStates(filterParsers);

  const debouncedSetFilterValues = useDebouncedCallback(
    (values: typeof filterValues) => {
      void setPage(1);
      void setFilterValues(values);
    },
    debounceMs
  );

  const initialColumnFilters: ColumnFiltersState = React.useMemo(() => {
    if (enableAdvancedFilter) return [];

    return Object.entries(filterValues).reduce<ColumnFiltersState>(
      (filters, [key, value]) => {
        if (value !== null) {
          const processedValue = Array.isArray(value)
            ? value
            : typeof value === "string" && /[^a-zA-Z0-9]/.test(value)
            ? value.split(/[^a-zA-Z0-9]+/).filter(Boolean)
            : [value];

          filters.push({
            id: key,
            value: processedValue,
          });
        }
        return filters;
      },
      []
    );
  }, [filterValues, enableAdvancedFilter]);

  const [columnFilters, setColumnFilters] =
    React.useState<ColumnFiltersState>(initialColumnFilters);

  const onColumnFiltersChange = React.useCallback(
    (updaterOrValue: Updater<ColumnFiltersState>) => {
      if (enableAdvancedFilter) return;

      setColumnFilters((prev) => {
        const next =
          typeof updaterOrValue === "function"
            ? updaterOrValue(prev)
            : updaterOrValue;

        const filterUpdates = next.reduce<
          Record<string, string | string[] | null>
        >((acc, filter) => {
          if (filterableColumns.find((column) => column.id === filter.id)) {
            acc[filter.id] = filter.value as string | string[];
          }
          return acc;
        }, {});

        for (const prevFilter of prev) {
          if (!next.some((filter) => filter.id === prevFilter.id)) {
            filterUpdates[prevFilter.id] = null;
          }
        }

        debouncedSetFilterValues(filterUpdates);
        return next;
      });
    },
    [debouncedSetFilterValues, filterableColumns, enableAdvancedFilter]
  );

  // MGX EDGE:- Fixed server-side data fetching function to prevent infinite loops
  const fetchServerData = React.useCallback(
    async (currentPage: number, currentPageSize: number) => {
      if (!apiUrl || !manualPagination) return;

      try {
        setLoading(true);
        setError(null);

        // MGX EDGE:- Build URL with pagination parameters (using perPage to match API expectations)
        const url = new URL(apiUrl, window.location.origin);
        url.searchParams.set("page", currentPage.toString());
        url.searchParams.set("perPage", currentPageSize.toString());

        // MGX EDGE:- Add sorting parameters if needed
        if (manualSorting && sorting.length > 0) {
          const sortParams = sorting.map(
            (sort) => `${sort.id}:${sort.desc ? "desc" : "asc"}`
          );
          url.searchParams.set("sort", sortParams.join(","));
        }

        // MGX EDGE:- Add filter parameters if needed
        if (manualFiltering && columnFilters.length > 0) {
          columnFilters.forEach((filter) => {
            if (Array.isArray(filter.value)) {
              url.searchParams.set(filter.id, filter.value.join(","));
            } else {
              url.searchParams.set(filter.id, String(filter.value));
            }
          });
        }

        const response = await fetch(url.toString());

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (!result.success) {
          throw new Error(result.message || "Failed to fetch data");
        }

        // MGX EDGE:- Handle the correct API response structure: { success: true, data: { assignments, totalCount } }
        const { assignments, totalCount } = result.data;
        const newPageCount = Math.ceil(totalCount / currentPageSize);

        setServerPageCount(newPageCount);

        // MGX EDGE:- Notify parent component of data changes
        if (onDataChange) {
          onDataChange(assignments || [], totalCount || 0);
        }

        return {
          data: assignments || [],
          totalCount: totalCount || 0,
          pageCount: newPageCount,
        };
      } catch (err) {
        console.error("Error fetching server data:", err);
        const errorMessage =
          err instanceof Error ? err.message : "An error occurred";
        setError(errorMessage);

        if (onDataChange) {
          onDataChange([], 0);
        }

        return {
          data: [],
          totalCount: 0,
          pageCount: 0,
        };
      } finally {
        setLoading(false);
      }
    },
    [
      apiUrl,
      manualPagination,
      manualSorting,
      manualFiltering,
      sorting,
      columnFilters,
      onDataChange,
    ]
  );

  // ROWS PER PAGE:- Updated effect to properly handle page size changes
  React.useEffect(() => {
    if (manualPagination && apiUrl) {
      const currentPage = pagination.pageIndex + 1; // Convert to 1-based
      const currentPageSize = pagination.pageSize;

      fetchServerData(currentPage, currentPageSize);
    }
  }, [
    // ROWS PER PAGE:- Direct dependency on pagination values to ensure changes trigger refetch
    pagination.pageIndex,
    pagination.pageSize,
    manualPagination,
    apiUrl,
    // MGX EDGE:- Only include serialized dependencies to prevent infinite loops
    JSON.stringify(sorting),
    JSON.stringify(columnFilters),
  ]);

  // MGX FIX: Keep fetchDataRef updated but don't use it in effect dependencies
  const fetchDataRef = React.useRef(fetchServerData);
  React.useEffect(() => {
    fetchDataRef.current = fetchServerData;
  });

  const table = useReactTable({
    ...tableProps,
    columns,
    initialState,
    // MGX EDGE:- Use serverPageCount for manual pagination, fallback to original pageCount
    pageCount: manualPagination ? serverPageCount : pageCount,
    state: {
      pagination,
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
    },
    defaultColumn: {
      ...tableProps.defaultColumn,
      enableColumnFilter: true,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onPaginationChange,
    onSortingChange,
    onColumnFiltersChange,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues(),
    // MGX EDGE:- Use the manual flags from props
    manualPagination,
    manualSorting,
    manualFiltering,
  });

  return {
    table,
    shallow,
    debounceMs,
    throttleMs,
    // MGX EDGE:- Expose loading and error states for the component
    loading,
    error,
    // MGX EDGE:- Expose fetch function for manual refetching
    refetch: () => {
      const currentPage = pagination.pageIndex + 1;
      const currentPageSize = pagination.pageSize;
      return fetchDataRef.current(currentPage, currentPageSize);
    },
  };
}
