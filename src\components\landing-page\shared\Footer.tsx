// src/components/landing-page/shared/Footer.tsx
"use client";

import React from "react";
import Link from "next/link";
import { useCompanyInfo } from "@/hooks/use-company-info";
import { Separator } from "@/components/ui/separator";
import { NewsletterSubscription } from "@/components/newsletter/newsletter-subscription";
import {
  FacebookIcon,
  TwitterIcon,
  InstagramIcon,
  LinkedinIcon,
  YoutubeIcon,
  ChevronRight,
} from "lucide-react";

export const Footer = () => {
  const { companyInfo } = useCompanyInfo();

  return (
    <footer className="bg-gray-900 text-white pt-16 pb-6">
      <div className="container mx-auto px-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <div className="w-10 h-10 rounded-full bg-blue-600 flex items-center justify-center">
                <span className="font-bold text-lg">A</span>
              </div>
              <span className="text-xl font-bold">{companyInfo?.companyName || "Essay App"}</span>
            </div>
            <p className="text-gray-400">
              {companyInfo?.description || "Professional academic writing services for students at all levels. Get expert help with essays, research papers, dissertations, and more."}
            </p>
            <div className="flex space-x-4">
              <Link href="#" className="hover:text-blue-400 transition-colors">
                <FacebookIcon className="h-5 w-5" />
              </Link>
              <Link href="#" className="hover:text-blue-400 transition-colors">
                <TwitterIcon className="h-5 w-5" />
              </Link>
              <Link href="#" className="hover:text-blue-400 transition-colors">
                <InstagramIcon className="h-5 w-5" />
              </Link>
              <Link href="#" className="hover:text-blue-400 transition-colors">
                <LinkedinIcon className="h-5 w-5" />
              </Link>
              <Link href="#" className="hover:text-blue-400 transition-colors">
                <YoutubeIcon className="h-5 w-5" />
              </Link>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link href="#" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <ChevronRight className="h-4 w-4" /> How It Works
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <ChevronRight className="h-4 w-4" /> Our Writers
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <ChevronRight className="h-4 w-4" /> Samples
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <ChevronRight className="h-4 w-4" /> Pricing
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <ChevronRight className="h-4 w-4" /> Reviews
                </Link>
              </li>
              <li>
                <Link href="/blog" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <ChevronRight className="h-4 w-4" /> Blog
                </Link>
              </li>
              <li>
                <Link href="/contact-us" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <ChevronRight className="h-4 w-4" /> Contact Us
                </Link>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Services</h3>
            <ul className="space-y-2">
              <li>
                <Link href="#" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <ChevronRight className="h-4 w-4" /> Essay Writing
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <ChevronRight className="h-4 w-4" /> Research Papers
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <ChevronRight className="h-4 w-4" /> Dissertation Writing
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <ChevronRight className="h-4 w-4" /> Term Papers
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <ChevronRight className="h-4 w-4" /> Editing & Proofreading
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <ChevronRight className="h-4 w-4" /> Admission Essays
                </Link>
              </li>
            </ul>
          </div>

          {/* Newsletter */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Newsletter</h3>
            <p className="text-gray-400 mb-4">
              Subscribe to our newsletter to receive updates and special offers.
            </p>
            <NewsletterSubscription
              source="landing-page-footer"
              placeholder="Your email"
              buttonText="Subscribe"
              variant="compact"
              showIcon={false}
              className="w-full"
            />
            <p className="text-xs text-gray-500 mt-2">
              We respect your privacy. Unsubscribe at any time.
            </p>
          </div>
        </div>

        <Separator className="my-8 bg-gray-800" />

        <div className="flex flex-col md:flex-row justify-between gap-4">
          <div className="text-gray-400 text-sm">
            © {new Date().getFullYear()} {companyInfo?.companyName || "Essay App"}. All rights reserved.
          </div>
          <div className="flex flex-wrap gap-4 text-sm text-gray-400">
            <Link href="#" className="hover:text-white transition-colors">
              Privacy Policy
            </Link>
            <Link href="#" className="hover:text-white transition-colors">
              Terms of Service
            </Link>
            <Link href="#" className="hover:text-white transition-colors">
              Cookie Policy
            </Link>
            <Link href="#" className="hover:text-white transition-colors">
              DMCA
            </Link>
            <Link href="#" className="hover:text-white transition-colors">
              Money-Back Guarantee
            </Link>
          </div>
        </div>

        <div className="text-xs text-gray-600 text-center mt-8">
          <p>
            Disclaimer: The products and services provided by AcademicWriters are meant to be used for research and reference purposes only. Any use of these materials for unauthorized purposes is prohibited.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;