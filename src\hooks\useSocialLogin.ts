import { signIn } from "next-auth/react";
import { toast } from "sonner";
import { UserRole } from "@prisma/client";

interface UseSocialLoginProps {
  intendedRole: UserRole;
  callbackUrl: string;
}

interface SocialLoginCheckResponse {
  canProceed: boolean;
  isNewUser: boolean;
  existingRole?: UserRole;
  correctLoginPage?: string;
  message: string;
}

export function useSocialLogin({ intendedRole, callbackUrl }: UseSocialLoginProps) {
  const handleSocialLogin = async (provider: string) => {
    try {
      // For social login, we can't pre-validate the user's email since we don't have it yet
      // Instead, we'll use a custom callback URL that includes role validation
      const customCallbackUrl = `${callbackUrl}?intended_role=${intendedRole}&provider=${provider}`;
      
      await signIn(provider, {
        callbackUrl: customCallbackUrl,
        redirect: true,
      });
    } catch (error) {
      console.error("Social login error:", error);
      toast.error(`${provider} login failed. Please try again.`);
    }
  };

  return { handleSocialLogin };
}

// Alternative approach: Pre-validate social login with email input
export function useSocialLoginWithEmailCheck({ intendedRole, callbackUrl }: UseSocialLoginProps) {
  const handleSocialLoginWithEmail = async (provider: string, email?: string) => {
    try {
      if (email) {
        // If we have the email, we can pre-validate
        const response = await fetch("/api/auth/social-login-check", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            email,
            intendedRole,
            provider,
          }),
        });

        if (response.ok) {
          const result: SocialLoginCheckResponse = await response.json();
          
          if (!result.canProceed) {
            toast.error(result.message);
            return;
          }
        }
      }

      // Proceed with social login
      await signIn(provider, {
        callbackUrl,
        redirect: true,
      });
    } catch (error) {
      console.error("Social login error:", error);
      toast.error(`${provider} login failed. Please try again.`);
    }
  };

  return { handleSocialLoginWithEmail };
}
