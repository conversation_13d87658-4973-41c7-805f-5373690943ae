// Utility to update assignment status and payment details after payment
import { AssignmentStatus, PaymentStatus } from "@prisma/client";
import  prisma  from "./prisma";
import { notificationService } from "./notification-service";
import { InvoiceData } from "./email-service";

export async function updateAssignmentStatus(assignmentId: string, status: string): Promise<void> {
  console.log("[PayPal Debug] Updating assignment status:", { assignmentId, status });

  try {
    const updatedAssignment = await prisma.assignment.update({
      where: { id: assignmentId },
      data: { status: status as AssignmentStatus },
    });
    console.log("[PayPal Debug] Assignment status updated successfully:", updatedAssignment.status);
  } catch (error) {
    console.error("[PayPal Debug] Failed to update assignment status:", error);
    throw error;
  }
}

export interface PaymentUpdateData {
  status: string;
  paymentStatus: string;
  paypalOrderId: string;
  paypalPayerId: string;
  paypalPaymentId: string;
}

export async function updateAssignmentWithPayment(
  assignmentId: string,
  paymentData: PaymentUpdateData
): Promise<void> {
  console.log("[PayPal Debug] Updating assignment with payment data:", { assignmentId, paymentData });

  try {
    const updatedAssignment = await prisma.assignment.update({
      where: { id: assignmentId },
      data: {
        status: paymentData.status as AssignmentStatus,
        paymentStatus: paymentData.paymentStatus as PaymentStatus,
        paypalOrderId: paymentData.paypalOrderId,
        paypalPayerId: paymentData.paypalPayerId,
        paypalPaymentId: paymentData.paypalPaymentId,
      },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    console.log("[PayPal Debug] Assignment updated successfully with payment details:", {
      status: updatedAssignment.status,
      paymentStatus: updatedAssignment.paymentStatus,
      paypalOrderId: updatedAssignment.paypalOrderId,
    });

    // Send payment notifications if payment status is PAID
    if (paymentData.paymentStatus === PaymentStatus.PAID && updatedAssignment.client) {
      try {
        // Generate invoice data
        const invoiceData: InvoiceData = {
          invoiceNumber: `INV-${updatedAssignment.taskId}-${Date.now()}`,
          date: new Date().toLocaleDateString(),
          clientName: updatedAssignment.client.name || "Client",
          clientEmail: updatedAssignment.client.email,
          assignmentTitle: updatedAssignment.title,
          taskId: updatedAssignment.taskId,
          amount: updatedAssignment.price,
          paymentMethod: "PayPal",
          transactionId: paymentData.paypalPaymentId,
        };

        // Notify client with invoice
        await notificationService.sendPaymentReceivedNotification(
          updatedAssignment.client.id,
          updatedAssignment.client.email,
          updatedAssignment.client.name || "Client",
          updatedAssignment.id,
          updatedAssignment.title,
          updatedAssignment.taskId,
          updatedAssignment.price,
          invoiceData
        );

        // Notify admins (without invoice)
        const admins = await notificationService.getAllAdmins();
        for (const admin of admins) {
          await notificationService.sendPaymentReceivedNotification(
            admin.id,
            admin.email,
            admin.name || "Admin",
            updatedAssignment.id,
            updatedAssignment.title,
            updatedAssignment.taskId,
            updatedAssignment.price
          );
        }
      } catch (notificationError) {
        console.error("[PayPal Debug] Error sending payment notifications:", notificationError);
        // Don't fail the payment update if notifications fail
      }
    }
  } catch (error) {
    console.error("[PayPal Debug] Failed to update assignment with payment data:", error);
    throw error;
  }
}
