"use client";

import { FC } from "react";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { useSession } from "next-auth/react";
import { useWriterApproval } from "@/hooks/use-writer-approval";

interface NavbarAuthProps {
  className?: string;
}

const NavbarAuth: FC<NavbarAuthProps> = ({ className }) => {
  // Use the useSession hook to get the current authentication status and user data
  const { data: session, status } = useSession();
  const { getWriterPath } = useWriterApproval();
  const isAuthenticated = status === "authenticated";

  // Determine the dashboard URL based on user role
  const getDashboardUrl = () => {
    if (!session?.user?.role) return "/client/dashboard";

    switch (session.user.role) {
      case "ADMIN":
        return "/admin/dashboard";
      case "WRITER":
        return getWriterPath();
      case "CLIENT":
      default:
        return "/client/dashboard";
    }
  };

  const dashboardUrl = getDashboardUrl();

  return (
    <div className={cn("flex items-center gap-1 sm:gap-2", className)}>
      {/*Conditionally render Login or Dashboard button based on auth status */}
      {isAuthenticated ? (
        <div className="flex items-center gap-1 sm:gap-2">
          <Link href={dashboardUrl}>
            <Button
              variant="outline"
              size="sm"
              className="hidden cursor-pointer sm:flex"
            >
              Dashboard
            </Button>
          </Link>
        </div>
      ) : (
        <Link href="/login/client">
          <Button
            variant="outline"
            size="sm"
            className="hidden cursor-pointer sm:flex"
          >
            Log in
          </Button>
        </Link>
      )}

      <Link href="/create-order">
        <Button
          size="sm"
          className="bg-primary text-primary-foreground hover:bg-primary/90 px-2 cursor-pointer sm:px-4 py-1 text-xs sm:text-sm"
        >
          Order Now
        </Button>
      </Link>
    </div>
  );
};

export { NavbarAuth };
