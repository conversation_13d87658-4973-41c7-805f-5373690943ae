// src/components/landing-page/sections/ContactSection.tsx
"use client";

import React from "react";
import Link from "next/link";
import { useCompanyInfo } from "@/hooks/use-company-info";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Phone, Mail, MapPin, Clock, MessageCircle } from "lucide-react";

export const ContactSection = () => {
  const { companyInfo } = useCompanyInfo();

  return (
    <section id="contact" className="py-8">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4 px-4 py-1 border-green-200 text-green-700 bg-green-50">
            Get In Touch
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Ready to Achieve Academic Success?
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto text-lg">
            Contact our team for personalized assistance with your academic writing needs
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <Card className="border-0 shadow-md">
              <CardHeader>
                <CardTitle>Contact Us</CardTitle>
                <CardDescription>
                  Fill out the form below and we&apos;ll get back to you within 1 hour.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <label htmlFor="name" className="text-sm font-medium">
                        Full Name
                      </label>
                      <Input id="name" placeholder="Your name" />
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="email" className="text-sm font-medium">
                        Email Address
                      </label>
                      <Input id="email" type="email" placeholder="Your email" />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <label htmlFor="subject" className="text-sm font-medium">
                        Subject
                      </label>
                      <Input id="subject" placeholder="Subject" />
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="urgency" className="text-sm font-medium">
                        Urgency
                      </label>
                      <select 
                        id="urgency" 
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        <option value="">Select deadline</option>
                        <option value="3hours">3 hours</option>
                        <option value="6hours">6 hours</option>
                        <option value="12hours">12 hours</option>
                        <option value="24hours">24 hours</option>
                        <option value="2days">2 days</option>
                        <option value="3days">3 days</option>
                        <option value="1week">1 week</option>
                      </select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="message" className="text-sm font-medium">
                      Message
                    </label>
                    <Textarea
                      id="message"
                      placeholder="Describe your requirements in detail..."
                      className="min-h-32"
                    />
                  </div>

                  <div className="flex items-start gap-2">
                    <input
                      type="checkbox"
                      id="privacy"
                      className="mt-1"
                    />
                    <label htmlFor="privacy" className="text-sm text-gray-600">
                      I agree to the <Link href="/privacy-policy" className="text-blue-600 hover:underline">privacy policy</Link> and consent to being contacted regarding my inquiry.
                    </label>
                  </div>

                  <Link href="/contact-us">
                    <Button className="w-full bg-green-600 hover:bg-green-700">
                      Go to Contact Page
                    </Button>
                  </Link>
                </form>
              </CardContent>
            </Card>
          </div>

          <div>
            <Card className="border-0 shadow-md">
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
                <CardDescription>
                  Multiple ways to reach our support team
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex gap-3 items-start">
                  <div className="bg-green-100 p-2 rounded-full">
                    <MessageCircle className="h-5 w-5 text-green-700" />
                  </div>
                  <div>
                    <h4 className="font-medium">Live Chat</h4>
                    <p className="text-gray-600 text-sm">Get instant responses from our support team</p>
                  </div>
                </div>

                <div className="flex gap-3 items-start">
                  <div className="bg-green-100 p-2 rounded-full">
                    <Mail className="h-5 w-5 text-green-700" />
                  </div>
                  <div>
                    <h4 className="font-medium">Email Us</h4>
                    <p className="text-gray-600 text-sm">{companyInfo?.supportEmail || "<EMAIL>"}</p>
                    <p className="text-gray-600 text-sm">{companyInfo?.inquiriesEmail || "<EMAIL>"}</p>
                  </div>
                </div>

                <div className="flex gap-3 items-start">
                  <div className="bg-green-100 p-2 rounded-full">
                    <Phone className="h-5 w-5 text-green-700" />
                  </div>
                  <div>
                    <h4 className="font-medium">Call Us</h4>
                    <p className="text-gray-600 text-sm">{companyInfo?.tollFreePhone || "+****************"} (Toll-free)</p>
                    <p className="text-gray-600 text-sm">{companyInfo?.internationalPhone || "+****************"} (International)</p>
                  </div>
                </div>

                <Separator />

                <div className="flex gap-3 items-start">
                  <div className="bg-green-100 p-2 rounded-full">
                    <Clock className="h-5 w-5 text-green-700" />
                  </div>
                  <div>
                    <h4 className="font-medium">Hours of Operation</h4>
                    <p className="text-gray-600 text-sm">{companyInfo?.businessHours || "24/7 Customer Support - Business hours: Mon-Fri 9am-6pm EST"}</p>
                  </div>
                </div>

                <div className="flex gap-3 items-start">
                  <div className="bg-green-100 p-2 rounded-full">
                    <MapPin className="h-5 w-5 text-green-700" />
                  </div>
                  <div>
                    <h4 className="font-medium">Office Location</h4>
                    <p className="text-gray-600 text-sm">
                      {companyInfo?.address || "1234 Academic Way, Suite 500"}<br />
                      {companyInfo?.city || "New York"}, {companyInfo?.state || "NY"} {companyInfo?.zipCode || "10001"}<br />
                      {companyInfo?.country || "United States"}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="mt-6">
              <Card className="border-0 shadow-md bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold mb-2">Emergency deadline?</h3>
                  <p className="mb-4 text-white/90">
                    Need your paper in 3 hours or less? Contact our emergency team now!
                  </p>
                  <Link href="/contact-us">
                    <Button variant="secondary" className="w-full">
                      Emergency Assistance
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;