import { <PERSON>ada<PERSON> } from "next";
import { Suspense, lazy } from "react";
import { Skeleton } from "@/components/ui/skeleton";

// Lazy load components for better performance
const TestimonialHero = lazy(() => import("./components/TestimonialHero"));
const TestimonialGrid = lazy(() => import("./components/TestimonialGrid"));
const TestimonialStats = lazy(() => import("./components/TestimonialStats"));
const TestimonialCTA = lazy(() => import("./components/TestimonialCTA"));

export const metadata: Metadata = {
  title: "Student Testimonials | Academic Writing Services",
  description: "Read authentic reviews from students who achieved academic success with our professional writing services. 98% satisfaction rate with verified testimonials.",
  keywords: "student testimonials, academic writing reviews, essay writing service reviews, dissertation help testimonials, research paper writing reviews",
  openGraph: {
    title: "Student Testimonials | Academic Writing Services",
    description: "Read authentic reviews from students who achieved academic success with our professional writing services.",
    type: "website",
  },
};

// Loading skeleton component
const TestimonialSkeleton = () => (
  <div className="space-y-8 py-16">
    <div className="container mx-auto px-4">
      <Skeleton className="h-12 w-3/4 mx-auto mb-4" />
      <Skeleton className="h-6 w-1/2 mx-auto mb-8" />
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="space-y-4">
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        ))}
      </div>
    </div>
  </div>
);

export default function TestimonialsPage() {
  return (
    <main className="min-h-screen bg-background">
      <Suspense fallback={<TestimonialSkeleton />}>
        <TestimonialHero />
      </Suspense>

      <Suspense fallback={<TestimonialSkeleton />}>
        <TestimonialGrid />
      </Suspense>

      <Suspense fallback={<TestimonialSkeleton />}>
        <TestimonialStats />
      </Suspense>

      <Suspense fallback={<TestimonialSkeleton />}>
        <TestimonialCTA />
      </Suspense>
    </main>
  );
}