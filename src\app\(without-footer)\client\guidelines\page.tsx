import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import IntroGuidelines from "@/components/dashboard/client/intro-guidelines";
import TermsGuidelines from "@/components/dashboard/client/terms-guidelines";

export default function GuidelinesDashboard() {
  const breadcrumbs = [
    { label: "Dashboard", href: "/client/dashboard" },
    { label: "Guidelines", isCurrentPage: true },
  ];

  return (
    <>
      {/* Header with Sidebar Trigger and Breadcrumbs */}
      <header className="flex h-16 shrink-0 items-center gap-2 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <Breadcrumb>
            <BreadcrumbList>
              {breadcrumbs.map((item, index) => (
                <div key={index} className="flex items-center">
                  {index > 0 && (
                    <BreadcrumbSeparator className="hidden md:block text-muted-foreground" />
                  )}
                  <BreadcrumbItem className="hidden md:block">
                    {item.isCurrentPage ? (
                      <BreadcrumbPage className="text-foreground">
                        {item.label}
                      </BreadcrumbPage>
                    ) : (
                      <BreadcrumbLink
                        href={item.href || "#"}
                        className="text-muted-foreground hover:text-foreground"
                      >
                        {item.label}
                      </BreadcrumbLink>
                    )}
                  </BreadcrumbItem>
                </div>
              ))}
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>

      {/* Main Content Area */}
      <div className="w-full max-w-6xl mx-auto pt-2 px-6 pb-6 bg-background">
        <Tabs defaultValue="introduction" className="w-full">
          <div className="flex justify-center w-full">
            <TabsList className="inline-flex w-full max-w-[600px] justify-center rounded-full bg-muted p-1 shadow-sm dark:bg-muted/50">
              <TabsTrigger
                value="introduction"
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground 
                   text-sm font-medium px-4 py-2 rounded-full transition-all duration-300 
                   text-muted-foreground hover:bg-primary/10 hover:text-primary
                   dark:text-muted-foreground dark:hover:text-primary
                   dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground"
              >
                Introduction
              </TabsTrigger>

              <TabsTrigger
                value="terms"
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground 
                   text-sm font-medium px-4 py-2 rounded-full transition-all duration-300 
                   text-muted-foreground hover:bg-primary/10 hover:text-primary
                   dark:text-muted-foreground dark:hover:text-primary
                   dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground"
              >
                Terms of Service
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="introduction" className="mt-6">
            <IntroGuidelines />
          </TabsContent>

          <TabsContent value="terms" className="mt-6">
            <TermsGuidelines />
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
}
