import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { checkPermission, apiError } from "@/lib/api-utils";
import { notificationService } from "@/lib/notification-service";
import { NotificationType } from "@prisma/client";

interface CapturePayoutRequest {
  orderId: string; // Assignment ID or bulk payment ID (format: bulk_{writerId}_{timestamp})
  batchId: string; // PayPal payout batch ID
  payoutItemId: string; // PayPal payout item ID
}

interface CapturePayoutResponse {
  id: string;
  status: string;
  paymentID: string;
}

export async function POST(req: NextRequest) {
  try {
    // Only admins can check writer payout status
    const permissionError = await checkPermission(["ADMIN"]);
    if (permissionError) return permissionError;

    const { orderId, batchId, payoutItemId }: CapturePayoutRequest = await req.json();
    console.log("[Writer PayPal Payout Check] Request:", { orderId, batchId, payoutItemId });

    // First, let's get the order details to see what we're working with
    const environment = process.env.PAYPAL_ENVIRONMENT;
    const isSandbox = environment !== "production";
    const PAYPAL_CLIENT_ID = isSandbox
      ? process.env.PAYPAL_CLIENT_ID_SANDBOX
      : process.env.PAYPAL_CLIENT_ID_LIVE;
    const PAYPAL_SECRET = isSandbox
      ? process.env.PAYPAL_SECRET_SANDBOX
      : process.env.PAYPAL_SECRET_LIVE;
    const PAYPAL_API_URL = isSandbox
      ? "https://api-m.sandbox.paypal.com"
      : "https://api-m.paypal.com";

    const basicAuth = Buffer.from(`${PAYPAL_CLIENT_ID}:${PAYPAL_SECRET}`).toString("base64");

    // Get PayPal access token
    const tokenRes = await fetch(`${PAYPAL_API_URL}/v1/oauth2/token`, {
      method: "POST",
      headers: {
        "Authorization": `Basic ${basicAuth}`,
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: "grant_type=client_credentials",
    });

    const tokenData = await tokenRes.json();
    if (!tokenRes.ok || !tokenData.access_token) {
      console.error("[Writer PayPal Capture] Token error:", tokenData);
      return apiError("Failed to authenticate with PayPal", 500);
    }

    // Check payout batch status to get individual items
    const payoutBatchRes = await fetch(`${PAYPAL_API_URL}/v1/payments/payouts/${batchId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${tokenData.access_token}`,
      },
    });

    const payoutBatchData = await payoutBatchRes.json();
    console.log("[Writer PayPal Payout Check] Payout batch response:", JSON.stringify(payoutBatchData, null, 2));

    if (!payoutBatchRes.ok) {
      console.error("[Writer PayPal Payout Check] Batch status check failed:", payoutBatchData);
      console.error("[Writer PayPal Payout Check] Response status:", payoutBatchRes.status);

      return apiError(
        payoutBatchData.message || "Failed to check payout batch status",
        payoutBatchRes.status
      );
    }

    // Extract the first payout item from the batch
    const payoutItems = payoutBatchData.items;
    if (!payoutItems || payoutItems.length === 0) {
      console.error("[Writer PayPal Payout Check] No payout items found in batch");
      return apiError("No payout items found in batch", 500);
    }

    const firstPayoutItem = payoutItems[0];
    const payoutStatus = firstPayoutItem.transaction_status;
    console.log("[Writer PayPal Payout Check] Current payout status:", payoutStatus);

    // For immediate processing, we'll accept PENDING, SUCCESS, and UNCLAIMED as valid statuses
    // UNCLAIMED means the recipient doesn't have a PayPal account yet, but the payout was sent successfully
    // In production, you might want to implement a webhook to handle status updates
    const validStatuses = ["SUCCESS", "PENDING", "UNCLAIMED"];
    if (!validStatuses.includes(payoutStatus)) {
      return apiError(
        `Payout failed with status: ${payoutStatus}. ${firstPayoutItem.errors?.[0]?.message || ""}`,
        400
      );
    }

    // Extract payout details from the first item
    const payoutTransactionId = firstPayoutItem.payout_item_id;
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const payoutAmount = firstPayoutItem.payout_item?.amount?.value;

    if (!payoutTransactionId) {
      console.error("[Writer PayPal Payout Check] No payout transaction ID found");
      return apiError("Payout transaction ID not found", 500);
    }

    // Check if this is a bulk payment (format: bulk_{writerId}_{timestamp})
    const isBulkPayment = orderId.startsWith("bulk_");

    if (isBulkPayment) {
      console.log("[Writer PayPal Payout Check] Processing bulk payment completion");

      // Extract writer ID from bulk payment ID
      const bulkIdParts = orderId.split("_");
      if (bulkIdParts.length < 3) {
        return apiError("Invalid bulk payment ID format", 400);
      }

      const writerId = bulkIdParts[1];
      console.log("[Writer PayPal Payout Check] Bulk payment completion for writer:", writerId);

      // Fetch all unpaid completed assignments for this writer
      const assignments = await prisma.assignment.findMany({
        where: {
          assignedWriterId: writerId,
          status: "COMPLETED",
          isWriterPaid: false,
          writerCompensation: { not: null },
        },
        include: {
          assignedWriter: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      if (assignments.length === 0) {
        return apiError("No unpaid assignments found for this writer", 404);
      }

      // Update all assignments with payout details
      const updatePromises = assignments.map(assignment =>
        prisma.assignment.update({
          where: { id: assignment.id },
          data: {
            isWriterPaid: true,
            writerPaymentDate: new Date(),
            writerPaypalOrderId: batchId,
            writerPaypalPaymentId: payoutTransactionId,
            writerPaypalEmail: assignment.assignedWriter?.email,
          },
        })
      );

      await Promise.all(updatePromises);

      // Calculate total payment amount
      const totalAmount = assignments.reduce((sum, assignment) => sum + (assignment.writerCompensation || 0), 0);

      // Send notification to writer about bulk payment
      const writer = assignments[0].assignedWriter;
      if (writer) {
        await notificationService.sendNotification({
          userId: writer.id,
          type: NotificationType.PAYMENT_RECEIVED,
          title: "Bulk Payment Received",
          message: `You have received a bulk payment of $${totalAmount.toFixed(2)} for ${assignments.length} completed assignments`,
          assignmentId: assignments[0].id, // Use first assignment ID as reference
          taskId: assignments.map(a => a.taskId).join(", "),
          emailData: {
            userEmail: writer.email,
            userName: writer.name || "Writer",
            assignmentTitle: `${assignments.length} assignments`,
            amount: totalAmount,
            customMessage: "Payment method: PayPal",
          },
        });
      }

      // Send notification to all admins about bulk payment completion
      const admins = await prisma.user.findMany({
        where: { role: "ADMIN" },
        select: { id: true },
      });

      if (admins.length > 0) {
        await notificationService.sendNotificationToMultipleUsers(
          admins.map(admin => admin.id),
          NotificationType.PAYMENT_COMPLETED,
          "Bulk Writer Payment Completed",
          `Bulk payment of $${totalAmount.toFixed(2)} has been disbursed to ${writer?.name || 'writer'} for ${assignments.length} assignments`,
          assignments.map(a => a.taskId).join(", "),
          assignments[0].id
        );
      }

    } else {
      console.log("[Writer PayPal Payout Check] Processing single assignment payment completion");

      // Original single assignment logic
      const assignment = await prisma.assignment.findUnique({
        where: { id: orderId },
        include: {
          assignedWriter: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      if (!assignment) {
        return apiError("Assignment not found", 404);
      }

      if (!assignment.assignedWriter) {
        return apiError("Writer not found", 404);
      }

      // Update assignment with payout details
      await prisma.assignment.update({
        where: { id: orderId },
        data: {
          isWriterPaid: true,
          writerPaymentDate: new Date(),
          writerPaypalOrderId: batchId,
          writerPaypalPaymentId: payoutTransactionId,
          writerPaypalEmail: assignment.assignedWriter.email,
        },
      });

      // Send notification to writer about payment
      await notificationService.sendNotification({
        userId: assignment.assignedWriter.id,
        type: NotificationType.PAYMENT_RECEIVED,
        title: "Payment Received",
        message: `You have received payment of $${assignment.writerCompensation?.toFixed(2) || '0.00'} for assignment "${assignment.title}"`,
        assignmentId: assignment.id,
        taskId: assignment.taskId,
        emailData: {
          userEmail: assignment.assignedWriter.email,
          userName: assignment.assignedWriter.name || "Writer",
          assignmentTitle: assignment.title,
          amount: assignment.writerCompensation || 0,
          customMessage: "Payment method: PayPal",
        },
      });

      // Send notification to all admins about payment completion
      const admins = await prisma.user.findMany({
        where: { role: "ADMIN" },
        select: { id: true },
      });

      if (admins.length > 0) {
        await notificationService.sendNotificationToMultipleUsers(
          admins.map(admin => admin.id),
          NotificationType.PAYMENT_COMPLETED,
          "Writer Payment Completed",
          `Payment of $${assignment.writerCompensation?.toFixed(2) || '0.00'} has been disbursed to ${assignment.assignedWriter.name || 'writer'} for assignment "${assignment.title}"`,
          assignment.taskId,
          assignment.id
        );
      }
    }

    const response: CapturePayoutResponse = {
      id: batchId,
      status: payoutStatus,
      paymentID: payoutTransactionId,
    };

    console.log("[Writer PayPal Payout Check] Payout completed successfully:", response);
    return NextResponse.json(response);
  } catch (error) {
    console.error("[Writer PayPal Payout Check] Unexpected error:", error);
    return apiError("Internal server error", 500);
  }
}
