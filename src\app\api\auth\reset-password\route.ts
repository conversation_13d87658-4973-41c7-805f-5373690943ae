import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import bcrypt from "bcryptjs";
import { resetPasswordSchema, verifyResetCodeSchema } from "@/lib/validations";
import { parseRequestBody, apiError, apiSuccess, isResetCodeExpired } from "@/lib/api-utils";

export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Parse and validate request body
    const body = await parseRequestBody(req, resetPasswordSchema);
    if ('success' in body && !body.success) {
      return apiError(body.message, 400, body.errors);
    }

    const requestData = body as { code: string; newPassword: string; confirmPassword: string };
    const { code, newPassword } = requestData;

    // Find the password reset token
    const resetToken = await prisma.passwordResetToken.findUnique({
      where: { code },
      include: {
        user: {
          select: { id: true, email: true, name: true }
        }
      }
    });

    if (!resetToken) {
      return apiError("Invalid verification code", 400);
    }

    // Check if token is expired
    if (isResetCodeExpired(resetToken.expires)) {
      // Delete expired token
      await prisma.passwordResetToken.delete({
        where: { id: resetToken.id }
      });
      return apiError("Verification code has expired. Please request a new one.", 400);
    }

    // Check if token is already used
    if (resetToken.isUsed) {
      return apiError("Verification code has already been used", 400);
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Update user password and mark token as used
    await prisma.$transaction([
      prisma.user.update({
        where: { id: resetToken.userId },
        data: { password: hashedPassword }
      }),
      prisma.passwordResetToken.update({
        where: { id: resetToken.id },
        data: { isUsed: true }
      })
    ]);

    // Delete all password reset tokens for this user (cleanup)
    await prisma.passwordResetToken.deleteMany({
      where: { userId: resetToken.userId }
    });

    return apiSuccess(
      { 
        message: "Password reset successfully",
        user: {
          email: resetToken.user.email,
          name: resetToken.user.name
        }
      },
      "Password reset successfully"
    );

  } catch (error) {
    console.error("Reset password error:", error);
    return apiError("An error occurred while resetting your password", 500);
  }
}

// Verify reset code endpoint (optional - for checking code validity before password reset)
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(req.url);
    const code = searchParams.get('code');

    if (!code) {
      return apiError("Verification code is required", 400);
    }

    // Validate code format
    const validation = verifyResetCodeSchema.safeParse({ code });
    if (!validation.success) {
      return apiError("Invalid verification code format", 400);
    }

    // Find the password reset token
    const resetToken = await prisma.passwordResetToken.findUnique({
      where: { code },
      include: {
        user: {
          select: { email: true }
        }
      }
    });

    if (!resetToken) {
      return apiError("Invalid verification code", 400);
    }

    // Check if token is expired
    if (isResetCodeExpired(resetToken.expires)) {
      // Delete expired token
      await prisma.passwordResetToken.delete({
        where: { id: resetToken.id }
      });
      return apiError("Verification code has expired", 400);
    }

    // Check if token is already used
    if (resetToken.isUsed) {
      return apiError("Verification code has already been used", 400);
    }

    return apiSuccess(
      { 
        valid: true,
        email: resetToken.user.email,
        expiresAt: resetToken.expires
      },
      "Verification code is valid"
    );

  } catch (error) {
    console.error("Verify reset code error:", error);
    return apiError("An error occurred while verifying the code", 500);
  }
}
