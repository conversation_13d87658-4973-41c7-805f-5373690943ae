"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertCircle, ArrowRight, Home } from "lucide-react";
import { Suspense } from "react";

function AuthErrorContent() {
  const searchParams = useSearchParams();
  const [errorInfo, setErrorInfo] = useState({
    error: "",
    message: "",
    correctUrl: "",
  });

  useEffect(() => {
    const error = searchParams.get("error") || "";
    const message = searchParams.get("message") || "";
    const correctUrl = searchParams.get("correctUrl") || "";

    setErrorInfo({ error, message, correctUrl });
  }, [searchParams]);

  const getErrorTitle = () => {
    switch (errorInfo.error) {
      case "WrongRole":
        return "Wrong Login Portal";
      case "RoleMismatch":
        return "Account Role Conflict";
      case "AccessDenied":
        return "Access Denied";
      case "Configuration":
        return "Configuration Error";
      default:
        return "Authentication Error";
    }
  };

  const getErrorDescription = () => {
    if (errorInfo.message) {
      return errorInfo.message;
    }

    switch (errorInfo.error) {
      case "WrongRole":
        return "You're trying to access the wrong login portal for your account type.";
      case "RoleMismatch":
        return "This email is already registered with a different account type. Please use the correct login page for your existing account.";
      case "AccessDenied":
        return "You don't have permission to access this resource.";
      case "Configuration":
        return "There's a configuration issue with the authentication system.";
      default:
        return "An error occurred during authentication. Please try again.";
    }
  };

  return (
    <div className="container flex h-screen w-full items-center justify-center px-4 py-8">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-destructive/10 rounded-full">
              <AlertCircle className="h-8 w-8 text-destructive" />
            </div>
          </div>
          <CardTitle className="text-xl font-bold text-destructive">
            {getErrorTitle()}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-center text-muted-foreground">
            {getErrorDescription()}
          </p>

          <div className="space-y-3">
            {errorInfo.correctUrl && (
              <Button asChild className="w-full">
                <Link href={errorInfo.correctUrl}>
                  <ArrowRight className="h-4 w-4 mr-2" />
                  Go to Correct Login Page
                </Link>
              </Button>
            )}

            <Button asChild variant="outline" className="w-full">
              <Link href="/">
                <Home className="h-4 w-4 mr-2" />
                Go to Homepage
              </Link>
            </Button>
          </div>

          {process.env.NODE_ENV === "development" && errorInfo.error && (
            <details className="text-left mt-4">
              <summary className="cursor-pointer text-sm text-muted-foreground">
                Debug Info (Development)
              </summary>
              <pre className="mt-2 text-xs bg-muted p-3 rounded overflow-auto">
                Error: {errorInfo.error}
                {errorInfo.message && `\nMessage: ${errorInfo.message}`}
                {errorInfo.correctUrl && `\nCorrect URL: ${errorInfo.correctUrl}`}
              </pre>
            </details>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export default function AuthErrorPage() {
  return (
    <Suspense fallback={
      <div className="container flex h-screen w-full items-center justify-center px-4 py-8">
        <Card className="w-full max-w-md">
          <CardContent className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p>Loading...</p>
          </CardContent>
        </Card>
      </div>
    }>
      <AuthErrorContent />
    </Suspense>
  );
}
