import { Resend } from "resend";
import { NotificationType } from "@prisma/client";

const resend = new Resend(process.env.RESEND_API_KEY);
const fromAddress = process.env.RESEND_VERIFIED_DOMAIN
  ? `noreply@${process.env.RESEND_VERIFIED_DOMAIN}`
  : "<EMAIL>";

// Log the email configuration on startup
console.log("📧 Email Service Configuration:");
console.log(`   From Address: ${fromAddress}`);
console.log(`   Verified Domain: ${process.env.RESEND_VERIFIED_DOMAIN || 'Not configured'}`);
console.log(`   Using ${process.env.RESEND_VERIFIED_DOMAIN ? 'VERIFIED DOMAIN' : 'TEST MODE (limited to your email only)'}`);
console.log("---");

export interface EmailNotificationData {
  to: string;
  type: NotificationType;
  data: {
    userName?: string;
    assignmentTitle?: string;
    assignmentId?: string;
    taskId?: string;
    status?: string;
    amount?: number;
    invoiceData?: InvoiceData;
    writerName?: string;
    clientName?: string;
    adminName?: string;
    dueDate?: string;
    customMessage?: string;
    paymentAmount?: string;
    paymentMethod?: string;
  };
}

export interface InvoiceData {
  invoiceNumber: string;
  date: string;
  clientName: string;
  clientEmail: string;
  assignmentTitle: string;
  taskId: string;
  amount: number;
  paymentMethod: string;
  transactionId?: string;
}

class EmailService {
  private getBaseUrl(): string {
    return process.env.NEXT_PUBLIC_BASE_URL || process.env.NEXTAUTH_URL || "http://localhost:3000";
  }

  private generateInvoiceHtml(invoiceData: InvoiceData): string {
    return `
      <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; background: #ffffff; border: 1px solid #e0e0e0;">
        <div style="background: #f8f9fa; padding: 20px; border-bottom: 1px solid #e0e0e0;">
          <h1 style="margin: 0; color: #333; font-size: 24px;">Invoice</h1>
          <p style="margin: 5px 0 0 0; color: #666;">Invoice #${invoiceData.invoiceNumber}</p>
        </div>
        
        <div style="padding: 20px;">
          <div style="margin-bottom: 30px;">
            <h3 style="margin: 0 0 10px 0; color: #333;">Bill To:</h3>
            <p style="margin: 0; color: #666;">${invoiceData.clientName}</p>
            <p style="margin: 0; color: #666;">${invoiceData.clientEmail}</p>
          </div>
          
          <div style="margin-bottom: 30px;">
            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
              <span style="color: #666;">Date:</span>
              <span style="color: #333;">${invoiceData.date}</span>
            </div>
            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
              <span style="color: #666;">Task ID:</span>
              <span style="color: #333;">${invoiceData.taskId}</span>
            </div>
          </div>
          
          <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px;">
            <thead>
              <tr style="background: #f8f9fa;">
                <th style="padding: 12px; text-align: left; border: 1px solid #e0e0e0; color: #333;">Description</th>
                <th style="padding: 12px; text-align: right; border: 1px solid #e0e0e0; color: #333;">Amount</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td style="padding: 12px; border: 1px solid #e0e0e0; color: #666;">${invoiceData.assignmentTitle}</td>
                <td style="padding: 12px; text-align: right; border: 1px solid #e0e0e0; color: #666;">$${invoiceData.amount.toFixed(2)}</td>
              </tr>
            </tbody>
            <tfoot>
              <tr style="background: #f8f9fa;">
                <td style="padding: 12px; font-weight: bold; border: 1px solid #e0e0e0; color: #333;">Total</td>
                <td style="padding: 12px; text-align: right; font-weight: bold; border: 1px solid #e0e0e0; color: #333;">$${invoiceData.amount.toFixed(2)}</td>
              </tr>
            </tfoot>
          </table>
          
          <div style="margin-bottom: 20px;">
            <h4 style="margin: 0 0 10px 0; color: #333;">Payment Information</h4>
            <p style="margin: 0; color: #666;">Payment Method: ${invoiceData.paymentMethod}</p>
            ${invoiceData.transactionId ? `<p style="margin: 0; color: #666;">Transaction ID: ${invoiceData.transactionId}</p>` : ''}
          </div>
          
          <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
            <p style="margin: 0; color: #666; font-size: 14px;">Thank you for your business! This invoice has been automatically generated and payment has been processed successfully.</p>
          </div>
        </div>
      </div>
    `;
  }

  private getEmailTemplate(type: NotificationType, data: EmailNotificationData['data']): { subject: string; html: string } {
    const baseUrl = this.getBaseUrl();
    
    switch (type) {
      case NotificationType.WRITER_ASSIGNED:
        return {
          subject: `New Assignment: ${data.assignmentTitle}`,
          html: `
            <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
              <h2 style="color: #333;">New Assignment Assigned to You</h2>
              <p>Hello ${data.userName},</p>
              <p>You have been assigned a new assignment:</p>
              <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h3 style="margin: 0 0 10px 0; color: #333;">${data.assignmentTitle}</h3>
                <p style="margin: 0; color: #666;">Task ID: ${data.taskId}</p>
                ${data.dueDate ? `<p style="margin: 0; color: #666;">Due Date: ${data.dueDate}</p>` : ''}
              </div>
              <p>Please log in to your dashboard to view the full assignment details and start working on it.</p>
              <a href="${baseUrl}/writer/assignments/${data.assignmentId}" style="display: inline-block; background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px 0;">View Assignment</a>
              <p>Best regards,<br>Academic App Team</p>
            </div>
          `
        };

      case NotificationType.STATUS_CHANGE:
        return {
          subject: `Assignment Status Update: ${data.status}`,
          html: `
            <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
              <h2 style="color: #333;">Assignment Status Update</h2>
              <p>Hello ${data.userName},</p>
              <p>The status of your assignment has been updated:</p>
              <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h3 style="margin: 0 0 10px 0; color: #333;">${data.assignmentTitle}</h3>
                <p style="margin: 0; color: #666;">Task ID: ${data.taskId}</p>
                <p style="margin: 0; color: #333; font-weight: bold;">New Status: ${data.status}</p>
              </div>
              ${data.customMessage ? `<p>${data.customMessage}</p>` : ''}
              <p>Please log in to your dashboard to view more details.</p>
              <a href="${baseUrl}/dashboard" style="display: inline-block; background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px 0;">View Dashboard</a>
              <p>Best regards,<br>Academic App Team</p>
            </div>
          `
        };

      case NotificationType.JOB_POSTED:
        return {
          subject: `New Job Posted: ${data.assignmentTitle}`,
          html: `
            <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
              <h2 style="color: #333;">New Job Posted Successfully</h2>
              <p>Hello ${data.userName},</p>
              <p>Your assignment has been posted successfully and is now available for writers to bid on:</p>
              <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h3 style="margin: 0 0 10px 0; color: #333;">${data.assignmentTitle}</h3>
                <p style="margin: 0; color: #666;">Task ID: ${data.taskId}</p>
                ${data.dueDate ? `<p style="margin: 0; color: #666;">Due Date: ${data.dueDate}</p>` : ''}
              </div>
              <p>You will receive notifications when writers submit bids for your assignment.</p>
              <a href="${baseUrl}/client/assignments/${data.assignmentId}" style="display: inline-block; background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px 0;">View Assignment</a>
              <p>Best regards,<br>Academic App Team</p>
            </div>
          `
        };

      case NotificationType.JOB_COMPLETED:
        return {
          subject: `Assignment Completed: ${data.assignmentTitle}`,
          html: `
            <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
              <h2 style="color: #333;">Assignment Completed</h2>
              <p>Hello ${data.userName},</p>
              <p>Great news! Your assignment has been completed:</p>
              <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h3 style="margin: 0 0 10px 0; color: #333;">${data.assignmentTitle}</h3>
                <p style="margin: 0; color: #666;">Task ID: ${data.taskId}</p>
                ${data.writerName ? `<p style="margin: 0; color: #666;">Completed by: ${data.writerName}</p>` : ''}
              </div>
              <p>Please log in to your dashboard to review and download your completed assignment.</p>
              <a href="${baseUrl}/client/assignments/${data.assignmentId}" style="display: inline-block; background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px 0;">View Completed Assignment</a>
              <p>Best regards,<br>Academic App Team</p>
            </div>
          `
        };

      case NotificationType.PAYMENT_RECEIVED:
        // For writer payment received notifications
        if (data.paymentAmount && data.paymentMethod) {
          return {
            subject: `Payment Received: $${data.paymentAmount}`,
            html: `
              <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
                <h2 style="color: #28a745;">💰 Payment Received!</h2>
                <p>Hello ${data.userName},</p>
                <p>Great news! You have received a payment for your completed assignment:</p>
                <div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;">
                  <h3 style="margin: 0 0 10px 0; color: #155724;">${data.assignmentTitle}</h3>
                  <p style="margin: 0; color: #155724;">Task ID: ${data.taskId}</p>
                  <p style="margin: 10px 0 0 0; color: #155724; font-weight: bold; font-size: 18px;">Amount: $${data.paymentAmount}</p>
                  <p style="margin: 5px 0 0 0; color: #155724;">Payment Method: ${data.paymentMethod}</p>
                </div>
                <p>The payment has been processed and should appear in your ${data.paymentMethod} account within 1-3 business days.</p>
                <a href="${baseUrl}/writer/payments/approved" style="display: inline-block; background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px 0;">View Payment History</a>
                <p>Thank you for your excellent work!</p>
                <p>Best regards,<br>Academic App Team</p>
              </div>
            `
          };
        }

        // For client payment received notifications (original)
        const invoiceHtml = data.invoiceData ? this.generateInvoiceHtml(data.invoiceData) : '';
        return {
          subject: `Payment Received - Invoice #${data.invoiceData?.invoiceNumber || 'N/A'}`,
          html: `
            <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
              <h2 style="color: #333;">Payment Received Successfully</h2>
              <p>Hello ${data.userName},</p>
              <p>We have successfully received your payment for the following assignment:</p>
              <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h3 style="margin: 0 0 10px 0; color: #333;">${data.assignmentTitle}</h3>
                <p style="margin: 0; color: #666;">Task ID: ${data.taskId}</p>
                <p style="margin: 0; color: #333; font-weight: bold;">Amount: $${data.amount?.toFixed(2) || '0.00'}</p>
              </div>
              <p>Please find your invoice attached below:</p>
              ${invoiceHtml}
              <p>Thank you for your business!</p>
              <p>Best regards,<br>Academic App Team</p>
            </div>
          `
        };

      case NotificationType.PAYMENT_COMPLETED:
        return {
          subject: `Writer Payment Completed: ${data.assignmentTitle}`,
          html: `
            <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
              <h2 style="color: #333;">Writer Payment Completed</h2>
              <p>Hello ${data.userName},</p>
              <p>A writer payment has been successfully processed:</p>
              <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h3 style="margin: 0 0 10px 0; color: #333;">${data.assignmentTitle}</h3>
                <p style="margin: 0; color: #666;">Task ID: ${data.taskId}</p>
                ${data.writerName ? `<p style="margin: 0; color: #666;">Writer: ${data.writerName}</p>` : ''}
                ${data.paymentAmount ? `<p style="margin: 0; color: #333; font-weight: bold;">Amount: $${data.paymentAmount}</p>` : ''}
              </div>
              <p>The payment has been disbursed to the writer and they have been notified.</p>
              <a href="${baseUrl}/admin/writer-payments" style="display: inline-block; background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px 0;">View Payment Dashboard</a>
              <p>Best regards,<br>Academic App Team</p>
            </div>
          `
        };

      default:
        return {
          subject: 'Notification from Academic App',
          html: `
            <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
              <h2 style="color: #333;">Notification</h2>
              <p>Hello ${data.userName},</p>
              <p>You have a new notification from Academic App.</p>
              <p>Please log in to your dashboard to view more details.</p>
              <a href="${baseUrl}/dashboard" style="display: inline-block; background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px 0;">View Dashboard</a>
              <p>Best regards,<br>Academic App Team</p>
            </div>
          `
        };
    }
  }

  async sendNotificationEmail(emailData: EmailNotificationData): Promise<boolean> {
    try {
      const { subject, html } = this.getEmailTemplate(emailData.type, emailData.data);

      console.log(`📧 Attempting to send email:`);
      console.log(`   From: ${fromAddress}`);
      console.log(`   To: ${emailData.to}`);
      console.log(`   Subject: ${subject}`);
      console.log(`   Type: ${emailData.type}`);

      const result = await resend.emails.send({
        from: fromAddress,
        to: emailData.to,
        subject,
        html,
      });

      if (result.error) {
        console.error("❌ Email sending error:", result.error);
        console.error("   This might be due to:");
        console.error("   1. Domain not verified on Resend");
        console.error("   2. Recipient email not allowed in test mode");
        console.error("   3. Invalid API key or configuration");
        return false;
      }

      console.log("✅ Email sent successfully:", result.data?.id);
      return true;
    } catch (error) {
      console.error("❌ Email service error:", error);
      return false;
    }
  }
}

export const emailService = new EmailService();
