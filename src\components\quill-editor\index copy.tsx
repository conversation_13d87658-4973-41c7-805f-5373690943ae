// /* eslint-disable @typescript-eslint/no-explicit-any */

// "use client";
// import React, { useMemo } from "react";
// import dynamic from "next/dynamic";
// import { cn } from "@/lib/utils";
// import { Tooltip, TooltipContent, <PERSON>ltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
// import "quill/dist/quill.snow.css";

// // Dynamically import ReactQuill
// const ReactQuill = dynamic(() => import("react-quill-new"), {
//   ssr: false,
//   loading: () => (
//     <div className="quill-editor">
//       <div className="h-32 bg-gray-50 border border-gray-200 rounded animate-pulse flex items-center justify-center">
//         <span className="text-gray-400">Loading editor...</span>
//       </div>
//     </div>
//   ),
// });

// const toolbarOptions = [
//   ["bold", "italic", "underline", "strike"],
//   ["blockquote", "code-block"],
//   ["link", "image", "video", "formula"],
//   [{ header: 1 }, { header: 2 }],
//   [{ list: "ordered" }, { list: "bullet" }, { list: "check" }],
//   [{ script: "sub" }, { script: "super" }],
//   [{ indent: "-1" }, { indent: "+1" }],
//   [{ direction: "rtl" }],
//   [{ size: ["small", false, "large", "huge"] }],
//   [{ header: [1, 2, 3, 4, 5, 6, false] }],
//   [{ color: [] }, { background: [] }],
//   [{ font: [] }],
//   [{ align: [] }],
//   ["clean"],
// ];

// // Map toolbar button to tooltip label
// const TOOLTIP_LABELS: Record<string, string> = {
//   bold: "Bold",
//   italic: "Italic",
//   underline: "Underline",
//   strike: "Strikethrough",
//   blockquote: "Blockquote",
//   "code-block": "Code Block",
//   link: "Insert Link",
//   image: "Insert Image",
//   video: "Insert Video",
//   formula: "Insert Formula",
//   header: "Header",
//   list: "List",
//   check: "Checklist",
//   script: "Script",
//   sub: "Subscript",
//   super: "Superscript",
//   indent: "Indent",
//   direction: "Text Direction",
//   size: "Font Size",
//   color: "Font Color",
//   background: "Background Color",
//   font: "Font Family",
//   align: "Text Align",
//   clean: "Remove Formatting",
// };

// // Helper for type-safe select rendering
// function renderSelectOptions(
//   item: Record<string, unknown>,
//   key: string
// ) {
//   // Only render options if the key exists and is an array
//   if (
//     Object.prototype.hasOwnProperty.call(item, key) &&
//     Array.isArray((item as { [k: string]: unknown })[key])
//   ) {
//     const values = (item as { [k: string]: unknown[] })[key];
//     return (values as (string | number | boolean)[]).map((val, idx) => {
//       // Only allow string | number | undefined for value
//       if (val === false) {
//         return (
//           <option value={undefined} key={idx}>
//             Normal
//           </option>
//         );
//       }
//       if (typeof val === "string" || typeof val === "number") {
//         return (
//           <option value={val} key={idx}>
//             {val}
//           </option>
//         );
//       }
//       // Ignore true/other booleans
//       return null;
//     });
//   }
//   // If it's not an array but exists, render single option
//   if (Object.prototype.hasOwnProperty.call(item, key)) {
//     const value = item[key];
//     if (typeof value === "string" || typeof value === "number") {
//       return (
//         <option value={value}>
//           {String(value)}
//         </option>
//       );
//     }
//     // Ignore booleans (shouldn't happen here, but for safety)
//     return null;
//   }
//   // fallback: render nothing
//   return null;
// }

// // Custom Toolbar with shadcn tooltips
// function QuillCustomToolbar() {
//   return (
//     <div id="quill-toolbar">
//       <TooltipProvider>
//         {toolbarOptions.map((group, i) => (
//           <span key={i} className="ql-formats">
//             {group.map((item, j) => {
//               const key = typeof item === "string" ? item : Object.keys(item)[0];
//               const label = TOOLTIP_LABELS[key] || key;
//               // Render the button with tooltip
//               return (
//                 <Tooltip key={j}>
//                   <TooltipTrigger asChild>
//                     {typeof item === "string" ? (
//                       <button type="button" className={`ql-${item}`} aria-label={label} />
//                     ) : (
//                       <select className={`ql-${key}`} aria-label={label}>
//                         {renderSelectOptions(item, key)}
//                       </select>
//                     )}
//                   </TooltipTrigger>
//                   <TooltipContent>{label}</TooltipContent>
//                 </Tooltip>
//               );
//             })}
//           </span>
//         ))}
//       </TooltipProvider>
//     </div>
//   );
// }

// export interface QuillEditorProps {
//   value: string;
//   onChange: (value: string) => void;
//   className?: string;
//   placeholder?: string;
//   readOnly?: boolean;
// }

// export default function QuillEditor({ value, onChange, className, placeholder, readOnly }: QuillEditorProps) {
//   const modules = useMemo(() => ({
//     toolbar: { 
//       container: "#quill-toolbar"
//     }
//   }), []);

//   return (
//     <div className={cn("quill-editor", className)}>
//       <QuillCustomToolbar />
//       <ReactQuill
//         value={value}
//         onChange={onChange}
//         modules={modules}
//         theme="snow"
//         placeholder={placeholder}
//         readOnly={readOnly}
//       />
//     </div>
//   );
// }