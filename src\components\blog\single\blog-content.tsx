// components/blog/single/BlogContent.tsx
"use client";

import React, { useState, useEffect } from "react";
import { Share2, BookO<PERSON>, Clock } from "lucide-react";
import { Button } from "@/components/ui/button";
import SocialShare from "./social-share";
import TableOfContents from "./table-of-contents";
import { NewsletterSubscription } from "@/components/newsletter/newsletter-subscription";
import { Card, CardContent } from "@/components/ui/card";

import { extractHeadings } from "@/lib/heading-utils";

interface BlogPost {
  id: string;
  createdAt: Date;
  title: string;
  body: string;
  slug: string;
  metaTitle: string;
  metaDescription: string;
  imageUrl: string;
  imageAlt: string;
  keywords: string[];
  faqs: string[];
  category: {
    id: string;
    name: string;
    slug: string;
  };
  author: {
    id: string;
    name: string;
    qualifications: string;
  };
}

interface BlogContentProps {
  post: BlogPost;
}

export default function BlogContent({ post }: BlogContentProps) {
  const [headings, setHeadings] = useState<
    Array<{ id: string; text: string; level: number }>
  >([]);
  const [readingProgress, setReadingProgress] = useState(0);
  const [showShare, setShowShare] = useState(false);

  // Content is already processed at the data level, no need to process here
  const processedContent = post.body;





  useEffect(() => {
    // Use a timeout to ensure the HTML content is fully rendered
    const timer = setTimeout(() => {
      const content = document.querySelector(".blog-content");
      if (content) {
        // Use the utility function to extract and process headings
        const headingList = extractHeadings(content);
        console.log('Blog Content: Generated headings:', headingList);
        setHeadings(headingList);
      }
    }, 100); // Small delay to ensure DOM is ready

    return () => clearTimeout(timer);
  }, [processedContent]); // Re-run when processed content changes

  useEffect(() => {
    // Reading progress tracking
    const handleScroll = () => {
      const content = document.querySelector(".blog-content");
      if (content) {
        // Type assertion to HTMLElement to access offsetTop and offsetHeight
        const htmlContent = content as HTMLElement;
        const contentTop = htmlContent.offsetTop;
        const contentHeight = htmlContent.offsetHeight;
        const windowHeight = window.innerHeight;
        const scrollY = window.scrollY;

        const scrolled = Math.max(0, scrollY - contentTop);
        const scrollableHeight = Math.max(0, contentHeight - windowHeight);
        const progress =
          scrollableHeight > 0 ? Math.min(scrolled / scrollableHeight, 1) : 0;

        setReadingProgress(progress * 100);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // REMOVED: The problematic formatContent function that was destroying HTML structure

  // Helper function to get word count from HTML content
  const getWordCount = (htmlContent: string): number => {
    // Remove HTML tags and count words
    const textContent = htmlContent.replace(/<[^>]*>/g, "").trim();
    return textContent.length > 0 ? textContent.split(/\s+/).length : 0;
  };

  const wordCount = getWordCount(processedContent);
  const estimatedReadTime = Math.ceil(wordCount / 200);

  return (
    <div className="relative">
      {/* Reading Progress Bar */}
      <div className="fixed top-0 left-0 right-0 z-50 h-1 bg-muted">
        <div
          className="h-full bg-primary transition-all duration-300 ease-out"
          style={{ width: `${readingProgress}%` }}
        />
      </div>

      {/* Content Header */}
      <div className="flex items-center justify-between mb-8 p-6 bg-muted/30 rounded-lg">
        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
          <div className="flex items-center space-x-1">
            <Clock className="w-4 h-4" />
            <span>{estimatedReadTime} min read</span>
          </div>
          <div className="flex items-center space-x-1">
            <BookOpen className="w-4 h-4" />
            <span>{wordCount} words</span>
          </div>
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowShare(!showShare)}
          className="flex items-center space-x-2"
        >
          <Share2 className="w-4 h-4" />
          <span>Share</span>
        </Button>
      </div>

      {/* Social Share */}
      {showShare && (
        <div className="mb-8">
          <SocialShare
            url={`${typeof window !== "undefined" ? window.location.origin : ""}/blog/${post.slug}`}
            title={post.title}
            description={post.metaDescription}
          />
        </div>
      )}

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Table of Contents - Desktop */}
        {headings.length > 0 && (
          <div className="hidden lg:block lg:w-64 shrink-0">
            <div className="sticky top-24">
              <TableOfContents headings={headings} />
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="flex-1 min-w-0">
          {/* Table of Contents - Mobile */}
          {headings.length > 0 && (
            <div className="lg:hidden mb-8">
              <TableOfContents headings={headings} />
            </div>
          )}

          {/* Article Content - Using typography plugin with custom styles */}
          <div
            className="blog-content prose prose-lg max-w-none dark:prose-invert prose-headings:scroll-mt-24"
            dangerouslySetInnerHTML={{ __html: processedContent }}
          />

          {/* Newsletter CTA */}
          <Card className="mt-12 bg-gradient-to-r from-primary/5 to-secondary/5 border-primary/20">
            <CardContent className="p-8 text-center">
              <h3 className="text-xl font-semibold mb-2">Enjoyed this article?</h3>
              <p className="text-muted-foreground mb-6">
                Subscribe to our newsletter for more insightful content, academic tips, and exclusive updates.
              </p>
              <div className="max-w-md mx-auto">
                <NewsletterSubscription
                  source="blog-content-cta"
                  placeholder="Enter your email address"
                  buttonText="Subscribe Now"
                  variant="inline"
                  showIcon={true}
                  className="w-full"
                />
              </div>
            </CardContent>
          </Card>

          {/* Author Bio */}
          <div className="mt-12 p-6 bg-muted/30 rounded-lg">
            <h3 className="text-lg font-semibold mb-3">About the Author</h3>
            <div className="flex items-start space-x-4">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                <span className="text-xl font-bold text-primary">
                  {post.author.name.charAt(0).toUpperCase()}
                </span>
              </div>
              <div>
                <h4 className="font-medium text-foreground">
                  {post.author.name}
                </h4>
                <p className="text-sm text-muted-foreground mt-1">
                  {post.author.qualifications}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    
    </div>
  );
}
