import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { apiSuccess, apiError } from "@/lib/api-utils";

// GET /api/faqs - Get all active FAQs (public endpoint)
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const url = new URL(request.url);
    const category = url.searchParams.get("category");

    // Build where clause
    const where: { isActive: boolean; category?: string } = {
      isActive: true,
    };

    if (category) {
      where.category = category;
    }

    // Fetch FAQs
    const faqs = await prisma.fAQ.findMany({
      where,
      orderBy: [
        { order: "asc" },
        { createdAt: "asc" }
      ],
      select: {
        id: true,
        question: true,
        answer: true,
        category: true,
        order: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // Get unique categories for filtering
    const categories = await prisma.fAQ.findMany({
      where: { isActive: true },
      select: { category: true },
      distinct: ["category"],
    });

    const uniqueCategories = categories
      .map(c => c.category)
      .filter(Boolean)
      .sort();

    return apiSuccess({
      faqs,
      categories: uniqueCategories,
      total: faqs.length,
    });
  } catch (error) {
    console.error("Error fetching FAQs:", error);
    return apiError("Failed to fetch FAQs", 500);
  }
}
