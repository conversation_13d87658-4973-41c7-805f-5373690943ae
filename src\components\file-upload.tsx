import * as React from 'react';
import { useCallback, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { X, Upload, Download, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { useSupabaseFileUpload } from '@/hooks/useSupabaseFileUpload';
import { UploadedFile } from '@/types/upload';
import { toast } from 'sonner';

interface FileUploadProps {
  onFileUpload: (file: UploadedFile) => void;
  onFileRemove?: (fileId: string) => void;
  uploadedFiles?: UploadedFile[];
  className?: string;
  folder?: string;
  multiple?: boolean;
  maxFiles?: number;
  assignmentId?: string; // For assignment-specific uploads
}

interface UploadingFile {
  id: string;
  name: string;
  size: number;
  progress: number;
  status: 'uploading' | 'success' | 'error';
  error?: string;
}

export const FileUpload: React.FC<FileUploadProps> = ({
  onFileUpload,
  onFileRemove,
  uploadedFiles = [],
  className = '',
  folder = 'assignments',
  multiple = true,
  maxFiles = 10,
  assignmentId
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragActive, setDragActive] = useState(false);
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);

  const { uploadFile, isUploading, uploadProgress, error, resetError } = useSupabaseFileUpload();

  const handleFileUpload = useCallback(async (file: File) => {
    if (uploadedFiles.length + uploadingFiles.length >= maxFiles) {
      toast.error(`Maximum ${maxFiles} files allowed`);
      return;
    }

    const uploadId = `${Date.now()}-${Math.random()}`;

    // Add file to uploading state
    const uploadingFile: UploadingFile = {
      id: uploadId,
      name: file.name,
      size: file.size,
      progress: 0,
      status: 'uploading'
    };

    setUploadingFiles(prev => [...prev, uploadingFile]);

    try {
      resetError();

      let uploadedFile: UploadedFile;

      if (assignmentId) {
        // Use assignment-specific API with progress tracking
        const formData = new FormData();
        formData.append('file', file);
        formData.append('assignmentId', assignmentId);
        formData.append('folder', folder);

        // Update progress to show we're starting
        setUploadingFiles(prev =>
          prev.map(f => f.id === uploadId ? { ...f, progress: 10 } : f)
        );

        const response = await fetch('/api/files/upload', {
          method: 'POST',
          body: formData,
        });

        // Update progress during upload
        setUploadingFiles(prev =>
          prev.map(f => f.id === uploadId ? { ...f, progress: 80 } : f)
        );

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Upload failed');
        }

        // Complete progress
        setUploadingFiles(prev =>
          prev.map(f => f.id === uploadId ? { ...f, progress: 100, status: 'success' } : f)
        );

        uploadedFile = {
          id: data.data.id,
          name: data.data.name,
          url: data.data.url,
          size: data.data.size,
          type: data.data.type,
          uploadedAt: data.data.uploadedAt
        };
      } else {
        // Use direct Supabase upload
        setUploadingFiles(prev =>
          prev.map(f => f.id === uploadId ? { ...f, progress: 20 } : f)
        );

        const result = await uploadFile(file, folder, assignmentId);

        setUploadingFiles(prev =>
          prev.map(f => f.id === uploadId ? { ...f, progress: 100, status: 'success' } : f)
        );

        uploadedFile = {
          id: `${Date.now()}-${Math.random()}`,
          name: result.name,
          url: result.url,
          size: result.size,
          type: result.type,
          uploadedAt: new Date().toISOString()
        };
      }

      // Remove from uploading state and add to uploaded files
      setTimeout(() => {
        setUploadingFiles(prev => prev.filter(f => f.id !== uploadId));
        onFileUpload(uploadedFile);
        toast.success(`${file.name} uploaded successfully!`);
      }, 500); // Small delay to show completion

    } catch (uploadError) {
      const errorMessage = uploadError instanceof Error ? uploadError.message : 'Upload failed';

      // Update uploading file to show error
      setUploadingFiles(prev =>
        prev.map(f => f.id === uploadId ? {
          ...f,
          status: 'error',
          error: errorMessage
        } : f)
      );

      toast.error(errorMessage);

      // Remove failed upload after delay
      setTimeout(() => {
        setUploadingFiles(prev => prev.filter(f => f.id !== uploadId));
      }, 3000);
    }
  }, [uploadFile, onFileUpload, folder, resetError, uploadedFiles.length, uploadingFiles.length, maxFiles, assignmentId]);

  const handleFiles = useCallback(async (files: FileList) => {
    const fileArray = Array.from(files);
    
    for (const file of fileArray) {
      await handleFileUpload(file);
    }
  }, [handleFileUpload]);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  }, [handleFiles]);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    if (e.target.files && e.target.files[0]) {
      handleFiles(e.target.files);
    }
  }, [handleFiles]);

  const handleButtonClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const handleRemoveFile = useCallback((fileId: string) => {
    if (onFileRemove) {
      onFileRemove(fileId);
    }
  }, [onFileRemove]);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type: string) => {
    if (type.includes('pdf')) return '📄';
    if (type.includes('word') || type.includes('document')) return '📝';
    if (type.includes('presentation') || type.includes('powerpoint')) return '📊';
    if (type.includes('spreadsheet') || type.includes('excel')) return '📈';
    if (type.includes('text')) return '📃';
    return '📎';
  };

  return (
    <div className={`space-y-4 w-full max-w-full overflow-hidden ${className}`}>
      {/* Upload Status Summary */}
      {uploadingFiles.length > 0 && (
        <div className="p-3 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg w-full max-w-full">
          <div className="flex items-center space-x-2 w-full">
            <Loader2 className="h-4 w-4 text-blue-500 animate-spin flex-shrink-0" />
            <span className="text-sm font-medium text-blue-700 dark:text-blue-300 truncate">
              Uploading {uploadingFiles.length} file{uploadingFiles.length > 1 ? 's' : ''}...
            </span>
          </div>
          <div className="mt-2 w-full">
            <div className="flex justify-between text-xs text-blue-600 dark:text-blue-400 mb-1">
              <span>Overall Progress</span>
              <span className="flex-shrink-0">
                {Math.round(uploadingFiles.reduce((acc, file) => acc + file.progress, 0) / uploadingFiles.length)}%
              </span>
            </div>
            <Progress
              value={uploadingFiles.reduce((acc, file) => acc + file.progress, 0) / uploadingFiles.length}
              className="h-2 w-full"
            />
          </div>
        </div>
      )}

      {/* Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-4 sm:p-6 text-center transition-colors w-full max-w-full ${
          dragActive
            ? 'border-primary bg-primary/5'
            : 'border-border hover:border-primary/50'
        } ${(isUploading || uploadingFiles.length > 0) ? 'opacity-50 pointer-events-none' : ''}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple={multiple}
          onChange={handleChange}
          className="hidden"
          accept=".pdf,.doc,.docx,.ppt,.pptx,.txt,.rtf,.xls,.xlsx"
        />
        
        <Upload className="h-8 w-8 sm:h-12 sm:w-12 text-muted-foreground mx-auto mb-3 sm:mb-4" />
        <p className="text-base sm:text-lg font-medium text-foreground mb-2">
          Upload Assignment Files
        </p>
        <p className="text-sm text-muted-foreground mb-3 sm:mb-4 px-2">
          Drag and drop files here, or click to browse
        </p>
        
        <Button
          type="button"
          onClick={handleButtonClick}
          disabled={isUploading || uploadingFiles.length > 0}
          variant="outline"
        >
          {(isUploading || uploadingFiles.length > 0) ? 'Uploading...' : 'Choose Files'}
        </Button>
        
        <p className="text-xs text-muted-foreground mt-2 px-2 break-words">
          Supported: PDF, DOC, DOCX, PPT, PPTX, TXT, RTF, XLS, XLSX (Max 50MB each)
        </p>
        
        {(uploadedFiles.length > 0 || uploadingFiles.length > 0) && (
          <p className="text-xs text-muted-foreground">
            {uploadedFiles.length + uploadingFiles.length}/{maxFiles} files {uploadingFiles.length > 0 ? 'uploading/uploaded' : 'uploaded'}
          </p>
        )}
      </div>

      {/* Uploading Files Progress */}
      {uploadingFiles.length > 0 && (
        <div className="space-y-3 w-full">
          <h4 className="text-sm font-medium text-foreground">Uploading Files:</h4>
          <div className="space-y-3 w-full">
            {uploadingFiles.map((file) => (
              <div
                key={file.id}
                className="p-3 bg-muted/50 rounded-lg border w-full max-w-full"
              >
                <div className="flex items-start justify-between mb-2 gap-2 w-full">
                  <div className="flex items-start space-x-2 flex-1 min-w-0 overflow-hidden">
                    <div className="relative flex-shrink-0 mt-0.5">
                      {file.status === 'uploading' && (
                        <Loader2 className="h-4 w-4 text-primary animate-spin" />
                      )}
                      {file.status === 'success' && (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      )}
                      {file.status === 'error' && (
                        <AlertCircle className="h-4 w-4 text-destructive" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0 overflow-hidden">
                      <p className="text-sm font-medium text-foreground truncate w-full" title={file.name}>
                        {file.name}
                      </p>
                      <div className="text-xs text-muted-foreground">
                        <span>{formatFileSize(file.size)}</span>
                        {file.status === 'error' && file.error && (
                          <div className="text-destructive mt-1 break-words">
                            {file.error}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="text-xs text-muted-foreground flex-shrink-0 ml-2">
                    {file.progress}%
                  </div>
                </div>
                <Progress
                  value={file.progress}
                  className={`w-full h-2 ${
                    file.status === 'error' ? 'bg-destructive/20' : ''
                  }`}
                />
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Legacy Upload Progress (for backward compatibility) */}
      {isUploading && uploadingFiles.length === 0 && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Uploading...</span>
            <span>{uploadProgress}%</span>
          </div>
          <Progress value={uploadProgress} className="w-full" />
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-md">
          <p className="text-sm text-destructive">{error}</p>
        </div>
      )}

      {/* Uploaded Files List */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-3 w-full">
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
            <h4 className="text-sm font-medium text-foreground">Successfully Uploaded Files:</h4>
          </div>
          <div className="space-y-2 w-full">
            {uploadedFiles.map((file) => (
              <div
                key={file.id}
                className="flex items-start justify-between p-3 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg w-full max-w-full gap-2"
              >
                <div className="flex items-start space-x-2 flex-1 min-w-0 overflow-hidden">
                  <span className="text-base flex-shrink-0 mt-0.5">{getFileIcon(file.type)}</span>
                  <div className="flex-1 min-w-0 overflow-hidden">
                    <p className="text-sm font-medium text-foreground truncate w-full" title={file.name}>
                      {file.name}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {formatFileSize(file.size)} • {new Date(file.uploadedAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-1 flex-shrink-0">
                  <Button
                    type="button"
                    size="sm"
                    variant="outline"
                    onClick={() => window.open(file.url, '_blank')}
                    className="h-7 w-7 p-0"
                    title="Download file"
                  >
                    <Download className="h-3 w-3" />
                  </Button>
                  {onFileRemove && (
                    <Button
                      type="button"
                      size="sm"
                      variant="outline"
                      onClick={() => handleRemoveFile(file.id)}
                      className="h-7 w-7 p-0 text-destructive hover:text-destructive"
                      title="Remove file"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
