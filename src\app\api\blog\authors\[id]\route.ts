import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { z } from "zod";
import { checkPermission, apiSuccess, apiError } from "@/lib/api-utils";

// Validation schema for author
const AuthorSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").max(100, "Name must be less than 100 characters"),
  qualifications: z.string().min(10, "Qualifications must be at least 10 characters").max(500, "Qualifications must be less than 500 characters"),
});

export async function GET(
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    const author = await prisma.author.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            blogs: true,
          },
        },
      },
    });

    if (!author) {
      return apiError("Author not found", 404);
    }

    return NextResponse.json(author);
  } catch (error) {
    console.error("Error fetching author:", error);
    return apiError("Failed to fetch author", 500);
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check admin permission
    const permissionError = await checkPermission(["ADMIN"]);
    if (permissionError) return permissionError;

    const { id } = await params;
    const body = await req.json();
    const parsed = AuthorSchema.safeParse(body);
    
    if (!parsed.success) {
      return apiError("Invalid input data", 400, parsed.error.flatten().fieldErrors);
    }

    const { name, qualifications } = parsed.data;

    // Check if author exists
    const existingAuthor = await prisma.author.findUnique({
      where: { id },
    });

    if (!existingAuthor) {
      return apiError("Author not found", 404);
    }

    // Check if another author with same name exists (excluding current author)
    const duplicateAuthor = await prisma.author.findFirst({
      where: {
        name,
        id: { not: id },
      },
    });

    if (duplicateAuthor) {
      return apiError("Author with this name already exists", 409);
    }

    const updatedAuthor = await prisma.author.update({
      where: { id },
      data: {
        name,
        qualifications,
      },
    });

    return apiSuccess(updatedAuthor, "Author updated successfully");
  } catch (error) {
    console.error("Error updating author:", error);
    return apiError("Failed to update author", 500);
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check admin permission
    const permissionError = await checkPermission(["ADMIN"]);
    if (permissionError) return permissionError;

    const { id } = await params;

    // Check if author exists
    const existingAuthor = await prisma.author.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            blogs: true,
          },
        },
      },
    });

    if (!existingAuthor) {
      return apiError("Author not found", 404);
    }

    // Check if author has associated blogs
    if (existingAuthor._count.blogs > 0) {
      return apiError(
        `Cannot delete author. This author has ${existingAuthor._count.blogs} associated blog post(s). Please reassign or delete the blog posts first.`,
        409
      );
    }

    await prisma.author.delete({
      where: { id },
    });

    return apiSuccess(null, "Author deleted successfully");
  } catch (error) {
    console.error("Error deleting author:", error);
    return apiError("Failed to delete author", 500);
  }
}
