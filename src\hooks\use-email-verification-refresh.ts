"use client";

import { useEffect } from "react";
import { useSession } from "next-auth/react";
import { useSearchParams, useRouter } from "next/navigation";

/**
 * Hook to handle session refresh after email verification
 * Checks for emailVerified query parameter and refreshes session if needed
 */
export function useEmailVerificationRefresh() {
  const { data: session, update: updateSession } = useSession();
  const searchParams = useSearchParams();
  const router = useRouter();

  useEffect(() => {
    const handleEmailVerificationRefresh = async () => {
      // Check if we have the emailVerified query parameter
      const emailVerified = searchParams.get('emailVerified');
      
      if (emailVerified === 'true') {
        console.log("🔄 Email verification detected, refreshing session...");
        
        try {
          // Force session refresh by calling the refresh endpoint
          const refreshResponse = await fetch('/api/auth/refresh-session', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
          });

          if (refreshResponse.ok) {
            const refreshData = await refreshResponse.json();
            console.log("✅ Session refresh successful:", refreshData.user);
            
            // Update the session with fresh data
            await updateSession();
            
            // Remove the query parameter from URL
            const newUrl = new URL(window.location.href);
            newUrl.searchParams.delete('emailVerified');
            router.replace(newUrl.pathname + newUrl.search, { scroll: false });
            
            console.log("🎉 Email verification refresh complete!");
          } else {
            console.error("❌ Failed to refresh session after email verification");
          }
        } catch (error) {
          console.error("❌ Error refreshing session after email verification:", error);
        }
      }
    };

    // Only run if we have a session and the emailVerified parameter
    if (session && searchParams.get('emailVerified') === 'true') {
      handleEmailVerificationRefresh();
    }
  }, [session, searchParams, updateSession, router]);

  return {
    isRefreshing: searchParams.get('emailVerified') === 'true',
  };
}
