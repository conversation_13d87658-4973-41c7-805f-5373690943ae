"use client"
import React, { useState, useEffect } from "react";
import <PERSON> from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Star,
  Clock,
  Shield,
  Users,
  CheckCircle,
  ArrowRight,
  BookOpen,
  GraduationCap,
  Award,
  Globe,
  Target,
} from "lucide-react";


export const ModernHeroSection = () => {
  const [currentService, setCurrentService] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  const services = [
    "Essays & Research Papers",
    "Thesis & Dissertations",
    "Case Studies & Reports",
    "Literature Reviews",
    "Business Plans",
  ];

  const stats = [
    {
      icon: Star,
      value: "4.9/5",
      label: "Average Rating",
      color: "text-yellow-500",
    },
    {
      icon: Clock,
      value: "3hrs",
      label: "Fastest Delivery",
      color: "text-blue-500",
    },
    {
      icon: Users,
      value: "150K+",
      label: "Happy Students",
      color: "text-green-500",
    },
    {
      icon: Award,
      value: "98%",
      label: "Success Rate",
      color: "text-purple-500",
    },
  ];

  const features = [
    { icon: CheckCircle, text: "100% Original & Plagiarism-Free Content" },
    { icon: Shield, text: "Confidential & Secure Service" },
    { icon: Clock, text: "On-Time Delivery Guaranteed" },
    { icon: Users, text: "PhD-Qualified Expert Writers" },
  ];

  const serviceTypes = [
    { name: "Essay", popular: true },
    { name: "Research Paper", popular: false },
    { name: "Case Study", popular: false },
    { name: "Literature Review", popular: true },
    { name: "Business Plan", popular: false },
    { name: "Thesis", popular: true },
    { name: "Dissertation", popular: false },
    { name: "Book Review", popular: false },
  ];

  useEffect(() => {
    setIsVisible(true);
    const interval = setInterval(() => {
      setCurrentService((prev) => (prev + 1) % services.length);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  return (
    <section
      id="hero"
      className="relative min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 text-white overflow-hidden"
      aria-labelledby="hero-heading"
    >
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-radial from-indigo-500/5 to-transparent rounded-full"></div>
      </div>

      {/* Hero Image Overlay */}
      <div
        className="absolute inset-0 opacity-10 bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: "url('/assets/blog.jpg')" }}
        aria-hidden="true"
      ></div>

      <div className="container mx-auto px-2 sm:px-4 md:px-6 pt-8 pb-8 md:pt-12 md:pb-16 lg:pt-16 lg:pb-20 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-12 items-center min-h-[70vh]">
          {/* Left Content */}
          <div className="lg:col-span-7 space-y-8">
            {/* Trust Badge */}
            <div
              className={`transform transition-all duration-1000 ${isVisible ? "translate-y-0 opacity-100" : "translate-y-10 opacity-0"}`}
            >
              <Badge
                variant="outline"
                className="border-white/20 backdrop-blur-sm text-white px-6 py-2 text-sm font-medium bg-white/10 hover:bg-white/20 transition-colors"
              >
                <Award className="w-4 h-4 mr-2" />
                #1 Academic Writing Platform • Trusted by 150K+ Students
              </Badge>
            </div>

            {/* Main Headline */}
            <div
              className={`space-y-4 transform transition-all duration-1000 delay-200 ${isVisible ? "translate-y-0 opacity-100" : "translate-y-10 opacity-0"}`}
            >
              <h1
                id="hero-heading"
                className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold leading-tight"
              >
                Get Expert Help with{" "}
                <span className="relative">
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-amber-400 via-orange-400 to-yellow-400 animate-pulse">
                    {services[currentService]}
                  </span>
                  <div className="absolute -bottom-2 left-0 w-full h-1 bg-gradient-to-r from-amber-400 to-yellow-400 rounded-full transform scale-x-0 animate-pulse"></div>
                </span>
              </h1>

              <p className="text-xl md:text-2xl text-gray-200 leading-relaxed max-w-2xl">
                Connect with qualified academic writers who deliver original,
                high-quality work tailored to your requirements.
                <span className="text-amber-300 font-semibold">
                  {" "}
                  Guaranteed results in as fast as 3 hours.
                </span>
              </p>
            </div>

            {/* Key Features */}
            <div
              className={`space-y-4 transform transition-all duration-1000 delay-400 ${isVisible ? "translate-y-0 opacity-100" : "translate-y-10 opacity-0"}`}
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {features.map((feature, index) => {
                  const Icon = feature.icon;
                  return (
                    <div
                      key={index}
                      className="flex items-center space-x-3 text-gray-200"
                    >
                      <Icon className="w-5 h-5 text-green-400 flex-shrink-0" />
                      <span className="text-sm md:text-base">
                        {feature.text}
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* CTA Buttons */}
            <div
              className={`flex flex-col sm:flex-row gap-4 pt-6 transform transition-all duration-1000 delay-600 ${isVisible ? "translate-y-0 opacity-100" : "translate-y-10 opacity-0"}`}
            >
              <Link href="/create-order">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-black font-bold py-4 px-8 text-lg shadow-2xl hover:shadow-amber-500/25 transition-all duration-300 hover:scale-105"
                >
                  <Target className="w-5 h-5 mr-2" />
                  Find Your Writer
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </Link>


            </div>

            {/* Stats Row */}
            <div
              className={`grid grid-cols-2 md:grid-cols-4 gap-6 pt-8 transform transition-all duration-1000 delay-800 ${isVisible ? "translate-y-0 opacity-100" : "translate-y-10 opacity-0"}`}
            >
              {stats.map((stat, index) => {
                const Icon = stat.icon;
                return (
                  <div key={index} className="text-center">
                    <Icon className={`w-8 h-8 mx-auto mb-2 ${stat.color}`} />
                    <div className="text-2xl md:text-3xl font-bold">
                      {stat.value}
                    </div>
                    <div className="text-xs md:text-sm text-gray-400">
                      {stat.label}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Right Content - Interactive Card */}
          <div className="lg:col-span-5">
            <div
              className={`transform transition-all duration-1000 delay-1000 ${isVisible ? "translate-y-0 opacity-100" : "translate-y-10 opacity-0"}`}
            >
              <Card className="bg-white/10 backdrop-blur-xl border-white/20 p-8 rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-500 hover:bg-white/15">
                <CardContent className="p-0 space-y-6">
                  {/* Card Header */}
                  <div className="text-center space-y-2">
                    <div className="flex items-center justify-center space-x-2 mb-4">
                      <GraduationCap className="w-6 h-6 text-amber-400" />
                      <h3 className="text-2xl font-bold">Start Your Order</h3>
                      <Badge className="bg-green-500 hover:bg-green-600 animate-pulse">
                        24/7 Available
                      </Badge>
                    </div>
                    <p className="text-gray-300">
                      Get matched with the perfect writer for your project
                    </p>
                  </div>

                  <Separator className="bg-white/20" />

                  {/* Service Types */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold flex items-center">
                      <BookOpen className="w-5 h-5 mr-2 text-blue-400" />
                      Popular Services
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                      {serviceTypes.slice(0, 6).map((service, index) => (
                        <Button
                          key={index}
                          variant="ghost"
                          size="sm"
                          className={`justify-start text-left h-auto py-2 px-3 ${
                            service.popular
                              ? "bg-amber-500/20 text-amber-300 hover:bg-amber-500/30"
                              : "bg-white/5 text-gray-300 hover:bg-white/10"
                          } transition-all duration-200`}
                        >
                          {service.name}
                          {service.popular && (
                            <Star className="w-3 h-3 ml-auto text-amber-400" />
                          )}
                        </Button>
                      ))}
                    </div>
                  </div>

                  <Separator className="bg-white/20" />

                  {/* Quick Stats */}
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div className="space-y-1">
                      <div className="text-2xl font-bold text-green-400">
                        3hrs
                      </div>
                      <div className="text-xs text-gray-400">Min Deadline</div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-2xl font-bold text-blue-400">
                        750+
                      </div>
                      <div className="text-xs text-gray-400">
                        Expert Writers
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-2xl font-bold text-purple-400">
                        $12
                      </div>
                      <div className="text-xs text-gray-400">
                        Starting Price
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="space-y-3">
                    <Link href="/create-order">
                      <Button className="w-full bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-black font-bold py-3 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                        <Globe className="w-5 h-5 mr-2" />
                        Get Started Now
                      </Button>
                    </Link>

                    <Link href="/services">
                      <Button
                        variant="outline"
                        className="w-full border-white/30 hover:bg-white/10 text-white backdrop-blur-sm"
                      >
                        View Sample Work
                      </Button>
                    </Link>
                  </div>

                  {/* Trust Indicators */}
                  <div className="flex justify-center space-x-4 pt-4 text-sm text-gray-400">
                    <div className="flex items-center">
                      <Shield className="w-4 h-4 mr-1 text-green-400" />
                      SSL Secured
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="w-4 h-4 mr-1 text-blue-400" />
                      Guaranteed
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Trust Bar */}
      <div className="absolute bottom-0 left-0 right-0 bg-white/5 backdrop-blur-sm border-t border-white/10">
        <div className="container mx-auto px-2 sm:px-4 md:px-6 py-4">
          <div className="flex flex-wrap justify-center md:justify-between items-center gap-4 text-sm text-gray-300">
            <div className="flex items-center space-x-6">
              <Badge
                variant="outline"
                className="border-white/20 text-white/90 bg-white/5"
              >
                <Award className="w-3 h-3 mr-1" />
                ISO 27001 Certified
              </Badge>
              <Badge
                variant="outline"
                className="border-white/20 text-white/90 bg-white/5"
              >
                <Shield className="w-3 h-3 mr-1" />
                GDPR Compliant
              </Badge>
              <Badge
                variant="outline"
                className="border-white/20 text-white/90 bg-white/5"
              >
                <CheckCircle className="w-3 h-3 mr-1" />
                Money-Back Guarantee
              </Badge>
            </div>
            <div className="flex items-center text-gray-400">
              <Globe className="w-4 h-4 mr-2" />
              Serving students in 150+ countries worldwide
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ModernHeroSection;
