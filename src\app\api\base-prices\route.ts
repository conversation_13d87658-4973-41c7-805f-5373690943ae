import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { apiSuccess, apiError, checkPermission } from "@/lib/api-utils";
import { AcademicLevel } from "@prisma/client";

// Get all base prices
export async function GET(): Promise<NextResponse> {
  try {
    const basePrices = await prisma.basePrice.findMany();
    return apiSuccess(basePrices);
  } catch (error) {
    console.error("Error fetching base prices:", error);
    return apiError("Failed to fetch base prices", 500);
  }
}

// Update base price for an academic level
export async function PUT(req: NextRequest): Promise<NextResponse> {
  try {
    // Only admins can update base prices
    const permissionError = await checkPermission(["ADMIN"]);
    if (permissionError) return permissionError;

    const data = await req.json();
    const { academicLevel, price } = data;

    if (!academicLevel || typeof price !== "number" || price < 0) {
      return apiError("Invalid input data", 400);
    }

    const updatedPrice = await prisma.basePrice.upsert({
      where: { academicLevel: academicLevel as AcademicLevel },
      create: {
        academicLevel: academicLevel as AcademicLevel,
        price,
      },
      update: { price },
    });

    return apiSuccess(updatedPrice, "Base price updated successfully");
  } catch (error) {
    console.error("Error updating base price:", error);
    return apiError("Failed to update base price", 500);
  }
}
