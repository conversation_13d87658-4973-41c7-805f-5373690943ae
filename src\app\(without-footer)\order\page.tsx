"use client";

import { useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";

const OrdersPage = () => {
  const router = useRouter();
  const { data: session, status } = useSession();

  useEffect(() => {
    if (status !== "authenticated") return;

    const role = session?.user?.role;

    switch (role) {
      case "ADMIN":
        router.replace("/admin/orders");
        break;
      case "WRITER":
        router.replace("/writer/dashboard");
        break;
      case "CLIENT":
        router.replace("/client/dashboard");
        break;
      default:
        router.replace("/auth/unauthorized"); // Optional fallback
    }
  }, [status, session, router]);

  // //Only show loading screen if session is still being fetched
  // if (status === "loading") {
  //   return (
  //     <div className="h-screen flex items-center justify-center">
  //       <p className="text-lg text-muted-foreground">Redirecting...</p>
  //     </div>
  //   );
  // }

  if (status === "loading") {
    return (
      <div className="h-screen flex items-center justify-center bg-muted">
        <div className="animate-fade-in p-6 rounded-2xl shadow-xl bg-white border border-border w-[90%] max-w-md text-center">
          <div className="flex justify-center mb-4">
            <div className="w-10 h-10 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />
          </div>
          <h2 className="text-xl font-semibold text-foreground mb-1">
            Redirecting to your dashboard...
          </h2>
          <p className="text-sm text-muted-foreground">
            Please wait while we log you in based on your role.
          </p>
        </div>
      </div>
    );
  }
  // No UI is rendered if redirect has been triggered
  return null;
};

export default OrdersPage;
