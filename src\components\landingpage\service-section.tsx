// components/ServicesSection.tsx
export const ServicesSection = () => {
  const services = [
    {
      title: "Essay Writing",
      description: "Let our writers create an essay for you",
      icon: "📝",
    },
    {
      title: "Homework Help",
      description: "Get help with your homework from our expert tutors",
      icon: "📚",
    },
    {
      title: "Research Papers",
      description: "We'll help you write a research paper with ease",
      icon: "🔍",
    },
  ];

  return (
    <section className="py-16 px-4 bg-[oklch(var(--background))]">
      <div className="max-w-6xl mx-auto">
        <h2 className="text-3xl font-bold mb-12 text-center">Our Services</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <div
              key={index}
              className="p-6 rounded-xl bg-[oklch(var(--card))] border border-[oklch(var(--border))]"
            >
              <div className="text-4xl mb-4">{service.icon}</div>
              <h3 className="text-xl font-semibold mb-2">{service.title}</h3>
              <p className="text-[oklch(var(--muted-foreground))]">
                {service.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
