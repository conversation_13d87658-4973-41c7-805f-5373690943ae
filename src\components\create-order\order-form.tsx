"use client";

import { useState, useEffect, use<PERSON>emo, useRef } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import { CalendarIcon, Upload, AlertCircle, CheckCircle2, Loader2 } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tabs,
  Tabs<PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

import { FileUpload } from "@/components/file-upload";
import { UploadedFile } from "@/types/upload";
import { CouponInput } from "@/components/ui/coupon-input";

import {
  createOrderSchema,
  type CreateOrderForm,
  assignmentTypeOptions,
  priorityOptions,
  academicLevelOptions,
  spacingOptions,
  languageStyleOptions,
  formatStyleOptions,
} from "@/types/order";
import { useOrderSessionStorage } from "@/lib/order-session-storage";
import { usePriceCalculation } from "@/hooks/use-pricing-realtime";
import { AcademicLevel, Priority, Spacing } from "@prisma/client";

interface OrderFormProps {
  onPriceChange?: (price: number) => void;
  onFormDataChange?: (data: Partial<CreateOrderForm>) => void;
  className?: string;
}

export function OrderForm({ onPriceChange, onFormDataChange, className = "" }: OrderFormProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { saveOrder } = useOrderSessionStorage();
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [activeTab, setActiveTab] = useState("basic");
  const [calculatedPrice, setCalculatedPrice] = useState(0);
  const [finalPrice, setFinalPrice] = useState(0);
  const [appliedCoupon, setAppliedCoupon] = useState<{
    code: string;
    discountAmount: number;
  } | null>(null);

  // Refs to prevent infinite loops
  const lastPriceRef = useRef<number>(0);
  const lastFormDataRef = useRef<string>("");

  const form = useForm<CreateOrderForm>({
    resolver: zodResolver(createOrderSchema),
    defaultValues: {
      assignmentType: "ESSAY",
      priority: "MEDIUM",
      academicLevel: "UNDERGRADUATE",
      spacing: "DOUBLE",
      languageStyle: "ENGLISH_US",
      formatStyle: "APA",
      numSources: 0,
      pageCount: 1,
      service: "Academic Writing",
      title: "",
      description: "",
      subject: "",
      guidelines: "",
    },
    mode: "onChange",
  });

  const watchedValues = form.watch();

  // Use real-time pricing hook
  const { priceBreakdown } = usePriceCalculation({
    academicLevel: (watchedValues.academicLevel as AcademicLevel) || AcademicLevel.UNDERGRADUATE,
    priority: (watchedValues.priority as Priority) || Priority.MEDIUM,
    spacing: (watchedValues.spacing as Spacing) || Spacing.DOUBLE,
    pageCount: watchedValues.pageCount || 1,
  });

  // Calculate final price from breakdown
  const calculatedPriceMemo = useMemo(() => {
    if (!watchedValues.pageCount || watchedValues.pageCount <= 0) {
      return 0;
    }
    return priceBreakdown?.finalPrice || 0;
  }, [priceBreakdown, watchedValues.pageCount]);

  // Update calculated price when memo changes
  useEffect(() => {
    if (calculatedPrice !== calculatedPriceMemo && lastPriceRef.current !== calculatedPriceMemo) {
      setCalculatedPrice(calculatedPriceMemo);
      lastPriceRef.current = calculatedPriceMemo;

      // Calculate final price with coupon discount
      const newFinalPrice = appliedCoupon
        ? calculatedPriceMemo - appliedCoupon.discountAmount
        : calculatedPriceMemo;
      setFinalPrice(newFinalPrice);
      onPriceChange?.(newFinalPrice);
    }
  }, [calculatedPriceMemo, calculatedPrice, appliedCoupon, onPriceChange]);

  // Notify parent of form data changes when specific fields change
  useEffect(() => {
    const formDataString = JSON.stringify(watchedValues);
    if (lastFormDataRef.current !== formDataString) {
      lastFormDataRef.current = formDataString;
      onFormDataChange?.(watchedValues);
    }
  }, [
    watchedValues.title,
    watchedValues.description,
    watchedValues.assignmentType,
    watchedValues.subject,
    watchedValues.pageCount,
    watchedValues.academicLevel,
    watchedValues.priority,
    watchedValues.spacing,
    watchedValues.languageStyle,
    watchedValues.formatStyle,
    watchedValues.numSources,
    watchedValues.guidelines,
    watchedValues.estTime,
    watchedValues.service,
    onFormDataChange,
    watchedValues
  ]);

  const handleFileUpload = (file: UploadedFile) => {
    setUploadedFiles((prev) => [...prev, file]);
    toast.success(`File "${file.name}" uploaded successfully`);
  };

  const handleFileRemove = (fileId: string) => {
    setUploadedFiles((prev) => prev.filter((file) => file.id !== fileId));
    toast.info("File removed");
  };

  const handleCouponApplied = (discountAmount: number, finalPrice: number, couponCode: string) => {
    setAppliedCoupon({ code: couponCode, discountAmount });
    setFinalPrice(finalPrice);
    onPriceChange?.(finalPrice);
    toast.success(`Coupon ${couponCode} applied! You saved $${discountAmount.toFixed(2)}`);
  };

  const handleCouponRemoved = () => {
    setAppliedCoupon(null);
    setFinalPrice(calculatedPrice);
    onPriceChange?.(calculatedPrice);
    toast.info("Coupon removed");
  };

  const generateTaskId = () => {
    const now = new Date();
    const day = now.getDate().toString().padStart(2, "0");
    const month = (now.getMonth() + 1).toString().padStart(2, "0");
    const year = now.getFullYear().toString().slice(-2);
    const randomStr = Math.floor(Math.random() * 1000)
      .toString()
      .padStart(3, "0");
    return `TASK-${day}${month}${year}${randomStr}`;
  };

  const onSubmit = async (data: CreateOrderForm) => {
    setIsSubmitting(true);

    try {
      // Check if user is authenticated
      if (status === "loading") {
        toast.error("Please wait while we check your authentication status");
        return;
      }

      if (session?.user) {
        // User is authenticated - create assignment directly
        await createAssignmentForAuthenticatedUser(data);
      } else {
        // User is not authenticated - save to session storage and redirect to registration
        await saveOrderAndRedirectToRegistration(data);
      }
    } catch (error) {
      console.error("Error submitting order:", error);
      toast.error(
        `Error submitting order: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const createAssignmentForAuthenticatedUser = async (data: CreateOrderForm) => {
    const taskId = generateTaskId();

    const submitData = {
      ...data,
      taskId,
      clientId: session!.user.id,
      estTime: data.estTime.toISOString(),
      // Include coupon data if applied
      ...(appliedCoupon && {
        couponCode: appliedCoupon.code,
        originalPrice: calculatedPrice,
        discountAmount: appliedCoupon.discountAmount,
      }),
    };

    // Step 1: Create the assignment
    const response = await fetch("/api/assignments", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(submitData),
    });

    const responseData = await response.json();

    if (!response.ok) {
      throw new Error(
        responseData.message ||
          `Failed to create assignment (${response.status})`
      );
    }

    const assignmentId = responseData.data?.id;

    // Step 2: Upload files and link them to the assignment
    if (uploadedFiles.length > 0 && assignmentId) {
      await linkFilesToAssignment(assignmentId);
    }

    toast.success("Assignment created successfully!");
    router.push("/client/dashboard");
  };

  const saveOrderAndRedirectToRegistration = async (data: CreateOrderForm) => {
    // Save order data to session storage
    const orderSessionData = {
      ...data,
      uploadedFiles: uploadedFiles.map(file => ({
        id: file.id,
        name: file.name,
        url: file.url,
        size: file.size,
        type: file.type,
      })),
      calculatedPrice,
      timestamp: Date.now(),
    };

    saveOrder(orderSessionData);

    toast.success("Order details saved! Please create an account to continue.");
    router.push("/register/client?from=create-order");
  };

  const linkFilesToAssignment = async (assignmentId: string) => {
    for (const file of uploadedFiles) {
      try {
        const fileData = {
          assignmentId,
          fileName: file.name,
          originalName: file.name,
          fileUrl: file.url,
          fileSize: file.size,
          fileType: file.type
        };

        const fileResponse = await fetch("/api/files/link", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(fileData),
        });

        if (!fileResponse.ok) {
          console.error(`Failed to link file ${file.name} to assignment`);
        }
      } catch (fileError) {
        console.error(`Error linking file ${file.name}:`, fileError);
      }
    }
  };

  const validateCurrentTab = () => {
    const errors = form.formState.errors;

    switch (activeTab) {
      case "basic":
        return !errors.title && !errors.description && !errors.assignmentType && !errors.subject;
      case "details":
        return !errors.pageCount && !errors.academicLevel && !errors.estTime;
      case "requirements":
        return true; // Optional fields
      default:
        return true;
    }
  };

  const getTabStatus = (tabName: string) => {
    const errors = form.formState.errors;

    switch (tabName) {
      case "basic":
        return !errors.title && !errors.description && !errors.assignmentType && !errors.subject;
      case "details":
        return !errors.pageCount && !errors.academicLevel && !errors.estTime;
      case "requirements":
        return true;
      default:
        return true;
    }
  };

  const nextTab = () => {
    if (!validateCurrentTab()) {
      toast.error("Please fill in all required fields before proceeding");
      return;
    }

    const tabs = ["basic", "details", "requirements"];
    const currentIndex = tabs.indexOf(activeTab);
    if (currentIndex < tabs.length - 1) {
      setActiveTab(tabs[currentIndex + 1]);
    }
  };

  const prevTab = () => {
    const tabs = ["basic", "details", "requirements"];
    const currentIndex = tabs.indexOf(activeTab);
    if (currentIndex > 0) {
      setActiveTab(tabs[currentIndex - 1]);
    }
  };

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2">
          <Upload className="w-5 h-5 text-primary" />
          Create Your Order
        </CardTitle>
        <CardDescription>
          Fill out the form below with your assignment details. All required fields are marked with an asterisk (*).
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-3 bg-muted p-1 rounded-xl dark:bg-muted/50">
                <TabsTrigger
                  value="basic"
                  className="flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300 data-[state=active]:shadow-sm"
                  disabled={isSubmitting}
                >
                  {getTabStatus("basic") ? (
                    <CheckCircle2 className="w-4 h-4" />
                  ) : (
                    <AlertCircle className="w-4 h-4" />
                  )}
                  Basic Info
                </TabsTrigger>
                <TabsTrigger
                  value="details"
                  className="flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300 data-[state=active]:shadow-sm"
                  disabled={isSubmitting}
                >
                  {getTabStatus("details") ? (
                    <CheckCircle2 className="w-4 h-4" />
                  ) : (
                    <AlertCircle className="w-4 h-4" />
                  )}
                  Details
                </TabsTrigger>
                <TabsTrigger
                  value="requirements"
                  className="flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300 data-[state=active]:shadow-sm"
                  disabled={isSubmitting}
                >
                  <CheckCircle2 className="w-4 h-4" />
                  Requirements
                </TabsTrigger>
              </TabsList>

              <AnimatePresence mode="wait">
                <motion.div
                  key={activeTab}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  {/* Basic Information Tab */}
                  <TabsContent value="basic" className="space-y-6 mt-6">
                    <div className="grid gap-6">
                      <FormField
                        control={form.control}
                        name="title"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="flex items-center gap-1">
                              Assignment Title *
                              <Badge variant="secondary" className="text-xs">Required</Badge>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter a clear, descriptive title for your assignment"
                                {...field}
                                className="transition-all duration-200 focus:ring-2 focus:ring-primary/20"
                              />
                            </FormControl>
                            <FormDescription>
                              Provide a clear title that describes your assignment topic
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="grid md:grid-cols-2 gap-6">
                        <FormField
                          control={form.control}
                          name="assignmentType"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="flex items-center gap-1">
                                Assignment Type *
                                <Badge variant="secondary" className="text-xs">Required</Badge>
                              </FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger className="transition-all duration-200 focus:ring-2 focus:ring-primary/20">
                                    <SelectValue placeholder="Select assignment type" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {assignmentTypeOptions.map((option) => (
                                    <SelectItem key={option.value} value={option.value}>
                                      <div className="flex flex-col">
                                        <span>{option.label}</span>
                                        <span className="text-xs text-muted-foreground">
                                          {option.description}
                                        </span>
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="subject"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="flex items-center gap-1">
                                Subject *
                                <Badge variant="secondary" className="text-xs">Required</Badge>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="e.g., Psychology, Mathematics, History"
                                  {...field}
                                  className="transition-all duration-200 focus:ring-2 focus:ring-primary/20"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name="description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="flex items-center gap-1">
                              Assignment Description *
                              <Badge variant="secondary" className="text-xs">Required</Badge>
                            </FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Provide a detailed description of your assignment requirements, including key points to cover, specific instructions, and any important details..."
                                rows={6}
                                {...field}
                                className="transition-all duration-200 focus:ring-2 focus:ring-primary/20 resize-none"
                              />
                            </FormControl>
                            <FormDescription>
                              Minimum 10 characters. Be as detailed as possible to help our writers understand your needs.
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </TabsContent>

                  {/* Details Tab */}
                  <TabsContent value="details" className="space-y-6 mt-6">
                    <div className="grid gap-6">
                      <div className="grid md:grid-cols-3 gap-4">
                        <FormField
                          control={form.control}
                          name="pageCount"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="flex items-center gap-1">
                                Page Count *
                                <Badge variant="secondary" className="text-xs">Required</Badge>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min={1}
                                  max={500}
                                  {...field}
                                  onChange={(e) => field.onChange(Number(e.target.value))}
                                  className="transition-all duration-200 focus:ring-2 focus:ring-primary/20"
                                />
                              </FormControl>
                              <FormDescription>
                                275 words per page
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="numSources"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Number of Sources</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min={0}
                                  max={200}
                                  {...field}
                                  onChange={(e) => field.onChange(Number(e.target.value))}
                                  className="transition-all duration-200 focus:ring-2 focus:ring-primary/20"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="priority"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Priority Level</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger className="transition-all duration-200 focus:ring-2 focus:ring-primary/20">
                                    <SelectValue placeholder="Select priority" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {priorityOptions.map((option) => (
                                    <SelectItem key={option.value} value={option.value}>
                                      <div className="flex items-center gap-2">
                                        <Badge className={option.color} variant="secondary">
                                          {option.label}
                                        </Badge>
                                        <span className="text-sm">{option.description}</span>
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="grid md:grid-cols-2 gap-6">
                        <FormField
                          control={form.control}
                          name="academicLevel"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="flex items-center gap-1">
                                Academic Level *
                                <Badge variant="secondary" className="text-xs">Required</Badge>
                              </FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger className="transition-all duration-200 focus:ring-2 focus:ring-primary/20">
                                    <SelectValue placeholder="Select academic level" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {academicLevelOptions.map((option) => (
                                    <SelectItem key={option.value} value={option.value}>
                                      <div className="flex flex-col">
                                        <span>{option.label}</span>
                                        <span className="text-xs text-muted-foreground">
                                          {option.description}
                                        </span>
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="spacing"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Spacing</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger className="transition-all duration-200 focus:ring-2 focus:ring-primary/20">
                                    <SelectValue placeholder="Select spacing" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {spacingOptions.map((option) => (
                                    <SelectItem key={option.value} value={option.value}>
                                      <div className="flex items-center justify-between w-full">
                                        <span>{option.label}</span>
                                        {option.multiplier < 1 && (
                                          <Badge variant="secondary" className="bg-green-100 text-green-800">
                                            50% OFF
                                          </Badge>
                                        )}
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="grid md:grid-cols-2 gap-6">
                        <FormField
                          control={form.control}
                          name="languageStyle"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Language Style</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger className="transition-all duration-200 focus:ring-2 focus:ring-primary/20">
                                    <SelectValue placeholder="Select language style" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {languageStyleOptions.map((option) => (
                                    <SelectItem key={option.value} value={option.value}>
                                      {option.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="formatStyle"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Citation Format</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger className="transition-all duration-200 focus:ring-2 focus:ring-primary/20">
                                    <SelectValue placeholder="Select format style" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {formatStyleOptions.map((option) => (
                                    <SelectItem key={option.value} value={option.value}>
                                      {option.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name="estTime"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <FormLabel className="flex items-center gap-1">
                              Deadline *
                              <Badge variant="secondary" className="text-xs">Required</Badge>
                            </FormLabel>
                            <Popover>
                              <PopoverTrigger asChild>
                                <FormControl>
                                  <Button
                                    variant="outline"
                                    className={cn(
                                      "w-full pl-3 text-left font-normal transition-all duration-200 focus:ring-2 focus:ring-primary/20",
                                      !field.value && "text-muted-foreground"
                                    )}
                                  >
                                    {field.value ? (
                                      format(field.value, "PPP")
                                    ) : (
                                      <span>Pick a deadline</span>
                                    )}
                                    <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                  </Button>
                                </FormControl>
                              </PopoverTrigger>
                              <PopoverContent className="w-auto p-0" align="start">
                                <Calendar
                                  mode="single"
                                  selected={field.value}
                                  onSelect={field.onChange}
                                  disabled={(date) =>
                                    date < new Date() || date < new Date("1900-01-01")
                                  }
                                  autoFocus
                                />
                              </PopoverContent>
                            </Popover>
                            <FormDescription>
                              When do you need this assignment completed?
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </TabsContent>

                  {/* Requirements Tab */}
                  <TabsContent value="requirements" className="space-y-6 mt-6">
                    <div className="grid gap-6">
                      <FormField
                        control={form.control}
                        name="guidelines"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Additional Guidelines & Instructions</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Provide any additional instructions, specific requirements, rubric details, or special guidelines for your assignment..."
                                rows={8}
                                {...field}
                                className="transition-all duration-200 focus:ring-2 focus:ring-primary/20 resize-none"
                              />
                            </FormControl>
                            <FormDescription>
                              Include any specific requirements, rubric details, or special instructions that will help our writers deliver exactly what you need.
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="service"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Service Type</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Academic Writing"
                                {...field}
                                className="transition-all duration-200 focus:ring-2 focus:ring-primary/20"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* File Upload Section */}
                      <div className="space-y-4">
                        <div>
                          <FormLabel className="text-base font-medium">Supporting Files</FormLabel>
                          <p className="text-sm text-muted-foreground mt-1">
                            Upload any reference materials, instructions, or supporting documents for your assignment.
                          </p>
                        </div>

                        <FileUpload
                          onFileUpload={handleFileUpload}
                          onFileRemove={handleFileRemove}
                          uploadedFiles={uploadedFiles}
                          folder="assignments"
                          multiple={true}
                          maxFiles={10}
                          className="border-2 border-dashed border-primary/20 hover:border-primary/40 transition-colors duration-200"
                        />

                        {uploadedFiles.length > 0 && (
                          <Alert>
                            <CheckCircle2 className="h-4 w-4" />
                            <AlertTitle>Files Ready</AlertTitle>
                            <AlertDescription>
                              {uploadedFiles.length} file{uploadedFiles.length > 1 ? 's' : ''} uploaded successfully.
                              These will be attached to your assignment.
                            </AlertDescription>
                          </Alert>
                        )}
                      </div>

                      {/* Coupon Section */}
                      <div className="space-y-4">
                        <Separator />
                        <CouponInput
                          originalPrice={calculatedPrice}
                          onCouponApplied={handleCouponApplied}
                          onCouponRemoved={handleCouponRemoved}
                          disabled={isSubmitting}
                          className="border rounded-lg p-4 bg-muted/30"
                        />
                      </div>

                      {/* Price Display */}
                      {calculatedPrice > 0 && (
                        <Card className="bg-primary/5 border-primary/20">
                          <CardContent className="pt-6">
                            <div className="flex items-center justify-between">
                              <div>
                                <h3 className="text-lg font-semibold">
                                  {appliedCoupon ? "Final Price" : "Estimated Price"}
                                </h3>
                                <p className="text-sm text-muted-foreground">
                                  {appliedCoupon ? "With coupon discount applied" : "Based on your requirements"}
                                </p>
                              </div>
                              <div className="text-right">
                                {appliedCoupon && (
                                  <div className="text-sm text-muted-foreground line-through mb-1">
                                    ${calculatedPrice.toFixed(2)}
                                  </div>
                                )}
                                <div className="text-2xl font-bold text-primary">
                                  ${(appliedCoupon ? finalPrice : calculatedPrice).toFixed(2)}
                                </div>
                                {appliedCoupon && (
                                  <div className="text-xs text-green-600 font-medium">
                                    Saved ${appliedCoupon.discountAmount.toFixed(2)}
                                  </div>
                                )}
                                <p className="text-xs text-muted-foreground">
                                  Final price may vary
                                </p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      )}
                    </div>
                  </TabsContent>
                </motion.div>
              </AnimatePresence>
            </Tabs>

            {/* Navigation and Submit Buttons */}
            <Separator />

            <div className="flex flex-col sm:flex-row gap-4 justify-between items-center">
              <div className="flex gap-2">
                {activeTab !== "basic" && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={prevTab}
                    disabled={isSubmitting}
                    className="flex items-center gap-2"
                  >
                    ← Previous
                  </Button>
                )}

                {activeTab !== "requirements" && (
                  <Button
                    type="button"
                    onClick={nextTab}
                    disabled={isSubmitting}
                    className="flex items-center gap-2"
                  >
                    Next →
                  </Button>
                )}
              </div>

              {activeTab === "requirements" && (
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => form.reset()}
                    disabled={isSubmitting}
                  >
                    Reset Form
                  </Button>

                  <Button
                    type="submit"
                    disabled={isSubmitting || !form.formState.isValid}
                    className="flex items-center gap-2 min-w-[140px]"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        Create Order
                      </>
                    )}
                  </Button>
                </div>
              )}
            </div>

            {/* Form Status */}
            {Object.keys(form.formState.errors).length > 0 && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Please fix the following errors:</AlertTitle>
                <AlertDescription>
                  <ul className="list-disc list-inside space-y-1 mt-2">
                    {Object.entries(form.formState.errors).map(([field, error]) => (
                      <li key={field} className="text-sm">
                        {error?.message}
                      </li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
