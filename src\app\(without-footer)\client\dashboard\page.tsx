//src/app/(without-footer)/client/dashboard/page.tsx

"use client";

import { Suspense } from "react";
import ClientProfileLargeDash from "@/components/dashboard/client/dashboard-large-card";
import ClientTodoList from "@/components/dashboard/client/dashboard-todo-list";
import { TotalOrdersChart, CompletedOrders<PERSON>hart } from "@/components/dashboard/client/dashboard-orders-charts";
import { useEmailVerificationRefresh } from "@/hooks/use-email-verification-refresh";

function ClientDashboardContent() {
  // Handle email verification refresh
  const { isRefreshing } = useEmailVerificationRefresh();

  if (isRefreshing) {
    return (
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Refreshing your session after email verification...</p>
          </div>
        </div>
      </div>
    );
  }
 

  return (
    <>
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        {/* Main dashboard content */}
        <div className="grid gap-4 grid-cols-1 xl:grid-cols-3 min-h-[calc(100vh-8rem)]">
          {/* Left column - Three stacked cards */}
          <div className="flex flex-col gap-4 xl:col-span-1 h-full">
            {/* Top card - Total Orders Chart */}
            <div className="bg-muted/50 rounded-xl border shadow-sm hover:shadow-md transition-shadow duration-200 h-72 md:h-80 shrink-0 overflow-hidden">
              <TotalOrdersChart />
            </div>

            {/* Middle card - Completed Orders Chart */}
            <div className="bg-muted/50 rounded-xl border shadow-sm hover:shadow-md transition-shadow duration-200 h-72 md:h-80 shrink-0 overflow-hidden">
              <CompletedOrdersChart />
            </div>

            {/* Bottom card - Todo list section that expands to fill remaining space */}
            <div className="bg-muted/50 rounded-xl border shadow-sm hover:shadow-md transition-shadow duration-200 flex-1 min-h-[300px] overflow-hidden">
              <ClientTodoList />
            </div>
          </div>

          {/* Right column - Large card */}
          <div className="xl:col-span-2">
            <div className="bg-muted/50 h-full rounded-xl border shadow-sm hover:shadow-md transition-shadow duration-200 min-h-[400px] xl:min-h-full overflow-hidden">
              <ClientProfileLargeDash />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default function ClientDashboardPage() {
  return (
    <Suspense fallback={
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading dashboard...</p>
          </div>
        </div>
      </div>
    }>
      <ClientDashboardContent />
    </Suspense>
  );
}
