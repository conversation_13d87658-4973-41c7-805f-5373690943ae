import { Metadata } from "next";
import prisma from "@/lib/prisma";

export async function generateFAQMetadata(): Promise<Metadata> {
  try {
    // Get company info for dynamic metadata
    const companyInfo = await prisma.companyInfo.findFirst();
    const companyName = companyInfo?.companyName || "Academic Writing Service";
    
    // Get FAQ count for description
    const faqCount = await prisma.fAQ.count({
      where: { isActive: true }
    });

    const baseUrl = process.env.NEXTAUTH_URL || "http://localhost:3000";

    return {
      title: `Frequently Asked Questions - ${companyName}`,
      description: `Find answers to ${faqCount}+ frequently asked questions about our academic writing services. Get help with orders, pricing, quality assurance, revisions, and more.`,
      keywords: [
        "FAQ",
        "frequently asked questions",
        "academic writing help",
        "student support",
        "writing service questions",
        "homework help FAQ",
        "essay writing questions",
        "academic assistance",
        companyName.toLowerCase(),
      ],
      openGraph: {
        title: `FAQ - ${companyName}`,
        description: `Get answers to common questions about our academic writing services. Quality, pricing, revisions, and more.`,
        url: `${baseUrl}/faqs`,
        siteName: companyName,
        type: "website",
        locale: "en_US",
      },
      twitter: {
        card: "summary",
        title: `FAQ - ${companyName}`,
        description: `Get answers to common questions about our academic writing services.`,
      },
      alternates: {
        canonical: `${baseUrl}/faqs`,
      },
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          "max-video-preview": -1,
          "max-image-preview": "large",
          "max-snippet": -1,
        },
      },
    };
  } catch (error) {
    console.error("Error generating FAQ metadata:", error);
    
    // Fallback metadata
    return {
      title: "Frequently Asked Questions - Academic Writing Service",
      description: "Find answers to frequently asked questions about our academic writing services. Get help with orders, pricing, quality assurance, and more.",
    };
  }
}
