// src/auth.ts
import { PrismaAdapter } from "@next-auth/prisma-adapter";
import CredentialsProvider from "next-auth/providers/credentials";
import GoogleProvider from "next-auth/providers/google";
import Twitter<PERSON>rovider from "next-auth/providers/twitter";
import Facebook<PERSON>rovider from "next-auth/providers/facebook";
import bcrypt from "bcryptjs";
import prisma from "@/lib/prisma";
import type { NextAuthOptions, DefaultSession } from "next-auth";
import type { JWT } from "next-auth/jwt";
import type { UserRole } from "@prisma/client";
import type { Adapter } from "next-auth/adapters";

// Enhanced type declarations
declare module "next-auth" {
  interface User {
    id: string;
    role: UserRole;
    email: string;
    name?: string | null;
    emailVerified: boolean;
  }

  interface Session extends DefaultSession {
    user: {
      id: string;
      role: UserRole;
      emailVerified: boolean;
    } & DefaultSession["user"];
  }
}

// Proper JWT module declaration
declare module "next-auth/jwt" {
  interface JWT {
    id: string;
    role: UserRole;
    emailVerified: boolean;
  }
}

// Made interface more robust
interface CredentialsType {
  email: string;
  password: string;
  role?: string; // Accept string version of UserRole
  name?: string; // For registration
  callbackUrl?: string; // CLAUDE: - Added callbackUrl to CredentialsType for better handling of redirects
}

// Helper function to generate unique accountId (same as in API routes)
const generateAccountId = (): string => {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  let result = "";
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

const generateUniqueAccountId = async (): Promise<string> => {
  let attempts = 0;
  const maxAttempts = 10;

  while (attempts < maxAttempts) {
    const accountId = generateAccountId();

    // Check if this accountId already exists
    const existing = await prisma.user.findFirst({
      where: { accountId },
      select: { id: true }
    });

    if (!existing) {
      return accountId;
    }

    attempts++;
  }

  // If we couldn't generate a unique ID after 10 attempts, throw an error
  throw new Error("Failed to generate unique account ID after multiple attempts");
};

// Create a custom adapter that handles emailVerified properly
const baseAdapter = PrismaAdapter(prisma);
const customAdapter = {
  ...baseAdapter,
  createUser: async (user: { email: string; name?: string | null; image?: string | null; emailVerified?: boolean | null | Date; role?: UserRole; [key: string]: unknown }) => {
    console.log("🔧 Custom createUser called with:", {
      email: user.email,
      name: user.name,
      emailVerified: user.emailVerified,
      role: user.role
    });

    // Generate unique accountId for social login users
    let uniqueAccountId: string;
    try {
      uniqueAccountId = await generateUniqueAccountId();
      console.log("🔧 Generated unique accountId:", uniqueAccountId);
    } catch (err) {
      console.error("❌ Error generating unique account ID:", err);
      throw new Error("Failed to generate unique account ID");
    }

    // Check for intended role from social login store before creating user
    let finalRole = user.role || ("CLIENT" as UserRole);

    if (user.email) {
      try {
        const { getSocialLoginRole, getAllStoredRoles, clearSocialLoginRole } = await import("@/lib/social-login-role-store");

        // First try to get role by email
        let intendedRole = getSocialLoginRole(user.email);

        // If not found by email, check for pending roles
        if (!intendedRole) {
          const allRoles = getAllStoredRoles();
          const pendingRoleEntry = Object.entries(allRoles).find(([key]) => key.startsWith("pending_"));

          if (pendingRoleEntry) {
            const [pendingKey, pendingRole] = pendingRoleEntry;
            intendedRole = pendingRole;

            // Clear the pending role
            clearSocialLoginRole(pendingKey);

            console.log("🔧 Found pending role during user creation:", {
              email: user.email,
              intendedRole,
              pendingKey
            });
          }
        }

        if (intendedRole) {
          finalRole = intendedRole;
          // Clear the role from store since we're using it
          clearSocialLoginRole(user.email);
          console.log("✅ Applied intended role during user creation:", {
            email: user.email,
            intendedRole,
            finalRole
          });
        }
      } catch (error) {
        console.error("❌ Error checking intended role during user creation:", error);
      }
    }

    // Ensure emailVerified is always a boolean, never null or Date
    // For social logins, we set emailVerified to false initially to match our memory requirements
    const userData = {
      email: user.email,
      name: user.name || null,
      image: user.image || null,
      emailVerified: false, // Always set to false initially for social logins to match memory requirements
      role: finalRole, // Use the determined role (either intended or default)
      accountId: uniqueAccountId, // Add the generated accountId
    };

    console.log("🔧 Creating user with data:", userData);

    const createdUser = await prisma.user.create({
      data: userData,
    });

    console.log("✅ User created successfully:", {
      id: createdUser.id,
      email: createdUser.email,
      role: createdUser.role,
      accountId: createdUser.accountId
    });

    // Send verification email for social users
    if (user.email) {
      try {
        await fetch(
          `${process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000"}/api/auth/send-verification-email`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              email: createdUser.email,
              userId: createdUser.id,
            }),
          }
        );
        console.log("📧 Verification email sent for new social user");
      } catch (emailError) {
        console.error("📧 Failed to send verification email for new social user:", emailError);
      }
    }

    return createdUser;
  },
} as Adapter;

// Create an auth config that can be imported without executing server code
export const authConfig: NextAuthOptions = {
  // Use custom adapter that handles emailVerified properly
  adapter: customAdapter,
  session: { strategy: "jwt" },

  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
        role: { label: "Role", type: "text" },
        name: { label: "Name", type: "text" }, // For registration
        callbackUrl: { label: "Callback URL", type: "text" }, // MAY 19: - Added for path detection
      },
      authorize: async (credentials) => {
        try {
          // This function only runs on the server, not in middleware
          if (
            !credentials?.email ||
            !credentials?.password ||
            typeof window !== "undefined"
          ) {
            return null;
          }

          const { email, password, role, callbackUrl } =
            credentials as CredentialsType;

          // MAY 19: - Added detection of registration path from callbackUrl
          // This ensures the role matches the registration page path
          let determinedRole = role as UserRole;
          if (callbackUrl) {
            if (callbackUrl.includes("/writer")) {
              determinedRole = "WRITER";
            } else if (callbackUrl.includes("/client")) {
              determinedRole = "CLIENT";
            }
          }

          // Find the user
          const user = await prisma.user.findUnique({
            where: { email },
          });

          // If user doesn't exist and we're in registration mode (role is provided)
          if (!user && determinedRole) {
            // Generate unique accountId for email/password registration
            let uniqueAccountId: string;
            try {
              uniqueAccountId = await generateUniqueAccountId();
              console.log("🔧 Generated unique accountId for email registration:", uniqueAccountId);
            } catch (err) {
              console.error("❌ Error generating unique account ID for email registration:", err);
              throw new Error("Failed to generate unique account ID");
            }

            // Hash the password
            const hashedPassword = await bcrypt.hash(password, 10);

            // Create new user
            const newUser = await prisma.user.create({
              data: {
                email,
                password: hashedPassword,
                role: determinedRole,
                name: credentials.name || null,
                emailVerified: false,
                accountId: uniqueAccountId, // Add the generated accountId
              },
            });

            // Send verification email
            try {
              await fetch(
                `${process.env.NEXT_PUBLIC_BASE_URL || process.env.NEXTAUTH_URL}/api/auth/send-verification-email`,
                {
                  method: "POST",
                  headers: { "Content-Type": "application/json" },
                  body: JSON.stringify({
                    email: newUser.email,
                    userId: newUser.id,
                  }),
                }
              );
            } catch (emailError) {
              console.error(
                "📧 Failed to send verification email:",
                emailError
              );
            }

            return {
              id: newUser.id,
              email: newUser.email,
              role: newUser.role,
              name: newUser.name || null,
              emailVerified: false,
            };
          }

          // For login - verify password
          if (user?.password) {
            const isValid = await bcrypt.compare(password, user.password);

            if (isValid) {
              // Block login if email is not verified
              if (user.emailVerified === false) {
                // IMPORTANT: Don't catch this error - let it bubble up to NextAuth
                throw new Error("EMAIL_NOT_VERIFIED");
              }

              return {
                id: user.id,
                email: user.email,
                role: user.role,
                name: user.name || null,
                emailVerified: user.emailVerified,
              };
            } else {
              console.error("❌ Invalid password");
            }
          } else {
          }

          return null;
          /* eslint-disable @typescript-eslint/no-explicit-any */
        } catch (error: any) {
          console.error("🚨 Error during authorization:", error);
          console.error("🚨 Error type:", typeof error);

          if (error instanceof Error) {
            console.error("🚨 Error message:", error.message);
          }

          // CRITICAL FIX: Re-throw specific errors instead of returning null
          if (error.message === "EMAIL_NOT_VERIFIED") {
            throw error; // This allows NextAuth to receive the specific error
          }
          // For other errors, you can either throw them or return null
          // Throwing will show the error to the user, returning null shows generic error
          throw error; // This will show the actual error message
          // return null; // This would show "CredentialsSignin" error
          // return null;
        }
      },
    }),

    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      profile(profile) {
        console.log("🔧 Google profile data:", {
          sub: profile.sub,
          email: profile.email,
          email_verified: profile.email_verified
        });

        // Try to extract intended role from state or other parameters
        // The role will be properly determined in the JWT callback
        return {
          id: profile.sub,
          name: profile.name,
          email: profile.email,
          image: profile.picture,
          role: "CLIENT" as UserRole, // Default, will be updated in JWT callback based on intended role
          emailVerified: false, // Always false for social logins to match memory requirements
        };
      },
    }),

    TwitterProvider({
      clientId: process.env.TWITTER_CLIENT_ID!,
      clientSecret: process.env.TWITTER_CLIENT_SECRET!,
      version: "2.0",
      profile(profile) {
        return {
          id: profile.sub,
          name: profile.name,
          email: profile.email,
          image: profile.picture_image_url,
          role: "CLIENT" as UserRole, // Default, will be updated in JWT callback
          emailVerified: false, // Will be handled in custom adapter
        };
      },
    }),
    FacebookProvider({
      clientId: process.env.FACEBOOK_CLIENT_ID!,
      clientSecret: process.env.FACEBOOK_CLIENT_SECRET!,
      profile(profile) {
        return {
          id: profile.sub,
          name: profile.name,
          email: profile.email,
          image: profile.picture.data.url,
          role: "CLIENT" as UserRole, // Default, will be updated in JWT callback
          emailVerified: false, // Will be handled in custom adapter
        };
      },
    }),
  ],
  callbacks: {
    // Add signIn callback to handle role-based authentication and prevent unwanted account linking
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async signIn({ user, account, profile }) {
      // Allow credentials provider (email/password) to proceed normally
      if (account?.provider === "credentials") {
        return true;
      }

      // For OAuth providers, ensure we're creating a new user or linking to the correct existing user
      if (account?.provider && user?.email) {
        try {
          // Check if user already exists
          const existingUser = await prisma.user.findUnique({
            where: { email: user.email },
            select: { id: true, role: true, email: true }
          });

          // If user exists, we need to validate the intended role
          if (existingUser) {
            const { getSocialLoginRole, getAllStoredRoles } = await import("@/lib/social-login-role-store");

            // Try to get intended role
            let intendedRole = getSocialLoginRole(user.email);

            // If not found by email, check for pending roles
            if (!intendedRole) {
              const allRoles = getAllStoredRoles();
              const pendingRoleEntry = Object.entries(allRoles).find(([key]) => key.startsWith("pending_"));

              if (pendingRoleEntry) {
                const [, pendingRole] = pendingRoleEntry;
                intendedRole = pendingRole;
              }
            }

            // If there's an intended role and it doesn't match the existing user's role
            if (intendedRole && existingUser.role !== intendedRole) {
              console.log("🚫 Role mismatch detected in signIn callback:", {
                email: user.email,
                existingRole: existingUser.role,
                intendedRole,
                provider: account.provider
              });

              // Prevent sign-in and redirect to error page
              return `/auth-error?error=RoleMismatch&message=This email is already registered with a different role. Please use the correct login page for your account type.&existingRole=${existingUser.role}&intendedRole=${intendedRole}`;
            }
          }

          console.log("✅ SignIn callback - allowing OAuth login:", {
            email: user.email,
            provider: account.provider,
            hasExistingUser: !!existingUser
          });

          return true;
        } catch (error) {
          console.error("❌ Error in signIn callback:", error);
          return false;
        }
      }

      return true;
    },

    // Enhanced JWT callback with role validation for social logins
    async jwt({ token, user, account }) {
      if (user) {
        token.id = user.id;
        // Check if user has role property (from our custom User type)
        const userWithRole = user as { role?: UserRole };
        token.role = userWithRole.role || "CLIENT"; // Fallback to CLIENT if role not set
        // Handle emailVerified - convert Date/null to boolean
        token.emailVerified = user.emailVerified ? true : false;
      }

      // Only fetch latest user data from database for existing sessions (not new OAuth sign-ins)
      // This prevents overriding the correct role for new social logins
      if (token.id && typeof window === "undefined" && !user && !account) {
        try {
          const latestUser = await prisma.user.findUnique({
            where: { id: token.id },
            select: { role: true, email: true, emailVerified: true },
          });

          if (latestUser) {
            // Update token with latest user data from database
            token.role = latestUser.role;
            token.emailVerified = latestUser.emailVerified;
            console.log("🔄 JWT token updated with latest user data (existing session):", {
              userId: token.id,
              emailVerified: latestUser.emailVerified,
              role: latestUser.role
            });
          }
        } catch (error) {
          console.error("❌ Error fetching latest user data in JWT callback:", error);
        }
      }

      // For OAuth sign-in, the role is now handled in createUser function
      // Just ensure we have the correct role from the user object
      if (account && account.provider !== "credentials" && typeof window === "undefined") {
        if (user) {
          // The role should already be correctly set by createUser function
          const userWithRole = user as { role?: UserRole };
          token.role = userWithRole.role || "CLIENT";
          token.emailVerified = false; // New social users need verification

          console.log("� OAuth JWT callback - using role from createUser:", {
            email: user.email,
            role: token.role,
            provider: account.provider
          });
        }
      }

      return token;
    },

    // Enhanced session callback
    async session({
      session,
      token,
    }: {
      session: DefaultSession & { user?: { id?: string; role?: UserRole; emailVerified?: boolean } };
      token: JWT;
    }) {
      if (token && session.user) {
        session.user.id = token.id;
        session.user.role = token.role;
        session.user.emailVerified = token.emailVerified as boolean;
      }
      return session;
    },

    // Dynamic redirect with role validation
    async redirect({ url, baseUrl }: { url: string; baseUrl: string }) {
      // Handle callback URLs
      if (url.startsWith("/api/auth/callback")) {
        return url;
      }

      // Extract intended role from callback URL if present
      const urlObj = new URL(url, baseUrl);
      const intendedRole = urlObj.searchParams.get("intended_role") as UserRole;

      // Store intended role for social login if present
      if (intendedRole && ["ADMIN", "WRITER", "CLIENT"].includes(intendedRole)) {
        try {
          // For social login redirects, we need to store the intended role
          // We'll use a temporary key since we don't have the email yet
          const { storeSocialLoginRole } = await import("@/lib/social-login-role-store");

          // Store with a temporary key that will be picked up in the JWT callback
          const tempKey = `pending_${intendedRole}_${Date.now()}`;
          storeSocialLoginRole(tempKey, intendedRole);

          console.log("🔧 Stored intended role from redirect URL with temp key:", {
            tempKey,
            intendedRole
          });
        } catch (error) {
          console.error("❌ Error storing intended role from redirect:", error);
        }
      }

      // Handle sign-in redirects with role validation
      if (url.startsWith(baseUrl + "/login") || url === baseUrl || intendedRole) {
        // For social login callbacks, validate role mismatch
        if (intendedRole && typeof window === "undefined") {
          try {
            const response = await fetch(`${baseUrl}/api/auth/session`);
            const session = await response.json();

            if (session?.user?.role && session.user.role !== intendedRole) {
              // Role mismatch - redirect to error page
              const correctLoginPage = getCorrectLoginPageForRole(session.user.role);
              const roleName = getRoleNameForRole(session.user.role);

              const errorUrl = new URL("/auth-error", baseUrl);
              errorUrl.searchParams.set("error", "WrongRole");
              errorUrl.searchParams.set("message", `Please login through the ${roleName} dashboard at ${correctLoginPage}`);
              errorUrl.searchParams.set("correctUrl", correctLoginPage);

              return errorUrl.toString();
            }

            // Check if email verification is required
            if (session?.user && session.user.emailVerified === false) {
              return `${baseUrl}/verify-email-required`;
            }
          } catch (error) {
            console.error("Error validating role during redirect:", error);
          }
        }

        // Get user's role and redirect to appropriate dashboard
        if (typeof window !== "undefined") {
          try {
            const response = await fetch("/api/auth/session");
            const session = await response.json();

            if (session?.user?.role) {
              // Check if email verification is required
              if (session.user.emailVerified === false) {
                return `${baseUrl}/verify-email-required`;
              }
              return getDashboardUrlForRole(session.user.role, baseUrl);
            }
          } catch (error) {
            console.error("Error fetching session:", error);
          }
        }

        // Default fallback
        return `${baseUrl}/client/dashboard`;
      }

      // For all other URLs
      return url.startsWith("/") ? `${baseUrl}${url}` : url;
    },
  },
  pages: {
    signIn: "/login/client", // Default sign-in page
    error: "/auth-error",
    signOut: "/login/client", // Default sign-out redirects to client login
  },
  // Better error handling and debug info
  debug: process.env.NODE_ENV === "development",
};

// Helper functions for role-based redirects
function getDashboardUrlForRole(role: UserRole, baseUrl: string): string {
  switch (role) {
    case "ADMIN":
      return `${baseUrl}/admin/dashboard`;
    case "WRITER":
      return `${baseUrl}/writer/dashboard`;
    case "CLIENT":
      return `${baseUrl}/client/dashboard`;
    default:
      return `${baseUrl}/client/dashboard`;
  }
}

function getCorrectLoginPageForRole(role: UserRole): string {
  switch (role) {
    case "ADMIN":
      return "/login/admin";
    case "WRITER":
      return "/login/writer";
    case "CLIENT":
      return "/login/client";
    default:
      return "/login/client";
  }
}

function getRoleNameForRole(role: UserRole): string {
  switch (role) {
    case "ADMIN":
      return "Admin";
    case "WRITER":
      return "Writer";
    case "CLIENT":
      return "Client";
    default:
      return "Client";
  }
}


