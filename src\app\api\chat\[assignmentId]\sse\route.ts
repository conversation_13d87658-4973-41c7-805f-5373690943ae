// src/app/api/chat/[assignmentId]/sse/route.ts
import { NextRequest } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authConfig } from "@/auth";
import type { ChatParticipantRole } from "@prisma/client";
import { connections, HEARTBEAT_INTERVAL, addConnection, removeConnection } from "@/lib/chat-broadcast";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ assignmentId: string }> }
) {
  try {
    const session = await getServerSession(authConfig);
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { assignmentId } = await params;
    const userId = session.user.id;
    const userRole = session.user.role;

    // Get chatType from URL params
    const url = new URL(request.url);
    const chatType = url.searchParams.get("chatType") || "client";

    // Verify user has access to this assignment
    const assignment = await prisma.assignment.findUnique({
      where: { id: assignmentId },
      include: {
        client: true,
        assignedWriter: true,
      },
    });

    if (!assignment) {
      return new Response("Assignment not found", { status: 404 });
    }

    // Check access permissions
    const hasAccess =
      userRole === "ADMIN" ||
      (userRole === "CLIENT" && assignment.clientId === userId) ||
      (userRole === "WRITER" && assignment.assignedWriterId === userId);

    if (!hasAccess) {
      return new Response("Forbidden", { status: 403 });
    }

    // Create or get existing chat
    let chat = await prisma.chat.findUnique({
      where: { assignmentId },
      include: {
        participants: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
                image: true,
              },
            },
          },
        },
        messages: {
          include: {
            sender: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
                image: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 50,
        },
      },
    });

    if (!chat) {
      chat = await prisma.chat.create({
        data: {
          assignmentId,
          participants: {
            create: [
              {
                userId: assignment.clientId,
                role: "CLIENT" as ChatParticipantRole,
              },
              ...(assignment.assignedWriterId
                ? [
                    {
                      userId: assignment.assignedWriterId,
                      role: "WRITER" as ChatParticipantRole,
                    },
                  ]
                : []),
            ],
          },
        },
        include: {
          participants: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  role: true,
                  image: true,
                },
              },
            },
          },
          messages: {
            include: {
              sender: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  role: true,
                  image: true,
                },
              },
            },
            orderBy: {
              createdAt: "desc",
            },
            take: 50,
          },
        },
      });
    }

    if (!chat) {
      return new Response("Failed to create or retrieve chat", { status: 500 });
    }

    // FIXED: Proper message filtering based on user role and conversation context
    const filteredMessages = chat.messages.filter((message) => {
      // Handle legacy messages (before conversation targeting was implemented)
      if (!message.conversationType || !message.targetParticipantId) {
        // For legacy messages, show to everyone (you might want to handle this differently)
        return true;
      }

      // Message filtering logic based on roles:
      if (userRole === "ADMIN") {
        // Admin can see messages based on the chatType they're viewing
        // If viewing client chat, show client conversation messages
        // If viewing writer chat, show writer conversation messages
        return message.conversationType === chatType;
      } else if (userRole === "CLIENT") {
        // Client can only see messages in "client" conversations
        // This includes messages they sent and messages sent to them
        return (
          message.conversationType === "client" &&
          (message.senderId === userId ||
            message.targetParticipantId === userId)
        );
      } else if (userRole === "WRITER") {
        // Writer can only see messages in "writer" conversations
        // This includes messages they sent and messages sent to them
        return (
          message.conversationType === "writer" &&
          (message.senderId === userId ||
            message.targetParticipantId === userId)
        );
      }

      return false;
    });

    // Create SSE stream
    const stream = new ReadableStream({
      start(controller) {
        const connectionId = `${userId}-${assignmentId}-${chatType}-${Date.now()}`;

        addConnection(connectionId, {
          controller,
          userId,
          assignmentId,
          userRole,
          chatType, // Store chatType in connection
          lastHeartbeat: Date.now(),
        });

        // Send initial data with filtered messages
        controller.enqueue(
          `data: ${JSON.stringify({
            type: "INITIAL_DATA",
            data: {
              chatId: chat!.id,
              messages: filteredMessages.reverse(),
              participants: chat!.participants,
            },
          })}\n\n`
        );

        // Send heartbeat
        const heartbeatInterval = setInterval(() => {
          try {
            const connection = connections.get(connectionId);
            if (connection) {
              connection.lastHeartbeat = Date.now();
              controller.enqueue(
                `data: ${JSON.stringify({ type: "HEARTBEAT" })}\n\n`
              );
            } else {
              clearInterval(heartbeatInterval);
            }
          } catch (error) {
            console.error(error);
            clearInterval(heartbeatInterval);
            removeConnection(connectionId);
          }
        }, HEARTBEAT_INTERVAL);

        // Cleanup on close
        request.signal.addEventListener("abort", () => {
          clearInterval(heartbeatInterval);
          removeConnection(connectionId);
        });
      },
    });

    return new Response(stream, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Cache-Control",
      },
    });
  } catch (error) {
    console.error("SSE connection error:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}