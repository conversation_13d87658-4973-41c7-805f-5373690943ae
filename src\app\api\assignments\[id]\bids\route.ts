// src/app/api/assignments/[id]/bids/route.ts

import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import {
  apiSuccess,
  apiError,
  parseRequestBody,
  checkPermission,
  getCurrentUserId,
  getCurrentUserRole,
} from "@/lib/api-utils";
import { AssignmentStatus, BidStatus, Prisma, UserRole } from "@prisma/client";
import { z } from "zod";

// Bid creation schema (could be moved to validations.ts)
const bidCreateSchema = z.object({
  message: z.string().min(1, "Message is required"),
});

//- Get bids for a specific assignment - Made params async
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const currentUserId = await getCurrentUserId();
    const userRole = await getCurrentUserRole();

    if (!currentUserId) {
      return apiError("Authentication required", 401);
    }

    //- Await the params to resolve the Promise
    const { id: assignmentId } = await params;

    // Check if assignment exists
    const assignment = await prisma.assignment.findUnique({
      where: { id: assignmentId },
    });

    if (!assignment) {
      return apiError("Assignment not found", 404);
    }

    // Check permissions based on role
    const canViewBids =
      userRole === UserRole.ADMIN || assignment.clientId === currentUserId;

    // For writers, they can only see their own bids
    const whereConditions: Prisma.BidWhereInput = { assignmentId };

    if (userRole === UserRole.WRITER) {
      whereConditions.writerId = currentUserId;
    }

    if (!canViewBids && userRole !== UserRole.WRITER) {
      return apiError(
        "You don't have permission to view bids for this assignment",
        403
      );
    }

    // Get bids with related data
    const bids = await prisma.bid.findMany({
      where: whereConditions,
      orderBy: { createdAt: "desc" },
      include: {
        writer: {
          select: {
            id: true,
            name: true,
            email: true,
            accountId: true, // Add this
          },
        },
      },
    });

    // Format the response
    const formattedBids = bids.map((bid) => ({
      id: bid.id,
      message: bid.message,
      status: bid.status,
      writerId: bid.writerId,
      assignmentId: bid.assignmentId,
      createdAt: bid.createdAt.toISOString(),
      updatedAt: bid.updatedAt.toISOString(),
      writer: bid.writer,
    }));

    return apiSuccess({
      bids: formattedBids,
      total: formattedBids.length,
    });
  } catch (error) {
    console.error("Error fetching bids:", error);
    return apiError("Failed to fetch bids", 500);
  }
}

//- Create a new bid for an assignment - Made params async
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Only writers can submit bids
    const permissionError = await checkPermission(["WRITER"]);
    if (permissionError) return permissionError;

    const currentUserId = await getCurrentUserId();
    if (!currentUserId) {
      return apiError("Authentication required", 401);
    }

    //- Await the params to resolve the Promise
    const { id: assignmentId } = await params;

    // Check if assignment exists and is open for bidding
    const assignment = await prisma.assignment.findUnique({
      where: { id: assignmentId },
    });

    if (!assignment) {
      return apiError("Assignment not found", 404);
    }

    if (assignment.status !== AssignmentStatus.POSTED) {
      return apiError("This assignment is not open for bidding", 400);
    }

    // Check if writer is approved
    const writer = await prisma.user.findUnique({
      where: { id: currentUserId },
    });

    if (!writer || !writer.isApproved) {
      return apiError("Only approved writers can submit bids", 403);
    }

    // Check if writer has already submitted a bid
    const existingBid = await prisma.bid.findFirst({
      where: {
        assignmentId,
        writerId: currentUserId,
      },
    });

    if (existingBid) {
      return apiError(
        "You have already submitted a bid for this assignment",
        409
      );
    }

    // Parse and validate the request body
    const parsed = await parseRequestBody(req, bidCreateSchema);
    if ("success" in parsed && parsed.success === false) {
      return apiError(parsed.message, 400, parsed.errors);
    }

    const { message } = parsed as { message: string };

    // Create the bid
    const newBid = await prisma.bid.create({
      data: {
        message,
        writerId: currentUserId,
        assignmentId,
        status: BidStatus.PENDING,
      },
      include: {
        writer: {
          select: {
            id: true,
            name: true,
            email: true,
            accountId: true, // Add this
          },
        },
      },
    });

    // Format the response
    const formattedBid = {
      id: newBid.id,
      message: newBid.message,
      status: newBid.status,
      writerId: newBid.writerId,
      assignmentId: newBid.assignmentId,
      createdAt: newBid.createdAt.toISOString(),
      updatedAt: newBid.updatedAt.toISOString(),
      writer: newBid.writer,
    };

    return apiSuccess(formattedBid, "Bid submitted successfully");
  } catch (error) {
    console.error("Error creating bid:", error);
    return apiError("Failed to submit bid", 500);
  }
}
