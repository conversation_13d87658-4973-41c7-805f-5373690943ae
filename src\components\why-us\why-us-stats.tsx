"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Users, 
  FileText, 
  Star, 
  Clock,
  GraduationCap,
  Award,
  Globe,
  TrendingUp
} from "lucide-react";

const stats = [
  {
    icon: Users,
    number: "50,000+",
    label: "Happy Students",
    description: "Students worldwide trust us",
    color: "text-blue-600",
    bgColor: "bg-blue-50 dark:bg-blue-950/20"
  },
  {
    icon: FileText,
    number: "100,000+",
    label: "Papers Delivered",
    description: "Successfully completed assignments",
    color: "text-green-600",
    bgColor: "bg-green-50 dark:bg-green-950/20"
  },
  {
    icon: Star,
    number: "98%",
    label: "Satisfaction Rate",
    description: "Customer satisfaction guarantee",
    color: "text-yellow-600",
    bgColor: "bg-yellow-50 dark:bg-yellow-950/20"
  },
  {
    icon: Clock,
    number: "99%",
    label: "On-Time Delivery",
    description: "Delivered before deadline",
    color: "text-purple-600",
    bgColor: "bg-purple-50 dark:bg-purple-950/20"
  },
  {
    icon: GraduationCap,
    number: "500+",
    label: "Expert Writers",
    description: "PhD qualified professionals",
    color: "text-indigo-600",
    bgColor: "bg-indigo-50 dark:bg-indigo-950/20"
  },
  {
    icon: Award,
    number: "15+",
    label: "Years Experience",
    description: "Serving students globally",
    color: "text-red-600",
    bgColor: "bg-red-50 dark:bg-red-950/20"
  },
  {
    icon: Globe,
    number: "150+",
    label: "Countries Served",
    description: "Global student community",
    color: "text-teal-600",
    bgColor: "bg-teal-50 dark:bg-teal-950/20"
  },
  {
    icon: TrendingUp,
    number: "95%",
    label: "Grade Improvement",
    description: "Students see better grades",
    color: "text-orange-600",
    bgColor: "bg-orange-50 dark:bg-orange-950/20"
  }
];

export function WhyUsStats() {
  return (
    <div className="container mx-auto px-4">
      <div className="max-w-6xl mx-auto">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <Badge 
            variant="secondary" 
            className="px-4 py-2 text-sm font-medium bg-primary/10 text-primary border-primary/20 mb-4"
          >
            Our Track Record
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
            Numbers That Speak for Themselves
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            These statistics reflect our commitment to excellence and the trust students place in our services
          </p>
        </motion.div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Card className="text-center hover:shadow-lg transition-all duration-300 border-2 hover:border-primary/20 group">
                <CardContent className="p-6">
                  {/* Icon */}
                  <div className={`inline-flex p-4 rounded-full ${stat.bgColor} mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    <stat.icon className={`w-8 h-8 ${stat.color}`} />
                  </div>

                  {/* Number */}
                  <motion.div
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.8, delay: 0.2 + index * 0.1 }}
                  >
                    <h3 className="text-3xl md:text-4xl font-bold text-primary mb-2 group-hover:scale-105 transition-transform duration-300">
                      {stat.number}
                    </h3>
                  </motion.div>

                  {/* Label */}
                  <h4 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                    {stat.label}
                  </h4>

                  {/* Description */}
                  <p className="text-sm text-muted-foreground">
                    {stat.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10 rounded-2xl p-8 border border-primary/20">
            <h3 className="text-2xl font-bold mb-4">
              Ready to Join Our Success Story?
            </h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Become part of our growing community of successful students who have achieved their academic goals with our help.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Badge variant="secondary" className="px-4 py-2">
                ✓ Trusted Worldwide
              </Badge>
              <Badge variant="secondary" className="px-4 py-2">
                ✓ Proven Results
              </Badge>
              <Badge variant="secondary" className="px-4 py-2">
                ✓ Student Success
              </Badge>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
