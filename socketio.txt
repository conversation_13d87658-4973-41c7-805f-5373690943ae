inside the order/[id]/page.tsx which is in the path "src/app/(without-footer)/order/[id]/page.tsx" 
i want you to implement a chat functionality. Here is the logic i want achieved. The chat functionality will have this logic, 
i do not want the client and writer to have any form of contact, the admin will have to serve as the intermediary, 
the image attached is how i want the chat functionality to look like if the user role===admin use-session-user-id.ts , 
you can use this hook to determine the role. if the user is admin, present a chat button that when clicked it opens
 a dialog that will have the same UI as the attached image, on the left edit it to have the Writer & Client. 
 Initially the UI will only allow the admin to chat with the Client (since they are the ones who posted the assignment), 
 however, when the assignment status changes to Assigned/Completed/Revision/Cancelled, this means a writer has been tasked with the assignment, 
 take the user id and it is at this time the Writer tab will become present for the admin (since now both the client and writer are present). 
 Only the admin can see both the client and writer (different tabs, use shadcn tabs), the Clients chat ui will only have Admin, 
 similar to that of the writer. Use socket.io to achieve this. Make the chat UI very presentable and modern (as seen in the image). 
 Once a new message is present it should show a number at the top (just like normal chat systems) and provide a collection of emojis for the chat. 
 Let me repeat make a popup dialog with a chat feature inside every "OrderDetailsPage" page which is in context, 
 Admin is allowed to chat with both client and writer at the same time (but they are separated by different tabs), 
 client and writer can only chat with admin, the client and writer ids can be obtained by utilizing the orderId to get their values from the database. 
 Go through the entire codebase to understand how to implement this, avoid using type "any" in the code since it always leads to build time errors, 
 and avoid type errors and eslint warnings, use best practices and the utilize every possible shadcn component to make the chat functionality future proof, 
 presentable and modern. For example tooltips etc. Maybe create a standaone component that accepts props and import it inside the page to 
 reduce the number of lines of code in one file


 ----------------------
 ADDITIONAL SETTINGS
 ----------------------
 However, there are some areas that could be improved for a more robust production environment:
Add message pagination for better performance with large chat histories
Implement proper reconnection handling
Add message queuing for offline/poor connection scenarios
Implement rate limiting to prevent abuse
Add message deduplication
Implement message caching
Add input sanitization
Add proper error boundaries
⚠️ No pagination for message history
⚠️ No message caching
⚠️ No lazy loading for older messages
⚠️ Socket reconnection strategy missing
⚠️ No handling of connection timeouts
⚠️ No exponential backoff for reconnection attempts