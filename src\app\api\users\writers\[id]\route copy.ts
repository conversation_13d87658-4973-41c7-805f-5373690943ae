// // src/app/api/users/writers/[id]/route.ts
// import { NextRequest } from "next/server";
// import type { NextResponse } from "next/server";
// import prisma from "@/lib/prisma";
// import bcrypt from "bcryptjs";
// import {
//   apiSuccess,
//   apiError,
//   parseRequestBody,
//   checkPermission,
//   getCurrentUserId,
//   safeJsonDate,
// } from "@/lib/api-utils";
// import { userUpdateSchema } from "@/lib/validations";
// import type { UserResponse, UserUpdateData } from "@/types/api";

// // GPT WRITERS: - use async params signature
// interface RouteParams {
//   params: Promise<{ id: string }>;
// }

// // GET a single writer
// export async function GET(
//   _req: NextRequest,
//   { params }: RouteParams
// ): Promise<NextResponse> {
//   const { id } = await params;
//   const me = await getCurrentUserId();
//   if (!me) return apiError("Authentication required", 401);

//   const you = await prisma.user.findUnique({
//     where: { id: me },
//     select: { role: true },
//   });
//   const allowed = you?.role === "ADMIN" || you?.role === "CLIENT" || me === id;
//   if (!allowed) return apiError("No permission", 403);

//   try {
//     const w = await prisma.user.findUnique({
//       where: { id, role: "WRITER" },
//       select: {
//         id: true,
//         email: true,
//         name: true,
//         phone: true,
//         role: true,
//         isApproved: true,
//         createdAt: true,
//         updatedAt: true,
//         image: true,
//         writerBids: {
//           select: { id: true, status: true, createdAt: true },
//           take: 5,
//           orderBy: { createdAt: "desc" },
//         },
//         assignedJobs: {
//           select: { id: true, status: true, startDate: true, deadline: true },
//           take: 5,
//           orderBy: { createdAt: "desc" },
//         },
//         _count: { select: { writerBids: true, assignedJobs: true } },
//       },
//     });
//     if (!w) return apiError("Writer not found", 404);

//     const resp = {
//       id: w.id,
//       email: you?.role === "CLIENT" && me !== id ? "[Protected]" : w.email,
//       name: w.name,
//       phone: w.phone,
//       role: w.role,
//       isApproved: w.isApproved,
//       createdAt: w.createdAt.toISOString(),
//       updatedAt: w.updatedAt.toISOString(),
//       image: w.image,
//       bidCount: w._count.writerBids,
//       jobCount: w._count.assignedJobs,
//       recentBids: w.writerBids,
//       recentJobs: w.assignedJobs,
//     };

//     return apiSuccess(safeJsonDate(resp));
//   } catch (err) {
//     console.error("Error fetching writer:", err);
//     return apiError("Failed to fetch writer", 500);
//   }
// }

// // PUT update a writer
// export async function PUT(
//   req: NextRequest,
//   { params }: RouteParams
// ): Promise<NextResponse> {
//   const { id } = await params;
//   const me = await getCurrentUserId();
//   if (!me) return apiError("Authentication required", 401);

//   const you = await prisma.user.findUnique({
//     where: { id: me },
//     select: { role: true },
//   });
//   const allowed = you?.role === "ADMIN" || me === id;
//   if (!allowed) return apiError("No permission", 403);

//   const parsed = await parseRequestBody(req, userUpdateSchema);
//   if ("success" in parsed && !parsed.success) {
//     return apiError(parsed.message, 400, parsed.errors);
//   }
//   const data = parsed as UserUpdateData;

//   try {
//     const exists = await prisma.user.findUnique({
//       where: { id, role: "WRITER" },
//     });
//     if (!exists) return apiError("Writer not found", 404);

//     // writers themselves cannot change role or approval
//     if (you?.role !== "ADMIN" && me === id) {
//       if (data.role !== undefined || data.isApproved !== undefined) {
//         return apiError("Cannot change role or approval", 403);
//       }
//     }

//     if (data.password) {
//       data.password = await bcrypt.hash(data.password, 10);
//     }

//     const upd = await prisma.user.update({
//       where: { id },
//       data,
//       select: {
//         id: true,
//         email: true,
//         name: true,
//         phone: true,
//         role: true,
//         isApproved: true,
//         createdAt: true,
//         updatedAt: true,
//         image: true,
//       },
//     });

//     return apiSuccess(
//       safeJsonDate(upd) as UserResponse,
//       "Writer updated successfully"
//     );
//   } catch (err) {
//     console.error("Error updating writer:", err);
//     if (err instanceof Error && err.message.includes("Unique constraint")) {
//       return apiError("Email in use", 409);
//     }
//     return apiError("Failed to update writer", 500);
//   }
// }

// // DELETE a writer
// export async function DELETE(
//   _req: NextRequest,
//   { params }: RouteParams
// ): Promise<NextResponse> {
//   const permissionError = await checkPermission(["ADMIN"]);
//   if (permissionError) return permissionError;

//   const { id } = await params;
//   try {
//     const ex = await prisma.user.findUnique({ where: { id, role: "WRITER" } });
//     if (!ex) return apiError("Writer not found", 404);

//     await prisma.user.delete({ where: { id } });
//     return apiSuccess(null, "Writer deleted successfully");
//   } catch (err) {
//     console.error("Error deleting writer:", err);
//     if (err instanceof Error && err.message.includes("foreign key")) {
//       return apiError("Cannot delete writer with related data", 409);
//     }
//     return apiError("Failed to delete writer", 500);
//   }
// }
