# Chat Functionality Setup Guide

This guide explains how to set up and use the real-time chat functionality in the Academic App.

## Overview

The chat system allows:
- **Admins** to chat with both clients and writers (in separate tabs)
- **Clients** to chat only with admins
- **Writers** to chat only with admins (when assigned to an assignment)

The chat prevents direct communication between clients and writers, with admins serving as intermediaries.

## Setup Instructions

### 1. Install Dependencies

```bash
npm install
```

This will install the required Socket.IO dependencies:
- `socket.io` (server)
- `socket.io-client` (client)
- `concurrently` (for running multiple processes)

### 2. Database Setup

The chat functionality requires new database models. Run the Prisma migration:

```bash
npx prisma db push
```

This will create the following new collections:
- `chats` - Chat rooms for each assignment
- `chat_participants` - Users participating in each chat
- `messages` - Individual chat messages

### 3. Environment Variables

Add the following to your `.env` file:

```env
# Socket.IO Configuration
NEXT_PUBLIC_SOCKET_URL="http://localhost:3001"
SOCKET_PORT=3001
```

### 4. Running the Application

#### Option 1: Run Both Services Together (Recommended)
```bash
npm run dev:full
```

This starts both the Next.js app (port 3000) and Socket.IO server (port 3001).

#### Option 2: Run Services Separately

Terminal 1 - Next.js App:
```bash
npm run dev
```

Terminal 2 - Socket.IO Server:
```bash
npm run dev:socket
```

## How It Works

### Chat Access Rules

1. **Admin Users**:
   - Can see both "Client" and "Writer" tabs
   - Writer tab only appears when assignment status is: `ASSIGNED`, `COMPLETED`, `REVISION`, or `CANCELLED`
   - Can chat with both client and writer simultaneously

2. **Client Users**:
   - Can only chat with admin
   - No tabs, direct chat interface

3. **Writer Users**:
   - Can only chat with admin
   - Only available when assigned to the assignment
   - No tabs, direct chat interface

### Chat Features

- **Real-time messaging** using Socket.IO
- **Emoji support** with emoji picker
- **Message read receipts** (delivered/read status)
- **Unread message counters** on tabs and chat button
- **Modern UI** using shadcn/ui components
- **Typing indicators** (planned feature)

### Database Schema

The chat system uses the following models:

```prisma
model Chat {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  assignmentId String   @unique @db.ObjectId
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  assignment   Assignment        @relation(fields: [assignmentId], references: [id])
  messages     Message[]
  participants ChatParticipant[]
}

model ChatParticipant {
  id     String              @id @default(auto()) @map("_id") @db.ObjectId
  chatId String              @db.ObjectId
  userId String              @db.ObjectId
  role   ChatParticipantRole

  chat Chat @relation(fields: [chatId], references: [id])
  user User @relation(fields: [userId], references: [id])
}

model Message {
  id        String      @id @default(auto()) @map("_id") @db.ObjectId
  chatId    String      @db.ObjectId
  senderId  String      @db.ObjectId
  content   String
  type      MessageType @default(TEXT)
  isRead    Boolean     @default(false)
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt

  chat   Chat @relation(fields: [chatId], references: [id])
  sender User @relation(fields: [senderId], references: [id])
}
```

## API Endpoints

### GET `/api/chat/[assignmentId]`
Retrieves chat data for an assignment including messages and participants.

### POST `/api/chat/[assignmentId]/messages`
Sends a new message to the chat.

### PUT `/api/chat/[assignmentId]/messages`
Marks messages as read.

## Socket.IO Events

### Client to Server
- `join-assignment-chat` - Join a specific assignment's chat room
- `send-message` - Send a message to the chat
- `mark-messages-read` - Mark messages as read
- `typing` - Indicate typing status

### Server to Client
- `new-message` - Receive a new message
- `message-read` - Message read status update
- `user-joined` - User joined the chat
- `user-left` - User left the chat
- `user-typing` - User typing status

## Troubleshooting

### Common Issues

1. **Chat button not appearing**:
   - Ensure user has proper role (ADMIN, CLIENT, or assigned WRITER)
   - Check assignment status for writer access

2. **Messages not sending**:
   - Verify Socket.IO server is running on port 3001
   - Check browser console for connection errors
   - Ensure `NEXT_PUBLIC_SOCKET_URL` is set correctly

3. **Database errors**:
   - Run `npx prisma db push` to ensure schema is up to date
   - Check MongoDB connection

4. **Real-time updates not working**:
   - Verify Socket.IO server is running
   - Check browser network tab for WebSocket connections
   - Ensure CORS settings allow your domain

### Development Tips

1. **Testing Chat**:
   - Use multiple browser windows/incognito tabs
   - Log in as different user roles
   - Test assignment status changes

2. **Debugging**:
   - Check Socket.IO server logs for connection info
   - Use browser dev tools to monitor WebSocket traffic
   - Enable verbose logging in development

## Security Considerations

- All chat access is validated server-side
- Users can only access chats for assignments they're involved in
- Message sending requires proper authentication
- Socket.IO rooms prevent unauthorized message access

## Future Enhancements

- File sharing in chat
- Voice messages
- Chat history export
- Advanced moderation tools
- Push notifications
- Mobile app support





 