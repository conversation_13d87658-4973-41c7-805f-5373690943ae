"use client"

import { useState, useCallback } from 'react';
import { supabase } from '@/lib/supabase';
import { FileUploadResult, FileUploadHookReturn } from '@/types/upload';
import { nanoid } from 'nanoid';
import { sanitizeFileName } from '@/lib/api-utils';

export const useSupabaseFileUpload = (): FileUploadHookReturn => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);

  const resetError = useCallback(() => {
    setError(null);
  }, []);

  const uploadFile = useCallback(async (
    file: File,
    folder: string = 'assignments',
    assignmentId?: string
  ): Promise<FileUploadResult> => {
    setIsUploading(true);
    setUploadProgress(0);
    setError(null);

    try {
      // Validate file type - allow documents and common file types
      const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain',
        'application/rtf',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ];

      if (!allowedTypes.includes(file.type)) {
        throw new Error('Invalid file type. Only documents (PDF, DOC, DOCX, PPT, PPTX, TXT, RTF, XLS, XLSX) are allowed.');
      }

      // Validate file size (50MB max for documents)
      const maxSize = 50 * 1024 * 1024; // 50MB
      if (file.size > maxSize) {
        throw new Error('File size too large. Maximum size is 50MB.');
      }

      // Generate unique file path with sanitized filename
      const fileExtension = file.name.split('.').pop();
      const originalFileName = file.name.replace(`.${fileExtension}`, '');
      const sanitizedFileName = sanitizeFileName(originalFileName);
      const uniqueId = nanoid(8);

      // Create folder structure with assignment ID if provided
      let filePath: string;
      if (assignmentId) {
        const assignmentFolder = `${folder}/${assignmentId}`;
        // Folder will be created automatically when we upload the first file
        filePath = `${assignmentFolder}/${sanitizedFileName}_${uniqueId}.${fileExtension}`;
      } else {
        filePath = `${folder}/${sanitizedFileName}_${uniqueId}.${fileExtension}`;
      }

      // Simulate progress updates for better UX
      setUploadProgress(25);

      // Upload file to Supabase Storage
      const { data, error: uploadError } = await supabase.storage
        .from('academic-files')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      setUploadProgress(75);

      if (uploadError) {
        throw new Error(uploadError.message || 'Upload failed');
      }

      if (!data) {
        throw new Error('Upload failed - no data returned');
      }

      // Get public URL for the uploaded file
      const { data: urlData } = supabase.storage
        .from('academic-files')
        .getPublicUrl(data.path);

      if (!urlData.publicUrl) {
        throw new Error('Failed to get public URL');
      }

      setUploadProgress(100);

      return {
        url: urlData.publicUrl,
        path: data.path,
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified
      };

    } catch (uploadError) {
      const errorMessage = uploadError instanceof Error ? uploadError.message : 'Upload failed';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsUploading(false);
      setTimeout(() => setUploadProgress(0), 1000);
    }
  }, []);

  return {
    uploadFile,
    isUploading,
    uploadProgress,
    error,
    resetError
  };
};
