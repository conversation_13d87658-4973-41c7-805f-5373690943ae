"use client";

import { motion } from "framer-motion";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Star, Quote } from "lucide-react";

const testimonials = [
  {
    name: "<PERSON>",
    role: "Psychology Major",
    university: "Stanford University",
    avatar: "/avatars/woman1.webp",
    rating: 5,
    text: "Homework Asylum saved my semester! Their writers are incredibly knowledgeable and delivered exactly what I needed. The quality exceeded my expectations.",
    subject: "Research Paper",
    grade: "A+"
  },
  {
    name: "<PERSON>",
    role: "Business Student",
    university: "Harvard Business School",
    avatar: "/avatars/man1.webp",
    rating: 5,
    text: "I was struggling with my dissertation until I found this service. The writer assigned to me was a PhD in my field and provided invaluable insights.",
    subject: "MBA Dissertation",
    grade: "A"
  },
  {
    name: "<PERSON>",
    role: "Literature Major",
    university: "Yale University",
    avatar: "/avatars/woman2.webp",
    rating: 5,
    text: "The attention to detail and adherence to academic standards is impressive. They helped me understand complex concepts while delivering top-quality work.",
    subject: "Literary Analysis",
    grade: "A+"
  },
  {
    name: "<PERSON>",
    role: "Engineering Student",
    university: "MIT",
    avatar: "/avatars/man2.webp",
    rating: 5,
    text: "Technical writing isn&apos;t easy, but their engineering experts made it look effortless. Great communication throughout the process.",
    subject: "Technical Report",
    grade: "A"
  },
  {
    name: "Jessica Park",
    role: "Medical Student",
    university: "Johns Hopkins",
    avatar: "/avatars/woman.webp",
    rating: 5,
    text: "The medical case study they helped me with was thorough and well-researched. It really helped me understand the subject better.",
    subject: "Case Study",
    grade: "A+"
  },
  {
    name: "Robert Wilson",
    role: "History Major",
    university: "Oxford University",
    avatar: "/avatars/man.webp",
    rating: 5,
    text: "Exceptional historical analysis with proper citations and methodology. The writer clearly understood the academic requirements.",
    subject: "History Essay",
    grade: "A"
  }
];

export function WhyUsTestimonials() {
  return (
    <div className="container mx-auto px-4">
      <div className="max-w-6xl mx-auto">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <Badge 
            variant="secondary" 
            className="px-4 py-2 text-sm font-medium bg-primary/10 text-primary border-primary/20 mb-4"
          >
            <Star className="w-4 h-4 mr-2" />
            Student Success Stories
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
            What Our Students Say
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Real testimonials from students who have achieved academic success with our help
          </p>
        </motion.div>

        {/* Testimonials Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Card className="h-full hover:shadow-lg transition-all duration-300 border-2 hover:border-primary/20 group relative overflow-hidden">
                {/* Quote Icon */}
                <div className="absolute top-4 right-4 opacity-10 group-hover:opacity-20 transition-opacity">
                  <Quote className="w-8 h-8 text-primary" />
                </div>

                <CardContent className="p-6">
                  {/* Rating */}
                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    ))}
                    <Badge 
                      variant="secondary" 
                      className="ml-2 text-xs bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400"
                    >
                      Grade: {testimonial.grade}
                    </Badge>
                  </div>

                  {/* Testimonial Text */}
                  <blockquote className="text-muted-foreground mb-6 leading-relaxed italic">
                    &quot;{testimonial.text}&quot;
                  </blockquote>

                  {/* Student Info */}
                  <div className="flex items-center">
                    <Avatar className="w-12 h-12 mr-4">
                      <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                      <AvatarFallback>
                        {testimonial.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <h4 className="font-semibold text-sm group-hover:text-primary transition-colors">
                        {testimonial.name}
                      </h4>
                      <p className="text-xs text-muted-foreground">
                        {testimonial.role}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {testimonial.university}
                      </p>
                    </div>
                  </div>

                  {/* Subject Badge */}
                  <div className="mt-4 pt-4 border-t border-border/50">
                    <Badge variant="outline" className="text-xs">
                      {testimonial.subject}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Bottom Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 rounded-2xl p-8 border border-primary/20">
            <h3 className="text-2xl font-bold mb-6">
              Join Thousands of Successful Students
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">4.9/5</div>
                <div className="text-sm text-muted-foreground">Average Rating</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">98%</div>
                <div className="text-sm text-muted-foreground">Satisfaction Rate</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">50K+</div>
                <div className="text-sm text-muted-foreground">Happy Students</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">24/7</div>
                <div className="text-sm text-muted-foreground">Support Available</div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
