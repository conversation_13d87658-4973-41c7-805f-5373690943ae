import { create } from "zustand";
import { useSession } from "next-auth/react";
import { useMemo, useEffect } from "react";
import { NotificationType } from "@prisma/client";

export type Notification = {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  taskId?: string;
  assignmentId?: string;
  createdAt: string;
  read: boolean;
};

interface NotificationStore {
  notifications: Notification[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
  sseConnected: boolean;
  addNotification: (
    targetUserId: string,
    notification: Omit<Notification, "id" | "createdAt" | "read">
  ) => Promise<void>;
  fetchNotifications: () => Promise<void>;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  removeNotification: (id: string) => Promise<void>;
  clearAll: () => Promise<void>;
  connectSSE: () => void;
  disconnectSSE: () => void;
  handleSSENotification: (notification: Notification) => void;
}

const createStore = () => {
  let eventSource: EventSource | null = null;

  return create<NotificationStore>()((set, get) => ({
    notifications: [],
    unreadCount: 0,
    loading: false,
    error: null,
    sseConnected: false,

    addNotification: async (targetUserId, notification) => {
      try {
        set({ loading: true, error: null });
        const response = await fetch("/api/notifications", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            targetUserId,
            notification,
          }),
        });

        if (!response.ok) {
          throw new Error("Failed to send notification");
        }

        const data = await response.json();
        if (data.success) {
          const newNotification = data.data;
          set((state) => ({
            notifications: [newNotification, ...state.notifications],
            unreadCount: state.unreadCount + 1,
            loading: false,
          }));
        }
      } catch (error) {
        const message =
          error instanceof Error
            ? error.message
            : "Failed to send notification";
        set({ error: message, loading: false });
        throw error;
      }
    },

    fetchNotifications: async () => {
      try {
        set({ loading: true, error: null });
        const response = await fetch("/api/notifications");

        if (!response.ok) {
          throw new Error("Failed to fetch notifications");
        }

        const data = await response.json();
        if (data.success) {
          set({
            notifications: data.data,
            unreadCount: data.data.filter((n: Notification) => !n.read).length,
            loading: false,
          });
        }
      } catch (error) {
        const message =
          error instanceof Error
            ? error.message
            : "Failed to fetch notifications";
        set({ error: message, loading: false });
      }
    },

    markAsRead: async (notificationId: string) => {
      try {
        set({ loading: true, error: null });
        const response = await fetch("/api/notifications", {
          method: "PATCH",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            notificationIds: [notificationId],
          }),
        });

        if (!response.ok) {
          throw new Error("Failed to mark notification as read");
        }

        set((state) => ({
          notifications: state.notifications.map((n) =>
            n.id === notificationId ? { ...n, read: true } : n
          ),
          unreadCount: state.unreadCount - 1,
          loading: false,
        }));
      } catch (error) {
        const message =
          error instanceof Error
            ? error.message
            : "Failed to mark notification as read";
        set({ error: message, loading: false });
      }
    },

    markAllAsRead: async () => {
      try {
        set({ loading: true, error: null });
        const notificationIds = get()
          .notifications.filter((n) => !n.read)
          .map((n) => n.id);

        if (notificationIds.length === 0) return;

        const response = await fetch("/api/notifications", {
          method: "PATCH",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ notificationIds }),
        });

        if (!response.ok) {
          throw new Error("Failed to mark all notifications as read");
        }

        set((state) => ({
          notifications: state.notifications.map((n) => ({
            ...n,
            read: true,
          })),
          unreadCount: 0,
          loading: false,
        }));
      } catch (error) {
        const message =
          error instanceof Error
            ? error.message
            : "Failed to mark all notifications as read";
        set({ error: message, loading: false });
      }
    },

    removeNotification: async (notificationId: string) => {
      try {
        set({ loading: true, error: null });
        const response = await fetch(`/api/notifications/${notificationId}`, {
          method: "DELETE",
        });

        if (!response.ok) {
          throw new Error("Failed to remove notification");
        }

        set((state) => ({
          notifications: state.notifications.filter(
            (n) => n.id !== notificationId
          ),
          unreadCount: state.notifications.filter(
            (n) => !n.read && n.id !== notificationId
          ).length,
          loading: false,
        }));
      } catch (error) {
        const message =
          error instanceof Error
            ? error.message
            : "Failed to remove notification";
        set({ error: message, loading: false });
      }
    },

    clearAll: async () => {
      try {
        set({ loading: true, error: null });
        const response = await fetch("/api/notifications", {
          method: "DELETE",
        });

        if (!response.ok) {
          throw new Error("Failed to clear notifications");
        }

        set({
          notifications: [],
          unreadCount: 0,
          loading: false,
        });
      } catch (error) {
        const message =
          error instanceof Error
            ? error.message
            : "Failed to clear notifications";
        set({ error: message, loading: false });
      }
    },

    connectSSE: () => {
      if (eventSource) {
        eventSource.close();
      }

      eventSource = new EventSource('/api/notifications/sse');

      eventSource.onopen = () => {
        console.log('SSE connection opened');
        set({ sseConnected: true, error: null });
      };

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          if (data.type === 'new-notification' && data.notification) {
            get().handleSSENotification(data.notification);
          } else if (data.type === 'connection-established') {
            console.log('SSE connection established');
          } else if (data.type === 'heartbeat') {
            // Handle heartbeat - just keep connection alive
          }
        } catch (error) {
          console.error('Error parsing SSE message:', error);
        }
      };

      eventSource.onerror = (error) => {
        console.error('SSE connection error:', error);
        set({ sseConnected: false, error: 'Real-time connection lost' });

        // Attempt to reconnect after 5 seconds
        setTimeout(() => {
          if (!get().sseConnected) {
            get().connectSSE();
          }
        }, 5000);
      };
    },

    disconnectSSE: () => {
      if (eventSource) {
        eventSource.close();
        eventSource = null;
        set({ sseConnected: false });
      }
    },

    handleSSENotification: (notification: Notification) => {
      set((state) => ({
        notifications: [notification, ...state.notifications],
        unreadCount: state.unreadCount + 1,
      }));
    },
  }));
};

// Create store instances
const authStore = createStore();
const emptyStore = createStore();

export function useNotifications() {
  const { data: session } = useSession();
  const store = useMemo(
    () => (session?.user?.id ? authStore : emptyStore),
    [session?.user?.id]
  );

  // Fetch notifications and connect to SSE when the store is initialized or user logs in
  useEffect(() => {
    if (session?.user?.id) {
      store.getState().fetchNotifications();
      store.getState().connectSSE();
    }

    // Cleanup SSE connection when component unmounts or user logs out
    return () => {
      if (session?.user?.id) {
        store.getState().disconnectSSE();
      }
    };
  }, [session?.user?.id, store]);

  // Return all store methods and state
  return {
    notifications: store.getState().notifications,
    unreadCount: store.getState().unreadCount,
    loading: store.getState().loading,
    error: store.getState().error,
    addNotification: store.getState().addNotification,
    fetchNotifications: store.getState().fetchNotifications,
    markAsRead: store.getState().markAsRead,
    markAllAsRead: store.getState().markAllAsRead,
    removeNotification: store.getState().removeNotification,
    clearAll: store.getState().clearAll,
  };
}
