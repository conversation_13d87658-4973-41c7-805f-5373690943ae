// src/app/api/assignments/post/route.ts
import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import {
  apiSuccess,
  apiError,
  parseRequestBody,
  getCurrentUserId,
  getCurrentUserRole,
} from "@/lib/api-utils";
import { AssignmentStatus, UserRole } from "@prisma/client";
import { z } from "zod";
import { notificationService } from "@/lib/notification-service";

const postAssignmentSchema = z.object({
  assignmentId: z.string(),
});

// Post an assignment for bidding
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    const currentUserId = await getCurrentUserId();
    const userRole = await getCurrentUserRole();

    if (!currentUserId) {
      return apiError("Authentication required", 401);
    }

    // Parse and validate the request body to get assignment ID
    const parsed = await parseRequestBody(req, postAssignmentSchema);

    if ("success" in parsed && parsed.success === false) {
      return apiError(parsed.message, 400, parsed.errors);
    }

    const { assignmentId } = parsed as { assignmentId: string };

    // Check if assignment exists
    const assignment = await prisma.assignment.findUnique({
      where: { id: assignmentId },
    });

    if (!assignment) {
      return apiError("Assignment not found", 404);
    }

    // Check permissions - only admins and the assignment's client can post
    const canPost =
      userRole === UserRole.ADMIN ||
      (userRole === UserRole.CLIENT && assignment.clientId === currentUserId);

    if (!canPost) {
      return apiError("You don't have permission to post this assignment", 403);
    }

    // Verify that the assignment is in a valid state to be posted
    if (assignment.status !== "DRAFT" && assignment.status !== "PENDING") {
      return apiError(
        `Cannot post an assignment with status ${assignment.status}. Assignment must be in DRAFT or PENDING status.`,
        400
      );
    }

    // Validate assignment completeness before posting
    const requiredFields = [
      "title",
      "description",
      "assignmentType",
      "subject",
      "service",
      "pageCount",
      "academicLevel",
      "estTime",
    ];

    const missingFields = requiredFields.filter(
      (field) => !assignment[field as keyof typeof assignment]
    );

    if (missingFields.length > 0) {
      return apiError(
        `Assignment is missing required fields: ${missingFields.join(
          ", "
        )}. Please complete these fields before posting.`,
        400
      );
    }

    // Update the assignment status to POSTED
    const updatedAssignment = await prisma.assignment.update({
      where: { id: assignmentId },
      data: { status: AssignmentStatus.POSTED },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        _count: {
          select: { bids: true },
        },
      },
    });

    // Send notifications for job posting
    try {
      // Notify client about successful job posting
      if (updatedAssignment.client) {
        await notificationService.sendJobPostedNotification(
          updatedAssignment.client.id,
          updatedAssignment.client.email,
          updatedAssignment.client.name || "Client",
          updatedAssignment.id,
          updatedAssignment.title,
          updatedAssignment.taskId,
          updatedAssignment.estTime.toISOString()
        );
      }

      // Notify all admins about new job posting
      const admins = await notificationService.getAllAdmins();
      for (const admin of admins) {
        await notificationService.sendJobPostedNotification(
          admin.id,
          admin.email,
          admin.name || "Admin",
          updatedAssignment.id,
          updatedAssignment.title,
          updatedAssignment.taskId,
          updatedAssignment.estTime.toISOString()
        );
      }
    } catch (notificationError) {
      console.error("Error sending job posting notifications:", notificationError);
      // Don't fail the request if notifications fail
    }

    // Format the response
    const response = {
      id: updatedAssignment.id,
      taskId: updatedAssignment.taskId,
      title: updatedAssignment.title,
      description: updatedAssignment.description,
      assignmentType: updatedAssignment.assignmentType,
      subject: updatedAssignment.subject,
      service: updatedAssignment.service,
      pageCount: updatedAssignment.pageCount,
      priority: updatedAssignment.priority,
      academicLevel: updatedAssignment.academicLevel,
      spacing: updatedAssignment.spacing,
      languageStyle: updatedAssignment.languageStyle,
      formatStyle: updatedAssignment.formatStyle,
      numSources: updatedAssignment.numSources,
      guidelines: updatedAssignment.guidelines,
      estTime: updatedAssignment.estTime.toISOString(),
      status: updatedAssignment.status,
      createdAt: updatedAssignment.createdAt.toISOString(),
      updatedAt: updatedAssignment.updatedAt.toISOString(),
      client:
        userRole === UserRole.ADMIN ? updatedAssignment.client : undefined,
      bidCount: updatedAssignment._count.bids,
    };

    return apiSuccess(
      response,
      "Assignment posted successfully and open for bidding"
    );
  } catch (error) {
    console.error("Error posting assignment:", error);
    return apiError("Failed to post assignment", 500);
  }
}
