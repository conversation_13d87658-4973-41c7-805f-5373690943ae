import { NextRequest } from "next/server";
import prisma from "@/lib/prisma";
import { checkPermission, apiSuccess, apiError, getCurrentUserId } from "@/lib/api-utils";

export async function GET(req: NextRequest) {
  try {
    // Only writers can access their own payments
    const permissionError = await checkPermission(["WRITER"]);
    if (permissionError) return permissionError;

    const currentUserId = await getCurrentUserId();
    if (!currentUserId) {
      return apiError("User not found", 401);
    }

    const { searchParams } = new URL(req.url);
    const status = searchParams.get("status"); // 'pending' or 'approved'
    const writerId = searchParams.get("writerId");
    const limit = parseInt(searchParams.get("limit") || "50");
    const offset = parseInt(searchParams.get("offset") || "0");

    // Ensure writer can only access their own payments
    const targetWriterId = writerId || currentUserId;
    if (targetWriterId !== currentUserId) {
      return apiError("Access denied", 403);
    }

    // Fetch assignments for the writer
    const assignments = await prisma.assignment.findMany({
      where: {
        assignedWriterId: targetWriterId,
        status: "COMPLETED",
        writerCompensation: { not: null },
      },
      orderBy: { updatedAt: "desc" },
      take: limit,
      skip: offset,
    });

    // Transform assignments to payment format
    const payments = assignments.map((assignment) => ({
      id: assignment.id,
      assignmentId: assignment.id,
      writerId: assignment.assignedWriterId!,
      writerCompensation: assignment.writerCompensation || 0,
      isWriterPaid: assignment.isWriterPaid,
      writerPaypalEmail: assignment.writerPaypalEmail,
      writerPaymentDate: assignment.writerPaymentDate?.toISOString(),
      assignment: {
        id: assignment.id,
        title: assignment.title,
        taskId: assignment.taskId,
        status: assignment.status,
        createdAt: assignment.createdAt.toISOString(),
        updatedAt: assignment.updatedAt.toISOString(),
      },
    }));

    // Filter based on payment status
    const pending = payments.filter(p => !p.isWriterPaid);
    const approved = payments.filter(p => p.isWriterPaid);

    // Return based on status filter or all
    if (status === "pending") {
      return apiSuccess({ 
        pending, 
        total: pending.length,
        totalAmount: pending.reduce((sum, p) => sum + p.writerCompensation, 0)
      });
    } else if (status === "approved") {
      return apiSuccess({ 
        approved, 
        total: approved.length,
        totalAmount: approved.reduce((sum, p) => sum + p.writerCompensation, 0)
      });
    } else {
      return apiSuccess({ 
        pending, 
        approved, 
        totals: {
          pending: pending.length,
          approved: approved.length,
          pendingAmount: pending.reduce((sum, p) => sum + p.writerCompensation, 0),
          approvedAmount: approved.reduce((sum, p) => sum + p.writerCompensation, 0),
          totalEarnings: approved.reduce((sum, p) => sum + p.writerCompensation, 0),
        }
      });
    }
  } catch (error) {
    console.error("Error fetching writer payments:", error);
    return apiError("Failed to fetch writer payments", 500);
  }
}

// Get payment statistics for writer dashboard
export async function POST(req: NextRequest) {
  try {
    // Only writers can access their payment stats
    const permissionError = await checkPermission(["WRITER"]);
    if (permissionError) return permissionError;

    const currentUserId = await getCurrentUserId();
    if (!currentUserId) {
      return apiError("User not found", 401);
    }

    const body = await req.json();
    const { action } = body;

    if (action === "stats") {
      // Get payment statistics
      const assignments = await prisma.assignment.findMany({
        where: {
          assignedWriterId: currentUserId,
          status: "COMPLETED",
          writerCompensation: { not: null },
        },
        select: {
          writerCompensation: true,
          isWriterPaid: true,
          writerPaymentDate: true,
        },
      });

      const totalEarnings = assignments
        .filter(a => a.isWriterPaid)
        .reduce((sum, a) => sum + (a.writerCompensation || 0), 0);

      const pendingAmount = assignments
        .filter(a => !a.isWriterPaid)
        .reduce((sum, a) => sum + (a.writerCompensation || 0), 0);

      // Calculate monthly earnings for current month
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      const monthlyEarnings = assignments
        .filter(a => {
          if (!a.isWriterPaid || !a.writerPaymentDate) return false;
          const paymentDate = new Date(a.writerPaymentDate);
          return paymentDate.getMonth() === currentMonth && paymentDate.getFullYear() === currentYear;
        })
        .reduce((sum, a) => sum + (a.writerCompensation || 0), 0);

      // Calculate last 6 months earnings
      const monthlyData = [];
      for (let i = 5; i >= 0; i--) {
        const date = new Date();
        date.setMonth(date.getMonth() - i);
        const month = date.getMonth();
        const year = date.getFullYear();
        
        const monthEarnings = assignments
          .filter(a => {
            if (!a.isWriterPaid || !a.writerPaymentDate) return false;
            const paymentDate = new Date(a.writerPaymentDate);
            return paymentDate.getMonth() === month && paymentDate.getFullYear() === year;
          })
          .reduce((sum, a) => sum + (a.writerCompensation || 0), 0);

        monthlyData.push({
          month: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
          earnings: monthEarnings,
        });
      }

      return apiSuccess({
        totalEarnings,
        pendingAmount,
        monthlyEarnings,
        totalAssignments: assignments.length,
        paidAssignments: assignments.filter(a => a.isWriterPaid).length,
        pendingAssignments: assignments.filter(a => !a.isWriterPaid).length,
        averagePayment: assignments.filter(a => a.isWriterPaid).length > 0 
          ? totalEarnings / assignments.filter(a => a.isWriterPaid).length 
          : 0,
        monthlyData,
      });
    }

    return apiError("Invalid action", 400);
  } catch (error) {
    console.error("Error processing writer payment request:", error);
    return apiError("Failed to process request", 500);
  }
}
