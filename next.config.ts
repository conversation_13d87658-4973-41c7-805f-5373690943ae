import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  images: {
    domains: [
      "res.cloudinary.com",
      "example.com",
      "kkjwhyudqtsiqkdjvfxy.supabase.co",
      "images.unsplash.com"
    ],
  },
  async redirects() {
    return [
      {
        source: "/admin",
        destination: "/admin/dashboard",
        permanent: true,
      },
      {
        source: "/client",
        destination: "/client/dashboard",
        permanent: true,
      },
      {
        source: "/writer",
        destination: "/writer/dashboard",
        permanent: true,
      },
    ];
  },
  /* config options here */
  turbopack: {
    resolveAlias: {
      "@/auth": "./src/auth",
      "@/lib/prisma": "./src/lib/prisma",
    },
  },
  reactStrictMode: true,
  // Changed from experimental.serverComponentsExternalPackages to serverExternalPackages
  serverExternalPackages: ["@prisma/client", "bcryptjs"],
};

export default nextConfig;
