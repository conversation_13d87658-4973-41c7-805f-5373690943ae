/* eslint-disable @typescript-eslint/no-unused-vars */

declare global {
  interface Window {
    paypal?: PayPalNamespace;
  }
}

interface PayPalNamespace {
  Buttons: (options: PayPalButtonsOptions) => PayPalButtonsInstance;
}

interface PayPalButtonsOptions {
  createOrder?: (...args: unknown[]) => unknown;
  onApprove?: (...args: unknown[]) => unknown;
  onError?: (...args: unknown[]) => unknown;
  onCancel?: (...args: unknown[]) => unknown;
}

interface PayPalButtonsInstance {
  render: (container: HTMLElement | null) => void;
}

import { useEffect, useRef } from "react";

// Type for PayPal window object
interface PayPalWindow extends Window {
  paypal?: typeof window.paypal;
}

// Props for the PayPalButton component
export interface PayPalButtonProps {
  orderId: string; // The assignment/order ID
  amount: number; // Amount to charge
  onSuccess: (details: { id: string; payerID: string; paymentID: string }) => void;
  onError?: (error: Error) => void;
  onCancel?: () => void;
}

const PayPalButton: React.FC<PayPalButtonProps> = ({
  orderId,
  amount,
  onSuccess,
  onError,
  onCancel,
}) => {
  const paypalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Dynamically load PayPal JS SDK
    const script = document.createElement("script");
    const clientId = process.env.NEXT_PUBLIC_PAYPAL_ENVIRONMENT !== "production"
  ? process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID_SANDBOX
  : process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID_LIVE;
console.log("[PayPalButton] Using client ID for SDK:", clientId);
script.src = `https://www.paypal.com/sdk/js?client-id=${clientId}&currency=USD`;
    script.async = true;
    script.onload = () => {
      const win = window as PayPalWindow;
      if (win.paypal && paypalRef.current) {
        win.paypal.Buttons({
          createOrder: async (...args: unknown[]) => {
            // args[0] is the PayPal data object
            // Call Next.js API to create order
            const res = await fetch("/api/payments/create-order", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ orderId, amount }),
            });
            const data = await res.json();
            if (!res.ok || !data.id) throw new Error("Failed to create PayPal order");
            return data.id;
          },
          onApprove: async (...args: unknown[]) => {
            try {
              const data = args[0] as { orderID: string };
              console.log("[PayPalButton] onApprove called with orderID:", data.orderID);

              // Capture the order
              const res = await fetch("/api/payments/capture-order", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ orderId, orderID: data.orderID }),
              });

              console.log("[PayPalButton] Capture response status:", res.status);
              const details = await res.json();
              console.log("[PayPalButton] Capture response data:", details);

              if (!res.ok) {
                const errorMsg = details.message || "Payment capture failed";
                console.error("[PayPalButton] Capture failed:", errorMsg);
                throw new Error(errorMsg);
              }

              console.log("[PayPalButton] Payment successful, calling onSuccess");
              onSuccess({
                id: details.id,
                payerID: details.payerID,
                paymentID: details.paymentID,
              });
            } catch (error) {
              console.error("[PayPalButton] onApprove error:", error);
              if (onError) {
                onError(error instanceof Error ? error : new Error(String(error)));
              }
            }
          },
          onError: (...args: unknown[]) => {
            console.error("[PayPalButton] PayPal SDK onError:", args);
            const err = args[0] instanceof Error ? args[0] : new Error(String(args[0]));
            if (onError) onError(err);
          },
          onCancel: () => {
            if (onCancel) onCancel();
          },
        }).render(paypalRef.current);
      }
    };
    document.body.appendChild(script);
    return () => {
      document.body.removeChild(script);
    };
  }, [orderId, amount, onSuccess, onError, onCancel]);

  return <div ref={paypalRef} />;
};

export default PayPalButton;
