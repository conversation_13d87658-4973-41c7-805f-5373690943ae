export interface AnalyticsTimeRange {
  start: Date;
  end: Date;
}

export interface AnalyticsDataPoint {
  date: string;
  value: number;
}

export interface ClientAnalytics {
  totalClients: number;
  activeClients: number;
  newClients: number;
  churnRate: number;
  clientGrowth: number;
  topClients: {
    id: string;
    name: string;
    email: string;
    totalSpent: number;
    totalOrders: number;
    lastActive: Date;
  }[];
  clientsByMonth: AnalyticsDataPoint[];
  clientActivity: {
    id: string;
    name: string;
    action: string;
    timestamp: Date;
  }[];
  clientDistribution: {
    category: string;
    value: number;
  }[];
}

export interface WriterAnalytics {
  totalWriters: number;
  activeWriters: number;
  newWriters: number;
  topWriters: {
    id: string;
    name: string;
    email: string;
    completedAssignments: number;
    averageRating: number;
    earnings: number;
  }[];
  writersByMonth: AnalyticsDataPoint[];
  writerActivity: {
    id: string;
    name: string;
    action: string;
    timestamp: Date;
  }[];
  writerDistribution: {
    category: string;
    value: number;
  }[];
  writerPerformance: {
    id: string;
    name: string;
    onTime: number;
    late: number;
    rejected: number;
  }[];
}

export interface RevenueAnalytics {
  totalRevenue: number;
  monthlyRevenue: number;
  revenueGrowth: number;
  averageOrderValue: number;
  revenueByMonth: AnalyticsDataPoint[];
  revenueByCategory: {
    category: string;
    value: number;
  }[];
  recentTransactions: {
    id: string;
    clientName: string;
    amount: number;
    date: Date;
    status: string;
  }[];
  projectedRevenue: AnalyticsDataPoint[];
}

export interface GrowthAnalytics {
  userGrowth: {
    clients: number;
    writers: number;
  };
  growthRate: number;
  conversionRate: number;
  retentionRate: number;
  growthByMonth: {
    date: string;
    clients: number;
    writers: number;
  }[];
  marketingEffectiveness: {
    channel: string;
    visitors: number;
    conversions: number;
    conversionRate: number;
    cost: number;
    roi: number;
  }[];
}
