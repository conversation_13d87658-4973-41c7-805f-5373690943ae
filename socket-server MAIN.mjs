// import { createServer } from 'http';
// import { Server } from 'socket.io';
// import next from 'next';

// const dev = process.env.NODE_ENV !== 'production';
// const hostname = 'localhost';
// const port = process.env.PORT || 3000;
// const socketPort = process.env.SOCKET_PORT || 3001;

// // Create Next.js app
// const app = next({ dev, hostname, port });
// const handler = app.getRequestHandler();

// app.prepare().then(() => {
//   // Create HTTP server for Socket.IO
//   const httpServer = createServer((req, res) => {
//     // Handle Next.js requests
//     return handler(req, res);
//   });
  
//   // Create Socket.IO server
//   const io = new Server(httpServer, {
//     cors: {
//       origin: [`http://localhost:${port}`, `http://localhost:3000`],
//       methods: ['GET', 'POST'],
//       credentials: true,
//     },
//   });

//   // Store active users and their rooms
//   const activeUsers = new Map();
//   const assignmentRooms = new Map();
//   // CLAUDE: Store typing users for each room
//   const typingUsers = new Map();

//   io.on('connection', (socket) => {
//     console.log('User connected:', socket.id);

//     // Handle user joining assignment chat
//     socket.on('join-assignment-chat', (data) => {
//       // CLAUDE: Handle both old format (string) and new format (object) for backward compatibility
//       const assignmentId = typeof data === 'string' ? data : data.assignmentId;
//       const userId = socket.handshake.query.userId;
//       const clientId = socket.handshake.query.clientId;
//       const userRole = socket.handshake.query.userRole; // Add user role to query
      
//       if (!userId || !assignmentId) {
//         console.log('Missing userId or assignmentId');
//         return;
//       }

//       // Store user info with role
//       activeUsers.set(socket.id, { 
//         userId, 
//         assignmentId, 
//         clientId,
//         userRole: userRole || 'CLIENT',  
//         name: data.name || 'User',
//       });
      
//       // Join main assignment room (for general notifications)
//       const mainRoomName = `assignment-${assignmentId}`;
//       socket.join(mainRoomName);
      
//       // Join specific conversation rooms based on user role
//       const clientRoomName = `assignment-${assignmentId}-client`;
//       const writerRoomName = `assignment-${assignmentId}-writer`;
      
//       // Join appropriate rooms based on role
//       if (userRole === 'ADMIN') {
//         // Admin joins both conversation rooms to receive all messages
//         socket.join(clientRoomName);
//         socket.join(writerRoomName);
//       } else if (userRole === 'CLIENT') {
//         // Client only joins client conversation room
//         socket.join(clientRoomName);
//       } else if (userRole === 'WRITER') {
//         // Writer only joins writer conversation room
//         socket.join(writerRoomName);
//       }
      
//       // Track users in assignment room
//       if (!assignmentRooms.has(assignmentId)) {
//         assignmentRooms.set(assignmentId, new Set());
//       }
//       assignmentRooms.get(assignmentId).add(userId);
      
//       console.log(`User ${userId} (${userRole}) joined assignment ${assignmentId} rooms`);
      
//       // Notify others in the main room
//       socket.to(mainRoomName).emit('user-joined', {
//         userId,
//         assignmentId,
//         userRole,
//         timestamp: new Date().toISOString(),
//       });
//     });

//     // Handle sending messages with conversation context
//     socket.on('send-message', (data) => {
//       const { assignmentId, message, conversationType } = data;
//       const userInfo = activeUsers.get(socket.id);
      
//       if (!userInfo || userInfo.assignmentId !== assignmentId) {
//         console.log('Unauthorized message attempt from socket:', socket.id);
//         return;
//       }

//       // Determine which room to broadcast to based on conversation type and user role
//       let targetRoom;
      
//       if (userInfo.userRole === 'ADMIN') {
//         // Admin messages go to specific conversation rooms
//         if (conversationType === 'client') {
//           targetRoom = `assignment-${assignmentId}-client`;
//         } else if (conversationType === 'writer') {
//           targetRoom = `assignment-${assignmentId}-writer`;
//         } else {
//           // Fallback for malformed admin messages
//           targetRoom = `assignment-${assignmentId}`;
//         }
//       } else if (userInfo.userRole === 'CLIENT') {
//         // Client messages always go to client conversation room
//         targetRoom = `assignment-${assignmentId}-client`;
//       } else if (userInfo.userRole === 'WRITER') {
//         // Writer messages always go to writer conversation room
//         targetRoom = `assignment-${assignmentId}-writer`;
//       } else {
//         // Fallback for unknown roles
//         targetRoom = `assignment-${assignmentId}`;
//       }
      
//       // Add sender info to message for verification
//       const messageWithSenderInfo = {
//         ...message,
//         conversationType,
//         socketId: socket.id, // For debugging purposes
//         senderRole: userInfo.userRole
//       };
      
//       // Broadcast message to appropriate room
//       io.to(targetRoom).emit('new-message', messageWithSenderInfo);
      
//       console.log(`Message sent by ${userInfo.userRole} in assignment ${assignmentId} (${conversationType}) to room ${targetRoom}:`, message.content);
//     });

//     // Handle marking messages as read
//     socket.on('mark-messages-read', (data) => {
//       const { assignmentId, messageIds, conversationType } = data;
//       const userInfo = activeUsers.get(socket.id);
      
//       if (!userInfo || userInfo.assignmentId !== assignmentId) {
//         console.log('Unauthorized read attempt from socket:', socket.id);
//         return;
//       }

//       // Determine target room based on conversation type and user role
//       let targetRoom;
      
//       if (userInfo.userRole === 'ADMIN') {
//         if (conversationType === 'client') {
//           targetRoom = `assignment-${assignmentId}-client`;
//         } else if (conversationType === 'writer') {
//           targetRoom = `assignment-${assignmentId}-writer`;
//         } else {
//           targetRoom = `assignment-${assignmentId}`;
//         }
//       } else if (userInfo.userRole === 'CLIENT') {
//         targetRoom = `assignment-${assignmentId}-client`;
//       } else if (userInfo.userRole === 'WRITER') {
//         targetRoom = `assignment-${assignmentId}-writer`;
//       } else {
//         targetRoom = `assignment-${assignmentId}`;
//       }
      
//       // Broadcast read status to appropriate room
//       messageIds.forEach(messageId => {
//         io.to(targetRoom).emit('message-read', { 
//           messageId,
//           conversationType,
//           readBy: userInfo.userId,
//           readByRole: userInfo.userRole
//         });
//       });
      
//       console.log(`Messages marked as read by ${userInfo.userRole} in assignment ${assignmentId} (${conversationType}):`, messageIds);
//     });

//     // Handle user typing with conversation context
//     socket.on('typing', (data) => {
//       const { assignmentId, isTyping, conversationType } = data;
//       const userInfo = activeUsers.get(socket.id);
      
//       if (!userInfo || userInfo.assignmentId !== assignmentId) {
//         return;
//       }

//       // Determine target room based on conversation type and user role
//       let targetRoom;
      
//       if (userInfo.userRole === 'ADMIN') {
//         if (conversationType === 'client') {
//           targetRoom = `assignment-${assignmentId}-client`;
//         } else if (conversationType === 'writer') {
//           targetRoom = `assignment-${assignmentId}-writer`;
//         } else {
//           targetRoom = `assignment-${assignmentId}`;
//         }
//       } else if (userInfo.userRole === 'CLIENT') {
//         targetRoom = `assignment-${assignmentId}-client`;
//       } else if (userInfo.userRole === 'WRITER') {
//         targetRoom = `assignment-${assignmentId}-writer`;
//       } else {
//         targetRoom = `assignment-${assignmentId}`;
//       }
      
//       // CLAUDE: Manage typing users state
//       const roomKey = `${targetRoom}-${userInfo.userId}`;
      
//       if (isTyping) {
//         // Add user to typing list with auto-cleanup after 3 seconds
//         typingUsers.set(roomKey, {
//           userId: userInfo.userId,
//           userRole: userInfo.userRole,
//           targetRoom,
//           conversationType,
//           timestamp: Date.now()
//         });
        
//         // Auto-cleanup typing status after 3 seconds
//         setTimeout(() => {
//           const typingInfo = typingUsers.get(roomKey);
//           if (typingInfo && Date.now() - typingInfo.timestamp >= 3000) {
//             typingUsers.delete(roomKey);
//             socket.to(targetRoom).emit('user-typing', {
//               userId: userInfo.userId,
//               userRole: userInfo.userRole,
//               isTyping: false,
//               conversationType,
//               name: userInfo.name,
//               timestamp: new Date().toISOString(),
//             });
//           }
//         }, 3000);
//       } else {
//         // Remove user from typing list
//         typingUsers.delete(roomKey);
//       }
      
//       // Broadcast typing status to others in the appropriate room
//       socket.to(targetRoom).emit('user-typing', {
//         userId: userInfo.userId,
//         userRole: userInfo.userRole,
//         isTyping,
//         conversationType,
//         name: userInfo.name,
//         timestamp: new Date().toISOString(),
//       });
      
//       console.log(`User ${userInfo.userId} (${userInfo.userRole}) ${isTyping ? 'started' : 'stopped'} typing in ${conversationType} conversation for assignment ${assignmentId}`);
//     });

//     // Handle joining specific conversation room (for targeted messaging)
//     socket.on('join-conversation', (data) => {
//       const { assignmentId, conversationType } = data;
//       const userInfo = activeUsers.get(socket.id);
      
//       if (!userInfo || userInfo.assignmentId !== assignmentId) {
//         console.log('Unauthorized conversation join attempt from socket:', socket.id);
//         return;
//       }

//       // Join specific conversation room
//       const roomName = `assignment-${assignmentId}-${conversationType}`;
//       socket.join(roomName);
      
//       console.log(`User ${userInfo.userId} (${userInfo.userRole}) joined ${conversationType} conversation for assignment ${assignmentId}`);
//     });

//     // Handle leaving specific conversation room
//     socket.on('leave-conversation', (data) => {
//       const { assignmentId, conversationType } = data;
//       const userInfo = activeUsers.get(socket.id);
      
//       if (!userInfo || userInfo.assignmentId !== assignmentId) {
//         return;
//       }

//       // Leave specific conversation room
//       const roomName = `assignment-${assignmentId}-${conversationType}`;
//       socket.leave(roomName);
      
//       console.log(`User ${userInfo.userId} (${userInfo.userRole}) left ${conversationType} conversation for assignment ${assignmentId}`);
//     });

//     // Handle disconnection
//     socket.on('disconnect', () => {
//       const userInfo = activeUsers.get(socket.id);
      
//       if (userInfo) {
//         const { userId, assignmentId, userRole } = userInfo;
//         const mainRoomName = `assignment-${assignmentId}`;
        
//         // CLAUDE: Clean up typing status for this user
//         const typingKeysToDelete = [];
//         typingUsers.forEach((typingInfo, key) => {
//           if (typingInfo.userId === userId) {
//             typingKeysToDelete.push(key);
//             // Notify others that user stopped typing
//             socket.to(typingInfo.targetRoom).emit('user-typing', {
//               userId,
//               userRole,
//               isTyping: false,
//               conversationType: typingInfo.conversationType,
//               timestamp: new Date().toISOString(),
//             });
//           }
//         });
//         typingKeysToDelete.forEach(key => typingUsers.delete(key));
        
//         // Remove user from tracking
//         activeUsers.delete(socket.id);
        
//         if (assignmentRooms.has(assignmentId)) {
//           assignmentRooms.get(assignmentId).delete(userId);
          
//           // Clean up empty rooms
//           if (assignmentRooms.get(assignmentId).size === 0) {
//             assignmentRooms.delete(assignmentId);
//           }
//         }
        
//         // Notify others in the main room
//         socket.to(mainRoomName).emit('user-left', {
//           userId,
//           assignmentId,
//           userRole,
//           timestamp: new Date().toISOString(),
//         });
        
//         console.log(`User ${userId} (${userRole}) disconnected from assignment ${assignmentId}`);
//       }
      
//       console.log('User disconnected:', socket.id);
//     });

//     // Handle errors
//     socket.on('error', (error) => {
//       console.error('Socket error:', error);
//       const userInfo = activeUsers.get(socket.id);
//       if (userInfo) {
//         console.error('Error from user:', userInfo.userId, 'Role:', userInfo.userRole);
//       }
//     });
//   });

//   // Start Socket.IO server
//   httpServer.listen(socketPort, () => {
//     console.log(`Socket.IO server running on http://localhost:${socketPort}`);
//   });

//   // Log active connections periodically with enhanced debugging
//   setInterval(() => {
//     console.log(`Active connections: ${activeUsers.size}`);
//     console.log(`Active assignment rooms: ${assignmentRooms.size}`);
    
//     // Log room details for debugging
//     if (activeUsers.size > 0) {
//       console.log('Active users by assignment and role:');
//       const assignmentUsersByRole = new Map();
      
//       activeUsers.forEach((userInfo, socketId) => {
//         const { assignmentId, userId, userRole } = userInfo;
//         if (!assignmentUsersByRole.has(assignmentId)) {
//           assignmentUsersByRole.set(assignmentId, {
//             ADMIN: [],
//             CLIENT: [],
//             WRITER: []
//           });
//         }
        
//         if (assignmentUsersByRole.get(assignmentId)[userRole]) {
//           assignmentUsersByRole.get(assignmentId)[userRole].push(`${userId}(${socketId.substring(0, 6)})`);
//         }
//       });
      
//       assignmentUsersByRole.forEach((roleUsers, assignmentId) => {
//         // CLAUDE: Fix the '[object Object]' issue by ensuring assignmentId is a string
//         const assignmentIdStr = typeof assignmentId === 'string' ? assignmentId : String(assignmentId);
//         console.log(`  Assignment ${assignmentIdStr}:`);
//         console.log(`    Admins: ${roleUsers.ADMIN.join(', ') || 'none'}`);
//         console.log(`    Clients: ${roleUsers.CLIENT.join(', ') || 'none'}`);
//         console.log(`    Writers: ${roleUsers.WRITER.join(', ') || 'none'}`);
//       });
//     }
//   }, 30000); // Every 30 seconds
// });

// // Handle graceful shutdown
// process.on('SIGTERM', () => {
//   console.log('SIGTERM received, shutting down gracefully');
//   process.exit(0);
// });

// process.on('SIGINT', () => {
//   console.log('SIGINT received, shutting down gracefully');
//   process.exit(0);
// });