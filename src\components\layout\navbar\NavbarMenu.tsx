"use client";

import { FC, useState, useEffect } from "react";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Menu } from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface NavbarMenuProps {
  className?: string;
}

const NavbarMenu: FC<NavbarMenuProps> = ({ className }) => {
  const [open, setOpen] = useState(false);
  const [isMediumScreen, setIsMediumScreen] = useState(false);

  // Check if we're on a medium screen (tablet) to adjust the menu items
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMediumScreen(window.innerWidth >= 768 && window.innerWidth < 1024);
    };

    // Set initial value
    checkScreenSize();

    // Add event listener
    window.addEventListener("resize", checkScreenSize);

    // Cleanup
    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  // Core menu items that should always be in the mobile menu
  const coreMenuItems = [
    {
      title: "Services",
      items: [
        {
          label: "Custom Essay Writing",
          href: "/services/custom-essay-writing",
        },
        { label: "Dissertation", href: "/services/dissertation" },
        { label: "Research Paper", href: "/services/research-paper" },
        { label: "Literature Review", href: "/services/literature-review" },
        { label: "Term Paper", href: "/services/term-paper" },
        { label: "View all Services", href: "/services" },
      ],
    },
  ];

  // Additional items that should only appear in mobile menu on small screens
  const additionalMenuItems = [
    {
      title: "Company",
      items: [
        { label: "About Us", href: "/about-us" },
        { label: "Contact Us", href: "/contact-us" },
        { label: "Testimonials", href: "/testimonials" },
        { label: "Careers", href: "/careers" },
      ],
    },
  ];

  // Single menu items (without dropdowns)
  const singleMenuItems = [
    { label: "Home", href: "/" },
    { label: "Why Us", href: "/why-us" },
  ];

  // Determine which menu items to display based on screen size
  const menuItems = isMediumScreen
    ? coreMenuItems
    : [...coreMenuItems, ...additionalMenuItems];

  return (
    <div className={cn(className)}>
      <Sheet open={open} onOpenChange={setOpen}>
        <SheetTrigger asChild>
          <Button
            size="icon"
            variant="ghost"
            className="p-1 h-8 w-8 sm:h-10 sm:w-10"
          >
            <Menu className="h-5 w-5" />
            <span className="sr-only">Toggle menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="right" className="w-[80vw] max-w-[400px]">
          <nav className="flex flex-col gap-2 py-4">
            {/* Single menu items */}
            {singleMenuItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className="px-4 py-2 text-base text-foreground hover:text-primary transition-colors"
                onClick={() => setOpen(false)}
              >
                {item.label}
              </Link>
            ))}

            {/* Accordion menu items */}
            {menuItems.map((section) => (
              <Accordion
                key={section.title}
                type="single"
                collapsible
                className="w-full"
              >
                <AccordionItem value={section.title}>
                  <AccordionTrigger className="text-base">
                    {section.title}
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="flex flex-col gap-2 pl-4">
                      {section.items.map((item) => (
                        <Link
                          key={item.href}
                          href={item.href}
                          className="py-2 text-sm text-foreground hover:text-primary transition-colors"
                          onClick={() => setOpen(false)}
                        >
                          {item.label}
                        </Link>
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            ))}

            {/* Always show FAQ link in the mobile menu */}
            <Link
              href="/help"
              className="px-4 py-2 text-base text-foreground hover:text-primary transition-colors"
              onClick={() => setOpen(false)}
            >
              FAQ
            </Link>
          </nav>
        </SheetContent>
      </Sheet>
    </div>
  );
};

export { NavbarMenu };
