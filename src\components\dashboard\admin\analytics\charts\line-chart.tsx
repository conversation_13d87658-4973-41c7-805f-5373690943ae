"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
} from "recharts";

interface LineChartProps {
  title: string;
  description?: string;
  data: Array<{
    date: string;
    [key: string]: string | number;
  }>;
  categories: Array<{
    name: string;
    color: string;
  }>;
  className?: string;
}

export function LineChartComponent({
  title,
  description,
  data,
  categories,
  className,
}: LineChartProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent className="pl-2">
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={data}
              margin={{
                top: 5,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="date" 
                tickFormatter={(value) => {
                  const date = new Date(value);
                  return `${date.getMonth() + 1}/${date.getFullYear()}`;
                }}
              />
              <YAxis />
              <Tooltip 
                formatter={(value: number, name: string) => [`${value}`, name]}
                labelFormatter={(label) => {
                  const date = new Date(label);
                  return `${date.toLocaleString('default', { month: 'long' })} ${date.getFullYear()}`;
                }}
              />
              <Legend />
              {categories.map((category, index) => (
                <Line
                  key={index}
                  type="monotone"
                  dataKey={category.name}
                  stroke={category.color}
                  activeDot={{ r: 8 }}
                  strokeWidth={2}
                />
              ))}
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
