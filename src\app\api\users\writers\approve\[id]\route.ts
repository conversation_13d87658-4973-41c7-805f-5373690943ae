// src/app/api/users/writers/approve/[id]/route.ts
import { NextRequest } from "next/server";
import type { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import {
  apiSuccess,
  apiError,
  parseRequestBody,
  checkPermission,
  safeJsonDate,
  isApiError, // <-- import the new guard
} from "@/lib/api-utils";
import { writerApprovalSchema } from "@/lib/validations";
import type { UserResponse } from "@/types/api";

interface RouteParams {
  params: Promise<{ id: string }>;
}

export async function PUT(
  req: NextRequest,
  { params }: RouteParams
): Promise<NextResponse> {
  const { id } = await params;

  // Only ADMINs may approve/unapprove
  const permErr = await checkPermission(["ADMIN"]);
  if (permErr) return permErr;

  // parseRequestBody can return ApiError or { isApproved: boolean }
  const result = await parseRequestBody(req, writerApprovalSchema);

  // GPT WRITERS: - early-return on error
  if (isApiError(result)) {
    return apiError(result.message, 400, result.errors);
  }

  // Here TS knows `result` is { isApproved: boolean }
  const { isApproved } = result;

  try {
    const existing = await prisma.user.findUnique({
      where: { id, role: "WRITER" },
    });
    if (!existing) {
      return apiError("Writer not found", 404);
    }

    const updated = await prisma.user.update({
      where: { id },
      data: { isApproved }, // safe boolean
      select: {
        id: true,
        email: true,
        name: true,
        phone: true,
        role: true,
        isApproved: true,
        createdAt: true,
        updatedAt: true,
        image: true,
      },
    });

    const msg = isApproved
      ? "Writer approved successfully"
      : "Writer unapproved successfully";

    return apiSuccess(safeJsonDate(updated) as UserResponse, msg);
  } catch (error) {
    console.error("Error updating writer approval:", error);
    return apiError("Failed to update writer approval status", 500);
  }
}
