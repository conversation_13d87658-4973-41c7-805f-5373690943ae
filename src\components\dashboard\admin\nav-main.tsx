// src/components/dashboard/admin/nav-main.tsx
"use client";

import { IconCirclePlusFilled, type Icon } from "@tabler/icons-react";
import Link from "next/link";

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { ThemeToggle } from "@/components/theme-toggle";
import { QuickCreateAssignment } from "@/components/dashboard/admin/quick-create-assignment";
import { eventEmitter } from "@/lib/events";

export function NavMain({
  items,
}: {
  items: {
    title: string;
    url: string;
    icon?: Icon;
  }[];
}) {
  // No longer need to fetch clients here since QuickCreateAssignment handles its own client fetching

  const handleAssignmentCreated = () => {
    // Emit an event when assignment is created
    eventEmitter.emit("assignmentCreated");
  };

  return (
    <SidebarGroup>
      <SidebarGroupContent className="flex flex-col gap-2">
        <SidebarMenu>
          <SidebarMenuItem className="flex items-center gap-2">
            <QuickCreateAssignment
              onSuccess={handleAssignmentCreated}
            >
              <SidebarMenuButton
                tooltip="Quick Create order"
                className="bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground active:bg-primary/90 active:text-primary-foreground min-w-8 duration-200 ease-linear"
              >
                <IconCirclePlusFilled />
                <span>Quick Create Order</span>
              </SidebarMenuButton>
            </QuickCreateAssignment>
            <div className="size-8 group-data-[collapsible=icon]:opacity-0">
              <ThemeToggle />
              <span className="sr-only">Toggle theme</span>
            </div>
          </SidebarMenuItem>
        </SidebarMenu>
        <SidebarMenu>
          {items.map((item) => (
            <SidebarMenuItem key={item.title}>
              <Link href={item.url} passHref legacyBehavior>
                <SidebarMenuButton tooltip={item.title} asChild>
                  <a>
                    {item.icon && <item.icon />}
                    <span>{item.title}</span>
                  </a>
                </SidebarMenuButton>
              </Link>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );
}
