// components/footer/SubscribeForm.tsx
'use client';

import { NewsletterSubscription } from '@/components/newsletter/newsletter-subscription';

export function SubscribeForm() {
  return (
    <div className="w-full max-w-md">
      <h3 className="text-lg font-medium mb-2">Subscribe to Our Newsletter</h3>
      <p className="text-sm text-muted-foreground mb-4">Get the latest updates and exclusive content!</p>

      <NewsletterSubscription
        source="footer"
        placeholder="Your email address"
        buttonText="Subscribe"
        variant="inline"
        showIcon={false}
        className="w-full"
      />

      <p className="text-xs text-muted-foreground mt-2">No spam, unsubscribe at any time.</p>
    </div>
  );
}
