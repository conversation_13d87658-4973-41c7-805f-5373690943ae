import React from 'react';
import { Heading } from '@/components/ui/heading';
import { Badge } from '@/components/ui/badge';

interface BlogCategoryHeaderProps {
  category: {
    id: string;
    name: string;
    slug: string;
  };
  postCount: number;
}

export function BlogCategoryHeader({ category, postCount }: BlogCategoryHeaderProps) {
  return (
    <div className="mb-8">
      <div className="flex flex-col gap-2">
        <Heading as="h1" size="3xl" className="font-bold">
          {category.name}
        </Heading>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="px-3 py-1 text-sm">
            {postCount} {postCount === 1 ? 'post' : 'posts'}
          </Badge>
        </div>
        <p className="text-muted-foreground mt-2 max-w-2xl">
          Browse all articles in the {category.name.toLowerCase()} category. Find expert advice, tips, and insights to help you succeed.
        </p>
      </div>
    </div>
  );
}
