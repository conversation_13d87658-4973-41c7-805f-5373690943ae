"use client"; 

import React from 'react';
import {  Shield, Clock, CreditCard, Alert<PERSON>riangle, CheckCircle, XCircle, HelpCircle } from 'lucide-react';


// Server Component
const RefundPolicyPage = () => {
  return (
    <div className="min-h-screen bg-background">
     
      {/* Hero Section */}
      <section className="w-full py-8 md:py-12 lg:py-16 xl:py-24">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center space-y-4 text-center">
            <div className="space-y-2">
              <div className="inline-block rounded-lg bg-muted px-3 py-1 text-sm">
                Legal Information
              </div>
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl/none">
                Refund Policy
              </h1>
              <p className="mx-auto max-w-[700px] text-muted-foreground md:text-xl">
                Understanding our refund terms and conditions for academic writing services. 
                We&rdquo;re committed to fair and transparent refund practices.
              </p>
            </div>
            <div className="space-x-4">
            <button 
                onClick={() => {
                  const overviewElement = document.getElementById('overview');
                  if (overviewElement) {
                    window.scrollTo({ 
                      top: overviewElement.offsetTop,
                      behavior: 'smooth' 
                    });
                  }
                }}
                className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
              >
                Read Policy
              </button>
              <button 
                onClick={() => {
                  const contactElement = document.getElementById('contact');
                  if (contactElement) {
                    window.scrollTo({ 
                      top: contactElement.offsetTop,
                      behavior: 'smooth' 
                    });
                  }
                }}
                className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
              >
                Get Support
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Last Updated */}
        <div className="mb-8 p-4 bg-muted rounded-lg">
          <p className="text-sm text-muted-foreground">
            <Clock className="inline h-4 w-4 mr-1" />
            Last updated: January 15, 2025
          </p>
        </div>

        {/* Overview Section */}
        <section id="overview" className="mb-12">
          <div className="flex items-center mb-6">
            <Shield className="h-6 w-6 mr-3 text-primary" />
            <h2 className="text-2xl font-bold">Refund Policy Overview</h2>
          </div>
          
          <div className="prose prose-gray dark:prose-invert max-w-none">
            <p className="text-muted-foreground mb-4">
              At our academic writing platform, we strive to provide high-quality services and ensure customer satisfaction. 
              This refund policy outlines the circumstances under which refunds may be requested and processed.
            </p>
            
            <div className="grid md:grid-cols-3 gap-6 my-8">
              <div className="border rounded-lg p-6">
                <CheckCircle className="h-8 w-8 text-green-500 mb-3" />
                <h3 className="font-semibold mb-2">Quality Guarantee</h3>
                <p className="text-sm text-muted-foreground">
                  We guarantee work that meets academic standards and assignment requirements.
                </p>
              </div>
              <div className="border rounded-lg p-6">
                <Clock className="h-8 w-8 text-blue-500 mb-3" />
                <h3 className="font-semibold mb-2">Timely Delivery</h3>
                <p className="text-sm text-muted-foreground">
                  Orders delivered on time as per agreed deadlines or eligible for refund.
                </p>
              </div>
              <div className="border rounded-lg p-6">
                <CreditCard className="h-8 w-8 text-purple-500 mb-3" />
                <h3 className="font-semibold mb-2">Fair Pricing</h3>
                <p className="text-sm text-muted-foreground">
                  Transparent pricing with refund options for unsatisfactory services.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Refund Eligibility */}
        <section id="eligibility" className="mb-12">
          <div className="flex items-center mb-6">
            <CheckCircle className="h-6 w-6 mr-3 text-primary" />
            <h2 className="text-2xl font-bold">Refund Eligibility</h2>
          </div>

          <div className="space-y-6">
            {/* Full Refund Cases */}
            <div className="border rounded-lg p-6 bg-green-50 dark:bg-green-950/20 border-green-200 dark:border-green-800">
              <h3 className="font-semibold text-green-800 dark:text-green-200 mb-3 flex items-center">
                <CheckCircle className="h-5 w-5 mr-2" />
                100% Refund Eligible
              </h3>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Order not delivered by the agreed deadline
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Work delivered does not match assignment requirements
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Plagiarized content detected (above 15% similarity)
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  No writer assigned within 24 hours of order placement  
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Order cancelled by customer within 1 hour of placement
                </li>
              </ul>
            </div>

            {/* Partial Refund Cases */}
            <div className="border rounded-lg p-6 bg-yellow-50 dark:bg-yellow-950/20 border-yellow-200 dark:border-yellow-800">
              <h3 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-3 flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2" />
                Partial Refund Eligible (50-75%)
              </h3>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-yellow-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Work requires substantial revisions beyond normal editing
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-yellow-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Delivered work partially meets requirements but has significant issues
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-yellow-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Order cancelled after work has begun but before 50% completion
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-yellow-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Quality issues that can be resolved with major revisions
                </li>
              </ul>
            </div>

            {/* No Refund Cases */}
            <div className="border rounded-lg p-6 bg-red-50 dark:bg-red-950/20 border-red-200 dark:border-red-800">
              <h3 className="font-semibold text-red-800 dark:text-red-200 mb-3 flex items-center">
                <XCircle className="h-5 w-5 mr-2" />
                No Refund Applicable
              </h3>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-red-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Order completed and delivered as per requirements
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-red-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Change of mind after order completion
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-red-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Refund request made after 14 days of order completion
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-red-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Customer provided incorrect or incomplete requirements
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-red-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Order used or submitted before requesting refund
                </li>
              </ul>
            </div>
          </div>
        </section>

        {/* Refund Process */}
        <section id="process" className="mb-12">
          <div className="flex items-center mb-6">
            <CreditCard className="h-6 w-6 mr-3 text-primary" />
            <h2 className="text-2xl font-bold">Refund Process</h2>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h3 className="font-semibold mb-4">How to Request a Refund</h3>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-semibold">1</div>
                  <div>
                    <p className="font-medium">Submit Request</p>
                    <p className="text-sm text-muted-foreground">Contact our support team within 14 days of order completion</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-semibold">2</div>
                  <div>
                    <p className="font-medium">Provide Details</p>
                    <p className="text-sm text-muted-foreground">Include order number, specific issues, and supporting evidence</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-semibold">3</div>
                  <div>
                    <p className="font-medium">Review Process</p>
                    <p className="text-sm text-muted-foreground">Our team reviews your case within 48-72 hours</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-semibold">4</div>
                  <div>
                    <p className="font-medium">Resolution</p>
                    <p className="text-sm text-muted-foreground">Refund processed or alternative solution provided</p>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-semibold mb-4">Processing Times</h3>
              <div className="space-y-4">
                <div className="border rounded p-4">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">PayPal</span>
                    <span className="text-sm text-muted-foreground">3-5 business days</span>
                  </div>
                </div>
                <div className="border rounded p-4">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Credit/Debit Card</span>
                    <span className="text-sm text-muted-foreground">5-10 business days</span>
                  </div>
                </div>
                <div className="border rounded p-4">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Bank Transfer</span>
                    <span className="text-sm text-muted-foreground">7-14 business days</span>
                  </div>
                </div>
                <div className="border rounded p-4">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Store Credit</span>
                    <span className="text-sm text-muted-foreground">Instant</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Writer Compensation */}
        <section className="mb-12">
          <div className="flex items-center mb-6">
            <HelpCircle className="h-6 w-6 mr-3 text-primary" />
            <h2 className="text-2xl font-bold">Writer Compensation & Refunds</h2>
          </div>
          
          <div className="bg-muted rounded-lg p-6">
            <p className="mb-4">
              Our platform ensures fair treatment for both customers and writers:
            </p>
            <ul className="space-y-2 text-sm">
              <li className="flex items-start">
                <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
                Writers are compensated for completed work that meets requirements
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
                Partial compensation for work completed before order cancellation
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
                No compensation for substandard or plagiarized work
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
                Dispute resolution process protects both parties&rdquo; interests
              </li>
            </ul>
          </div>
        </section>

        {/* Contact Section */}
        <section id="contact" className="mb-12">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">Need Help with a Refund?</h2>
            <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
              Our customer support team is here to help you with any refund-related questions or concerns. 
              We&rdquo;re committed to resolving issues fairly and promptly.
            </p>
            
            <div className="grid md:grid-cols-3 gap-6">
              <div className="border rounded-lg p-6 text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <HelpCircle className="h-6 w-6 text-primary" />
                </div>
                <h3 className="font-semibold mb-2">Live Chat</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Get instant help from our support team
                </p>
                <button className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-3">
                  Start Chat
                </button>
              </div>
              
              <div className="border rounded-lg p-6 text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <CreditCard className="h-6 w-6 text-primary" />
                </div>
                <h3 className="font-semibold mb-2">Email Support</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Send us detailed information about your refund request
                </p>
                <button className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3">
                  Send Email
                </button>
              </div>
              
              <div className="border rounded-lg p-6 text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Shield className="h-6 w-6 text-primary" />
                </div>
                <h3 className="font-semibold mb-2">Submit Ticket</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Create a support ticket for complex issues
                </p>
                <button className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3">
                  Create Ticket
                </button>
              </div>
            </div>
          </div>
        </section>

        {/* Legal Footer */}
        <div className="border-t pt-8">
          <div className="bg-muted rounded-lg p-6">
            <p className="text-sm text-muted-foreground">
              <strong>Legal Notice:</strong> This refund policy is subject to change without prior notice. 
              Any changes will be effective immediately upon posting on our website. 
              For the most current version of our refund policy, please refer to this page. 
              By using our services, you agree to be bound by the current version of this policy.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RefundPolicyPage;