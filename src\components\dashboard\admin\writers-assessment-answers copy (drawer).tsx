// "use client";

// import React, { useState, useEffect } from "react";
// import {
//   Drawer,
//   DrawerContent,
//   DrawerDescription,
//   DrawerHeader,
//   DrawerTitle,
// } from "@/components/ui/drawer";
// import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
// import { Button } from "@/components/ui/button";
// import { Badge } from "@/components/ui/badge";
// import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
// import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
// import { Separator } from "@/components/ui/separator";
// import { ScrollArea } from "@/components/ui/scroll-area";
// import {
//   User,
//   Mail,
//   CheckCircle,
//   XCircle,
//   Copy,
//   Loader2,
//   Award,
//   FileText,
//   GraduationCap,
//   Clock,
// } from "lucide-react";
// import Image from "next/image";

// interface WriterAnswer {
//   writerId: string;
//   multipleChoiceAnswers: string[];
//   essayText: string;
//   status?: "Passed" | "Failed";
// }

// interface Assessment {
//   id: string;
//   title: string;
//   multipleChoiceQuiz: Array<{
//     question: string;
//     options: string[];
//     correctAnswer: string;
//   }>;
//   essayExam: {
//     topic: string;
//     rubrics: string;
//   };
//   writersAnswers: WriterAnswer[];
// }

// interface WriterInfo {
//   id: string;
//   name: string;
//   email: string;
//   imageUrl?: string;
// }

// interface WriterAssessmentAnswersProps {
//   isOpen: boolean;
//   onOpenChange: (open: boolean) => void;
//   writerId: string;
//   assessment: Assessment;
//   writerAnswer: WriterAnswer;
//   onStatusUpdate: (status: "Passed" | "Failed") => void;
// }

// export function WriterAssessmentAnswers({
//   isOpen,
//   onOpenChange,
//   writerId,
//   assessment,
//   writerAnswer,
//   onStatusUpdate,
// }: WriterAssessmentAnswersProps) {
//   const [writerInfo, setWriterInfo] = useState<WriterInfo | null>(null);
//   const [loadingWriter, setLoadingWriter] = useState(false);
//   const [writerError, setWriterError] = useState<string | null>(null);
//   const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);

//   // Fetch writer information
//   useEffect(() => {
//     if (isOpen && writerId) {
//       const fetchWriterInfo = async () => {
//         setLoadingWriter(true);
//         setWriterError(null);
//         try {
//           console.log("🔄 Fetching writer info for:", writerId);
//           const response = await fetch(`/api/users/writers/${writerId}`, {
//             method: "GET",
//             headers: {
//               "Content-Type": "application/json",
//             },
//             credentials: "include",
//           });

//           if (!response.ok) {
//             throw new Error(`Failed to fetch writer info: ${response.status}`);
//           }

//           const writer = await response.json();
//           console.log("✅ Fetched writer info:", writer);
//           setWriterInfo(writer);
//         } catch (err) {
//           console.error("❌ Error fetching writer info:", err);
//           setWriterError("Failed to load writer information");
//         } finally {
//           setLoadingWriter(false);
//         }
//       };

//       fetchWriterInfo();
//     }
//   }, [isOpen, writerId]);

//   // Calculate multiple choice score
//   const calculateScore = () => {
//     const answers = Array.isArray(writerAnswer.multipleChoiceAnswers)
//       ? writerAnswer.multipleChoiceAnswers
//       : [];

//     const correctAnswers = answers.filter(
//       (answer, index) =>
//         answer === assessment.multipleChoiceQuiz[index]?.correctAnswer
//     ).length;
//     const totalQuestions = assessment.multipleChoiceQuiz.length;
//     return {
//       correct: correctAnswers,
//       total: totalQuestions,
//       percentage:
//         totalQuestions > 0
//           ? Math.round((correctAnswers / totalQuestions) * 100)
//           : 0,
//     };
//   };

//   const score = calculateScore();

//   const copyEssayText = async () => {
//     try {
//       const textToCopy = writerAnswer.essayText || "No essay submitted";
//       await navigator.clipboard.writeText(textToCopy);
//       alert("Essay text copied to clipboard");
//     } catch (err) {
//       console.error("Failed to copy text:", err);
//       alert("Failed to copy text");
//     }
//   };

//   const handleStatusUpdate = async (status: "Passed" | "Failed") => {
//     setIsUpdatingStatus(true);
//     try {
//       const response = await fetch(
//         `/api/admin/assessments/${assessment.id}/writers/${writerId}/status`,
//         {
//           method: "PATCH",
//           headers: {
//             "Content-Type": "application/json",
//           },
//           credentials: "include",
//           body: JSON.stringify({ status }),
//         }
//       );

//       if (!response.ok) {
//         throw new Error("Failed to update status");
//       }

//       onStatusUpdate(status);
//       alert(`Status updated to ${status}`);
//     } catch (err) {
//       console.error("Error updating status:", err);
//       alert("Failed to update status");
//     } finally {
//       setIsUpdatingStatus(false);
//     }
//   };

//   const getScoreBadgeColor = (percentage: number) => {
//     if (percentage >= 80)
//       return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
//     if (percentage >= 60)
//       return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
//     return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
//   };

//   const getStatusBadge = () => {
//     if (!writerAnswer.status) return null;

//     return (
//       <Badge
//         variant={writerAnswer.status === "Passed" ? "default" : "destructive"}
//         className="flex items-center gap-1"
//       >
//         {writerAnswer.status === "Passed" ? (
//           <CheckCircle className="h-3 w-3" />
//         ) : (
//           <XCircle className="h-3 w-3" />
//         )}
//         {writerAnswer.status}
//       </Badge>
//     );
//   };

//   return (
//     <Drawer open={isOpen} onOpenChange={onOpenChange}>
//       <DrawerContent className="h-[95vh] max-w-none">
//         <div className="mx-auto w-full max-w-6xl h-full flex flex-col">
//           <DrawerHeader className="border-b">
//             <DrawerTitle className="flex items-center gap-2 justify-between">
//               <div className="flex items-center gap-2">
//                 <GraduationCap className="h-5 w-5" />
//                 Assessment Review
//                 {getStatusBadge()}
//               </div>
//               <div className="flex items-center gap-2">
//                 <Badge
//                   className={`${getScoreBadgeColor(score.percentage)} font-semibold`}
//                 >
//                   Score: {score.percentage}%
//                 </Badge>
//               </div>
//             </DrawerTitle>
//             <DrawerDescription>
//               Review and evaluate {writerInfo?.name || "writer"}&apos;s
//               assessment submission for &quot;{assessment.title}&quot;
//             </DrawerDescription>
//           </DrawerHeader>

//           <div className="flex-1 overflow-hidden">
//             {/* Writer Information Section */}
//             <div className="px-6 py-4 border-b bg-muted/30">
//               {loadingWriter ? (
//                 <div className="flex items-center gap-2">
//                   <Loader2 className="h-4 w-4 animate-spin" />
//                   <span className="text-sm text-muted-foreground">
//                     Loading writer information...
//                   </span>
//                 </div>
//               ) : writerError ? (
//                 <div className="text-sm text-destructive">{writerError}</div>
//               ) : writerInfo ? (
//                 <div className="flex items-center gap-4">
//                   <Avatar className="h-12 w-12">
//                     <AvatarImage
//                       src={writerInfo.imageUrl}
//                       alt={writerInfo.name}
//                     />
//                     <AvatarFallback>
//                       {/* {writerInfo.name.charAt(0).toUpperCase()} */}
//                       <Image
//                         src="/avatars/shadcn.jpg"
//                         alt="Avatar"
//                         width={48}
//                         height={48}
//                       />
//                     </AvatarFallback>
//                   </Avatar>
//                   <div className="flex-1">
//                     <div className="flex items-center gap-2 mb-1">
//                       <User className="h-4 w-4 text-muted-foreground" />
//                       <span className="font-medium">{writerInfo.name}</span>
//                     </div>
//                     <div className="flex items-center gap-2">
//                       <Mail className="h-4 w-4 text-muted-foreground" />
//                       <span className="text-sm text-muted-foreground">
//                         {writerInfo.email}
//                       </span>
//                     </div>
//                   </div>
//                   <div className="text-right">
//                     <p className="text-sm text-muted-foreground">Performance</p>
//                     <p className="text-lg font-semibold">
//                       {score.correct}/{score.total}
//                     </p>
//                   </div>
//                 </div>
//               ) : null}
//             </div>

//             {/* Assessment Content */}
//             <div className="flex-1 overflow-hidden">
//               <Tabs
//                 defaultValue="multiple-choice"
//                 className="h-full flex flex-col"
//               >
//                 <div className="px-6 py-2 border-b">
//                   <TabsList className="grid w-full grid-cols-2">
//                     <TabsTrigger
//                       value="multiple-choice"
//                       className="flex items-center gap-2"
//                     >
//                       <Award className="h-4 w-4" />
//                       Multiple Choice ({assessment.multipleChoiceQuiz.length})
//                     </TabsTrigger>
//                     <TabsTrigger
//                       value="essay"
//                       className="flex items-center gap-2"
//                     >
//                       <FileText className="h-4 w-4" />
//                       Essay Review
//                     </TabsTrigger>
//                   </TabsList>
//                 </div>

//                 <TabsContent
//                   value="multiple-choice"
//                   className="flex-1 overflow-hidden px-6 py-4"
//                 >
//                   <ScrollArea className="h-full">
//                     <div className="space-y-6 pr-4">
//                       {assessment.multipleChoiceQuiz.map((question, index) => {
//                         const answers = Array.isArray(
//                           writerAnswer.multipleChoiceAnswers
//                         )
//                           ? writerAnswer.multipleChoiceAnswers
//                           : [];
//                         const userAnswer = answers[index] || "";
//                         const isCorrect = userAnswer === question.correctAnswer;

//                         return (
//                           <Card
//                             key={index}
//                             className={`border-l-4 ${isCorrect ? "border-l-green-500" : "border-l-red-500"}`}
//                           >
//                             <CardHeader className="pb-3">
//                               <div className="flex items-start gap-3">
//                                 <div className="flex-shrink-0 mt-1">
//                                   {isCorrect ? (
//                                     <CheckCircle className="h-5 w-5 text-green-600" />
//                                   ) : (
//                                     <XCircle className="h-5 w-5 text-red-600" />
//                                   )}
//                                 </div>
//                                 <div className="flex-1">
//                                   <div className="flex items-center justify-between mb-2">
//                                     <CardTitle className="text-base">
//                                       Question {index + 1}
//                                     </CardTitle>
//                                     <Badge
//                                       variant={
//                                         isCorrect ? "default" : "destructive"
//                                       }
//                                     >
//                                       {isCorrect ? "Correct" : "Incorrect"}
//                                     </Badge>
//                                   </div>
//                                   <div className="text-sm text-muted-foreground whitespace-pre-wrap">
//                                     {question.question}
//                                   </div>
//                                 </div>
//                               </div>
//                             </CardHeader>
//                             <CardContent className="pt-0">
//                               <div className="space-y-3">
//                                 <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
//                                   {question.options.map(
//                                     (option, optionIndex) => (
//                                       <div
//                                         key={optionIndex}
//                                         className={`p-3 rounded-lg border text-sm ${
//                                           option === question.correctAnswer
//                                             ? "bg-green-50 border-green-200 text-green-800 dark:bg-green-950 dark:border-green-800 dark:text-green-300"
//                                             : option === userAnswer &&
//                                                 !isCorrect
//                                               ? "bg-red-50 border-red-200 text-red-800 dark:bg-red-950 dark:border-red-800 dark:text-red-300"
//                                               : "bg-muted/50 border-muted"
//                                         }`}
//                                       >
//                                         <div className="flex items-center justify-between">
//                                           <span>{option}</span>
//                                           <div className="flex gap-1">
//                                             {option ===
//                                               question.correctAnswer && (
//                                               <CheckCircle className="h-4 w-4 text-green-600" />
//                                             )}
//                                             {option === userAnswer &&
//                                               option !==
//                                                 question.correctAnswer && (
//                                                 <XCircle className="h-4 w-4 text-red-600" />
//                                               )}
//                                           </div>
//                                         </div>
//                                       </div>
//                                     )
//                                   )}
//                                 </div>

//                                 <Separator />

//                                 <div className="flex flex-wrap gap-4 text-sm">
//                                   <div className="flex items-center gap-2">
//                                     <span className="font-medium">
//                                       Writer&apos;s Answer:
//                                     </span>
//                                     <Badge
//                                       variant={
//                                         isCorrect ? "default" : "destructive"
//                                       }
//                                     >
//                                       {userAnswer || "No answer"}
//                                     </Badge>
//                                   </div>
//                                   {!isCorrect && (
//                                     <div className="flex items-center gap-2">
//                                       <span className="font-medium">
//                                         Correct Answer:
//                                       </span>
//                                       <Badge
//                                         variant="secondary"
//                                         className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
//                                       >
//                                         {question.correctAnswer}
//                                       </Badge>
//                                     </div>
//                                   )}
//                                 </div>
//                               </div>
//                             </CardContent>
//                           </Card>
//                         );
//                       })}
//                     </div>
//                   </ScrollArea>
//                 </TabsContent>

//                 <TabsContent
//                   value="essay"
//                   className="flex-1 overflow-hidden px-6 py-4"
//                 >
//                   <div className="h-full flex flex-col space-y-4">
//                     <Card>
//                       <CardHeader className="pb-3">
//                         <div className="flex items-center justify-between">
//                           <CardTitle className="text-lg flex items-center gap-2">
//                             <FileText className="h-5 w-5" />
//                             Essay Topic & Requirements
//                           </CardTitle>
//                           <Button
//                             variant="outline"
//                             size="sm"
//                             onClick={copyEssayText}
//                             className="flex items-center gap-2"
//                           >
//                             <Copy className="h-4 w-4" />
//                             Copy Essay
//                           </Button>
//                         </div>
//                       </CardHeader>
//                       <CardContent className="space-y-4">
//                         <div>
//                           <h4 className="font-medium mb-2 flex items-center gap-2">
//                             <FileText className="h-4 w-4" />
//                             Topic:
//                           </h4>
//                           <p className="text-sm text-muted-foreground p-3 bg-muted rounded-lg">
//                             {assessment.essayExam.topic}
//                           </p>
//                         </div>

//                         <div>
//                           <h4 className="font-medium mb-2 flex items-center gap-2">
//                             <Award className="h-4 w-4" />
//                             Rubrics:
//                           </h4>
//                           <p className="text-sm text-muted-foreground p-3 bg-muted rounded-lg">
//                             {assessment.essayExam.rubrics}
//                           </p>
//                         </div>
//                       </CardContent>
//                     </Card>

//                     <Card className="flex-1 overflow-hidden">
//                       <CardHeader className="pb-3">
//                         <CardTitle className="text-lg flex items-center gap-2">
//                           <User className="h-5 w-5" />
//                           Writer&apos;s Submission
//                           <Badge variant="outline" className="ml-auto">
//                             {writerAnswer.essayText?.length || 0} characters
//                           </Badge>
//                         </CardTitle>
//                       </CardHeader>
//                       <CardContent className="h-full overflow-hidden">
//                         <ScrollArea className="h-full border rounded-lg p-4">
//                           <div className="text-sm leading-relaxed whitespace-pre-wrap">
//                             {writerAnswer.essayText || (
//                               <div className="text-muted-foreground italic flex items-center gap-2">
//                                 <Clock className="h-4 w-4" />
//                                 No essay submitted
//                               </div>
//                             )}
//                           </div>
//                         </ScrollArea>
//                       </CardContent>
//                     </Card>
//                   </div>
//                 </TabsContent>
//               </Tabs>
//             </div>
//           </div>

//           {/* Action Buttons */}
//           <div className="px-6 py-4 border-t bg-background">
//             <div className="flex items-center justify-between">
//               <div className="flex items-center gap-2 text-sm text-muted-foreground">
//                 <Clock className="h-4 w-4" />
//                 Assessment Review Panel
//               </div>
//               <div className="flex items-center gap-3">
//                 <Button variant="outline" onClick={() => onOpenChange(false)}>
//                   Close
//                 </Button>
//                 <Button
//                   variant="destructive"
//                   onClick={() => handleStatusUpdate("Failed")}
//                   disabled={isUpdatingStatus}
//                   className="flex items-center gap-2"
//                 >
//                   {isUpdatingStatus ? (
//                     <Loader2 className="h-4 w-4 animate-spin" />
//                   ) : (
//                     <XCircle className="h-4 w-4" />
//                   )}
//                   Fail Assessment
//                 </Button>
//                 <Button
//                   onClick={() => handleStatusUpdate("Passed")}
//                   disabled={isUpdatingStatus}
//                   className="flex items-center gap-2"
//                 >
//                   {isUpdatingStatus ? (
//                     <Loader2 className="h-4 w-4 animate-spin" />
//                   ) : (
//                     <CheckCircle className="h-4 w-4" />
//                   )}
//                   Pass Assessment
//                 </Button>
//               </div>
//             </div>
//           </div>
//         </div>
//       </DrawerContent>
//     </Drawer>
//   );
// }
