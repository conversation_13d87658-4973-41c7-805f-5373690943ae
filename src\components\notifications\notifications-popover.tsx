"use client";

import { <PERSON>, <PERSON>, Trash2, <PERSON>, User, Alert<PERSON><PERSON><PERSON>, DollarSign, FileText, CheckCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useNotifications, type Notification } from "@/hooks/use-notifications";
import { formatDistanceToNow } from "date-fns";
import { useRouter } from "next/navigation";
import { useEffect, useState, useMemo } from "react";
import { cn } from "@/lib/utils";
import { NotificationType } from "@prisma/client";

const DEBUG = false;

// Helper function to get the appropriate icon for each notification type
const getNotificationIcon = (type: NotificationType) => {
  switch (type) {
    case NotificationType.WRITER_ASSIGNED:
      return <User className="h-4 w-4 text-blue-500" />;
    case NotificationType.STATUS_CHANGE:
      return <AlertCircle className="h-4 w-4 text-orange-500" />;
    case NotificationType.PAYMENT_RECEIVED:
      return <DollarSign className="h-4 w-4 text-green-500" />;
    case NotificationType.JOB_POSTED:
      return <FileText className="h-4 w-4 text-purple-500" />;
    case NotificationType.JOB_COMPLETED:
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    case NotificationType.ASSIGNMENT:
    case NotificationType.SYSTEM:
    default:
      return <Bell className="h-4 w-4 text-gray-500" />;
  }
};

export function NotificationsPopover() {
  const [mounted, setMounted] = useState(false);
  const router = useRouter();
  const store = useNotifications();

  // Handle client-side mounting
  useEffect(() => {
    setMounted(true);
    return () => {
      if (DEBUG) console.log("[Notifications] Component unmounted");
    };
  }, []);

  // Memoize date formatting
  const formattedDates = useMemo(() => {
    if (!mounted || !store.notifications.length) return {};

    if (DEBUG) {
      console.log(
        "[Notifications] Formatting dates for",
        store.notifications.length,
        "notifications"
      );
    }

    return store.notifications.reduce<Record<string, string>>(
      (acc, notification) => {
        acc[notification.id] = formatDistanceToNow(
          new Date(notification.createdAt),
          {
            addSuffix: true,
          }
        );
        return acc;
      },
      {}
    );
  }, [store.notifications, mounted]);

  const handleNotificationClick = (notification: Notification) => {
    if (DEBUG) store.markAsRead(notification.id);
    if (notification.assignmentId) {
      router.push(`/order/${notification.assignmentId}`);
    }
  };

  if (!mounted) return null;

  if (DEBUG) {
    console.log("[Notifications] Render", {
      notificationsCount: store.notifications.length,
      unreadCount: store.unreadCount,
    });
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="relative"
          aria-label="Open notifications"
        >
          <Bell className="h-5 w-5" />
          {store.unreadCount > 0 && (
            <span className="absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center rounded-full bg-primary text-[11px] text-primary-foreground animate-pulse">
              {store.unreadCount}
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80" align="end" sideOffset={4}>
        <div className="flex items-center justify-between border-b border-border px-4 py-2">
          <h4 className="font-semibold">Notifications</h4>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => store.markAllAsRead()}
              disabled={store.unreadCount === 0}
              title="Mark all as read"
            >
              <Check className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => store.clearAll()}
              disabled={store.notifications.length === 0}
              title="Clear all notifications"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <ScrollArea className="max-h-80">
          {store.notifications.length === 0 ? (
            <div className="p-4 text-center text-sm text-muted-foreground">
              No notifications
            </div>
          ) : (
            <div className="divide-y divide-border">
              {store.notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={cn(
                    "group flex items-start gap-2 p-4 hover:bg-accent/50 relative transition-colors duration-200",
                    !notification.read && "bg-muted/50"
                  )}
                >
                  <div
                    className="flex-1 cursor-pointer"
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <div className="flex items-center gap-2">
                      {getNotificationIcon(notification.type)}
                      <p
                        className={cn(
                          "text-sm font-medium",
                          !notification.read && "text-primary"
                        )}
                      >
                        {notification.title}
                      </p>
                      {!notification.read && (
                        <span className="h-2 w-2 rounded-full bg-primary animate-pulse" />
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      {notification.message}
                    </p>
                    {notification.taskId && (
                      <p className="text-xs text-primary font-medium mt-1">
                        Task ID: {notification.taskId}
                      </p>
                    )}
                    <p className="mt-1 text-xs text-muted-foreground">
                      {formattedDates[notification.id]}
                    </p>
                  </div>
                  <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    {!notification.read && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 shrink-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          store.markAsRead(notification.id);
                        }}
                        title="Mark as read"
                      >
                        <Check className="h-4 w-4" />
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 shrink-0"
                      onClick={(e) => {
                        e.stopPropagation();
                        store.removeNotification(notification.id);
                      }}
                      title="Remove notification"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
}
