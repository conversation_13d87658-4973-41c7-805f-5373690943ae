"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  ChevronDown, 
  ChevronUp, 
  HelpCircle, 
  Clock, 
  CreditCard, 
  FileText, 
  Users,
  MessageCircle
} from "lucide-react";

const faqCategories = [
  {
    id: "general",
    title: "General Questions",
    icon: HelpCircle,
    color: "bg-blue-100 text-blue-700",
  },
  {
    id: "orders",
    title: "Orders & Services",
    icon: FileText,
    color: "bg-green-100 text-green-700",
  },
  {
    id: "payment",
    title: "Payment & Billing",
    icon: CreditCard,
    color: "bg-purple-100 text-purple-700",
  },
  {
    id: "support",
    title: "Support & Help",
    icon: Users,
    color: "bg-orange-100 text-orange-700",
  },
];

const faqs = [
  {
    category: "general",
    question: "How quickly can you complete my assignment?",
    answer: "Our turnaround time depends on the complexity and length of your assignment. We offer rush services with completion times as fast as 3 hours for urgent deadlines. Standard delivery times range from 24 hours to 14 days.",
  },
  {
    category: "general",
    question: "What subjects do you cover?",
    answer: "We cover a wide range of academic subjects including but not limited to: English Literature, History, Psychology, Business, Economics, Science, Mathematics, Engineering, Law, and many more. Our team consists of experts from various academic backgrounds.",
  },
  {
    category: "orders",
    question: "How do I place an order?",
    answer: "Placing an order is simple! Click on 'Create Order', fill in your assignment details, upload any relevant files, choose your deadline, and proceed to payment. You$apos;ll receive confirmation and can track your order progress in your dashboard.",
  },
  {
    category: "orders",
    question: "Can I communicate with my writer?",
    answer: "Yes! We provide a secure messaging system that allows you to communicate directly with your assigned writer. You can ask questions, provide additional instructions, or request updates on your order progress.",
  },
  {
    category: "orders",
    question: "What if I$apos;m not satisfied with my order?",
    answer: "We offer unlimited free revisions within 14 days of delivery. If you$apos;re still not satisfied, we provide a money-back guarantee. Our goal is to ensure you$apos;re completely happy with your assignment.",
  },
  {
    category: "payment",
    question: "What payment methods do you accept?",
    answer: "We accept all major credit cards (Visa, MasterCard, American Express), PayPal, and bank transfers. All payments are processed securely through encrypted payment gateways.",
  },
  {
    category: "payment",
    question: "When do I need to pay?",
    answer: "Payment is required upfront when you place your order. This ensures your assignment is immediately assigned to a qualified writer and work begins promptly.",
  },
  {
    category: "payment",
    question: "Do you offer refunds?",
    answer: "Yes, we have a comprehensive refund policy. Full refunds are available if we cannot complete your order or if you$apos;re not satisfied with the quality after revisions. Partial refunds may apply in certain circumstances.",
  },
  {
    category: "support",
    question: "How can I contact customer support?",
    answer: "You can reach our support team 24/7 through live chat, email (<EMAIL>), or phone. We typically respond to emails within 2-4 hours and live chat inquiries immediately.",
  },
  {
    category: "support",
    question: "Is my personal information secure?",
    answer: "Absolutely! We take privacy seriously. All personal information is encrypted and stored securely. We never share your details with third parties, and all communication is confidential.",
  },
];

export function ContactFAQ() {
  const [selectedCategory, setSelectedCategory] = useState("general");
  const [expandedFAQ, setExpandedFAQ] = useState<number | null>(null);

  const filteredFAQs = faqs.filter(faq => faq.category === selectedCategory);

  const toggleFAQ = (index: number) => {
    setExpandedFAQ(expandedFAQ === index ? null : index);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      <Card className="shadow-lg border-0">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold flex items-center justify-center gap-2">
            <HelpCircle className="w-6 h-6 text-primary" />
            Frequently Asked Questions
          </CardTitle>
          <CardDescription>
            Find quick answers to common questions about our services
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          {/* Category Tabs */}
          <div className="flex flex-wrap gap-2 mb-6 justify-center">
            {faqCategories.map((category) => {
              const Icon = category.icon;
              const isActive = selectedCategory === category.id;
              return (
                <Button
                  key={category.id}
                  variant={isActive ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex items-center gap-2 ${
                    isActive ? "" : "hover:bg-gray-50"
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {category.title}
                  <Badge variant="secondary" className="ml-1">
                    {faqs.filter(faq => faq.category === category.id).length}
                  </Badge>
                </Button>
              );
            })}
          </div>

          {/* FAQ Items */}
          <div className="space-y-4">
            <AnimatePresence mode="wait">
              {filteredFAQs.map((faq, index) => (
                <motion.div
                  key={`${selectedCategory}-${index}`}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
                >
                  <button
                    onClick={() => toggleFAQ(index)}
                    className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                  >
                    <span className="font-semibold text-gray-800 pr-4">
                      {faq.question}
                    </span>
                    {expandedFAQ === index ? (
                      <ChevronUp className="w-5 h-5 text-gray-500 shrink-0" />
                    ) : (
                      <ChevronDown className="w-5 h-5 text-gray-500 shrink-0" />
                    )}
                  </button>
                  
                  <AnimatePresence>
                    {expandedFAQ === index && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: "auto", opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="overflow-hidden"
                      >
                        <div className="px-6 pb-4 text-gray-600 leading-relaxed border-t border-gray-100 pt-4">
                          {faq.answer}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>

          {/* Still Have Questions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="mt-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg text-center"
          >
            <MessageCircle className="w-12 h-12 text-blue-600 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-gray-800 mb-2">
              Still have questions?
            </h3>
            <p className="text-gray-600 mb-4">
              Can$apos;t find the answer you$apos;re looking for? Our support team is here to help!
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button className="bg-blue-600 hover:bg-blue-700">
                <MessageCircle className="w-4 h-4 mr-2" />
                Start Live Chat
              </Button>
              <Button variant="outline">
                <Clock className="w-4 h-4 mr-2" />
                Schedule a Call
              </Button>
            </div>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
