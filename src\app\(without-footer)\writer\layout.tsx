//src/app/(without-footer)/writer/layout.tsx

import { AppSidebar } from "@/components/dashboard/writer/app-sidebar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";

interface WriterLayoutProps {
  children: React.ReactNode;
}

export default function WriterLayout({ children }: WriterLayoutProps) {
  return (
    <SidebarProvider>
      <AppSidebar />
      {/* <SidebarInset>{children}</SidebarInset> */}
      <SidebarInset>
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
              {children}
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
