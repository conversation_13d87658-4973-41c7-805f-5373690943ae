import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Shield, Eye, Lock, Users, Database, FileText, Mail, Phone } from "lucide-react"

export default function PrivacyPolicy() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header Section */}
      <div className="bg-gradient-to-r from-primary/10 via-primary/5 to-background border-b">
        <div className="container mx-auto px-4 py-12 sm:py-16">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex items-center justify-center mb-4">
              <Shield className="w-8 h-8 text-primary mr-3" />
              <Badge variant="outline" className="px-3 py-1">
                Last updated: {new Date().toLocaleDateString('en-US', { 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}
              </Badge>
            </div>
            <h1 className="text-4xl sm:text-5xl font-bold text-foreground mb-4">
              Privacy Policy
            </h1>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              We are committed to protecting your privacy and ensuring the security of your personal information. 
              This policy explains how we collect, use, and safeguard your data on our academic writing platform.
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto space-y-8">
          
          {/* Quick Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Eye className="w-5 h-5 mr-2 text-primary" />
                Quick Overview
              </CardTitle>
              <CardDescription>
                Key points about how we handle your information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-start space-x-3">
                  <Lock className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-foreground">Secure Data Handling</h4>
                    <p className="text-sm text-muted-foreground">All data is encrypted and stored securely</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Users className="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-foreground">No Data Selling</h4>
                    <p className="text-sm text-muted-foreground">We never sell your personal information</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Database className="w-5 h-5 text-purple-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-foreground">Minimal Collection</h4>
                    <p className="text-sm text-muted-foreground">We only collect necessary information</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <FileText className="w-5 h-5 text-orange-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-foreground">Your Rights</h4>
                    <p className="text-sm text-muted-foreground">Access, modify, or delete your data anytime</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Information We Collect */}
          <Card>
            <CardHeader>
              <CardTitle>1. Information We Collect</CardTitle>
              <CardDescription>
                Types of information we gather to provide our services
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h4 className="font-semibold text-foreground mb-3">Account Information</h4>
                <ul className="space-y-2 text-muted-foreground">
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    Name, email address, and profile information
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    Educational background and academic credentials (for writers)
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    Payment information and transaction history
                  </li>
                </ul>
              </div>

              <Separator />

              <div>
                <h4 className="font-semibold text-foreground mb-3">Assignment Data</h4>
                <ul className="space-y-2 text-muted-foreground">
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    Assignment details, requirements, and uploaded files
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    Communication between students and writers
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    Bidding history and project completion data
                  </li>
                </ul>
              </div>

              <Separator />

              <div>
                <h4 className="font-semibold text-foreground mb-3">Technical Information</h4>
                <ul className="space-y-2 text-muted-foreground">
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    IP address, browser type, and device information
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    Usage patterns and platform interaction data
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    Cookies and similar tracking technologies
                  </li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* How We Use Information */}
          <Card>
            <CardHeader>
              <CardTitle>2. How We Use Your Information</CardTitle>
              <CardDescription>
                The purposes for which we process your personal data
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="font-semibold text-foreground">Platform Operations</h4>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    <li>• Facilitate assignment posting and bidding</li>
                    <li>• Process payments and transactions</li>
                    <li>• Enable communication between users</li>
                    <li>• Provide customer support</li>
                  </ul>
                </div>
                <div className="space-y-3">
                  <h4 className="font-semibold text-foreground">Service Improvement</h4>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    <li>• Analyze platform usage and performance</li>
                    <li>• Develop new features and services</li>
                    <li>• Personalize user experience</li>
                    <li>• Prevent fraud and ensure security</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Information Sharing */}
          <Card>
            <CardHeader>
              <CardTitle>3. Information Sharing and Disclosure</CardTitle>
              <CardDescription>
                When and how we share your information with others
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-muted/50 p-4 rounded-lg">
                <h4 className="font-semibold text-foreground mb-2">We DO NOT sell your personal information</h4>
                <p className="text-sm text-muted-foreground">
                  Your data is never sold to third parties for marketing or advertising purposes.
                </p>
              </div>
              
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-foreground mb-2">Limited Sharing Occurs When:</h4>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      Required by law or legal process
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      With service providers who help operate our platform (under strict confidentiality)
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      To protect rights, property, or safety of users and the platform
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      In connection with business transfers (mergers, acquisitions)
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Data Security */}
          <Card>
            <CardHeader>
              <CardTitle>4. Data Security</CardTitle>
              <CardDescription>
                How we protect your information from unauthorized access
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="bg-green-50 dark:bg-green-950/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                  <h4 className="font-medium text-green-800 dark:text-green-300 mb-2">Encryption</h4>
                  <p className="text-sm text-green-700 dark:text-green-400">
                    All data is encrypted in transit and at rest using industry-standard protocols
                  </p>
                </div>
                <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                  <h4 className="font-medium text-blue-800 dark:text-blue-300 mb-2">Access Controls</h4>
                  <p className="text-sm text-blue-700 dark:text-blue-400">
                    Strict access controls and authentication mechanisms protect your data
                  </p>
                </div>
                <div className="bg-purple-50 dark:bg-purple-950/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
                  <h4 className="font-medium text-purple-800 dark:text-purple-300 mb-2">Regular Audits</h4>
                  <p className="text-sm text-purple-700 dark:text-purple-400">
                    We conduct regular security audits and vulnerability assessments
                  </p>
                </div>
                <div className="bg-orange-50 dark:bg-orange-950/20 p-4 rounded-lg border border-orange-200 dark:border-orange-800">
                  <h4 className="font-medium text-orange-800 dark:text-orange-300 mb-2">Data Backup</h4>
                  <p className="text-sm text-orange-700 dark:text-orange-400">
                    Secure backup systems ensure data availability and integrity
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Your Rights */}
          <Card>
            <CardHeader>
              <CardTitle>5. Your Privacy Rights</CardTitle>
              <CardDescription>
                Control over your personal information and how to exercise your rights
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-foreground mb-2">Access &amp; Portability</h4>
                    <p className="text-sm text-muted-foreground">
                      Request a copy of your personal data in a portable format
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium text-foreground mb-2">Correction</h4>
                    <p className="text-sm text-muted-foreground">
                      Update or correct inaccurate personal information
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium text-foreground mb-2">Deletion</h4>
                    <p className="text-sm text-muted-foreground">
                      Request deletion of your personal data (subject to legal requirements)
                    </p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-foreground mb-2">Opt-out</h4>
                    <p className="text-sm text-muted-foreground">
                      Withdraw consent for certain data processing activities
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium text-foreground mb-2">Restriction</h4>
                    <p className="text-sm text-muted-foreground">
                      Limit how we process your personal information
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium text-foreground mb-2">Objection</h4>
                    <p className="text-sm text-muted-foreground">
                      Object to processing based on legitimate interests
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Cookies and Tracking */}
          <Card>
            <CardHeader>
              <CardTitle>6. Cookies and Tracking Technologies</CardTitle>
              <CardDescription>
                How we use cookies and similar technologies
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-foreground mb-2">Essential Cookies</h4>
                  <p className="text-sm text-muted-foreground mb-2">
                    Required for basic platform functionality, including authentication and security.
                  </p>
                  <Badge variant="outline" className="text-xs">Always Active</Badge>
                </div>
                
                <Separator />
                
                <div>
                  <h4 className="font-medium text-foreground mb-2">Analytics Cookies</h4>
                  <p className="text-sm text-muted-foreground mb-2">
                    Help us understand how users interact with our platform to improve user experience.
                  </p>
                  <Badge variant="secondary" className="text-xs">Optional</Badge>
                </div>
                
                <Separator />
                
                <div>
                  <h4 className="font-medium text-foreground mb-2">Preference Cookies</h4>
                  <p className="text-sm text-muted-foreground mb-2">
                    Remember your settings and preferences for a personalized experience.
                  </p>
                  <Badge variant="secondary" className="text-xs">Optional</Badge>
                </div>
              </div>
              
              <div className="bg-muted/50 p-4 rounded-lg">
                <p className="text-sm text-muted-foreground">
                  You can manage cookie preferences through your browser settings or our cookie consent banner.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Data Retention */}
          <Card>
            <CardHeader>
              <CardTitle>7. Data Retention</CardTitle>
              <CardDescription>
                How long we keep your information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 border rounded-lg">
                    <h4 className="font-medium text-foreground mb-2">Account Data</h4>
                    <p className="text-2xl font-bold text-primary mb-1">Active + 3 years</p>
                    <p className="text-xs text-muted-foreground">After account closure</p>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <h4 className="font-medium text-foreground mb-2">Assignment Data</h4>
                    <p className="text-2xl font-bold text-primary mb-1">5 years</p>
                    <p className="text-xs text-muted-foreground">For academic integrity</p>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <h4 className="font-medium text-foreground mb-2">Payment Data</h4>
                    <p className="text-2xl font-bold text-primary mb-1">7 years</p>
                    <p className="text-xs text-muted-foreground">Legal requirement</p>
                  </div>
                </div>
                <div className="bg-muted/50 p-4 rounded-lg">
                  <p className="text-sm text-muted-foreground">
                    Data is securely deleted when retention periods expire, unless legal obligations require longer retention.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* International Transfers */}
          <Card>
            <CardHeader>
              <CardTitle>8. International Data Transfers</CardTitle>
              <CardDescription>
                How we handle data across borders
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-muted-foreground">
                  Our platform may transfer your data internationally to provide our services. We ensure appropriate 
                  safeguards are in place for all international transfers, including:
                </p>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    Standard Contractual Clauses approved by relevant authorities
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    Adequacy decisions by data protection authorities
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    Industry-standard security measures and encryption
                  </li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Children's Privacy */}
          <Card>
            <CardHeader>
              <CardTitle>9. Children&rsquo;s Privacy</CardTitle>
              <CardDescription>
                Special protections for users under 18
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-yellow-50 dark:bg-yellow-950/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-800">
                <h4 className="font-medium text-yellow-800 dark:text-yellow-300 mb-2">
                  Age Restrictions
                </h4>
                <p className="text-sm text-yellow-700 dark:text-yellow-400 mb-3">
                  Our platform is intended for users 18 years and older. Users under 18 require parental consent 
                  and supervision when using our services.
                </p>
                <p className="text-sm text-yellow-700 dark:text-yellow-400">
                  If you are under 18, please ensure your parent or guardian reviews this privacy policy 
                  and approves your use of our platform.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle>10. Contact Us</CardTitle>
              <CardDescription>
                How to reach us with privacy-related questions or concerns
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <Mail className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-medium text-foreground">Email</h4>
                      <p className="text-sm text-muted-foreground"><EMAIL></p>
                      <p className="text-xs text-muted-foreground mt-1">
                        For privacy-related inquiries and data requests
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <Phone className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-medium text-foreground">Phone</h4>
                      <p className="text-sm text-muted-foreground">+****************</p>
                      <p className="text-xs text-muted-foreground mt-1">
                        Business hours: Mon-Fri 9AM-5PM EST
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="bg-muted/50 p-4 rounded-lg">
                  <h4 className="font-medium text-foreground mb-2">Response Time</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    We aim to respond to all privacy inquiries within 30 days. Complex requests may take longer, 
                    and we&rsquo;ll keep you informed of our progress.
                  </p>
                  <p className="text-sm text-muted-foreground">
                    For urgent privacy matters, please mark your email as &ldquo;URGENT&rdquo; in the subject line.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Changes to Policy */}
          <Card>
            <CardHeader>
              <CardTitle>11. Changes to This Privacy Policy</CardTitle>
              <CardDescription>
                How we handle updates to our privacy practices
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-muted-foreground">
                  We may update this privacy policy from time to time to reflect changes in our practices, 
                  technology, legal requirements, or other factors.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <h4 className="font-medium text-foreground">Minor Changes</h4>
                    <p className="text-sm text-muted-foreground">
                      For minor updates, we&rsquo;ll post the revised policy with an updated &ldquo;Last Modified&rdquo; date.
                    </p>
                  </div>
                  <div className="space-y-3">
                    <h4 className="font-medium text-foreground">Major Changes</h4>
                    <p className="text-sm text-muted-foreground">
                      For significant changes, we&rsquo;ll notify you via email or prominent platform notice 30 days before implementation.
                    </p>
                  </div>
                </div>
                
                <div className="bg-muted/50 p-4 rounded-lg">
                  <p className="text-sm text-muted-foreground">
                    Your continued use of our platform after policy changes constitutes acceptance of the updated terms.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

        </div>
      </div>

      {/* Footer */}
      <div className="footer-bg border-t mt-16">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto text-center">
            <p className="footer-text-muted text-sm">
              This privacy policy is effective as of {new Date().toLocaleDateString('en-US', { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })} and governs the collection and use of information on our academic writing platform.
            </p>
            <div className="flex justify-center space-x-6 mt-4">
              <Link href="/terms" className="footer-link text-sm hover:underline transition-colors">
                Terms of Service
              </Link>
              <Link href="/contact" className="footer-link text-sm hover:underline transition-colors">
                Contact Us
              </Link>
              <Link href="/support" className="footer-link text-sm hover:underline transition-colors">
                Support
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}