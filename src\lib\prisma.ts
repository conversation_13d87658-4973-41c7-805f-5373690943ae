// src/lib/prisma.ts
import { PrismaClient } from '@prisma/client';




// 1. Fix global type declaration
declare global {
  // eslint-disable-next-line no-var
  var prisma: PrismaClient | undefined
}

// 2. Proper singleton pattern implementation
const prisma = global.prisma || new PrismaClient()

// 3. Development-specific configuration
if (process.env.NODE_ENV === 'development') {
  global.prisma = prisma
}

export default prisma