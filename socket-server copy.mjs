// import { createServer } from 'http';
// import { Server } from 'socket.io';
// import next from 'next';

// const dev = process.env.NODE_ENV !== 'production';
// const hostname = 'localhost';
// const port = process.env.PORT || 3000;
// const socketPort = process.env.SOCKET_PORT || 3001;

// // Create Next.js app
// const app = next({ dev, hostname, port });
// const handler = app.getRequestHandler();

// app.prepare().then(() => {
//   // Create HTTP server for Socket.IO
//   const httpServer = createServer((req, res) => {
//     // Handle Next.js requests
//     return handler(req, res);
//   });
  
//   // Create Socket.IO server
//   const io = new Server(httpServer, {
//     cors: {
//       origin: [`http://localhost:${port}`, `http://localhost:3000`],
//       methods: ['GET', 'POST'],
//       credentials: true,
//     },
//   });

//   // Store active users and their rooms
//   const activeUsers = new Map();
//   const assignmentRooms = new Map();

//   io.on('connection', (socket) => {
//     console.log('User connected:', socket.id);

//     // Handle user joining assignment chat
//     socket.on('join-assignment-chat', (assignmentId) => {
//       const userId = socket.handshake.query.userId;
//       const clientId = socket.handshake.query.clientId;
      
//       if (!userId || !assignmentId) {
//         console.log('Missing userId or assignmentId');
//         return;
//       }

//       // Store user info
//       activeUsers.set(socket.id, { userId, assignmentId, clientId });
      
//       // Join main assignment room (for general notifications)
//       const mainRoomName = `assignment-${assignmentId}`;
//       socket.join(mainRoomName);
      
//       // Join specific conversation rooms
//       const clientRoomName = `assignment-${assignmentId}-client`;
//       const writerRoomName = `assignment-${assignmentId}-writer`;
      
//       // All users join both rooms to receive messages appropriately
//       socket.join(clientRoomName);
//       socket.join(writerRoomName);
      
//       // Track users in assignment room
//       if (!assignmentRooms.has(assignmentId)) {
//         assignmentRooms.set(assignmentId, new Set());
//       }
//       assignmentRooms.get(assignmentId).add(userId);
      
//       console.log(`User ${userId} joined assignment ${assignmentId} rooms`);
      
//       // Notify others in the main room
//       socket.to(mainRoomName).emit('user-joined', {
//         userId,
//         assignmentId,
//         timestamp: new Date().toISOString(),
//       });
//     });

//     // Handle sending messages with conversation context
//     socket.on('send-message', (data) => {
//       const { assignmentId, message, conversationType } = data;
//       const userInfo = activeUsers.get(socket.id);
      
//       if (!userInfo || userInfo.assignmentId !== assignmentId) {
//         console.log('Unauthorized message attempt');
//         return;
//       }

//       // Determine which room to broadcast to based on conversation type
//       let targetRoom;
      
//       if (conversationType === 'client') {
//         targetRoom = `assignment-${assignmentId}-client`;
//       } else if (conversationType === 'writer') {
//         targetRoom = `assignment-${assignmentId}-writer`;
//       } else {
//         // Fallback for non-admin users or system messages
//         targetRoom = `assignment-${assignmentId}`;
//       }
      
//       // Broadcast message to appropriate room
//       io.to(targetRoom).emit('new-message', {
//         ...message,
//         conversationType // Include conversation type in the emitted message
//       });
      
//       console.log(`Message sent in assignment ${assignmentId} (${conversationType}):`, message.content);
//     });

//     // Handle marking messages as read
//     socket.on('mark-messages-read', (data) => {
//       const { assignmentId, messageIds, conversationType } = data;
//       const userInfo = activeUsers.get(socket.id);
      
//       if (!userInfo || userInfo.assignmentId !== assignmentId) {
//         console.log('Unauthorized read attempt');
//         return;
//       }

//       // Determine target room based on conversation type
//       let targetRoom;
      
//       if (conversationType === 'client') {
//         targetRoom = `assignment-${assignmentId}-client`;
//       } else if (conversationType === 'writer') {
//         targetRoom = `assignment-${assignmentId}-writer`;
//       } else {
//         targetRoom = `assignment-${assignmentId}`;
//       }
      
//       // Broadcast read status to appropriate room
//       messageIds.forEach(messageId => {
//         io.to(targetRoom).emit('message-read', { 
//           messageId,
//           conversationType 
//         });
//       });
      
//       console.log(`Messages marked as read in assignment ${assignmentId} (${conversationType}):`, messageIds);
//     });

//     // Handle user typing with conversation context
//     socket.on('typing', (data) => {
//       const { assignmentId, isTyping, conversationType } = data;
//       const userInfo = activeUsers.get(socket.id);
      
//       if (!userInfo || userInfo.assignmentId !== assignmentId) {
//         return;
//       }

//       // Determine target room based on conversation type
//       let targetRoom;
      
//       if (conversationType === 'client') {
//         targetRoom = `assignment-${assignmentId}-client`;
//       } else if (conversationType === 'writer') {
//         targetRoom = `assignment-${assignmentId}-writer`;
//       } else {
//         targetRoom = `assignment-${assignmentId}`;
//       }
      
//       // Broadcast typing status to others in the appropriate room
//       socket.to(targetRoom).emit('user-typing', {
//         userId: userInfo.userId,
//         isTyping,
//         conversationType,
//         timestamp: new Date().toISOString(),
//       });
//     });

//     // Handle joining specific conversation room (for targeted messaging)
//     socket.on('join-conversation', (data) => {
//       const { assignmentId, conversationType } = data;
//       const userInfo = activeUsers.get(socket.id);
      
//       if (!userInfo || userInfo.assignmentId !== assignmentId) {
//         console.log('Unauthorized conversation join attempt');
//         return;
//       }

//       // Join specific conversation room
//       const roomName = `assignment-${assignmentId}-${conversationType}`;
//       socket.join(roomName);
      
//       console.log(`User ${userInfo.userId} joined ${conversationType} conversation for assignment ${assignmentId}`);
//     });

//     // Handle leaving specific conversation room
//     socket.on('leave-conversation', (data) => {
//       const { assignmentId, conversationType } = data;
//       const userInfo = activeUsers.get(socket.id);
      
//       if (!userInfo || userInfo.assignmentId !== assignmentId) {
//         return;
//       }

//       // Leave specific conversation room
//       const roomName = `assignment-${assignmentId}-${conversationType}`;
//       socket.leave(roomName);
      
//       console.log(`User ${userInfo.userId} left ${conversationType} conversation for assignment ${assignmentId}`);
//     });

//     // Handle disconnection
//     socket.on('disconnect', () => {
//       const userInfo = activeUsers.get(socket.id);
      
//       if (userInfo) {
//         const { userId, assignmentId } = userInfo;
//         const mainRoomName = `assignment-${assignmentId}`;
        
//         // Remove user from tracking
//         activeUsers.delete(socket.id);
        
//         if (assignmentRooms.has(assignmentId)) {
//           assignmentRooms.get(assignmentId).delete(userId);
          
//           // Clean up empty rooms
//           if (assignmentRooms.get(assignmentId).size === 0) {
//             assignmentRooms.delete(assignmentId);
//           }
//         }
        
//         // Notify others in the main room
//         socket.to(mainRoomName).emit('user-left', {
//           userId,
//           assignmentId,
//           timestamp: new Date().toISOString(),
//         });
        
//         console.log(`User ${userId} disconnected from assignment ${assignmentId}`);
//       }
      
//       console.log('User disconnected:', socket.id);
//     });

//     // Handle errors
//     socket.on('error', (error) => {
//       console.error('Socket error:', error);
//     });
//   });

//   // Start Socket.IO server
//   httpServer.listen(socketPort, () => {
//     console.log(`Socket.IO server running on http://localhost:${socketPort}`);
//   });

//   // Log active connections periodically
//   setInterval(() => {
//     console.log(`Active connections: ${activeUsers.size}`);
//     console.log(`Active assignment rooms: ${assignmentRooms.size}`);
    
//     // Log room details for debugging
//     if (activeUsers.size > 0) {
//       console.log('Active users by assignment:');
//       const assignmentUsers = new Map();
//       activeUsers.forEach((userInfo, socketId) => {
//         const { assignmentId, userId } = userInfo;
//         if (!assignmentUsers.has(assignmentId)) {
//           assignmentUsers.set(assignmentId, []);
//         }
//         assignmentUsers.get(assignmentId).push(userId);
//       });
//       assignmentUsers.forEach((users, assignmentId) => {
//         console.log(`  Assignment ${assignmentId}: ${users.join(', ')}`);
//       });
//     }
//   }, 30000); // Every 30 seconds
// });

// // Handle graceful shutdown
// process.on('SIGTERM', () => {
//   console.log('SIGTERM received, shutting down gracefully');
//   process.exit(0);
// });

// process.on('SIGINT', () => {
//   console.log('SIGINT received, shutting down gracefully');
//   process.exit(0);
// });