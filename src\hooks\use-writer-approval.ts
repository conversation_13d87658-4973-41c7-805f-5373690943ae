"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";

export function useWriterApproval() {
  const { data: session, status } = useSession();
  const [isApproved, setIsApproved] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function checkApprovalStatus() {
      if (status === "loading") return;

      if (!session?.user?.id || session.user.role !== "WRITER") {
        setIsApproved(false);
        setIsLoading(false);
        return;
      }

      // First, try to use the isApproved field from the session if available
      if (session.user.isApproved !== undefined) {
        console.log("🔍 [useWriterApproval] Using approval status from session:", session.user.isApproved);
        setIsApproved(session.user.isApproved);
        setIsLoading(false);
        return;
      }

      // Fallback to API call if session doesn't have isApproved field
      try {
        console.log(
          "🔍 [useWriterApproval] Checking approval via API for:",
          session.user.id
        );
        const response = await fetch(`/api/users/writers/${session.user.id}`);
        const data = await response.json();

        console.log("📝 [useWriterApproval] API Response:", {
          success: data.success,
          isApproved: data.data?.isApproved,
        });

        setIsApproved(data.success && data.data?.isApproved);
      } catch (err) {
        console.error("❌ [useWriterApproval] Error:", err);
        setError(
          err instanceof Error ? err.message : "Failed to check approval status"
        );
        setIsApproved(false);
      } finally {
        setIsLoading(false);
      }
    }

    checkApprovalStatus();
  }, [session?.user?.id, session?.user?.isApproved, status]);

  const getWriterPath = () => {
    if (isLoading || !session) return "/login/writer";
    if (session.user.role !== "WRITER") return "/login/writer";
    return isApproved ? "/writer/dashboard" : "/writer-assessment";
  };

  return {
    isApproved,
    isLoading,
    error,
    getWriterPath,
  };
}
