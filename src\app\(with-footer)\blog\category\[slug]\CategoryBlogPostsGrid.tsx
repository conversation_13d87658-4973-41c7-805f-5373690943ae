import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { formatDistanceToNow } from 'date-fns';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardFooter, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  createdAt: Date;
  imageUrl?: string;
  imageAlt?: string;
  metaDescription?: string;
  readTime?: number;
  author: {
    name: string;
  };
  category: {
    name: string;
    slug: string;
  };
}

interface CategoryBlogPostsGridProps {
  posts: BlogPost[];
}

export function CategoryBlogPostsGrid({ posts }: CategoryBlogPostsGridProps) {
  if (!posts || posts.length === 0) {
    return (
      <div className="text-center py-12">
        <h3 className="text-xl font-medium mb-2">No posts found in this category</h3>
        <p className="text-muted-foreground">
          Check back later for new content or browse other categories.
        </p>
      </div>
    );
  }

  return (
    <section className="py-4">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {posts.map((post) => (
          <Card key={post.id} className="overflow-hidden flex flex-col h-full transition-all duration-200 hover:shadow-md">
            <Link href={`/blog/${post.slug}`} className="block">
              <div className="relative h-48 w-full">
                <Image
                  src={post.imageUrl || '/assets/blog.jpg'}
                  alt={post.imageAlt || post.title}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
              </div>
            </Link>
            
            <CardHeader className="p-4 pb-2">
              <div className="flex items-center justify-between mb-2">
                <Badge variant="outline" className="text-xs">
                  {post.category.name}
                </Badge>
                <span className="text-xs text-muted-foreground">
                  {post.readTime || '5'} min read
                </span>
              </div>
              <Link href={`/blog/${post.slug}`} className="hover:underline">
                <h3 className="font-semibold text-lg line-clamp-2">{post.title}</h3>
              </Link>
            </CardHeader>
            
            <CardContent className="p-4 pt-0 flex-grow">
              <p className="text-muted-foreground text-sm line-clamp-3">
                {post.metaDescription || 'Read this article to learn more about this topic.'}
              </p>
            </CardContent>
            
            <CardFooter className="p-4 pt-0 flex items-center justify-between text-xs text-muted-foreground">
              <span>{post.author.name}</span>
              <time dateTime={post.createdAt.toISOString()}>
                {formatDistanceToNow(post.createdAt, { addSuffix: true })}
              </time>
            </CardFooter>
          </Card>
        ))}
      </div>
    </section>
  );
}
