"use client";

import { <PERSON> } from "react";
import Link from "next/link";
import { cn } from "@/lib/utils";

import { Navbar<PERSON>ogo } from "./NavbarLogo";
import { NavbarDropdown } from "./NavbarDropdown";
import { NavbarMenu } from "./NavbarMenu";
import { NavbarAuth } from "./NavbarAuth";
import { ThemeToggle } from "@/components/theme-toggle";

interface NavbarProps {
  className?: string;
}

const Navbar: FC<NavbarProps> = ({ className }) => {
  return (
    <header
      className={cn(
        "sticky top-0 z-50 w-full border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",
        className
      )}
    >
      <div className="container flex h-16 items-center px-2 sm:px-4 md:px-6">
        {/* Logo - Left side */}
        <div className="flex items-center">
          <NavbarLogo />
        </div>

        {/* Navigation - Center */}
        <nav className="hidden md:flex items-center gap-1 lg:gap-2 flex-1 justify-center">
          {/* These items only visible on large screens (desktop) */}
          <div className="hidden lg:flex items-center gap-1 lg:gap-2">
            <Link
              href="/"
              className="text-foreground hover:text-primary transition-colors text-sm lg:text-base px-2 lg:px-4 py-2 rounded-md hover:bg-accent/50"
            >
              Home
            </Link>
            <Link
              href="/why-us"
              className="text-foreground hover:text-primary transition-colors text-sm lg:text-base px-2 lg:px-4 py-2 rounded-md hover:bg-accent/50"
            >
              Why Us
            </Link>
            <NavbarDropdown
              title="Services"
              items={[
                {
                  label: "Custom Essay Writing",
                  href: "/services/custom-essay-writing",
                },
                { label: "Dissertation", href: "/services/dissertation" },
                { label: "Research Paper", href: "/services/research-paper" },
                {
                  label: "Literature Review",
                  href: "/services/literature-review",
                },
                { label: "Term Paper", href: "/services/term-paper" },
                { label: "View all Services", href: "/services" },
              ]}
            />
          </div>
          {/* These items visible on both medium (tablet) and large screens */}
          <NavbarDropdown
            title="Company"
            items={[
              { label: "About Us", href: "/about-us" },
              { label: "Contact Us", href: "/contact-us" },
              { label: "Testimonials", href: "/testimonials" },
              { label: "How it Works", href: "/how-it-works" },
              { label: "Careers", href: "/careers" },
            ]}
          />
          <Link
            href="/faqs"
            className="text-foreground hover:text-primary transition-colors text-sm lg:text-base px-2 lg:px-4 py-2 rounded-md hover:bg-accent/50"
          >
            FAQ
          </Link>
        </nav>

        {/* Auth & Actions - Right side */}
        <div className="flex items-center gap-2 sm:gap-4">
          <ThemeToggle />
          <NavbarAuth />
          <NavbarMenu className="lg:hidden" />
        </div>
      </div>
    </header>
  );
};

export { Navbar };
