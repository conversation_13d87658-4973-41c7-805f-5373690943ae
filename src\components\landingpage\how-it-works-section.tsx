// components/HowItWorks.tsx
export const HowItWorks = () => {
  const steps = [
    {
      title: "Submit Your Order",
      description:
        "Tell us about your assignment and we'll find the perfect writer to help you",
    },
    {
      title: "We Match You With a Writer",
      description:
        "Our writer will start working on your assignment right away",
    },
    {
      title: "Download Your Work",
      description:
        "Once your order is complete, you can download it from your account",
    },
  ];

  return (
    <section className="py-16 px-4 bg-[oklch(var(--card))]">
      <div className="max-w-6xl mx-auto">
        <h2 className="text-3xl font-bold mb-12 text-center">How It Works</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {steps.map((step, index) => (
            <div
              key={index}
              className="p-6 text-center border border-[oklch(var(--border))] rounded-xl"
            >
              <div className="w-12 h-12 bg-[oklch(var(--primary))] text-[oklch(var(--primary-foreground))] rounded-full flex items-center justify-center mx-auto mb-4">
                {index + 1}
              </div>
              <h3 className="text-xl font-semibold mb-2">{step.title}</h3>
              <p className="text-[oklch(var(--muted-foreground))]">
                {step.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
