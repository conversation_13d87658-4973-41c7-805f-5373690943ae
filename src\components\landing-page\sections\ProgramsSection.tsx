// src/components/landing-page/sections/ProgramSection.tsx
"use client"
import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { Input } from "@/components/ui/input";
import {
  CheckCircle,
  Award,
  BookOpen,
  Users,
  GraduationCap,
  FileText,
  Search,
  Clock,
  Star,
  TrendingUp,
  Zap,
  Shield,
  Microscope,
  Briefcase,
  Palette,
  Languages,
  Heart,
  Globe,
} from "lucide-react";

const services = [
  {
    id: "essays",
    title: "Essays & Papers",
    description:
      "High-quality essays, research papers, and academic assignments",
    icon: FileText,
    duration: "24-72 hours",
    popularity: 95,
    level: ["Undergraduate", "Graduate", "PhD"],
    subjects: ["Literature", "History", "Philosophy", "Social Sciences"],
    price: "From $12/page",
    features: ["Original content", "Proper citations", "Free revisions"],
  },
  {
    id: "research",
    title: "Research Projects",
    description: "Comprehensive research papers with in-depth analysis",
    icon: Microscope,
    duration: "3-7 days",
    popularity: 88,
    level: ["Graduate", "PhD"],
    subjects: ["Sciences", "Technology", "Medicine", "Engineering"],
    price: "From $18/page",
    features: ["Extensive research", "Data analysis", "Methodology"],
  },
  {
    id: "thesis",
    title: "Thesis & Dissertations",
    description: "Complete thesis and dissertation writing assistance",
    icon: GraduationCap,
    duration: "2-4 weeks",
    popularity: 92,
    level: ["Graduate", "PhD"],
    subjects: ["All Disciplines"],
    price: "Custom pricing",
    features: [
      "Chapter-by-chapter",
      "Defense preparation",
      "Unlimited revisions",
    ],
  },
  {
    id: "business",
    title: "Business Studies",
    description: "Case studies, business plans, and market analysis",
    icon: Briefcase,
    duration: "2-5 days",
    popularity: 85,
    level: ["Undergraduate", "Graduate", "MBA"],
    subjects: ["Business", "Economics", "Management", "Finance"],
    price: "From $15/page",
    features: ["Real case studies", "Financial analysis", "Strategic planning"],
  },
  {
    id: "creative",
    title: "Creative Writing",
    description: "Creative essays, personal statements, and narratives",
    icon: Palette,
    duration: "1-3 days",
    popularity: 78,
    level: ["All Levels"],
    subjects: ["Literature", "Arts", "Communications"],
    price: "From $14/page",
    features: ["Original creativity", "Personal touch", "Engaging content"],
  },
  {
    id: "language",
    title: "Language Studies",
    description: "Essays and assignments in multiple languages",
    icon: Languages,
    duration: "24-48 hours",
    popularity: 82,
    level: ["All Levels"],
    subjects: ["Foreign Languages", "Linguistics", "Literature"],
    price: "From $16/page",
    features: ["Native speakers", "Cultural context", "Perfect grammar"],
  },
];

const stats = [
  { label: "Active Writers", value: "750+", icon: Users },
  { label: "Subjects Covered", value: "80+", icon: BookOpen },
  { label: "Customer Satisfaction", value: "98%", icon: Heart },
  { label: "On-Time Delivery", value: "99.2%", icon: Clock },
];

export const ProgramSection = () => {
  const [activeTab, setActiveTab] = useState("undergraduate");
  const [searchTerm, setSearchTerm] = useState("");

  const filteredServices = services.filter(
    (service) =>
      service.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      service.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      service.subjects.some((subject) =>
        subject.toLowerCase().includes(searchTerm.toLowerCase())
      )
  );

  const getServicesByLevel = (level: string) => {
    return filteredServices.filter((service) =>
      service.level.some((l) => l.toLowerCase().includes(level.toLowerCase()))
    );
  };

  const ServiceCard = ({ service }: { service: (typeof services)[0] }) => {
    const IconComponent = service.icon;

    return (
      <HoverCard>
        <HoverCardTrigger asChild>
          <Card className="group cursor-pointer transition-all duration-300 hover:shadow-lg hover:-translate-y-1 border-border/50 hover:border-primary/20">
            <CardHeader className="pb-0">
              <div className="flex items-start gap-3">
                <div className="p-2 rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors">
                  <IconComponent className="h-5 w-5 text-primary" />
                </div>
                <div className="flex-1 min-w-0">
                  <CardTitle className="text-lg font-semibold group-hover:text-primary transition-colors">
                    {service.title}
                  </CardTitle>
                  <div className="flex items-center gap-2 mt-1 flex-wrap">
                    <Badge variant="secondary" className="text-xs">
                      {service.price}
                    </Badge>
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                      <span className="text-xs text-muted-foreground">
                        {service.popularity}% satisfaction
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="mb-3">
                <Badge
                  variant="outline"
                  className="text-xs mb-2 inline-flex items-center"
                >
                  <Clock className="h-3 w-3 mr-1" />
                  {service.duration}
                </Badge>
                <CardDescription className="text-sm">
                  {service.description}
                </CardDescription>
              </div>

              <div className="space-y-3">
                <div className="flex flex-wrap gap-1">
                  {service.subjects.slice(0, 3).map((subject, index) => (
                    <Badge
                      key={index}
                      variant="secondary"
                      className="text-xs py-1"
                    >
                      {subject}
                    </Badge>
                  ))}
                  {service.subjects.length > 3 && (
                    <Badge variant="secondary" className="text-xs py-1">
                      +{service.subjects.length - 3} more
                    </Badge>
                  )}
                </div>

                <div className="flex flex-wrap gap-2 text-xs text-muted-foreground">
                  {service.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-1">
                      <CheckCircle className="h-3 w-3 text-green-600" />
                      <span>{feature}</span>
                    </div>
                  ))}
                </div>
              </div>

              <Button className="w-full mt-4 group-hover:bg-primary/90 transition-colors">
                Get Started
              </Button>
            </CardContent>
          </Card>
        </HoverCardTrigger>
        <HoverCardContent className="w-80" side="top">
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <service.icon className="h-4 w-4 text-primary" />
              <h4 className="font-semibold">{service.title}</h4>
            </div>
            <p className="text-sm text-muted-foreground">
              {service.description}
            </p>
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Available for:</span>
                <div className="flex gap-1">
                  {service.level.map((level, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {level}
                    </Badge>
                  ))}
                </div>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">All subjects:</span>
                <span className="text-xs">{service.subjects.join(", ")}</span>
              </div>
            </div>
          </div>
        </HoverCardContent>
      </HoverCard>
    );
  };

  return (
    <section
      id="services"
      className="py-8 bg-gradient-to-b from-background to-muted/20"
    >
      <div className="container mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-16">
          <Badge
            variant="outline"
            className="mb-4 px-4 py-2 border-primary/20 text-primary bg-primary/5"
          >
            <Award className="h-3 w-3 mr-2" />
            Our Academic Services
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            Expert Writing Services for Every Academic Need
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto text-lg">
            Connect with qualified writers who specialize in your field. From
            essays to dissertations, we&apos;ve got your academic success
            covered.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
          {stats.map((stat, index) => {
            const IconComponent = stat.icon;
            return (
              <div
                key={index}
                className="text-center p-4 rounded-lg bg-card border border-border/50"
              >
                <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 mb-3">
                  <IconComponent className="h-6 w-6 text-primary" />
                </div>
                <div className="text-2xl font-bold text-foreground">
                  {stat.value}
                </div>
                <div className="text-sm text-muted-foreground">
                  {stat.label}
                </div>
              </div>
            );
          })}
        </div>

        {/* Search and Tabs */}
        <div className="max-w-4xl mx-auto mb-8">
          <div className="relative mb-6">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search services by type or subject..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 h-12 bg-background/50 border-border/50 focus:border-primary/50"
            />
          </div>

          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-3 mb-8 h-12 bg-muted/50">
              <TabsTrigger
                value="undergraduate"
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300"
              >
                <GraduationCap className="h-4 w-4 mr-2" />
                Undergraduate
              </TabsTrigger>
              <TabsTrigger
                value="graduate"
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300"
              >
                <BookOpen className="h-4 w-4 mr-2" />
                Graduate
              </TabsTrigger>
              <TabsTrigger
                value="specialty"
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300"
              >
                <Award className="h-4 w-4 mr-2" />
                Specialty Services
              </TabsTrigger>
            </TabsList>

            <TabsContent value="undergraduate">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {getServicesByLevel("undergraduate").length > 0 ? (
                  getServicesByLevel("undergraduate").map((service) => (
                    <ServiceCard key={service.id} service={service} />
                  ))
                ) : (
                  <div className="col-span-full text-center py-8">
                    <p className="text-muted-foreground">
                      No services found matching your search.
                    </p>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="graduate">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {getServicesByLevel("graduate").length > 0 ? (
                  getServicesByLevel("graduate").map((service) => (
                    <ServiceCard key={service.id} service={service} />
                  ))
                ) : (
                  <div className="col-span-full text-center py-8">
                    <p className="text-muted-foreground">
                      No services found matching your search.
                    </p>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="specialty">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {services
                  .filter(
                    (s) =>
                      s.id === "thesis" ||
                      s.id === "creative" ||
                      s.id === "language"
                  )
                  .map((service) => (
                    <ServiceCard key={service.id} service={service} />
                  ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>

        {/* Mission Statement */}
        <div className="mt-16">
          <Card className="border-primary/20 bg-gradient-to-r from-primary/5 to-primary/10">
            <CardContent className="p-8">
              <div className="flex items-start gap-6">
                <div className="flex-shrink-0">
                  <div className="w-16 h-16 rounded-full bg-primary/20 flex items-center justify-center">
                    <Shield className="h-8 w-8 text-primary" />
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold mb-3">
                    Our Commitment to Academic Excellence
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    We empower students worldwide by connecting them with
                    qualified writers who understand their academic needs. Our
                    platform ensures quality, originality, and timely delivery
                    for every project, helping students achieve their
                    educational goals with confidence.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="flex items-center gap-2">
                      <Zap className="h-4 w-4 text-primary" />
                      <span className="text-sm font-medium">
                        Lightning Fast Matching
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4 text-primary" />
                      <span className="text-sm font-medium">
                        Global Expert Network
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-primary" />
                      <span className="text-sm font-medium">
                        Proven Success Rate
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Trust Badges */}
        <div className="mt-12 flex flex-wrap justify-center gap-4 lg:gap-8">
          <Badge
            variant="secondary"
            className="py-3 px-6 text-sm font-medium bg-background border-border/50"
          >
            <Shield className="h-4 w-4 mr-2" />
            SSL Secured Platform
          </Badge>
          <Badge
            variant="secondary"
            className="py-3 px-6 text-sm font-medium bg-background border-border/50"
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            Money-Back Guarantee
          </Badge>
          <Badge
            variant="secondary"
            className="py-3 px-6 text-sm font-medium bg-background border-border/50"
          >
            <Award className="h-4 w-4 mr-2" />
            Verified Writers Only
          </Badge>
          <Badge
            variant="secondary"
            className="py-3 px-6 text-sm font-medium bg-background border-border/50"
          >
            <Clock className="h-4 w-4 mr-2" />
            24/7 Support Available
          </Badge>
        </div>
      </div>
    </section>
  );
};

export default ProgramSection;
