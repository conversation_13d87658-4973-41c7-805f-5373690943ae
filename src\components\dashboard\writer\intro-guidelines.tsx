import React from "react";
import {
  Card,
  CardContent,
  // CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  GraduationCap,
  Users,
  Shield,
  BookOpen,
  CheckCircle,
  Star,
  FileText,
  Search,
  Clock,
  Award,
  Lightbulb,
  Target,
  Heart,
} from "lucide-react";

export default function IntroGuidelines() {
  const services = [
    {
      icon: FileText,
      title: "Essays",
      description: "Argumentative to reflective essays across all topics",
    },
    {
      icon: Search,
      title: "Research Papers",
      description: "Following IMRaD format with proper structure",
    },
    {
      icon: BookOpen,
      title: "Lab Reports",
      description: "Method reporting and data analysis for sciences",
    },
    {
      icon: Award,
      title: "Literature Reviews",
      description: "Source evaluation and research summaries",
    },
    {
      icon: GraduationCap,
      title: "Thesis & Dissertations",
      description: "Comprehensive research and writing assistance",
    },
  ];

  const whyChooseUs = [
    {
      icon: Shield,
      title: "Trusted & Transparent",
      description: "Clear and safe process for all users",
    },
    {
      icon: Lightbulb,
      title: "Support for Learning",
      description: "Tools and guides to improve academic skills",
    },
    {
      icon: Clock,
      title: "Fast & Reliable",
      description: "Meet deadlines without compromising quality",
    },
    {
      icon: Heart,
      title: "Fair & Rewarding",
      description: "Fair payment for writers, great value for students",
    },
  ];

  const integrityFeatures = [
    "Original Work Only - 100% plagiarism-free content",
    "Proper Citations - Correct referencing (APA, MLA, Chicago)",
    "Multiple Writing Styles - Descriptive, analytical, persuasive, critical",
    "Ethical Guidelines - No shortcuts or unethical practices",
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-background to-purple-50 dark:from-blue-950/20 dark:via-background dark:to-purple-950/20">
      <div className="max-w-7xl mx-auto p-6 space-y-12">
        {/* Hero Section */}
        <div className="text-center space-y-6 py-12">
          <div className="inline-flex items-center gap-2 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300 px-4 py-2 rounded-full text-sm font-medium">
            <Star className="w-4 h-4" />
            Welcome to Excellence
          </div>
          <h1 className="text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400 bg-clip-text text-transparent leading-tight">
            Academic Writing Platform
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Connect with skilled writers and deliver exceptional academic
            content in a supportive, ethical environment designed for your
            success.
          </p>
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4 mb-8">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <Target className="w-4 h-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="purpose" className="flex items-center gap-2">
              <Heart className="w-4 h-4" />
              Our Purpose
            </TabsTrigger>
            <TabsTrigger value="services" className="flex items-center gap-2">
              <BookOpen className="w-4 h-4" />
              Services
            </TabsTrigger>
            <TabsTrigger value="integrity" className="flex items-center gap-2">
              <Shield className="w-4 h-4" />
              Integrity
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-8">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2">
              <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-blue-500 dark:bg-card dark:border-l-blue-400">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 dark:bg-blue-900/50 rounded-lg">
                      <Users className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <CardTitle className="text-xl">Platform Overview</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                    Our academic writing platform connects skilled writers with
                    clients who need high-quality academic content. Access
                    various assignments and tools needed to deliver exceptional
                    work.
                  </p>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-green-500">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <GraduationCap className="w-6 h-6 text-green-600" />
                    </div>
                    <CardTitle className="text-xl">Your Role</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 leading-relaxed">
                    Create original, well-researched academic content that meets
                    quality standards and client requirements. Your expertise
                    helps students achieve their academic goals.
                  </p>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-purple-500">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <Award className="w-6 h-6 text-purple-600" />
                    </div>
                    <CardTitle className="text-xl">Quality Standards</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 leading-relaxed">
                    We maintain high standards including proper formatting,
                    accurate citations, original content, and adherence to
                    academic writing conventions.
                  </p>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-orange-500">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-orange-100 rounded-lg">
                      <Heart className="w-6 h-6 text-orange-600" />
                    </div>
                    <CardTitle className="text-xl">Support System</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 leading-relaxed">
                    Our support team is available for questions and concerns. We
                    provide resources and guidelines to ensure your success on
                    the platform.
                  </p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="purpose" className="space-y-8">
            <Alert className="border-blue-200 bg-blue-50">
              <Target className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-800 font-medium">
                Our platform is designed with one clear goal: to connect
                students with skilled academic writers in a safe, supportive,
                and ethical environment.
              </AlertDescription>
            </Alert>

            <div className="grid gap-8 lg:grid-cols-2">
              <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3 text-blue-800">
                    <GraduationCap className="w-6 h-6" />
                    For Students
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                    <span className="text-blue-700">
                      Fast access to qualified writers
                    </span>
                  </div>
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                    <span className="text-blue-700">
                      Helpful writing resources and guidance
                    </span>
                  </div>
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                    <span className="text-blue-700">
                      Support for all types of academic tasks
                    </span>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3 text-green-800">
                    <Users className="w-6 h-6" />
                    For Writers
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span className="text-green-700">
                      Professional space to find writing jobs
                    </span>
                  </div>
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span className="text-green-700">
                      Clear quality standards and guidelines
                    </span>
                  </div>
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span className="text-green-700">
                      Career growth with tools and support
                    </span>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card className="bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
              <CardHeader>
                <CardTitle className="text-center text-purple-800">
                  Our Commitment to Academic Integrity
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-purple-700 leading-relaxed">
                  We are committed to maintaining academic integrity in all
                  services. This means no shortcuts, no copied work, and no
                  unethical practices. Instead, we focus on guiding both
                  students and writers toward success through quality and
                  honesty.
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="services" className="space-y-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
                Services We Cover
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300">
                From simple essays to complete dissertations, we support you at
                every academic stage
              </p>
            </div>

            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {services.map((service, index) => (
                <Card
                  key={index}
                  className="hover:shadow-lg transition-all duration-300 group dark:bg-card"
                >
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <div className="p-3 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/50 dark:to-purple-900/50 rounded-lg group-hover:scale-110 transition-transform">
                        <service.icon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                      </div>
                      <CardTitle className="text-lg">{service.title}</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 dark:text-gray-300">
                      {service.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>

            <Alert className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/30 dark:to-blue-950/30 border-green-200 dark:border-green-800">
              <BookOpen className="h-4 w-4 text-green-600 dark:text-green-400" />
              <AlertDescription className="text-green-800 dark:text-green-300">
                <strong>Academic Standards:</strong> Our writing system aligns
                with widely accepted academic standards, ensuring your work not
                only meets but exceeds expectations.
              </AlertDescription>
            </Alert>
          </TabsContent>

          <TabsContent value="integrity" className="space-y-8">
            <div className="text-center mb-8">
              <Badge
                variant="outline"
                className="mb-4 text-sm px-4 py-2 dark:border-blue-800"
              >
                <Shield className="w-4 h-4 mr-2" />
                Academic Integrity Focus
              </Badge>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Ethical Excellence
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Every assignment follows strict ethical guidelines to maintain
                the highest standards of academic integrity
              </p>
            </div>

            <div className="grid gap-6 md:grid-cols-2">
              <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3 text-blue-800">
                    <Shield className="w-6 h-6" />
                    Our Integrity Standards
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {integrityFeatures.map((feature, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <CheckCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                      <span className="text-blue-700 text-sm">{feature}</span>
                    </div>
                  ))}
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-purple-50 to-pink-50 border-purple-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3 text-purple-800">
                    <Lightbulb className="w-6 h-6" />
                    Learning & Growth
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-purple-700 leading-relaxed mb-4">
                    We help both students and writers understand the value of
                    learning and critical thinking, not just submitting work for
                    deadlines.
                  </p>
                  <div className="bg-white/60 p-4 rounded-lg">
                    <p className="text-sm text-purple-600 font-medium">
                      &quot;Education, honesty, and progress&quot; - Our core
                      values
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        <Separator className="my-12" />

        {/* Why Choose Us Section */}
        <div className="space-y-8">
          <div className="text-center">
            <Badge
              variant="outline"
              className="mb-4 text-sm px-4 py-2 dark:border-blue-800"
            >
              <Star className="w-4 h-4 mr-2" />
              Why Choose Us
            </Badge>
            <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400 bg-clip-text text-transparent mb-4">
              The Right Choice for Success
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300">
              Whether you are a student or writer, here is what makes us
              different
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            {whyChooseUs.map((item, index) => (
              <Card
                key={index}
                className="text-center hover:shadow-lg transition-all duration-300 group dark:bg-card"
              >
                <CardHeader>
                  <div className="mx-auto p-4 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/50 dark:to-purple-900/50 rounded-full w-fit group-hover:scale-110 transition-transform">
                    <item.icon className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                  </div>
                  <CardTitle className="text-lg">{item.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    {item.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <Card className="bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-800 dark:to-purple-800 text-white text-center">
          <CardContent className="py-12">
            <h3 className="text-2xl font-bold mb-4">Ready to Get Started?</h3>
            <p className="text-blue-100 dark:text-blue-200 mb-6 max-w-2xl mx-auto">
              Join our platform today and experience the difference that
              quality, integrity, and support can make in your academic journey.
            </p>
            <div className="flex items-center justify-center gap-2 text-sm">
              <Heart className="w-4 h-4" />
              <span>Built with care for academic excellence</span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
