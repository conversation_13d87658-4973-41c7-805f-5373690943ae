"use client";
import React, { useState } from "react";
import { BlogHeader } from "@/components/blog/BlogHeader";
import { BlogIntroSection } from "@/components/blog/BlogIntroSection";
import { BlogPostsGrid } from "@/components/blog/BlogPostsGrid";
import { BlogCategoryNav } from "@/components/blog/BlogCategoryNav";
// import { DynamicBreadcrumbs } from "@/components/layout/DynamicBreadcrumbs";

export default function BlogMain() {
  const [search, setSearch] = useState("");

  return (
    <main className="container mx-auto px-2 md:px-8 lg:px-16 py-8" itemScope itemType="https://schema.org/Blog">
      {/* <DynamicBreadcrumbs /> */}
      <BlogHeader />
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-8">
        <BlogCategoryNav />
        <div className="relative w-full md:w-auto md:min-w-[240px]">
          <input
            type="search"
            placeholder="Search posts..."
            className="w-full px-4 py-2 rounded-md border border-input bg-background text-sm"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>
      </div>
      <BlogIntroSection />
      <BlogPostsGrid />
    </main>
  );
}
