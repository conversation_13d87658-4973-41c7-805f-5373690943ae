// components/blog/single/TableOfContents.tsx
'use client';

import { useState, useEffect } from 'react';
import { List } from 'lucide-react';

interface Heading {
  id: string;
  text: string;
  level: number;
}

interface TableOfContentsProps {
  headings: Heading[];
}

export default function TableOfContents({ headings }: TableOfContentsProps) {
  const [activeId, setActiveId] = useState<string>('');
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Debug: Log the headings received by TOC
  useEffect(() => {
    console.log('TOC: Received headings:', headings);
  }, [headings]);

  useEffect(() => {
    if (headings.length === 0) return;

    const observer = new IntersectionObserver(
      (entries) => {
        // Find the entry that's most visible
        const visibleEntries = entries.filter(entry => entry.isIntersecting);
        if (visibleEntries.length > 0) {
          // Sort by intersection ratio and pick the most visible one
          const mostVisible = visibleEntries.sort((a, b) => b.intersectionRatio - a.intersectionRatio)[0];
          setActiveId(mostVisible.target.id);
        }
      },
      {
        rootMargin: '-10% 0% -50% 0%', // Adjusted for better detection
        threshold: [0, 0.25, 0.5, 0.75, 1], // Multiple thresholds for better accuracy
      }
    );

    // Add a small delay to ensure elements are in the DOM
    const timer = setTimeout(() => {
      headings.forEach(({ id }) => {
        const element = document.getElementById(id);
        if (element) {
          observer.observe(element);
        } else {
          console.warn(`Heading element with id "${id}" not found in DOM`);
        }
      });
    }, 200);

    return () => {
      clearTimeout(timer);
      observer.disconnect();
    };
  }, [headings]);

  const scrollToHeading = (id: string) => {
    console.log('TOC: Attempting to scroll to:', id);
    console.log('TOC: Available headings:', headings.map(h => ({ id: h.id, text: h.text })));

    const element = document.getElementById(id);

    if (element) {
      console.log('TOC: Element found:', element);

      // Use the CSS scroll-margin-top we defined
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });

      // Update the active heading immediately for better UX
      setActiveId(id);
    } else {
      console.error('TOC: Element not found with ID:', id);

      // Debug: List all elements with IDs in the blog content
      const blogContent = document.querySelector('.blog-content');
      if (blogContent) {
        const allElementsWithIds = blogContent.querySelectorAll('[id]');
        console.log('TOC: All elements with IDs in blog content:', Array.from(allElementsWithIds).map(el => ({ tag: el.tagName, id: el.id, text: el.textContent?.substring(0, 50) })));

        // Try to find by text content as fallback
        const allHeadings = blogContent.querySelectorAll('h1, h2, h3, h4, h5, h6');
        const targetHeading = Array.from(allHeadings).find(h => {
          const headingData = headings.find(hd => hd.text === h.textContent);
          return headingData && headingData.id === id;
        });

        if (targetHeading) {
          console.log('TOC: Found heading via text match, scrolling to:', targetHeading);
          targetHeading.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
          });
        }
      }
    }
  };

  if (headings.length === 0) return null;

  return (
    <div className="bg-muted/30 rounded-lg p-4">
      <div className="flex items-center justify-between mb-3">
        <h4 className="text-sm font-medium text-foreground flex items-center space-x-2">
          <List className="w-4 h-4" />
          <span>Table of Contents</span>
        </h4>
        <button
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="lg:hidden text-xs text-muted-foreground hover:text-foreground transition-colors"
        >
          {isCollapsed ? 'Show' : 'Hide'}
        </button>
      </div>

      <nav className={`space-y-1 ${isCollapsed ? 'hidden lg:block' : 'block'}`}>
        <ul className="space-y-1">
          {headings.map((heading) => (
            <li key={heading.id}>
              <button
                onClick={() => scrollToHeading(heading.id)}
                className={`
                  w-full text-left text-xs leading-relaxed transition-colors duration-200
                  hover:text-foreground hover:bg-muted/50 rounded px-2 py-1
                  ${activeId === heading.id 
                    ? 'text-foreground bg-muted/50 font-medium' 
                    : 'text-muted-foreground'
                  }
                  ${heading.level === 1 ? 'pl-2' : ''}
                  ${heading.level === 2 ? 'pl-4' : ''}
                  ${heading.level === 3 ? 'pl-6' : ''}
                  ${heading.level === 4 ? 'pl-8' : ''}
                  ${heading.level === 5 ? 'pl-10' : ''}
                  ${heading.level === 6 ? 'pl-12' : ''}
                `}
              >
                {heading.text}
              </button>
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
}