// src/hooks/use-session-user-id.ts

"use client";

import { useSession } from "next-auth/react";
import { useState, useEffect } from "react";

// Hook to get the current user's MongoDB ID from session
export function useCurrentUserId(): {
  userId: string | null;
  loading: boolean;
  error: string | null;
} {
  const { data: session, status } = useSession();

  const loading = status === "loading";
  const userId = session?.user?.id || null;
  const error = status === "unauthenticated" ? "User not authenticated" : null;

  return {
    userId,
    loading,
    error,
  };
}

// Enhanced hook to check user role
export function useUserRole() {
  const { data: session, status } = useSession();
  const [userRole, setUserRole] = useState<string | null>(null);
  const [roleLoading, setRoleLoading] = useState(false);
  const [roleError, setRoleError] = useState<string | null>(null);

  const loading = status === "loading" || roleLoading;
  const userId = session?.user?.id || null;
  const error = status === "unauthenticated" ? "User not authenticated" : null;

  const checkUserRole = async (targetUserId: string): Promise<string | null> => {
    if (!targetUserId) return null;

    try {
      setRoleLoading(true);
      setRoleError(null);

      const response = await fetch(`/api/users/${targetUserId}/role`);

      if (!response.ok) {
        throw new Error(`Failed to fetch user role: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success && data.data?.role) {
        setUserRole(data.data.role);
        return data.data.role;
      } else {
        throw new Error(data.message || "Failed to fetch user role");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch user role";
      setRoleError(errorMessage);
      console.error("Error fetching user role:", err);
      return null;
    } finally {
      setRoleLoading(false);
    }
  };

  // Auto-fetch role when userId is available
  useEffect(() => {
    if (userId && !userRole && !roleLoading) {
      checkUserRole(userId);
    }
  }, [userId, userRole, roleLoading]);

  return {
    userId,
    userRole,
    loading,
    error,
    checkUserRole,
    roleError,
  };
}
