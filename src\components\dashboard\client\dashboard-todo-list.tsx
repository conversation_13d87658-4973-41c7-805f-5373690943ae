// src/components/dashboard/client/dashboard-todo-list.tsx
"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Plus,
  Clock,
  Calendar,
  Flag,
  Zap,
  CheckCircle2,
  Circle,
  Sparkles,
  Loader2,
} from "lucide-react";
import { useTodos } from "@/hooks/use-todos";
import { Priority } from "@prisma/client";

const ClientTodoList: React.FC = () => {
  const {
    todos,
    loading,
    error,
    createTodo,
    toggleTodo,
    totalCount,
  } = useTodos();

  const [activeTab, setActiveTab] = useState("all");
  const [isAddingTodo, setIsAddingTodo] = useState(false);
  const [newTodo, setNewTodo] = useState({
    title: "",
    description: "",
    priority: "MEDIUM" as Priority,
    category: "",
    dueDate: "",
  });

  const handleAddTodo = async () => {
    if (!newTodo.title.trim()) return;

    const success = await createTodo({
      title: newTodo.title,
      description: newTodo.description || null,
      priority: newTodo.priority,
      category: newTodo.category || null,
      dueDate: newTodo.dueDate && newTodo.dueDate.trim() ? newTodo.dueDate : null,
    });

    if (success) {
      setNewTodo({
        title: "",
        description: "",
        priority: "MEDIUM" as Priority,
        category: "",
        dueDate: "",
      });
      setIsAddingTodo(false);
    }
  };

  const getPriorityIcon = (priority: Priority) => {
    switch (priority) {
      case "HIGH":
        return <Flag className="h-3 w-3" />;
      case "MEDIUM":
        return <Clock className="h-3 w-3" />;
      case "LOW":
        return <Circle className="h-3 w-3" />;
      default:
        return <Circle className="h-3 w-3" />;
    }
  };

  const getPriorityColor = (priority: Priority) => {
    switch (priority) {
      case "HIGH":
        return "bg-orange-500 text-white";
      case "MEDIUM":
        return "bg-yellow-500 text-black";
      case "LOW":
        return "bg-muted text-muted-foreground";
      default:
        return "bg-muted text-muted-foreground";
    }
  };

  const filteredTodos = todos.filter((todo) => {
    if (activeTab === "all") return true;
    if (activeTab === "active") return !todo.isCompleted;
    if (activeTab === "done") return todo.isCompleted;
    return false;
  });

  const completedCount = todos.filter((todo) => todo.isCompleted).length;
  const completionPercentage =
    totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0;

  if (loading) {
    return (
      <Card className="max-h-[600px] h-full flex flex-col bg-gradient-to-br from-card via-card/95 to-muted/30 border-2 border-border/50 backdrop-blur-sm">
        <CardContent className="flex items-center justify-center h-full">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">Loading todos...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="max-h-[600px] h-full flex flex-col bg-gradient-to-br from-card via-card/95 to-muted/30 border-2 border-border/50 backdrop-blur-sm">
      <CardHeader className="pb-3 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="p-2 rounded-lg bg-primary/10 border border-primary/20">
              <Zap className="h-4 w-4 text-primary" />
            </div>
            <div>
              <CardTitle className="text-lg font-semibold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">
                Client Tasks
              </CardTitle>
              <p className="text-xs text-muted-foreground mt-0.5">
                {completedCount} of {totalCount} completed (
                {completionPercentage}%)
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              onClick={() => setIsAddingTodo(!isAddingTodo)}
              className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 transition-all duration-300 hover:scale-105 shadow-lg"
            >
              <Plus className="h-3 w-3 mr-1" />
              Add
            </Button>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mt-3">
          <div className="flex justify-between text-xs text-muted-foreground mb-1">
            <span>Progress</span>
            <span>{completionPercentage}%</span>
          </div>
          <div className="w-full bg-muted/50 rounded-full h-2 overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-primary via-primary/80 to-primary/60 transition-all duration-700 ease-out shadow-sm"
              style={{ width: `${completionPercentage}%` }}
            />
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0 flex-1 flex flex-col min-h-0 overflow-hidden">
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="flex-1 flex flex-col h-full"
        >
          <TabsList className="grid w-full grid-cols-3 mb-4 bg-muted/30 p-1 flex-shrink-0">
            <TabsTrigger value="all" className="text-xs">
              All
            </TabsTrigger>
            <TabsTrigger value="active" className="text-xs">
              Active
            </TabsTrigger>
            <TabsTrigger value="done" className="text-xs">
              Done
            </TabsTrigger>
          </TabsList>

          {/* Quick Add Task Input */}
          {isAddingTodo && (
            <div className="mb-4 p-3 bg-muted/20 rounded-lg border border-dashed border-primary/30 flex-shrink-0">
              <div className="space-y-3">
                <Input
                  placeholder="Add a new task..."
                  value={newTodo.title}
                  onChange={(e) => setNewTodo({ ...newTodo, title: e.target.value })}
                  className="text-sm"
                />
                <div className="flex flex-col sm:flex-row gap-2">
                  <Select
                    value={newTodo.priority}
                    onValueChange={(value) => setNewTodo({ ...newTodo, priority: value as Priority })}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="LOW">Low</SelectItem>
                      <SelectItem value="MEDIUM">Medium</SelectItem>
                      <SelectItem value="HIGH">High</SelectItem>
                    </SelectContent>
                  </Select>
                  <Input
                    type="date"
                    value={newTodo.dueDate}
                    onChange={(e) => setNewTodo({ ...newTodo, dueDate: e.target.value })}
                    className="w-full"
                  />
                </div>
                <div className="flex flex-col sm:flex-row gap-2">
                  <Button size="sm" onClick={handleAddTodo} disabled={!newTodo.title.trim()} className="w-full sm:w-auto">
                    <Plus className="h-3 w-3 mr-1" />
                    Add Task
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => setIsAddingTodo(false)} className="w-full sm:w-auto">
                    Cancel
                  </Button>
                </div>
              </div>
            </div>
          )}

          <Separator className="mb-4 flex-shrink-0" />

          <TabsContent
            value={activeTab}
            className="mt-0 flex-1 min-h-0 overflow-hidden"
          >
            <ScrollArea className="h-full">
              <div className="space-y-2 pr-4">
                {filteredTodos.map((todo, index) => (
                  <div
                    key={todo.id}
                    className={`group relative p-3 rounded-lg border transition-all duration-300 hover:shadow-md hover:border-primary/30 ${
                      todo.isCompleted
                        ? "bg-muted/30 border-muted/50"
                        : "bg-card/50 border-border/50 hover:bg-card/80"
                    } backdrop-blur-sm`}
                    style={{
                      animationDelay: `${index * 50}ms`,
                      animation: "fadeInUp 0.5s ease-out forwards",
                    }}
                  >
                    <div className="flex items-start gap-3">
                      <div className="mt-0.5">
                        <Checkbox
                          checked={todo.isCompleted}
                          onCheckedChange={() => toggleTodo(todo.id)}
                          className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                        />
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h4
                            className={`font-medium text-sm leading-tight ${
                              todo.isCompleted
                                ? "line-through text-muted-foreground"
                                : "text-foreground"
                            }`}
                          >
                            {todo.title}
                          </h4>
                          {todo.isCompleted && (
                            <CheckCircle2 className="h-3 w-3 text-green-500 animate-pulse" />
                          )}
                        </div>

                        {todo.description && (
                          <p
                            className={`text-xs mb-2 leading-relaxed ${
                              todo.isCompleted
                                ? "text-muted-foreground/70"
                                : "text-muted-foreground"
                            }`}
                          >
                            {todo.description}
                          </p>
                        )}

                        <div className="flex items-center gap-1.5 flex-wrap">
                          <Badge
                            variant="secondary"
                            className={`text-xs px-2 py-0.5 ${getPriorityColor(
                              todo.priority
                            )} flex items-center gap-1`}
                          >
                            {getPriorityIcon(todo.priority)}
                            {todo.priority.toLowerCase()}
                          </Badge>

                          {todo.category && (
                            <Badge
                              variant="secondary"
                              className="text-xs px-2 py-0.5 bg-blue-500 text-white"
                            >
                              {todo.category}
                            </Badge>
                          )}

                          {todo.dueDate && (
                            <Badge
                              variant="outline"
                              className="text-xs px-2 py-0.5 flex items-center gap-1"
                            >
                              <Calendar className="h-2.5 w-2.5" />
                              {new Date(todo.dueDate).toLocaleDateString()}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Hover effect */}
                    <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
                  </div>
                ))}

                {filteredTodos.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    <Sparkles className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">
                      {activeTab === "all" && "No tasks found"}
                      {activeTab === "active" && "No active tasks"}
                      {activeTab === "done" && "No completed tasks"}
                    </p>
                    <p className="text-xs mt-1">
                      {activeTab !== "done" && "Time to add some goals!"}
                    </p>
                  </div>
                )}

                {error && (
                  <div className="text-center py-8 text-destructive">
                    <p className="text-sm">Error loading todos</p>
                    <p className="text-xs mt-1">{error}</p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </CardContent>

      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </Card>
  );
};

export default ClientTodoList;
