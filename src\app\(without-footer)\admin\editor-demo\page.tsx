"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Eye, Code } from "lucide-react";
import HeadlessQuillEditor from "@/components/quill-editor/headless-editor";
import HybridQuillEditor from "@/components/quill-editor/hybrid-editor";
import QuillEditor from "@/components/quill-editor";

export default function EditorDemoPage() {
  const [headlessContent, setHeadlessContent] = useState("<p>Select this text to see the headless toolbar in action! Try selecting different parts of this content to see how the floating toolbar appears above your selection.</p><p><strong>Bold text</strong>, <em>italic text</em>, and <u>underlined text</u> can all be formatted using the headless toolbar.</p><h2>This is a heading</h2><p>You can also create lists:</p><ul><li>First item</li><li>Second item</li><li>Third item</li></ul><p>The toolbar is responsive and will adjust its position to stay within the viewport on both desktop and mobile devices.</p>");

  const [hybridContent, setHybridContent] = useState("<p>This is the hybrid editor with BOTH traditional toolbar at the top AND headless toolbar when you select text! This gives you the best of both worlds - always accessible tools at the top, plus context-aware floating toolbar.</p><p>Try selecting this text to see the floating toolbar appear while still having access to the full toolbar above.</p>");

  const [traditionalContent, setTraditionalContent] = useState("<p>This is the traditional Quill editor with the standard toolbar at the top. Compare the user experience with the headless version above.</p>");
  const [showPreview, setShowPreview] = useState<boolean>(false);

  const stripHtml = (html: string) => {
    return html.replace(/<[^>]+>/g, '');
  };

  return (
    <div className="container mx-auto py-8 px-4 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">Quill Editor Demo</h1>
        <p className="text-muted-foreground">
          Compare the traditional Quill editor with our new headless toolbar implementation.
          The headless toolbar appears when you select text, providing a more modern and intuitive editing experience.
        </p>
      </div>

      <div className="grid gap-8 lg:grid-cols-3">
        {/* Headless Editor */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  Headless Only
                  <Badge variant="secondary">Pure</Badge>
                </CardTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  Select text to see the floating toolbar
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowPreview(!showPreview)}
                  className="flex items-center gap-1"
                >
                  {showPreview ? <Code className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                  {showPreview ? 'Code' : 'Preview'}
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {showPreview ? (
              <div className="min-h-[300px] p-4 border rounded-md bg-muted/30">
                <div className="prose prose-sm max-w-none" dangerouslySetInnerHTML={{ __html: headlessContent }} />
              </div>
            ) : (
              <HeadlessQuillEditor
                value={headlessContent}
                onChange={setHeadlessContent}
                placeholder="Start typing and select text to see the headless toolbar..."
                className="min-h-[300px]"
                showHeadlessToolbar={true}
              />
            )}
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <span>Characters: {stripHtml(headlessContent).length}</span>
              <span>•</span>
              <span>Words: {stripHtml(headlessContent).split(/\s+/).filter(word => word.length > 0).length}</span>
            </div>
          </CardContent>
        </Card>

        {/* Hybrid Editor */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  Hybrid Editor
                  <Badge variant="default">Best</Badge>
                </CardTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  Both top toolbar AND floating toolbar
                </p>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <HybridQuillEditor
              value={hybridContent}
              onChange={setHybridContent}
              placeholder="Best of both worlds - traditional + headless toolbar..."
              className="min-h-[300px]"
              showHeadlessToolbar={true}
            />
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <span>Characters: {stripHtml(hybridContent).length}</span>
              <span>•</span>
              <span>Words: {stripHtml(hybridContent).split(/\s+/).filter(word => word.length > 0).length}</span>
            </div>
          </CardContent>
        </Card>

        {/* Traditional Editor */}
        <Card>
          <CardHeader>
            <CardTitle>Traditional Only</CardTitle>
            <p className="text-sm text-muted-foreground">
              Standard Quill editor with fixed toolbar
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <QuillEditor
              value={traditionalContent}
              onChange={setTraditionalContent}
              placeholder="Traditional editor with toolbar at the top..."
              className="min-h-[300px]"
            />
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <span>Characters: {stripHtml(traditionalContent).length}</span>
              <span>•</span>
              <span>Words: {stripHtml(traditionalContent).split(/\s+/).filter(word => word.length > 0).length}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Features Comparison */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Feature Comparison</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-2">
            <div>
              <h3 className="font-semibold text-green-600 mb-3">Headless Toolbar Benefits</h3>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <span className="text-green-500 mt-0.5">✓</span>
                  <span>Context-aware toolbar appears only when needed</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-green-500 mt-0.5">✓</span>
                  <span>Saves vertical space in the interface</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-green-500 mt-0.5">✓</span>
                  <span>Modern, intuitive user experience</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-green-500 mt-0.5">✓</span>
                  <span>Responsive design for mobile devices</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-green-500 mt-0.5">✓</span>
                  <span>Automatic positioning to stay in viewport</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-green-500 mt-0.5">✓</span>
                  <span>Cleaner, less cluttered interface</span>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-blue-600 mb-3">Traditional Toolbar</h3>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <span className="text-blue-500 mt-0.5">•</span>
                  <span>Always visible toolbar</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-blue-500 mt-0.5">•</span>
                  <span>Familiar interface for users</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-blue-500 mt-0.5">•</span>
                  <span>All tools immediately accessible</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-blue-500 mt-0.5">•</span>
                  <span>Fixed position at top of editor</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-orange-500 mt-0.5">⚠</span>
                  <span>Takes up more vertical space</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-orange-500 mt-0.5">⚠</span>
                  <span>Can feel cluttered on smaller screens</span>
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Usage Instructions */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>How to Use the Headless Toolbar</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid gap-4 md:grid-cols-3">
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl mb-2">1️⃣</div>
                <h4 className="font-semibold mb-2">Select Text</h4>
                <p className="text-sm text-muted-foreground">
                  Highlight any text in the headless editor above
                </p>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl mb-2">2️⃣</div>
                <h4 className="font-semibold mb-2">Toolbar Appears</h4>
                <p className="text-sm text-muted-foreground">
                  A floating toolbar will appear above your selection
                </p>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl mb-2">3️⃣</div>
                <h4 className="font-semibold mb-2">Format Text</h4>
                <p className="text-sm text-muted-foreground">
                  Click any formatting option to apply it
                </p>
              </div>
            </div>
            
            <Separator />
            
            <div>
              <h4 className="font-semibold mb-2">Available Formatting Options</h4>
              <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-3 text-sm">
                <div className="flex items-center gap-2">
                  <Badge variant="outline">Bold</Badge>
                  <span className="text-muted-foreground">Make text bold</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">Italic</Badge>
                  <span className="text-muted-foreground">Make text italic</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">Underline</Badge>
                  <span className="text-muted-foreground">Underline text</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">Strike</Badge>
                  <span className="text-muted-foreground">Strikethrough text</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">Quote</Badge>
                  <span className="text-muted-foreground">Create blockquote</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">Code</Badge>
                  <span className="text-muted-foreground">Code block</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">H1/H2</Badge>
                  <span className="text-muted-foreground">Headings</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">Lists</Badge>
                  <span className="text-muted-foreground">Bullet/numbered lists</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">Align</Badge>
                  <span className="text-muted-foreground">Text alignment</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">Link</Badge>
                  <span className="text-muted-foreground">Insert links</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">Image</Badge>
                  <span className="text-muted-foreground">Insert images</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">Video</Badge>
                  <span className="text-muted-foreground">Embed videos</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
