// src/components/dashboard/admin/quick-create-assignment.tsx
"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { useCurrentUserId } from "@/hooks/use-session-user-id";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
// import {
//   Command,
//   CommandEmpty,
//   CommandGroup,
//   CommandInput,
//   CommandItem,
//   CommandList,
// } from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Upload, X, FileText } from "lucide-react";

import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from "sonner";

import { InlineCouponInput } from "@/components/ui/coupon-input";
import { usePriceCalculation } from "@/hooks/use-pricing-realtime";
import { AcademicLevel, Priority, Spacing } from "@prisma/client";

// Client interface
// interface Client {
//   id: string;
//   name: string;
//   email: string;
// }

// Enums from Prisma schema
const assignmentTypeOptions = [
  { label: "Essay", value: "ESSAY" },
  { label: "Research Paper", value: "RESEARCH_PAPER" },
  { label: "Term Paper", value: "TERM_PAPER" },
  { label: "Dissertation", value: "DISSERTATION" },
  { label: "Thesis", value: "THESIS" },
  { label: "Book Review", value: "BOOK_REVIEW" },
  { label: "Article Review", value: "ARTICLE_REVIEW" },
  { label: "Case Study", value: "CASE_STUDY" },
  { label: "Discussion", value: "DISCUSSION" },
  { label: "Lab Report", value: "LAB_REPORT" },
  { label: "Literature Review", value: "LITERATURE_REVIEW" },
  { label: "Personal Statement", value: "PERSONAL_STATEMENT" },
  { label: "Reflection Paper", value: "REFLECTION_PAPER" },
  { label: "Other", value: "OTHER" },
];

const priorityOptions = [
  { value: "LOW", label: "Low Priority" },
  { value: "MEDIUM", label: "Medium Priority" },
  { value: "HIGH", label: "High Priority" },
] as const;

const academicLevelOptions = [
  { value: "HIGH_SCHOOL", label: "High School" },
  { value: "UNDERGRADUATE", label: "Undergraduate" },
  { value: "MASTERS", label: "Masters" },
  { value: "PHD", label: "PhD" },
  { value: "PROFESSIONAL", label: "Professional" },
] as const;

const spacingOptions = [
  { value: "SINGLE", label: "Single Spaced" },
  { value: "DOUBLE", label: "Double Spaced" },
] as const;

const languageStyleOptions = [
  { value: "ENGLISH_US", label: "English (US)" },
  { value: "ENGLISH_UK", label: "English (UK)" },
  { value: "ENGLISH_AU", label: "English (AU)" },
  { value: "OTHER", label: "Other" },
] as const;

const formatStyleOptions = [
  { value: "APA", label: "APA" },
  { value: "MLA", label: "MLA" },
  { value: "CHICAGO", label: "Chicago" },
  { value: "HARVARD", label: "Harvard" },
  { value: "IEEE", label: "IEEE" },
  { value: "OTHER", label: "Other" },
] as const;

// Form validation schema - matches server-side assignmentCreateSchema
const createAssignmentSchema = z.object({
  title: z.string().min(1, "Title is required").max(200, "Title is too long"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  assignmentType: z.enum([
    "ESSAY",
    "RESEARCH_PAPER",
    "TERM_PAPER",
    "DISSERTATION",
    "THESIS",
    "BOOK_REVIEW",
    "ARTICLE_REVIEW",
    "CASE_STUDY",
    "DISCUSSION",
    "LAB_REPORT",
    "LITERATURE_REVIEW",
    "PERSONAL_STATEMENT",
    "REFLECTION_PAPER",
    "OTHER",
  ]),
  subject: z.string().min(1, "Subject is required"),
  service: z.string().min(1, "Service is required"),
  pageCount: z.number().min(1, "Page count must be at least 1"),
  priority: z.enum(["LOW", "MEDIUM", "HIGH"]),
  academicLevel: z.enum([
    "HIGH_SCHOOL",
    "UNDERGRADUATE",
    "MASTERS",
    "PHD",
    "PROFESSIONAL",
  ]),
  spacing: z.enum(["SINGLE", "DOUBLE"]),
  languageStyle: z.enum(["ENGLISH_US", "ENGLISH_UK", "ENGLISH_AU", "OTHER"]),
  formatStyle: z.enum(["APA", "MLA", "CHICAGO", "HARVARD", "IEEE", "OTHER"]),
  numSources: z.number().min(0, "Number of sources cannot be negative"),
  guidelines: z.string().optional(),
  estTime: z.date({
    required_error: "Estimated completion time is required",
  }),
  // Additional fields that will be added during submission
  taskId: z.string().optional(), // Will be generated
  clientId: z.string().optional(), // Will be set to current user
  price: z.number().optional(), // Will be calculated
});

type CreateAssignmentForm = z.infer<typeof createAssignmentSchema>;

interface ClientQuickCreateAssignmentProps {
  children: React.ReactNode;
  onSuccess?: () => void;
}

export function ClientQuickCreateAssignment({
  children,
  onSuccess,
}: ClientQuickCreateAssignmentProps) {
  const [open, setOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [appliedCoupon, setAppliedCoupon] = useState<{
    code: string;
    discountAmount: number;
  } | null>(null);
  const { userId } = useCurrentUserId();

  const form = useForm<CreateAssignmentForm>({
    resolver: zodResolver(createAssignmentSchema),
    defaultValues: {
      assignmentType: "ESSAY",
      priority: "MEDIUM",
      academicLevel: "UNDERGRADUATE",
      spacing: "DOUBLE",
      languageStyle: "ENGLISH_US",
      formatStyle: "APA",
      numSources: 0,
      pageCount: 1,
      service: "Academic Writing",
      title: "",
      description: "",
      subject: "",
      guidelines: "",
    },
  });

  const watchedValues = form.watch();

  // Use real-time pricing hook
  const { priceBreakdown } = usePriceCalculation({
    academicLevel: (watchedValues.academicLevel as AcademicLevel) || AcademicLevel.UNDERGRADUATE,
    priority: (watchedValues.priority as Priority) || Priority.MEDIUM,
    spacing: (watchedValues.spacing as Spacing) || Spacing.DOUBLE,
    pageCount: watchedValues.pageCount || 1,
  });

  const calculatedPrice = priceBreakdown?.finalPrice || 0;

  const handleFileUpload = (files: FileList) => {
    const fileArray = Array.from(files);
    setUploadedFiles((prev) => [...prev, ...fileArray]);
  };

  const handleFileRemove = (fileName: string) => {
    setUploadedFiles((prev) => prev.filter((file) => file.name !== fileName));
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleCouponApplied = (discountAmount: number, finalPrice: number, couponCode: string) => {
    setAppliedCoupon({ code: couponCode, discountAmount });
    toast.success(`Coupon ${couponCode} applied! Discount: $${discountAmount.toFixed(2)}`);
  };

  const handleCouponRemoved = () => {
    setAppliedCoupon(null);
    toast.info("Coupon removed");
  };

  // Replace the existing generateTaskId function with this improved version
  const generateTaskId = () => {
    const now = new Date();

    // Get current date (pad with 0 if single digit)
    const day = now.getDate().toString().padStart(2, "0");

    // Get current month (pad with 0 if single digit, months are 0-indexed so add 1)
    const month = (now.getMonth() + 1).toString().padStart(2, "0");

    // Get last 2 digits of current year
    const year = now.getFullYear().toString().slice(-2);

    // Generate 3-digit random string (000-999)
    const randomStr = Math.floor(Math.random() * 1000)
      .toString()
      .padStart(3, "0");

    return `TASK-${day}${month}${year}${randomStr}`;
  };

  const onSubmit = async (data: CreateAssignmentForm) => {
    if (!userId) {
      toast.error("User ID not found. Please log in again.");
      return;
    }

    setIsSubmitting(true);

    try {
      const taskId = generateTaskId();

      const submitData = {
        ...data,
        taskId,
        clientId: userId, // Use the current user's ID
        price: calculatedPrice, // Add calculated price
        estTime: data.estTime.toISOString(),
        // Include coupon data if applied
        ...(appliedCoupon && {
          couponCode: appliedCoupon.code,
          originalPrice: calculatedPrice,
          discountAmount: appliedCoupon.discountAmount,
        }),
      };

      // Step 1: Create the assignment
      const response = await fetch("/api/assignments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(submitData),
      });

      const responseData = await response.json();

      if (!response.ok) {
        throw new Error(
          responseData.message ||
            `Failed to create assignment (${response.status})`
        );
      }

      const assignmentId = responseData.data?.id;

      // Step 2: Upload files to assignment folder
      if (uploadedFiles.length > 0 && assignmentId) {
        toast.info("Uploading files...");

        for (const file of uploadedFiles) {
          try {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('assignmentId', assignmentId);
            formData.append('folder', 'assignments');

            const fileResponse = await fetch("/api/files/upload", {
              method: "POST",
              body: formData,
            });

            if (!fileResponse.ok) {
              console.error(`Failed to upload file ${file.name} to assignment folder`);
            }
          } catch (fileError) {
            console.error(`Error uploading file ${file.name}:`, fileError);
          }
        }
      }

      toast.success("Assignment created successfully");

      // Reset form and close dialog
      form.reset();
      setUploadedFiles([]);
      setOpen(false);

      // Call onSuccess callback to trigger refresh
      onSuccess?.();
    } catch (error) {
      console.error("Error creating assignment:", error);
      toast.error(
        `Error creating assignment: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Quick Create Assignment</DialogTitle>
          <DialogDescription>
            Create a new assignment with all the necessary details.
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="max-h-[calc(90vh-8rem)] pr-4">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 w-full max-w-full overflow-hidden">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Basic Information</h3>

                {/* TaskId field - completely removed from form, will be generated during submission */}
                {/* Hidden taskId field */}
                {/* <FormField
                  control={form.control}
                  name="taskId"
                  render={({ field }) => (
                    <FormItem className="hidden">
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                    </FormItem>
                  )}
                /> */}

                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Title *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter assignment title"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Short Description *</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Provide detailed description of the assignment"
                          rows={4}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="assignmentType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Assignment Type *</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {assignmentTypeOptions.map((option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="subject"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Subject *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="e.g., Psychology, Mathematics"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="service"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Service *</FormLabel>
                        <FormControl>
                          <Input placeholder="Academic Writing" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Assignment Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Assignment Details</h3>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="pageCount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Page Count *</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min={1}
                            {...field}
                            onChange={(e) =>
                              field.onChange(Number(e.target.value))
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="numSources"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Number of Sources</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min={0}
                            {...field}
                            onChange={(e) =>
                              field.onChange(Number(e.target.value))
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="priority"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Priority</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select priority" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {priorityOptions.map((option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="academicLevel"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Academic Level *</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select level" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {academicLevelOptions.map((option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="spacing"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Spacing</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select spacing" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {spacingOptions.map((option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="languageStyle"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Language Style</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select language" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {languageStyleOptions.map((option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="formatStyle"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Format Style</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select format" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {formatStyleOptions.map((option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="estTime"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Estimated Completion Time *</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) =>
                              date < new Date() || date < new Date("1900-01-01")
                            }
                          />
                        </PopoverContent>
                      </Popover>
                      <FormDescription>
                        When do you expect this assignment to be completed?
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Additional Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">
                  Additional Information
                </h3>

                <FormField
                  control={form.control}
                  name="guidelines"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Key Guidelines</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Any special instructions or guidelines for this assignment"
                          rows={5}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Provide all relevant rubric instructions and specific
                        requirements critical to assignment
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* File Upload Section */}
                <div className="space-y-2">
                  <FormLabel>Supporting Files</FormLabel>
                  <div className="space-y-3">
                    <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                      <Upload className="h-8 w-8 text-muted-foreground mx-auto mb-3" />
                      <p className="text-sm text-muted-foreground mb-3">
                        Select files to upload with assignment
                      </p>
                      <input
                        type="file"
                        multiple
                        accept=".pdf,.doc,.docx,.ppt,.pptx,.txt,.rtf,.xls,.xlsx"
                        onChange={(e) => {
                          if (e.target.files) {
                            handleFileUpload(e.target.files);
                          }
                        }}
                        className="hidden"
                        id="file-upload-client"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => document.getElementById('file-upload-client')?.click()}
                      >
                        Choose Files
                      </Button>
                      <p className="text-xs text-muted-foreground mt-2">
                        Supported: PDF, DOC, DOCX, PPT, PPTX, TXT, RTF, XLS, XLSX (Max 50MB each)
                      </p>
                    </div>

                    {/* Selected Files List */}
                    {uploadedFiles.length > 0 && (
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Selected Files ({uploadedFiles.length}):</p>
                        <div className="space-y-2">
                          {uploadedFiles.map((file, index) => (
                            <div
                              key={`${file.name}-${index}`}
                              className="flex items-center justify-between p-2 bg-muted/50 rounded-lg"
                            >
                              <div className="flex items-center space-x-2">
                                <FileText className="h-4 w-4 text-muted-foreground" />
                                <div>
                                  <p className="text-sm font-medium">{file.name}</p>
                                  <p className="text-xs text-muted-foreground">
                                    {formatFileSize(file.size)}
                                  </p>
                                </div>
                              </div>
                              <Button
                                type="button"
                                size="sm"
                                variant="ghost"
                                onClick={() => handleFileRemove(file.name)}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Coupon Section */}
                {calculatedPrice > 0 && (
                  <div className="space-y-2">
                    <FormLabel>Apply Coupon (Optional)</FormLabel>
                    <InlineCouponInput
                      originalPrice={calculatedPrice}
                      onCouponApplied={handleCouponApplied}
                      onCouponRemoved={handleCouponRemoved}
                      disabled={isSubmitting}
                    />
                    {appliedCoupon && (
                      <p className="text-sm text-green-600">
                        Coupon {appliedCoupon.code} applied! Saved ${appliedCoupon.discountAmount.toFixed(2)}
                      </p>
                    )}
                  </div>
                )}

                {/* Price Display */}
                {calculatedPrice > 0 && (
                  <div className="bg-muted/50 p-4 rounded-lg">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">
                        {appliedCoupon ? "Final Price:" : "Estimated Price:"}
                      </span>
                      <div className="text-right">
                        {appliedCoupon && (
                          <div className="text-sm text-muted-foreground line-through">
                            ${calculatedPrice.toFixed(2)}
                          </div>
                        )}
                        <div className="text-lg font-bold text-primary">
                          ${(appliedCoupon ? calculatedPrice - appliedCoupon.discountAmount : calculatedPrice).toFixed(2)}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Submit Button */}
              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setOpen(false)}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "Creating..." : "Create Assignment"}
                </Button>
              </div>
            </form>
          </Form>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
