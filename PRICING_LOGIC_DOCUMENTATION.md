# Academic Writing Platform - Pricing Logic Documentation

## Overview

This document provides a comprehensive analysis of the pricing logic implemented across the academic writing platform. 
The system uses a multi-layered pricing approach with different calculation methods for different contexts.

## Table of Contents

1. [Pricing Systems Overview](#pricing-systems-overview)
2. [Database-Driven Pricing (Legacy)](#database-driven-pricing-legacy)
3. [Configuration-Based Pricing (Current)](#configuration-based-pricing-current)
4. [Writer Compensation System](#writer-compensation-system)
5. [Revenue Calculation](#revenue-calculation)
6. [Payment Processing](#payment-processing)
7. [Pricing Display Components](#pricing-display-components)
8. [Inconsistencies & Issues](#inconsistencies--issues)
9. [Recommendations](#recommendations)

---

## Pricing Systems Overview

The platform currently implements **TWO DIFFERENT PRICING SYSTEMS** that operate independently:

### System 1: Database-Driven Pricing (Legacy)
- **Location**: `src/lib/price-utils.ts`
- **Used by**: Assignment creation API (`src/app/api/assignments/route.ts`)
- **Data source**: `BasePrice` database table with seeded values

### System 2: Configuration-Based Pricing (Current)
- **Location**: `src/types/order.ts` (PRICING_CONFIG)
- **Used by**: Create-order form, pricing calculator, order form
- **Data source**: Hardcoded configuration constants

---

## Database-Driven Pricing (Legacy)

### Implementation
**File**: `src/lib/price-utils.ts`

```typescript
export async function calculateAssignmentPrice(
  academicLevel: AcademicLevel,
  pageCount: number
): Promise<number> {
  const basePrice = await prisma.basePrice.findUnique({
    where: { academicLevel },
  });
  
  const pricePerPage = basePrice?.price ?? getDefaultBasePrice(academicLevel);
  return pricePerPage * pageCount;
}
```

### Default Pricing (Fallback)
```typescript
function getDefaultBasePrice(academicLevel: AcademicLevel): number {
  switch (academicLevel) {
    case AcademicLevel.HIGH_SCHOOL: return 9.0;
    case AcademicLevel.UNDERGRADUATE: return 10.0;
    case AcademicLevel.MASTERS: return 12.0;
    case AcademicLevel.PHD: return 12.0;
    case AcademicLevel.PROFESSIONAL: return 12.0;
    default: return 10.0;
  }
}
```

### Database Seed Data
**File**: `prisma/seed.ts`

```typescript
const basePrices = [
  { academicLevel: AcademicLevel.HIGH_SCHOOL, price: 9.0 },
  { academicLevel: AcademicLevel.UNDERGRADUATE, price: 10.0 },
  { academicLevel: AcademicLevel.MASTERS, price: 12.0 },
  { academicLevel: AcademicLevel.PHD, price: 12.0 },
  { academicLevel: AcademicLevel.PROFESSIONAL, price: 12.0 },
];
```

### Usage
- **Assignment Creation API**: Uses this system when creating assignments
- **Admin Base Price Management**: API endpoint at `/api/base-prices`
- **Database Table**: `BasePrice` model in Prisma schema

---

## Configuration-Based Pricing (Current)

### Implementation
**File**: `src/types/order.ts`

```typescript
export const PRICING_CONFIG = {
  BASE_PRICE_PER_PAGE: 15, // Base price per page in USD
  MINIMUM_PRICE: 10,
  RUSH_MULTIPLIERS: {
    LOW: 1.0,      // Standard delivery
    MEDIUM: 1.5,   // Priority delivery
    HIGH: 2.0,     // Urgent delivery
  },
  ACADEMIC_LEVEL_MULTIPLIERS: {
    HIGH_SCHOOL: 1.0,
    UNDERGRADUATE: 1.2,
    MASTERS: 1.5,
    PHD: 2.0,
    PROFESSIONAL: 1.8,
  },
  SPACING_MULTIPLIERS: {
    SINGLE: 1.0,
    DOUBLE: 0.5,   // 50% discount for double spacing
  },
} as const;
```

### Calculation Formula
```typescript
// Base calculation
let basePrice = PRICING_CONFIG.BASE_PRICE_PER_PAGE * pageCount;

// Apply multipliers
basePrice *= ACADEMIC_LEVEL_MULTIPLIERS[academicLevel];
basePrice *= RUSH_MULTIPLIERS[priority];
basePrice *= SPACING_MULTIPLIERS[spacing];

// Ensure minimum price
finalPrice = Math.max(basePrice, PRICING_CONFIG.MINIMUM_PRICE);
```

### Usage
- **Create Order Form**: Real-time price calculation
- **Pricing Calculator Component**: Live price breakdown
- **Order Summary**: Price display and validation

### Price Breakdown Example
For a 5-page Masters level essay with Medium priority and Double spacing:

1. **Base Price**: 5 pages × $15 = $75
2. **Academic Level**: $75 × 1.5 (Masters) = $112.50
3. **Priority**: $112.50 × 1.5 (Medium) = $168.75
4. **Spacing**: $168.75 × 0.5 (Double) = $84.38
5. **Final Price**: $84.38

---

## Writer Compensation System

### Implementation
**File**: `src/hooks/use-writer-price.ts`

```typescript
export function useWriterPrice(adminPrice: number, config: PriceConfig = {}) {
  const {
    writerPercentage = 0.35,     // Writer gets 35% by default
    minimumPricePerPage = 3,     // Minimum $3 per page
  } = config;

  const calculatedPrice = adminPrice * writerPercentage;
  const pageCount = adminPrice / 10; // Assumes $10 base price
  const minimumPrice = pageCount * minimumPricePerPage;

  return Math.max(calculatedPrice, minimumPrice);
}
```

### Writer Payment Rules
- **Base Percentage**: 35% of client payment
- **Minimum Rate**: $3 per page
- **Calculation**: Uses whichever is higher between percentage and minimum
- **Assumption**: Assumes $10 base price for page count calculation

---

## Revenue Calculation

### Implementation
**File**: `src/hooks/useRevenue.ts`

```typescript
const calculateRevenue = (assignments: Assignment[]): RevenueData => {
  // Filter only PAID assignments
  const paidAssignments = assignments.filter(
    (assignment) => assignment.paymentStatus === PaymentStatus.PAID
  );

  // Calculate total revenue
  const totalRevenue = paidAssignments.reduce(
    (sum, assignment) => sum + assignment.price, 0
  );
  
  // Additional calculations for monthly revenue, growth, etc.
};
```

### Revenue Metrics
- **Total Revenue**: Sum of all PAID assignments
- **Monthly Revenue**: Current month PAID assignments
- **Revenue Growth**: Month-over-month comparison
- **Average Order Value**: Total revenue ÷ number of paid assignments
- **Revenue by Month**: 12-month historical data

---

## Payment Processing

### PayPal Integration
**Files**: 
- `src/app/api/payments/create-order/route.ts`
- `src/app/api/payments/capture-order/route.ts`
- `src/components/PayPalButton.tsx`

### Payment Flow
1. **Order Creation**: PayPal order created with assignment price
2. **Payment Capture**: Payment processed and assignment status updated
3. **Status Update**: Assignment marked as PAID
4. **Revenue Tracking**: Included in revenue calculations

### Currency & Formatting
- **Currency**: USD (hardcoded)
- **Formatting**: Uses `Intl.NumberFormat` for currency display
- **Precision**: Prices calculated to 2 decimal places

---

## Pricing Display Components

### 1. Pricing Calculator (`src/components/create-order/pricing-calculator.tsx`)
- **Real-time calculation** using PRICING_CONFIG
- **Price breakdown** with detailed explanations
- **Visual indicators** for discounts and multipliers
- **Responsive design** with mobile optimization

### 2. Order Form (`src/components/create-order/order-form.tsx`)
- **Live price updates** as user fills form
- **Validation** against minimum price
- **Integration** with session storage for unauthenticated users

### 3. Admin Dashboard (`src/components/dashboard/admin/section-cards.tsx`)
- **Revenue display** with currency formatting
- **Growth metrics** with percentage calculations
- **Historical data** visualization

---

## Inconsistencies & Issues

### 1. **Dual Pricing Systems**
- **Problem**: Two different pricing systems with different base prices
  - Database system: $9-12 per page
  - Config system: $15 per page
- **Impact**: Price discrepancies between order creation and assignment creation

### 2. **Academic Level Multipliers Mismatch**
- **Database System**: Fixed prices per level
- **Config System**: Multipliers applied to base price
- **Result**: Different final prices for same academic level

### 3. **Writer Price Calculation Issues**
- **Hardcoded Assumption**: Assumes $10 base price for page count
- **Inconsistent Base**: Doesn't match either pricing system
- **Minimum Rate**: $3/page may not align with actual pricing

### 4. **Missing Features in Database System**
- **No Priority Multipliers**: Database system doesn't account for urgency
- **No Spacing Discounts**: No double-spacing discount in database system
- **Limited Flexibility**: Harder to adjust pricing dynamically

### 5. **Configuration Management**
- **Hardcoded Values**: PRICING_CONFIG values are hardcoded in code
- **No Admin Interface**: No way for admins to adjust config-based pricing
- **Version Control**: Pricing changes require code deployment

---

## Recommendations

### 1. **Unify Pricing Systems**
- **Migrate** to single pricing system
- **Extend database model** to support multipliers and rules
- **Deprecate** hardcoded PRICING_CONFIG

### 2. **Enhanced Database Schema**
```sql
-- Proposed enhanced pricing table
CREATE TABLE pricing_rules (
  id VARCHAR PRIMARY KEY,
  rule_type ENUM('base_price', 'academic_multiplier', 'priority_multiplier', 'spacing_multiplier'),
  academic_level ENUM(...) NULL,
  priority_level ENUM(...) NULL,
  spacing_type ENUM(...) NULL,
  value DECIMAL(10,2),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);
```

### 3. **Admin Pricing Management**
- **Create admin interface** for pricing rule management
- **Real-time updates** without code deployment
- **Pricing history** and audit trail
- **A/B testing** capabilities for pricing strategies

### 4. **Improved Writer Compensation**
- **Flexible percentage** based on academic level
- **Dynamic minimum rates** tied to current pricing
- **Performance bonuses** for high-rated writers
- **Transparent calculation** showing breakdown to writers

### 5. **Enhanced Price Calculation Service**
```typescript
// Proposed unified pricing service
class PricingService {
  async calculatePrice(params: PricingParams): Promise<PriceBreakdown> {
    const rules = await this.getPricingRules();
    return this.applyRules(params, rules);
  }
  
  async getWriterCompensation(clientPrice: number, writerId: string): Promise<number> {
    const writerRules = await this.getWriterRules(writerId);
    return this.calculateWriterPrice(clientPrice, writerRules);
  }
}
```

---

## Implementation Priority

### Phase 1: Immediate Fixes
1. **Align pricing systems** to use same base values
2. **Fix writer price calculation** assumptions
3. **Add validation** to prevent price discrepancies

### Phase 2: System Unification
1. **Migrate to database-driven pricing** with enhanced schema
2. **Create admin pricing interface**
3. **Implement pricing rule engine**

### Phase 3: Advanced Features
1. **Dynamic pricing** based on demand/supply
2. **Promotional pricing** and discount codes
3. **Multi-currency support**
4. **Advanced analytics** and pricing optimization

---

*This documentation reflects the current state of the pricing system as of the latest codebase analysis. Regular updates are recommended as the system evolves.*
