"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Shield, 
  RefreshCw, 
  Clock, 
  Lock,
  Award,
  CheckCircle,
  DollarSign,
  FileCheck
} from "lucide-react";

const guarantees = [
  {
    icon: Shield,
    title: "Quality Guarantee",
    description: "Every paper meets the highest academic standards with thorough quality checks.",
    features: ["Expert review process", "Academic standards compliance", "Quality assurance team"],
    color: "text-blue-600",
    bgColor: "bg-blue-50 dark:bg-blue-950/20"
  },
  {
    icon: RefreshCw,
    title: "Free Revisions",
    description: "Unlimited revisions until you&apos;re completely satisfied with your work.",
    features: ["Unlimited revisions", "No additional charges", "Quick turnaround"],
    color: "text-green-600",
    bgColor: "bg-green-50 dark:bg-green-950/20"
  },
  {
    icon: Clock,
    title: "On-Time Delivery",
    description: "We guarantee timely delivery of your assignments, every single time.",
    features: ["99% on-time rate", "Early delivery option", "Deadline tracking"],
    color: "text-purple-600",
    bgColor: "bg-purple-50 dark:bg-purple-950/20"
  },
  {
    icon: Lock,
    title: "Privacy Protection",
    description: "Your personal information and academic work remain completely confidential.",
    features: ["Data encryption", "Anonymous transactions", "Secure communication"],
    color: "text-red-600",
    bgColor: "bg-red-50 dark:bg-red-950/20"
  },
  {
    icon: Award,
    title: "Plagiarism-Free",
    description: "100% original content written from scratch with plagiarism reports included.",
    features: ["Original writing", "Plagiarism detection", "Authenticity reports"],
    color: "text-yellow-600",
    bgColor: "bg-yellow-50 dark:bg-yellow-950/20"
  },
  {
    icon: DollarSign,
    title: "Money-Back Guarantee",
    description: "Full refund if we fail to meet your requirements or expectations.",
    features: ["100% refund policy", "Satisfaction guarantee", "No questions asked"],
    color: "text-teal-600",
    bgColor: "bg-teal-50 dark:bg-teal-950/20"
  }
];

export function WhyUsGuarantees() {
  return (
    <div className="container mx-auto px-4">
      <div className="max-w-6xl mx-auto">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <Badge 
            variant="secondary" 
            className="px-4 py-2 text-sm font-medium bg-primary/10 text-primary border-primary/20 mb-4"
          >
            <Shield className="w-4 h-4 mr-2" />
            Our Guarantees
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
            Your Success is Guaranteed
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            We stand behind our work with comprehensive guarantees that protect your investment and ensure your satisfaction
          </p>
        </motion.div>

        {/* Guarantees Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {guarantees.map((guarantee, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Card className="h-full hover:shadow-lg transition-all duration-300 border-2 hover:border-primary/20 group">
                <CardContent className="p-6">
                  {/* Icon */}
                  <div className={`inline-flex p-3 rounded-lg ${guarantee.bgColor} mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    <guarantee.icon className={`w-6 h-6 ${guarantee.color}`} />
                  </div>

                  {/* Title */}
                  <h3 className="text-xl font-semibold mb-3 group-hover:text-primary transition-colors">
                    {guarantee.title}
                  </h3>

                  {/* Description */}
                  <p className="text-muted-foreground mb-4 leading-relaxed">
                    {guarantee.description}
                  </p>

                  {/* Features List */}
                  <ul className="space-y-2">
                    {guarantee.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        <span className="text-muted-foreground">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Trust Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 rounded-2xl p-8 border border-primary/20 text-center"
        >
          <div className="max-w-3xl mx-auto">
            <FileCheck className="w-12 h-12 text-primary mx-auto mb-4" />
            <h3 className="text-2xl font-bold mb-4">
              Risk-Free Academic Support
            </h3>
            <p className="text-muted-foreground mb-6 leading-relaxed">
              Our comprehensive guarantee system ensures that you can trust us with your academic success. 
              We&apos;ve designed these guarantees based on years of experience and thousands of satisfied students.
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary mb-1">100%</div>
                <div className="text-sm text-muted-foreground">Satisfaction</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary mb-1">24/7</div>
                <div className="text-sm text-muted-foreground">Support</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary mb-1">0%</div>
                <div className="text-sm text-muted-foreground">Plagiarism</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary mb-1">∞</div>
                <div className="text-sm text-muted-foreground">Revisions</div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
