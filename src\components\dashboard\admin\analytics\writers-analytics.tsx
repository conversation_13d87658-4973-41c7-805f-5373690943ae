"use client";

import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AnalyticsCard } from "./analytics-card";
import { WriterAnalytics } from "@/types/analytics";
import { AreaChartComponent } from "./charts/area-chart";
import { PieChartComponent } from "./charts/pie-chart";
import { BarChartComponent } from "./charts/bar-chart";
import { BarChartNegative } from "./charts/bar-chart-negative";
import { DataTable } from "@/components/ui/data-table";
import { ColumnDef } from "@tanstack/react-table";
import { IconTrendingUp, IconUsers, IconStar } from "@tabler/icons-react";
import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";

// Define table columns for top writers
const topWritersColumns: ColumnDef<WriterAnalytics["topWriters"][0]>[] = [
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "email",
    header: "Email",
  },
  {
    accessorKey: "completedAssignments",
    header: "Completed Assignments",
  },
  {
    accessorKey: "averageRating",
    header: "Rating",
    cell: ({ row }) => {
      const value = row.getValue("averageRating") as number;
      return `${value.toFixed(1)} ⭐`;
    },
  },
  {
    accessorKey: "earnings",
    header: "Earnings",
    cell: ({ row }) => {
      const value = row.getValue("earnings") as number;
      return `$${value.toFixed(2)}`;
    },
  },
];

// Define table columns for writer performance
const writerPerformanceColumns: ColumnDef<WriterAnalytics["writerPerformance"][0]>[] = [
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "onTime",
    header: "On Time",
  },
  {
    accessorKey: "late",
    header: "Late",
  },
  {
    accessorKey: "rejected",
    header: "Rejected",
  },
  {
    id: "onTimeRate",
    header: "On-Time Rate",
    cell: ({ row }) => {
      const onTime = row.getValue("onTime") as number;
      const late = row.getValue("late") as number;
      const total = onTime + late;
      const rate = total > 0 ? (onTime / total) * 100 : 100;
      return `${rate.toFixed(1)}%`;
    },
  },
];

interface WritersAnalyticsProps {
  variant: "card" | "chart" | "full";
  className?: string;
}

export function WritersAnalytics({ variant, className }: WritersAnalyticsProps) {
  const [data, setData] = useState<WriterAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch("/api/analytics/writers");
        if (!response.ok) {
          throw new Error(`Error fetching writer analytics: ${response.statusText}`);
        }
        const result = await response.json();
        setData(result);
      } catch (err) {
        setError((err as Error).message || "Failed to fetch writer analytics");
        console.error("Error fetching writer analytics:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle><Skeleton className="h-4 w-[200px]" /></CardTitle>
          <CardDescription><Skeleton className="h-4 w-[300px]" /></CardDescription>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[300px] w-full" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle>Error Loading Writer Analytics</CardTitle>
          <CardDescription>{error}</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  if (!data) {
    return null;
  }

  // Card variant - summary statistics
  if (variant === "card") {
    return (
      <AnalyticsCard
        title="Total Writers"
        value={data.totalWriters}
        description={`${data.activeWriters} active writers`}
        icon={<IconUsers className="h-4 w-4" />}
        className={className}
      />
    );
  }

  // Chart variant - writers performance chart using the negative bar chart
  if (variant === "chart") {
    // Transform writer performance data for the chart
    const performanceData = data.writerPerformance.map(writer => ({
      name: writer.name,
      onTime: writer.onTime,
      late: -writer.late,
      rejected: -writer.rejected,
    }));

    return (
      <BarChartComponent
        title="Writer Performance"
        description="Assignments completed on time vs late/rejected"
        data={performanceData}
        categories={[
          { name: "onTime", color: "hsl(var(--success))" },
          { name: "late", color: "hsl(var(--warning))" },
          { name: "rejected", color: "hsl(var(--destructive))" },
        ]}
        className={className}
      />
    );
  }

  // Full variant - comprehensive writer analytics
  return (
    <div className={cn("space-y-6", className)}>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <AnalyticsCard
          title="Total Writers"
          value={data.totalWriters}
          icon={<IconUsers className="h-4 w-4" />}
        />
        <AnalyticsCard
          title="Active Writers"
          value={data.activeWriters}
          description="Active in the last 30 days"
          icon={<IconUsers className="h-4 w-4" />}
        />
        <AnalyticsCard
          title="New Writers"
          value={data.newWriters}
          description="Joined in the last 30 days"
          icon={<IconTrendingUp className="h-4 w-4" />}
        />
        <AnalyticsCard
          title="Average Rating"
          value={data.topWriters.length > 0 
            ? (data.topWriters.reduce((sum, writer) => sum + writer.averageRating, 0) / data.topWriters.length).toFixed(1) 
            : "N/A"}
          description="All writers"
          icon={<IconStar className="h-4 w-4" />}
        />
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <AreaChartComponent
          title="Writer Growth"
          description="New writers registered per month"
          data={data.writersByMonth.map(item => ({
            ...item,
            date: `${item.date}-01`, // Convert to YYYY-MM-DD format
          }))}
          categories={[{ name: "value", color: "hsl(var(--primary))" }]}
        />
        <PieChartComponent
          title="Writer Distribution"
          description="Distribution by education level"
          data={data.writerDistribution.map(({ category, value }) => ({ name: category, value }))}
        />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Top Writers</CardTitle>
          <CardDescription>Writers with the highest performance</CardDescription>
        </CardHeader>
        <CardContent>
          <DataTable columns={topWritersColumns} data={data.topWriters} />
        </CardContent>
      </Card>

      {/* Writer performance section with negative bar chart */}
      <Card>
        <CardHeader>
          <CardTitle>Writer Performance</CardTitle>
          <CardDescription>On-time vs late submissions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] mb-6">
            <BarChartNegative
              title=""
              data={data.writerPerformance.map(writer => ({
                name: writer.name,
                value: writer.onTime - (writer.late + writer.rejected),
              }))}
            />
          </div>
          <DataTable columns={writerPerformanceColumns} data={data.writerPerformance} />
        </CardContent>
      </Card>
    </div>
  );
}
