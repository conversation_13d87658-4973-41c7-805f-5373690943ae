// import React, { useState, useEffect } from "react";
// import {
//   Card,
//   CardContent,
//   CardDescription,
//   CardHeader,
//   CardTitle,
// } from "@/components/ui/card";
// import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
// import { Button } from "@/components/ui/button";
// import { Alert, AlertDescription } from "@/components/ui/alert";
// import { Badge } from "@/components/ui/badge";
// import { Textarea } from "@/components/ui/textarea";
// import { Label } from "@/components/ui/label";
// import { Separator } from "@/components/ui/separator";
// import { Progress } from "@/components/ui/progress";
// import {
//   Dialog,
//   DialogContent,
//   DialogDescription,
//   DialogHeader,
//   DialogTitle,
// } from "@/components/ui/dialog";
// import {
//   Clock,
//   FileText,
//   CheckCircle,
//   AlertTriangle,
//   PenTool,
// } from "lucide-react";

// // Types
// interface MultipleChoiceQuestion {
//   question: string;
//   options: string[];
//   correctAnswer: string;
// }

// interface EssayExam {
//   topic: string;
//   rubrics: string;
// }

// interface AssessmentData {
//   id: string;
//   title: string;
//   multipleChoiceQuiz: MultipleChoiceQuestion[];
//   essayExam: EssayExam;
//   createdAt: string;
//   updatedAt: string;
// }

// export default function WriterAssessmentPage() {
//   const [assessment, setAssessment] = useState<AssessmentData | null>(null);
//   const [loading, setLoading] = useState(true);
//   const [error, setError] = useState<string | null>(null);
//   const [activeTab, setActiveTab] = useState("multiple-choice");
//   const [showTimeDialog, setShowTimeDialog] = useState(true);

//   // Multiple choice state
//   const [selectedAnswers, setSelectedAnswers] = useState<{
//     [key: number]: string;
//   }>({});
//   const [mcProgress, setMcProgress] = useState(0);

//   // Essay state
//   const [essayText, setEssayText] = useState("");
//   const [wordCount, setWordCount] = useState(0);
//   const [charCount, setCharCount] = useState(0);

//   // Mock fetch assessment data
//   useEffect(() => {
//     const fetchAssessment = async () => {
//       try {
//         // Simulating API call - replace with actual API call
//         await new Promise((resolve) => setTimeout(resolve, 1000));

//         // Mock data based on your seed
//         const mockAssessment: AssessmentData = {
//           id: "1",
//           title: "Academic Writing Assessment",
//           multipleChoiceQuiz: [
//             {
//               question:
//                 "APA Citation Format\nIn APA 7th edition, which of the following is the correct in-text citation for a direct quote from page 45 of a work by Johnson and Smith published in 2023?",
//               options: [
//                 "A) (Johnson & Smith, 2023, p. 45)",
//                 "B) (Johnson and Smith, 2023: 45)",
//                 "C) (Johnson & Smith 2023, pg. 45)",
//                 "D) [Johnson & Smith, 2023, p. 45]",
//               ],
//               correctAnswer: "A) (Johnson & Smith, 2023, p. 45)",
//             },
//             {
//               question:
//                 "MLA Works Cited\nAccording to MLA 9th edition guidelines, which reference entry is correctly formatted for a journal article accessed online?",
//               options: [
//                 "A) Martinez, Ana. 'Digital Literacy in Modern Education.' Educational Review, vol. 34, no. 2, 2023, pp. 123-145. Web. 15 Jan. 2024.",
//                 "B) Martinez, Ana. 'Digital Literacy in Modern Education.' Educational Review, vol. 34, no. 2, 2023, pp. 123-145, doi:10.1234/er.2023.456.",
//                 "C) Ana Martinez. 'Digital Literacy in Modern Education.' Educational Review 34.2 (2023): 123-145. DOI: 10.1234/er.2023.456.",
//                 "D) Martinez, A. (2023). Digital literacy in modern education. Educational Review, 34(2), 123-145.",
//               ],
//               correctAnswer:
//                 "B) Martinez, Ana. 'Digital Literacy in Modern Education.' Educational Review, vol. 34, no. 2, 2023, pp. 123-145, doi:10.1234/er.2023.456.",
//             },
//             {
//               question:
//                 "Harvard Referencing System\nIn Harvard style, what is the correct format for citing a book with three authors in the reference list?",
//               options: [
//                 "A) Thompson, J., Wilson, K. & Davis, M. (2022) Research Methods in Psychology, 3rd edn, London: Academic Press.",
//                 "B) Thompson, J., Wilson, K., and Davis, M. 2022. Research Methods in Psychology. 3rd ed. London: Academic Press.",
//                 "C) Thompson, J.; Wilson, K.; Davis, M. (2022). Research Methods in Psychology (3rd ed.). Academic Press.",
//                 "D) Thompson, J., K. Wilson, and M. Davis. Research Methods in Psychology. 3rd edition. London: Academic Press, 2022.",
//               ],
//               correctAnswer:
//                 "A) Thompson, J., Wilson, K. & Davis, M. (2022) Research Methods in Psychology, 3rd edn, London: Academic Press.",
//             },
//             {
//               question:
//                 "Academic Integrity\nA student asks you to write their entire research paper because they 'don't have time.' What is the most appropriate professional response?",
//               options: [
//                 "A) Accept the job but charge a premium rate for the urgency",
//                 "B) Decline and explain that this constitutes academic dishonesty, but offer to provide tutoring or editing services instead",
//                 "C) Write the paper but advise the student to make minor changes before submission",
//                 "D) Accept but require the student to sign a disclaimer acknowledging the work is for 'reference purposes only'",
//               ],
//               correctAnswer:
//                 "B) Decline and explain that this constitutes academic dishonesty, but offer to provide tutoring or editing services instead",
//             },
//           ],
//           essayExam: {
//             topic:
//               "Navigating the Ethical Landscape of Academic Writing Support: Establishing Professional Boundaries in Educational Assistance Services",
//             rubrics:
//               "Write a comprehensive essay (500-850 words) that demonstrates your understanding of professional academic writing practices and ethical considerations in providing educational support services.",
//           },
//           createdAt: new Date().toISOString(),
//           updatedAt: new Date().toISOString(),
//         };

//         setAssessment(mockAssessment);
//       } catch (err) {
//         setError("Failed to load assessment data");
//       } finally {
//         setLoading(false);
//       }
//     };

//     fetchAssessment();
//   }, []);

//   // Update progress when answers change
//   useEffect(() => {
//     if (assessment) {
//       const answeredCount = Object.keys(selectedAnswers).length;
//       const totalQuestions = assessment.multipleChoiceQuiz.length;
//       setMcProgress((answeredCount / totalQuestions) * 100);
//     }
//   }, [selectedAnswers, assessment]);

//   // Update word and character count
//   useEffect(() => {
//     const words = essayText
//       .trim()
//       .split(/\s+/)
//       .filter((word) => word.length > 0);
//     setWordCount(words.length);
//     setCharCount(essayText.length);
//   }, [essayText]);

//   const handleAnswerSelect = (questionIndex: number, answer: string) => {
//     setSelectedAnswers((prev) => ({
//       ...prev,
//       [questionIndex]: answer,
//     }));
//   };

//   const handleNextToEssay = () => {
//     setActiveTab("essay");
//   };

//   const handleSubmitAssessment = () => {
//     // TODO: implement submit logic here
//     console.log("Submitting assessment...", {
//       multipleChoiceAnswers: selectedAnswers,
//       essayText: essayText,
//     });
//   };

//   if (loading) {
//     return (
//       <div className="min-h-screen bg-background flex items-center justify-center">
//         <div className="text-center space-y-4">
//           <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
//           <p className="text-muted-foreground">Loading assessment...</p>
//         </div>
//       </div>
//     );
//   }

//   if (error || !assessment) {
//     return (
//       <div className="min-h-screen bg-background flex items-center justify-center p-4">
//         <Alert className="max-w-md">
//           <AlertTriangle className="h-4 w-4" />
//           <AlertDescription>
//             {error ||
//               "Assessment not ready. Please check back later or contact support."}
//           </AlertDescription>
//         </Alert>
//       </div>
//     );
//   }

//   const allMcAnswered =
//     assessment.multipleChoiceQuiz.length ===
//     Object.keys(selectedAnswers).length;

//   return (
//     <div className="min-h-screen bg-background">
//       {/* Time Limit Dialog */}
//       <Dialog open={showTimeDialog} onOpenChange={setShowTimeDialog}>
//         <DialogContent className="sm:max-w-md">
//           <DialogHeader>
//             <DialogTitle className="flex items-center gap-2">
//               <Clock className="h-5 w-5 text-primary" />
//               Assessment Time Limit
//             </DialogTitle>
//             <DialogDescription className="space-y-2">
//               <p>
//                 You have <strong>72 hours (3 days)</strong> to complete this
//                 assessment.
//               </p>
//               <p>
//                 The assessment includes multiple choice questions and an essay
//                 exam. Make sure to save your progress regularly.
//               </p>
//             </DialogDescription>
//           </DialogHeader>
//           <div className="flex justify-end">
//             <Button onClick={() => setShowTimeDialog(false)}>
//               Got it, Let's Start
//             </Button>
//           </div>
//         </DialogContent>
//       </Dialog>

//       <div className="container mx-auto py-8 px-4">
//         {/* Header */}
//         <div className="mb-8">
//           <div className="flex items-center gap-3 mb-4">
//             <div className="p-2 bg-primary/10 rounded-lg">
//               <PenTool className="h-6 w-6 text-primary" />
//             </div>
//             <div>
//               <h1 className="text-3xl font-bold tracking-tight">
//                 {assessment.title}
//               </h1>
//               <p className="text-muted-foreground">
//                 Complete both sections to finish your assessment
//               </p>
//             </div>
//           </div>

//           <div className="flex items-center gap-4 text-sm text-muted-foreground">
//             <Badge variant="outline" className="flex items-center gap-1">
//               <Clock className="h-3 w-3" />
//               72 hours remaining
//             </Badge>
//             <span>•</span>
//             <span>
//               {assessment.multipleChoiceQuiz.length} Multiple Choice Questions
//             </span>
//             <span>•</span>
//             <span>1 Essay Question</span>
//           </div>
//         </div>

//         {/* Assessment Tabs */}
//         <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
//           <TabsList className="grid w-full grid-cols-2 mb-8">
//             <TabsTrigger
//               value="multiple-choice"
//               className="flex items-center gap-2"
//             >
//               <CheckCircle className="h-4 w-4" />
//               Multiple Choice
//               {mcProgress === 100 && (
//                 <Badge variant="secondary" className="ml-2">
//                   Complete
//                 </Badge>
//               )}
//             </TabsTrigger>
//             <TabsTrigger value="essay" className="flex items-center gap-2">
//               <FileText className="h-4 w-4" />
//               Essay Exam
//             </TabsTrigger>
//           </TabsList>

//           {/* Multiple Choice Tab */}
//           <TabsContent value="multiple-choice" className="space-y-6">
//             <Card>
//               <CardHeader>
//                 <div className="flex items-center justify-between">
//                   <div>
//                     <CardTitle>Multiple Choice Questions</CardTitle>
//                     <CardDescription>
//                       Select the best answer for each question
//                     </CardDescription>
//                   </div>
//                   <div className="text-right">
//                     <div className="text-sm text-muted-foreground mb-1">
//                       Progress: {Object.keys(selectedAnswers).length}/
//                       {assessment.multipleChoiceQuiz.length}
//                     </div>
//                     <Progress value={mcProgress} className="w-32" />
//                   </div>
//                 </div>
//               </CardHeader>
//               <CardContent className="space-y-8">
//                 {assessment.multipleChoiceQuiz.map((question, index) => (
//                   <div key={index} className="space-y-4">
//                     <div className="flex items-start gap-3">
//                       <Badge variant="outline" className="mt-1 shrink-0">
//                         Q{index + 1}
//                       </Badge>
//                       <div className="flex-1">
//                         <h3 className="font-medium mb-4 whitespace-pre-line leading-relaxed">
//                           {question.question}
//                         </h3>
//                         <div className="space-y-3">
//                           {question.options.map((option, optionIndex) => (
//                             <div key={optionIndex}>
//                               <label className="flex items-start gap-3 p-4 rounded-lg border cursor-pointer hover:bg-accent/50 transition-colors">
//                                 <input
//                                   type="radio"
//                                   name={`question-${index}`}
//                                   value={option}
//                                   checked={selectedAnswers[index] === option}
//                                   onChange={() =>
//                                     handleAnswerSelect(index, option)
//                                   }
//                                   className="mt-1 h-4 w-4 text-primary focus:ring-primary"
//                                 />
//                                 <span className="flex-1 text-sm leading-relaxed">
//                                   {option}
//                                 </span>
//                               </label>
//                             </div>
//                           ))}
//                         </div>
//                       </div>
//                     </div>
//                     {index < assessment.multipleChoiceQuiz.length - 1 && (
//                       <Separator className="my-6" />
//                     )}
//                   </div>
//                 ))}
//               </CardContent>
//             </Card>

//             <div className="flex justify-end">
//               <Button
//                 onClick={handleNextToEssay}
//                 disabled={!allMcAnswered}
//                 size="lg"
//                 className="px-8"
//               >
//                 Next: Essay Exam
//                 <FileText className="ml-2 h-4 w-4" />
//               </Button>
//             </div>
//           </TabsContent>

//           {/* Essay Tab */}
//           <TabsContent value="essay" className="space-y-6">
//             <Card>
//               <CardHeader>
//                 <CardTitle>Essay Examination</CardTitle>
//                 <CardDescription>
//                   Write a comprehensive response to the prompt below
//                 </CardDescription>
//               </CardHeader>
//               <CardContent className="space-y-6">
//                 {/* Essay Topic */}
//                 <div className="space-y-3">
//                   <Label className="text-base font-medium">Essay Topic</Label>
//                   <div className="p-4 bg-muted/50 rounded-lg border-l-4 border-l-primary">
//                     <p className="font-medium leading-relaxed">
//                       {assessment.essayExam.topic}
//                     </p>
//                   </div>
//                 </div>

//                 {/* Rubrics */}
//                 <div className="space-y-3">
//                   <Label className="text-base font-medium">
//                     Instructions & Rubrics
//                   </Label>
//                   <div className="p-4 bg-accent/20 rounded-lg">
//                     <p className="text-sm leading-relaxed">
//                       {assessment.essayExam.rubrics}
//                     </p>
//                   </div>
//                 </div>

//                 {/* Essay Input */}
//                 <div className="space-y-3">
//                   <div className="flex items-center justify-between">
//                     <Label
//                       htmlFor="essay-text"
//                       className="text-base font-medium"
//                     >
//                       Your Essay
//                     </Label>
//                     <div className="flex items-center gap-4 text-sm text-muted-foreground">
//                       <span>
//                         Words: <strong>{wordCount}</strong>
//                       </span>
//                       <span>
//                         Characters: <strong>{charCount}</strong>
//                       </span>
//                     </div>
//                   </div>
//                   <Textarea
//                     id="essay-text"
//                     placeholder="Begin writing your essay here..."
//                     value={essayText}
//                     onChange={(e) => setEssayText(e.target.value)}
//                     className="min-h-[400px] resize-none"
//                   />

//                   {/* Word count guidance */}
//                   <div className="flex items-center gap-2 text-xs text-muted-foreground">
//                     <div className="flex items-center gap-1">
//                       {wordCount >= 500 && wordCount <= 850 ? (
//                         <CheckCircle className="h-3 w-3 text-green-500" />
//                       ) : (
//                         <AlertTriangle className="h-3 w-3 text-amber-500" />
//                       )}
//                       <span>
//                         Target: 500-850 words
//                         {wordCount < 500 &&
//                           ` (${500 - wordCount} words needed)`}
//                         {wordCount > 850 &&
//                           ` (${wordCount - 850} words over limit)`}
//                       </span>
//                     </div>
//                   </div>
//                 </div>

//                 {/* Submit Button */}
//                 <div className="pt-6 border-t">
//                   <div className="flex justify-end">
//                     <Button
//                       onClick={handleSubmitAssessment}
//                       size="lg"
//                       className="px-8"
//                       disabled={wordCount < 500 || wordCount > 850}
//                     >
//                       Submit Assessment
//                     </Button>
//                   </div>
//                 </div>
//               </CardContent>
//             </Card>
//           </TabsContent>
//         </Tabs>
//       </div>
//     </div>
//   );
// }
