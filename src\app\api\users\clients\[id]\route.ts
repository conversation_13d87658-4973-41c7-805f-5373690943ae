// src/app/api/users/clients/[id]/route.ts
import { NextRequest } from "next/server";
import type { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import bcrypt from "bcryptjs";
import {
  apiSuccess,
  apiError,
  parseRequestBody,
  checkPermission,
  getCurrentUserId,
} from "@/lib/api-utils";
import { userUpdateSchema } from "@/lib/validations";
import type { UserUpdateData } from "@/types/api";

// CLAUDE FINAL BUILD: - Updated to handle async params in Next.js 15

interface RouteParams {
  params: Promise<{ id: string }>;
}

// Get a single client by ID
export async function GET(
  request: NextRequest,
  props: RouteParams
): Promise<NextResponse> {
  try {
    const params = await props.params;
    const clientId = params.id;

    const currentUserId = await getCurrentUserId();

    if (!currentUserId) {
      return apiError("Authentication required", 401);
    }

    const currentUser = await prisma.user.findUnique({
      where: { id: currentUserId },
      select: { role: true },
    });

    const isAdmin = currentUser?.role === "ADMIN";
    const isSelf = currentUserId === clientId;

    if (!isAdmin && !isSelf) {
      return apiError("You don't have permission to access this resource", 403);
    }

    const client = await prisma.user.findUnique({
      where: { id: clientId, role: "CLIENT" },
      select: {
        id: true,
        email: true,
        name: true,
        phone: true,
        role: true,
        isApproved: true,
        createdAt: true,
        updatedAt: true,
        image: true,
        clientAssignments: {
          select: { id: true, title: true, status: true, createdAt: true },
          take: 5,
          orderBy: { createdAt: "desc" },
        },
        _count: { select: { clientAssignments: true } },
      },
    });

    if (!client) return apiError("Client not found", 404);

    const formatted = {
      id: client.id,
      email: client.email,
      name: client.name,
      phone: client.phone,
      role: client.role,
      isApproved: client.isApproved,
      createdAt: client.createdAt.toISOString(),
      updatedAt: client.updatedAt.toISOString(),
      image: client.image,
      assignmentCount: client._count.clientAssignments,
      recentAssignments: client.clientAssignments.map((a) => ({
        id: a.id,
        title: a.title,
        status: a.status,
        createdAt: a.createdAt.toISOString(),
      })),
    };

    return apiSuccess(formatted);
  } catch (error) {
    console.error("Error fetching client:", error);
    return apiError("Failed to fetch client details", 500);
  }
}

// Update a client
export async function PUT(
  request: NextRequest,
  props: RouteParams
): Promise<NextResponse> {
  try {
    const params = await props.params;
    const clientId = params.id;

    const currentUserId = await getCurrentUserId();

    if (!currentUserId) {
      return apiError("Authentication required", 401);
    }

    const currentUser = await prisma.user.findUnique({
      where: { id: currentUserId },
      select: { role: true },
    });

    const isAdmin = currentUser?.role === "ADMIN";
    const isSelf = currentUserId === clientId;

    if (!isAdmin && !isSelf) {
      return apiError("You don't have permission to update this client", 403);
    }

    // Parse and validate body
    const parsed = await parseRequestBody(request, userUpdateSchema);
    if ("success" in parsed && parsed.success === false) {
      return apiError(parsed.message, 400, parsed.errors);
    }

    const updateData = parsed as UserUpdateData;

    // Ensure client exists
    const existing = await prisma.user.findUnique({
      where: { id: clientId, role: "CLIENT" },
    });

    if (!existing) {
      return apiError("Client not found", 404);
    }

    // Prevent non-admin changing role
    if (!isAdmin && updateData.role && updateData.role !== "CLIENT") {
      return apiError("You don't have permission to change the role", 403);
    }

    // Hash password if provided
    if (updateData.password) {
      updateData.password = await bcrypt.hash(updateData.password, 10);
    }

    const updated = await prisma.user.update({
      where: { id: clientId },
      data: updateData,
      select: {
        id: true,
        email: true,
        name: true,
        phone: true,
        role: true,
        isApproved: true,
        createdAt: true,
        updatedAt: true,
        image: true,
      },
    });

    const formatted = {
      ...updated,
      createdAt: updated.createdAt.toISOString(),
      updatedAt: updated.updatedAt.toISOString(),
    };

    return apiSuccess(formatted, "Client updated successfully");
  } catch (error) {
    console.error("Error updating client:", error);
    if (error instanceof Error && error.message.includes("Unique constraint")) {
      return apiError("Email is already in use by another user", 409);
    }
    return apiError("Failed to update client", 500);
  }
}

// Delete a client
export async function DELETE(
  request: NextRequest,
  props: RouteParams
): Promise<NextResponse> {
  try {
    const params = await props.params;
    const clientId = params.id;

    const permissionError = await checkPermission(["ADMIN"]);
    if (permissionError) return permissionError;

    const existing = await prisma.user.findUnique({
      where: { id: clientId, role: "CLIENT" },
      include: {
        clientAssignments: {
          select: { id: true },
        },
      },
    });

    if (!existing) {
      return apiError("Client not found", 404);
    }

    // Check if client has active assignments
    const hasAssignments = existing.clientAssignments.length > 0;

    if (hasAssignments) {
      // Option 1: Prevent deletion if client has assignments
      return apiError(
        "Cannot delete client with active assignments. Please remove or reassign all assignments first.",
        409
      );

      // Option 2: Delete assignments first (uncomment if you want cascading delete)
      /*
      await prisma.assignment.deleteMany({
        where: { clientId: clientId }
      });
      */
    }

    // Now delete the client
    await prisma.user.delete({ where: { id: clientId } });
    return apiSuccess(null, "Client deleted successfully");
  } catch (error) {
    console.error("Error deleting client:", error);

    // Handle Prisma foreign key constraint errors
    if (error instanceof Error && error.message.includes("P2014")) {
      return apiError(
        "Cannot delete client with related data. Please remove all assignments first.",
        409
      );
    }

    return apiError("Failed to delete client", 500);
  }
}