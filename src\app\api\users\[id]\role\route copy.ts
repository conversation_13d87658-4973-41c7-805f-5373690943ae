// import { NextRequest, NextResponse } from "next/server";
// import prisma from "@/lib/prisma";
// import { apiSuccess, apiError } from "@/lib/api-utils";

// type UserRoleResponse = {
//   role: string;
// };

// // Fix: Update the handler signature to match Next.js route requirements
// export async function GET(
//   request: NextRequest,
//   context: { params: { id: string } }
// ): Promise<NextResponse> {
//   try {
//     const { id } = context.params;

//     const user = await prisma.user.findUnique({
//       where: { id },
//       select: { role: true },
//     });

//     if (!user) {
//       return apiError("User not found", 404);
//     }

//     return apiSuccess<UserRoleResponse>({ role: user.role });
//   } catch (error) {
//     console.error("Error fetching user role:", error);
//     return apiError("Failed to fetch user role", 500);
//   }
// }
