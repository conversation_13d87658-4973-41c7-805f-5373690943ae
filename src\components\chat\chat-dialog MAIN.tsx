// "use client";

// import React, {
//   useState,
//   useEffect,
//   useRef,
//   useCallback,
//   useMemo,
// } from "react";
// import {
//   Dialog,
//   DialogContent,
//   DialogHeader,
//   DialogTitle,
//   DialogTrigger,
// } from "@/components/ui/dialog";
// import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import { Badge } from "@/components/ui/badge";
// import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
// import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
// import { ScrollArea } from "@/components/ui/scroll-area";
// import {
//   Tooltip,
//   TooltipContent,
//   TooltipTrigger,
// } from "@/components/ui/tooltip";
// import { Separator } from "@/components/ui/separator";
// import {
//   MessageCircle,
//   Send,
//   Smile,
//   User,
//   Users,
//   Clock,
//   CheckCheck,
//   Check,
// } from "lucide-react";
// import { cn } from "@/lib/utils";
// import { useCurrentUserId } from "@/hooks/use-session-user-id";

// import { toast } from "sonner";
// import { usePersistentSocket } from "@/hooks/use-persistent-socket";

// // CLAUDE: Keep all existing types - no changes needed
// interface Message {
//   id: string;
//   chatId: string;
//   senderId: string;
//   content: string;
//   type: "TEXT" | "EMOJI" | "SYSTEM";
//   isRead: boolean;
//   createdAt: string;
//   targetParticipantId?: string;
//   conversationType?: string;
//   sender: {
//     id: string;
//     name: string | null;
//     email: string;
//     role: "ADMIN" | "CLIENT" | "WRITER";
//   };
// }

// interface ChatParticipant {
//   id: string;
//   userId: string;
//   role: "ADMIN" | "CLIENT" | "WRITER";
//   user: {
//     id: string;
//     name: string | null;
//     email: string;
//     role: "ADMIN" | "CLIENT" | "WRITER";
//   };
// }

// interface ChatData {
//   id: string;
//   assignmentId: string;
//   messages: Message[];
//   participants: ChatParticipant[];
// }

// interface ChatDialogProps {
//   assignmentId: string;
//   clientId: string;
//   writerId?: string;
//   userRole: "ADMIN" | "CLIENT" | "WRITER";
//   assignmentStatus: string;
// }

// interface ChatParticipantInfo {
//   id: string;
//   name: string | null;
//   email: string;
//   role: "ADMIN" | "CLIENT" | "WRITER";
// }

// const emojis = [
//   "😀",
//   "😃",
//   "😄",
//   "😁",
//   "😆",
//   "😅",
//   "😂",
//   "🤣",
//   "😊",
//   "😇",
//   "🙂",
//   "🙃",
//   "😉",
//   "😌",
//   "😍",
//   "🥰",
//   "😘",
//   "😗",
//   "😙",
//   "😚",
//   "😋",
//   "😛",
//   "😝",
//   "😜",
//   "🤪",
//   "🤨",
//   "🧐",
//   "🤓",
//   "😎",
//   "🤩",
//   "🥳",
//   "😏",
//   "👍",
//   "👎",
//   "👌",
//   "✌️",
//   "🤞",
//   "🤟",
//   "🤘",
//   "🤙",
//   "👏",
//   "🙌",
//   "👐",
//   "🤲",
//   "🤝",
//   "🙏",
//   "✍️",
//   "💪",
//   "🎉",
//   "🎊",
//   "🎈",
//   "🎁",
//   "🏆",
//   "🥇",
//   "🥈",
//   "🥉",
// ];

// export function ChatDialog({
//   assignmentId,
//   clientId,
//   writerId,
//   userRole,
//   assignmentStatus,
// }: ChatDialogProps) {
//   const { userId } = useCurrentUserId();
//   const [isOpen, setIsOpen] = useState(false);
//   const [chatData, setChatData] = useState<ChatData | null>(null);
//   const [loading, setLoading] = useState(false);
//   const [activeTab, setActiveTab] = useState("client");
//   const [messageInput, setMessageInput] = useState("");
//   const [showEmojiPicker, setShowEmojiPicker] = useState(false);
//   const [unreadCounts, setUnreadCounts] = useState<{
//     client: number;
//     writer: number;
//   }>({ client: 0, writer: 0 });
//   // CLAUDE: Add typing state
//   const [typingUsers, setTypingUsers] = useState<
//     Record<string, { name: string; role: string }>
//   >({});
//   const messagesEndRef = useRef<HTMLDivElement>(null);
//   const inputRef = useRef<HTMLInputElement>(null);
//   // CLAUDE: Add typing timeout ref
//   const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

//   // CLAUDE: Fetch chat data function
//   const fetchChatData = useCallback(async () => {
//     if (!assignmentId) return;

//     try {
//       setLoading(true);
//       const response = await fetch(`/api/chat/${assignmentId}`);
//       const data = await response.json();

//       if (data.success) {
//         setChatData(data.data);
//         // CLAUDE: Calculate unread counts
//         const clientMessages = data.data.messages.filter(
//           (msg: Message) =>
//             msg.sender.role === "CLIENT" &&
//             !msg.isRead &&
//             msg.senderId !== userId
//         );
//         const writerMessages = data.data.messages.filter(
//           (msg: Message) =>
//             msg.sender.role === "WRITER" &&
//             !msg.isRead &&
//             msg.senderId !== userId
//         );
//         setUnreadCounts({
//           client: clientMessages.length,
//           writer: writerMessages.length,
//         });
//       }
//     } catch (error) {
//       console.error("Error fetching chat data:", error);
//       toast.error("Failed to load chat data");
//     } finally {
//       setLoading(false);
//     }
//   }, [assignmentId, userId]);

//   // CLAUDE: Create stable event handlers using useCallback to prevent unnecessary re-renders
//   const handleNewMessage = useCallback(
//     (message: Message) => {
//       console.log("Processing new message in dialog:", message);
//       setChatData((prev) => {
//         if (!prev) return prev;
//         return {
//           ...prev,
//           messages: [...prev.messages, message],
//         };
//       });

//       // CLAUDE: Update unread counts based on current active tab
//       if (message.senderId !== userId) {
//         const senderRole = message.sender.role;
//         if (senderRole === "CLIENT" && activeTab !== "client") {
//           setUnreadCounts((prev) => ({ ...prev, client: prev.client + 1 }));
//         } else if (senderRole === "WRITER" && activeTab !== "writer") {
//           setUnreadCounts((prev) => ({ ...prev, writer: prev.writer + 1 }));
//         }
//       }
//     },
//     [userId, activeTab]
//   );

//   const handleMessageRead = useCallback(
//     ({ messageId }: { messageId: string }) => {
//       setChatData((prev) => {
//         if (!prev) return prev;
//         return {
//           ...prev,
//           messages: prev.messages.map((msg) =>
//             msg.id === messageId ? { ...msg, isRead: true } : msg
//           ),
//         };
//       });
//     },
//     []
//   );

//   const handleConnect = useCallback(() => {
//     console.log("Socket connected - refreshing chat data");
//     if (isOpen) {
//       fetchChatData();
//     }
//   }, [isOpen, fetchChatData]);

//   const handleDisconnect = useCallback(() => {
//     console.log("Socket disconnected");
//   }, []);

//   // CLAUDE: Add typing event handler
//   const handleUserTyping = useCallback(
//     (data: {
//       userId: string;
//       userRole: string;
//       isTyping: boolean;
//       conversationType?: string;
//       name?: string;
//     }) => {
//       const { userId: typingUserId, userRole, isTyping, name } = data;
//       if (typingUserId === userId) return;
//       setTypingUsers((prev) => {
//         const newState = { ...prev };
//         if (isTyping) {
//           newState[typingUserId] = { name: name || "User", role: userRole };
//         } else {
//           delete newState[typingUserId];
//         }
//         return newState;
//       });
//     },
//     [userId]
//   );

//   // CLAUDE: Use stable event handlers
//   const socketEventHandlers = useMemo(
//     () => ({
//       onNewMessage: handleNewMessage,
//       onMessageRead: handleMessageRead,
//       onConnect: handleConnect,
//       onDisconnect: handleDisconnect,
//       onUserTyping: handleUserTyping,
//     }),
//     [
//       handleNewMessage,
//       handleMessageRead,
//       handleConnect,
//       handleDisconnect,
//       handleUserTyping,
//     ]
//   );

//   // CLAUDE: Initialize persistent socket connection
//   const {
//     sendMessage: socketSendMessage,
//     sendTyping,
//     isConnected,
//   } = usePersistentSocket({
//     userId,
//     assignmentId,
//     clientId,
//     userRole,
//     writerId,
//     eventHandlers: socketEventHandlers,
//   });

//   // CLAUDE: Debug logging
//   useEffect(() => {
//     console.log("ChatDialog mounted with props:", {
//       assignmentId,
//       clientId,
//       writerId,
//       userRole,
//       assignmentStatus,
//     });
//   }, [assignmentId, clientId, writerId, userRole, assignmentStatus]);

//   useEffect(() => {
//     console.log("ChatDialog isOpen state changed:", isOpen);
//   }, [isOpen]);

//   // CLAUDE: Determine if writer tab should be available
//   const isWriterAvailable =
//     writerId &&
//     ["ASSIGNED", "COMPLETED", "REVISION", "CANCELLED"].includes(
//       assignmentStatus
//     );

//   // CLAUDE: Load chat data when dialog opens
//   useEffect(() => {
//     if (isOpen) {
//       fetchChatData();
//     }
//   }, [isOpen, fetchChatData]);

//   // CLAUDE: Scroll to bottom when new messages arrive
//   useEffect(() => {
//     messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
//   }, [chatData?.messages]);

//   // CLAUDE: Clear unread count when switching tabs
//   useEffect(() => {
//     if (activeTab === "client") {
//       setUnreadCounts((prev) => ({ ...prev, client: 0 }));
//     } else if (activeTab === "writer") {
//       setUnreadCounts((prev) => ({ ...prev, writer: 0 }));
//     }
//   }, [activeTab]);

//   // CLAUDE: Send message function using the persistent socket
//   const sendMessage = useCallback(async () => {
//     if (!messageInput.trim() || !socketSendMessage || !chatData) return;

//     const targetParticipantId =
//       userRole === "ADMIN"
//         ? activeTab === "client"
//           ? clientId
//           : writerId
//         : undefined;

//     const messageData = {
//       content: messageInput.trim(),
//       type: "TEXT" as const,
//       targetParticipantId,
//       conversationType:
//         userRole === "ADMIN" ? activeTab : userRole.toLowerCase(),
//     };

//     const sentMessage = await socketSendMessage(messageData);
//     if (sentMessage) {
//       setMessageInput("");

//       // CLAUDE: Stop typing indicator when message is sent
//       if (sendTyping) {
//         const conversationType =
//           userRole === "ADMIN" ? activeTab : userRole.toLowerCase();
//         sendTyping(false, conversationType);
//         if (typingTimeoutRef.current) {
//           clearTimeout(typingTimeoutRef.current);
//         }
//       }
//     }
//   }, [
//     messageInput,
//     socketSendMessage,
//     chatData,
//     userRole,
//     activeTab,
//     clientId,
//     writerId,
//     sendTyping,
//   ]);

//   // CLAUDE: Send emoji function using the persistent socket
//   const sendEmoji = useCallback(
//     async (emoji: string) => {
//       if (!socketSendMessage || !chatData) return;

//       const targetParticipantId =
//         userRole === "ADMIN"
//           ? activeTab === "client"
//             ? clientId
//             : writerId
//           : undefined;

//       const messageData = {
//         content: emoji,
//         type: "EMOJI" as const,
//         targetParticipantId,
//         conversationType:
//           userRole === "ADMIN" ? activeTab : userRole.toLowerCase(),
//       };

//       const sentMessage = await socketSendMessage(messageData);
//       if (sentMessage) {
//         setShowEmojiPicker(false);
//       }
//     },
//     [socketSendMessage, chatData, userRole, activeTab, clientId, writerId]
//   );

//   // CLAUDE: Handle input change with typing indicators
//   const handleInputChange = useCallback(
//     (e: React.ChangeEvent<HTMLInputElement>) => {
//       const value = e.target.value;
//       setMessageInput(value);

//       // CLAUDE: Send typing indicator
//       if (sendTyping && value.trim()) {
//         const conversationType =
//           userRole === "ADMIN" ? activeTab : userRole.toLowerCase();
//         sendTyping(true, conversationType);

//         // Clear previous timeout
//         if (typingTimeoutRef.current) {
//           clearTimeout(typingTimeoutRef.current);
//         }

//         // Set timeout to stop typing indicator
//         typingTimeoutRef.current = setTimeout(() => {
//           sendTyping(false, conversationType);
//         }, 1000);
//       } else if (sendTyping && !value.trim()) {
//         // Stop typing immediately if input is empty
//         const conversationType =
//           userRole === "ADMIN" ? activeTab : userRole.toLowerCase();
//         sendTyping(false, conversationType);
//         if (typingTimeoutRef.current) {
//           clearTimeout(typingTimeoutRef.current);
//         }
//       }
//     },
//     [sendTyping, userRole, activeTab]
//   );

//   // CLAUDE: Handle key press for sending messages
//   const handleKeyPress = useCallback(
//     (e: React.KeyboardEvent) => {
//       if (e.key === "Enter" && !e.shiftKey) {
//         e.preventDefault();
//         sendMessage();

//         // CLAUDE: Stop typing indicator when message is sent
//         if (sendTyping) {
//           const conversationType =
//             userRole === "ADMIN" ? activeTab : userRole.toLowerCase();
//           sendTyping(false, conversationType);
//           if (typingTimeoutRef.current) {
//             clearTimeout(typingTimeoutRef.current);
//           }
//         }
//       }
//     },
//     [sendMessage, sendTyping, userRole, activeTab]
//   );

//   // CLAUDE: Filter messages based on user role and active tab
//   const getFilteredMessages = useCallback(() => {
//     if (!chatData) return [];

//     if (userRole === "ADMIN") {
//       if (activeTab === "client") {
//         return chatData.messages.filter((msg) => {
//           if (msg.sender.role === "CLIENT" && msg.sender.id === clientId) {
//             return true;
//           }
//           if (msg.sender.role === "ADMIN") {
//             if (msg.conversationType) {
//               return msg.conversationType === "client";
//             }
//             if (msg.targetParticipantId) {
//               return msg.targetParticipantId === clientId;
//             }
//             return true;
//           }
//           return false;
//         });
//       } else {
//         return chatData.messages.filter((msg) => {
//           if (msg.sender.role === "WRITER" && msg.sender.id === writerId) {
//             return true;
//           }
//           if (msg.sender.role === "ADMIN") {
//             if (msg.conversationType) {
//               return msg.conversationType === "writer";
//             }
//             if (msg.targetParticipantId) {
//               return msg.targetParticipantId === writerId;
//             }
//             return true;
//           }
//           return false;
//         });
//       }
//     } else {
//       return chatData.messages.filter((msg) => {
//         if (msg.sender.role === userRole && msg.sender.id === userId) {
//           return true;
//         }
//         if (msg.sender.role === "ADMIN") {
//           if (msg.conversationType) {
//             return msg.conversationType === userRole.toLowerCase();
//           }
//           if (msg.targetParticipantId) {
//             return msg.targetParticipantId === userId;
//           }
//           return true;
//         }
//         return false;
//       });
//     }
//   }, [chatData, userRole, activeTab, clientId, writerId, userId]);

//   // CLAUDE: Get participant information
//   const getParticipantInfo = useCallback(
//     (role: "CLIENT" | "WRITER"): ChatParticipantInfo | null => {
//       if (!chatData) return null;

//       const participantId = role === "CLIENT" ? clientId : writerId;
//       const participant = chatData.participants.find(
//         (p) => p.role === role && p.userId === participantId
//       );

//       if (!participant?.user) return null;

//       return {
//         id: participant.user.id,
//         name: participant.user.name,
//         email: participant.user.email,
//         role: participant.user.role,
//       };
//     },
//     [chatData, clientId, writerId]
//   );

//   const totalUnreadCount = unreadCounts.client + unreadCounts.writer;

//   const handleChatButtonClick = () => {
//     console.log("Chat button clicked, opening dialog");
//     setIsOpen(true);
//   };

//   return (
//     <Dialog
//       open={isOpen}
//       onOpenChange={(open) => {
//         console.log("Dialog onOpenChange called with:", open);
//         setIsOpen(open);
//       }}
//     >
//       <DialogTrigger asChild>
//         <Tooltip>
//           <TooltipTrigger asChild>
//             <Button
//               variant="outline"
//               size="sm"
//               className="relative"
//               onClick={handleChatButtonClick}
//             >
//               <MessageCircle className="h-4 w-4 mr-2" />
//               Chat
//               {totalUnreadCount > 0 && (
//                 <Badge
//                   variant="destructive"
//                   className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
//                 >
//                   {totalUnreadCount > 99 ? "99+" : totalUnreadCount}
//                 </Badge>
//               )}
//               {/* CLAUDE: Add connection status indicator */}
//               <div
//                 className={cn(
//                   "absolute -bottom-1 -right-1 h-2 w-2 rounded-full",
//                   isConnected ? "bg-green-500" : "bg-red-500"
//                 )}
//               />
//             </Button>
//           </TooltipTrigger>
//           <TooltipContent>
//             <p>
//               Open chat with {userRole === "ADMIN" ? "participants" : "admin"}
//               {!isConnected && " (Disconnected)"}
//             </p>
//           </TooltipContent>
//         </Tooltip>
//       </DialogTrigger>

//       <DialogContent className="max-w-4xl h-[80vh] flex flex-col p-0">
//         <DialogHeader className="px-6 py-4 border-b">
//           <DialogTitle className="flex items-center gap-2">
//             <MessageCircle className="h-5 w-5" />
//             Assignment Chat
//             {/* CLAUDE: Show connection status in header */}
//             <Badge
//               variant={isConnected ? "default" : "destructive"}
//               className="text-xs"
//             >
//               {isConnected ? "Connected" : "Disconnected"}
//             </Badge>
//           </DialogTitle>
//         </DialogHeader>

//         {loading ? (
//           <div className="flex-1 flex items-center justify-center">
//             <div className="text-center">
//               <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
//               <p className="text-muted-foreground">Loading chat...</p>
//             </div>
//           </div>
//         ) : (
//           <div className="flex-1 flex flex-col min-h-0 px-6 pb-6">
//             {userRole === "ADMIN" ? (
//               <Tabs
//                 value={activeTab}
//                 onValueChange={setActiveTab}
//                 className="flex-1 flex flex-col min-h-0"
//               >
//                 <TabsList className="grid w-full grid-cols-2 mb-4">
//                   <TabsTrigger value="client" className="relative">
//                     <User className="h-4 w-4 mr-2" />
//                     Client
//                     {unreadCounts.client > 0 && (
//                       <Badge
//                         variant="destructive"
//                         className="ml-2 h-4 w-4 rounded-full p-0 flex items-center justify-center text-xs"
//                       >
//                         {unreadCounts.client}
//                       </Badge>
//                     )}
//                   </TabsTrigger>
//                   <TabsTrigger
//                     value="writer"
//                     disabled={!isWriterAvailable}
//                     className="relative"
//                   >
//                     <Users className="h-4 w-4 mr-2" />
//                     Writer
//                     {unreadCounts.writer > 0 && (
//                       <Badge
//                         variant="destructive"
//                         className="ml-2 h-4 w-4 rounded-full p-0 flex items-center justify-center text-xs"
//                       >
//                         {unreadCounts.writer}
//                       </Badge>
//                     )}
//                   </TabsTrigger>
//                 </TabsList>

//                 <TabsContent
//                   value="client"
//                   className="flex-1 flex flex-col min-h-0 mt-0"
//                 >
//                   <ChatInterface
//                     messages={getFilteredMessages()}
//                     currentUserId={userId}
//                     onSendMessage={sendMessage}
//                     onSendEmoji={sendEmoji}
//                     messageInput={messageInput}
//                     showEmojiPicker={showEmojiPicker}
//                     setShowEmojiPicker={setShowEmojiPicker}
//                     onKeyPress={handleKeyPress}
//                     onInputChange={handleInputChange}
//                     participantInfo={getParticipantInfo("CLIENT")}
//                     messagesEndRef={messagesEndRef}
//                     inputRef={inputRef}
//                     typingUsers={typingUsers}
//                   />
//                 </TabsContent>

//                 <TabsContent
//                   value="writer"
//                   className="flex-1 flex flex-col min-h-0 mt-0"
//                 >
//                   {isWriterAvailable ? (
//                     <ChatInterface
//                       messages={getFilteredMessages()}
//                       currentUserId={userId}
//                       onSendMessage={sendMessage}
//                       onSendEmoji={sendEmoji}
//                       messageInput={messageInput}
//                       showEmojiPicker={showEmojiPicker}
//                       setShowEmojiPicker={setShowEmojiPicker}
//                       onKeyPress={handleKeyPress}
//                       onInputChange={handleInputChange}
//                       participantInfo={getParticipantInfo("WRITER")}
//                       messagesEndRef={messagesEndRef}
//                       inputRef={inputRef}
//                       typingUsers={typingUsers}
//                     />
//                   ) : (
//                     <div className="flex-1 flex items-center justify-center">
//                       <div className="text-center text-muted-foreground">
//                         <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
//                         <p>
//                           Writer will be available once assignment is assigned
//                         </p>
//                       </div>
//                     </div>
//                   )}
//                 </TabsContent>
//               </Tabs>
//             ) : (
//               <ChatInterface
//                 messages={getFilteredMessages()}
//                 currentUserId={userId}
//                 onSendMessage={sendMessage}
//                 onSendEmoji={sendEmoji}
//                 messageInput={messageInput}
//                 showEmojiPicker={showEmojiPicker}
//                 setShowEmojiPicker={setShowEmojiPicker}
//                 onKeyPress={handleKeyPress}
//                 onInputChange={handleInputChange}
//                 participantInfo={null}
//                 messagesEndRef={messagesEndRef}
//                 inputRef={inputRef}
//                 typingUsers={typingUsers}
//               />
//             )}
//           </div>
//         )}
//       </DialogContent>
//     </Dialog>
//   );
// }

// // CLAUDE: Updated ChatInterface component with typing indicators
// type ChatInterfaceProps = {
//   messages: Message[];
//   currentUserId: string | null;
//   onSendMessage: () => void;
//   onSendEmoji: (emoji: string) => void;
//   messageInput: string;
//   showEmojiPicker: boolean;
//   setShowEmojiPicker: (show: boolean) => void;
//   onKeyPress: (e: React.KeyboardEvent) => void;
//   onInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void; // CLAUDE: Added for typing indicator
//   participantInfo: ChatParticipantInfo | null;
//   messagesEndRef: React.RefObject<HTMLDivElement | null>;
//   inputRef: React.RefObject<HTMLInputElement | null>;
//   typingUsers: Record<string, { name: string; role: string }>;
// };

// function ChatInterface({
//   messages,
//   currentUserId,
//   onSendMessage,
//   onSendEmoji,
//   messageInput,
//   showEmojiPicker,
//   setShowEmojiPicker,
//   onKeyPress,
//   onInputChange, // CLAUDE: Added for typing indicator
//   participantInfo,
//   messagesEndRef,
//   inputRef,
//   typingUsers, // CLAUDE: Added typing users state
// }: ChatInterfaceProps) {
//   return (
//     <div className="flex flex-col h-full min-h-0">
//       {/* Participant Info */}
//       {participantInfo && (
//         <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg mb-4 flex-shrink-0">
//           <Avatar className="h-8 w-8">
//             <AvatarImage src="" alt={participantInfo.name || "User"} />
//             <AvatarFallback>
//               {(participantInfo.name || participantInfo.email)
//                 .split(" ")
//                 .map((n) => n[0])
//                 .join("")}
//             </AvatarFallback>
//           </Avatar>
//           <div>
//             <p className="font-medium text-sm">
//               {participantInfo.name || "Anonymous User"}
//             </p>
//             <p className="text-xs text-muted-foreground">
//               {participantInfo.email}
//             </p>
//           </div>
//         </div>
//       )}
//       {/* Messages - This is the key fix */}
//       <div className="flex-1 min-h-0 mb-4">
//         <ScrollArea className="h-full">
//           <div className="space-y-4 pr-4">
//             {messages.map((message) => {
//               const isOwn = message.senderId === currentUserId;
//               const isEmoji = message.type === "EMOJI";

//               return (
//                 <div
//                   key={message.id}
//                   className={cn(
//                     "flex gap-3",
//                     isOwn ? "justify-end" : "justify-start"
//                   )}
//                 >
//                   {!isOwn && (
//                     <Avatar className="h-8 w-8 mt-1 flex-shrink-0">
//                       <AvatarImage src="" alt={message.sender.name || "User"} />
//                       <AvatarFallback className="text-xs">
//                         {(message.sender.name || message.sender.email)
//                           .split(" ")
//                           .map((n) => n[0])
//                           .join("")}
//                       </AvatarFallback>
//                     </Avatar>
//                   )}

//                   <div
//                     className={cn(
//                       "max-w-[70%] space-y-1 flex flex-col",
//                       isOwn ? "items-end" : "items-start"
//                     )}
//                   >
//                     {!isOwn && (
//                       <div className="flex items-center gap-2">
//                         <span className="text-xs font-medium">
//                           {message.sender.name || "Anonymous"}
//                         </span>
//                         <Badge variant="outline" className="text-xs px-1 py-0">
//                           {message.sender.role}
//                         </Badge>
//                       </div>
//                     )}

//                     <div
//                       className={cn(
//                         "rounded-lg px-3 py-2 text-sm break-words",
//                         isOwn
//                           ? "bg-primary text-primary-foreground"
//                           : "bg-muted",
//                         isEmoji && "text-2xl px-2 py-1"
//                       )}
//                     >
//                       {message.content}
//                     </div>

//                     <div
//                       className={cn(
//                         "flex items-center gap-1 text-xs text-muted-foreground",
//                         isOwn ? "justify-end" : "justify-start"
//                       )}
//                     >
//                       <Clock className="h-3 w-3" />
//                       <span>{formatTime(message.createdAt)}</span>
//                       {isOwn && (
//                         <Tooltip>
//                           <TooltipTrigger>
//                             {message.isRead ? (
//                               <CheckCheck className="h-3 w-3 text-blue-500" />
//                             ) : (
//                               <Check className="h-3 w-3" />
//                             )}
//                           </TooltipTrigger>
//                           <TooltipContent>
//                             <p>{message.isRead ? "Read" : "Delivered"}</p>
//                           </TooltipContent>
//                         </Tooltip>
//                       )}
//                     </div>
//                   </div>

//                   {isOwn && (
//                     <Avatar className="h-8 w-8 mt-1 flex-shrink-0">
//                       <AvatarImage src="" alt="You" />
//                       <AvatarFallback className="text-xs">You</AvatarFallback>
//                     </Avatar>
//                   )}
//                 </div>
//               );
//             })}

//             {/* CLAUDE: Typing indicators */}
//             {Object.entries(typingUsers).map(([userId, user]) => (
//               <div
//                 key={`typing-${userId}`}
//                 className="flex gap-3 justify-start"
//               >
//                 <Avatar className="h-8 w-8 mt-1 flex-shrink-0">
//                   <AvatarImage src="" alt={user.name || "User"} />
//                   <AvatarFallback className="text-xs">
//                     {(user.name || "U")
//                       .split(" ")
//                       .map((n) => n[0])
//                       .join("")}
//                   </AvatarFallback>
//                 </Avatar>
//                 <div className="max-w-[70%] space-y-1 flex flex-col items-start">
//                   <div className="flex items-center gap-2">
//                     <span className="text-xs font-medium">{user.name}</span>
//                     <Badge variant="outline" className="text-xs px-1 py-0">
//                       {user.role}
//                     </Badge>
//                   </div>
//                   <div className="rounded-lg px-3 py-2 text-sm bg-muted text-muted-foreground italic">
//                     typing...
//                   </div>
//                 </div>
//               </div>
//             ))}

//             <div ref={messagesEndRef} />
//           </div>
//         </ScrollArea>
//       </div>

//       <Separator className="mb-4 flex-shrink-0" />

//       {/* Message Input */}
//       <div className="space-y-3 flex-shrink-0">
//         {showEmojiPicker && (
//           <div className="grid grid-cols-8 gap-2 p-3 bg-muted/50 rounded-lg max-h-32 overflow-y-auto">
//             {emojis.map((emoji) => (
//               <Button
//                 key={emoji}
//                 variant="ghost"
//                 size="sm"
//                 className="h-8 w-8 p-0 text-lg hover:bg-muted flex-shrink-0"
//                 onClick={() => onSendEmoji(emoji)}
//               >
//                 {emoji}
//               </Button>
//             ))}
//           </div>
//         )}

//         <div className="flex gap-2">
//           <div className="flex-1 relative">
//             <Input
//               ref={inputRef}
//               value={messageInput}
//               onChange={onInputChange} // CLAUDE: Use the new input change handler for typing indicators
//               onKeyPress={onKeyPress}
//               placeholder="Type your message..."
//               className="pr-12"
//             />
//             <Button
//               variant="ghost"
//               size="sm"
//               className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
//               onClick={() => setShowEmojiPicker(!showEmojiPicker)}
//             >
//               <Smile className="h-4 w-4" />
//             </Button>
//           </div>
//           <Button
//             onClick={onSendMessage}
//             disabled={!messageInput.trim()}
//             size="sm"
//             className="flex-shrink-0"
//           >
//             <Send className="h-4 w-4" />
//           </Button>
//         </div>
//       </div>
//     </div>
//   );
// }

// function formatTime(dateString: string) {
//   return new Date(dateString).toLocaleTimeString("en-US", {
//     hour: "2-digit",
//     minute: "2-digit",
//   });
// }
