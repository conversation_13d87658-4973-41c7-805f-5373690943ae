// src/lib/price-utils.ts
import prisma from "./prisma";
import { AcademicLevel } from "@prisma/client";

export async function calculateAssignmentPrice(
  academicLevel: AcademicLevel,
  pageCount: number
): Promise<number> {
  // Get the base price for the academic level
  const basePrice = await prisma.basePrice.findUnique({
    where: { academicLevel },
  });

  // If no base price is found, use default values
  const pricePerPage = basePrice?.price ?? getDefaultBasePrice(academicLevel);

  // Calculate total price
  return pricePerPage * pageCount;
}

function getDefaultBasePrice(academicLevel: AcademicLevel): number {
  switch (academicLevel) {
    case AcademicLevel.HIGH_SCHOOL:
      return 9.0;
    case AcademicLevel.UNDERGRADUATE:
      return 10.0;
    case AcademicLevel.MASTERS:
      return 12.0;
    case AcademicLevel.PHD:
      return 12.0;
    case AcademicLevel.PROFESSIONAL:
      return 12.0;
    default:
      return 10.0; // fallback for any future additions to the enum
  }
}
