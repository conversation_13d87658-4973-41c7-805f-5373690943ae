// src/lib/chat-broadcast.ts

interface SSEConnection {
    controller: ReadableStreamDefaultController;
    userId: string;
    assignmentId: string;
    userRole: string;
    chatType?: string; // Add chatType to connection tracking
    lastHeartbeat: number;
  }
  
  // Store active connections - this needs to be shared across the application
  export const connections = new Map<string, SSEConnection>();
  
  // Cleanup inactive connections
  const HEARTBEAT_INTERVAL = 30000; // 30 seconds
  const CONNECTION_TIMEOUT = 60000; // 1 minute
  
  // Set up cleanup interval
  setInterval(() => {
    const now = Date.now();
    for (const [connectionId, connection] of connections.entries()) {
      if (now - connection.lastHeartbeat > CONNECTION_TIMEOUT) {
        try {
          connection.controller.close();
        } catch (error) {
          console.error("Error closing connection:", error);
        }
        connections.delete(connectionId);
      }
    }
  }, HEARTBEAT_INTERVAL);
  
  // Broadcast function with targeted messaging
  export async function broadcastMessage(
    assignmentId: string,
    /* eslint-disable @typescript-eslint/no-explicit-any */
    message: any,
    targetParticipants?: string[]
  ) {
    const messageData = `data: ${JSON.stringify({
      type: "NEW_MESSAGE",
      data: message,
    })}\n\n`;
  
    for (const [connectionId, connection] of connections.entries()) {
      if (connection.assignmentId === assignmentId) {
        // If target participants are specified, only send to those users
        if (targetParticipants && targetParticipants.length > 0) {
          if (!targetParticipants.includes(connection.userId)) {
            continue; // Skip this connection
          }
        }
  
        // Apply the same filtering logic as in the GET route for chatType support
        let shouldSendToConnection = true;
  
        // Only apply filtering if message has conversation targeting
        if (message.conversationType && message.targetParticipantId) {
          shouldSendToConnection = false;
  
          if (connection.userRole === "ADMIN") {
            // Admin should only receive messages for the conversation type they're viewing
            shouldSendToConnection =
              message.conversationType === connection.chatType;
          } else if (connection.userRole === "CLIENT") {
            // Client should only receive messages in "client" conversations where they're involved
            shouldSendToConnection =
              message.conversationType === "client" &&
              (message.senderId === connection.userId ||
                message.targetParticipantId === connection.userId);
          } else if (connection.userRole === "WRITER") {
            // Writer should only receive messages in "writer" conversations where they're involved
            shouldSendToConnection =
              message.conversationType === "writer" &&
              (message.senderId === connection.userId ||
                message.targetParticipantId === connection.userId);
          }
        }
  
        if (shouldSendToConnection) {
          try {
            connection.controller.enqueue(messageData);
          } catch (error) {
            console.error("Error broadcasting message:", error);
            connections.delete(connectionId);
          }
        }
      }
    }
  }
  
  // Helper function to broadcast message read status
  export async function broadcastMessageRead(
    assignmentId: string,
    messageId: string,
    readBy: string
  ) {
    const readData = `data: ${JSON.stringify({
      type: "MESSAGE_READ",
      data: { messageId, readBy },
    })}\n\n`;
  
    for (const [connectionId, connection] of connections.entries()) {
      if (connection.assignmentId === assignmentId) {
        try {
          connection.controller.enqueue(readData);
        } catch (error) {
          console.error("Error broadcasting read status:", error);
          connections.delete(connectionId);
        }
      }
    }
  }
  
  // Helper function to add a connection
  export function addConnection(
    connectionId: string,
    connection: SSEConnection
  ) {
    connections.set(connectionId, connection);
  }
  
  // Helper function to remove a connection
  export function removeConnection(connectionId: string) {
    connections.delete(connectionId);
  }
  
  // Export constants and types
  export { HEARTBEAT_INTERVAL, CONNECTION_TIMEOUT };
  export type { SSEConnection };