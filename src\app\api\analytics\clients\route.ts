import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { ClientAnalytics } from "@/types/analytics";

export async function GET() {
  try {
    // Get total clients
    const totalClients = await prisma.user.count({
      where: { role: "CLIENT" },
    });

    // Get active clients (clients who logged in within the last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const activeClients = await prisma.user.count({
      where: {
        role: "CLIENT",
        updatedAt: {
          gte: thirtyDaysAgo,
        },
      },
    });

    // Get new clients in the last 30 days
    const newClients = await prisma.user.count({
      where: {
        role: "CLIENT",
        createdAt: {
          gte: thirtyDaysAgo,
        },
      },
    });

    // Calculate churn rate (simplified)
    const sixtyDaysAgo = new Date();
    sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);
    
    const clientsFromPreviousMonth = await prisma.user.count({
      where: {
        role: "CLIENT",
        createdAt: {
          gte: sixtyDaysAgo,
          lte: thirtyDaysAgo,
        },
      },
    });
    
    // Simple churn calculation: (inactive clients from previous month) / (total clients from previous month)
    const churnRate = clientsFromPreviousMonth > 0 
      ? ((clientsFromPreviousMonth - activeClients) / clientsFromPreviousMonth) * 100 
      : 0;

    // Calculate client growth
    const previousMonthTotal = await prisma.user.count({
      where: {
        role: "CLIENT",
        createdAt: {
          lte: thirtyDaysAgo,
        },
      },
    });
    
    const clientGrowth = previousMonthTotal > 0 
      ? ((totalClients - previousMonthTotal) / previousMonthTotal) * 100 
      : 100;

    // Get top clients by assignments
    const topClients = await prisma.user.findMany({
      where: { role: "CLIENT" },
      select: {
        id: true,
        name: true,
        email: true,
        updatedAt: true,
        clientAssignments: {
          select: {
            id: true,
            price: true,
          },
        },
      },
      orderBy: {
        clientAssignments: {
          _count: "desc",
        },
      },
      take: 5,
    });

    // Format top clients data
    const formattedTopClients = topClients.map((client) => ({
      id: client.id,
      name: client.name || "Anonymous",
      email: client.email,
      totalSpent: client.clientAssignments.reduce((sum, assignment) => sum + (assignment.price || 0), 0),
      totalOrders: client.clientAssignments.length,
      lastActive: client.updatedAt,
    }));

    // Generate clients by month data (last 12 months)
    const clientsByMonth = [];
    for (let i = 11; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
      const endOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0);
      
      const count = await prisma.user.count({
        where: {
          role: "CLIENT",
          createdAt: {
            gte: startOfMonth,
            lte: endOfMonth,
          },
        },
      });
      
      clientsByMonth.push({
        date: `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`,
        value: count,
      });
    }

    // Generate client distribution by academic level
    const assignments = await prisma.assignment.findMany({
      where: {
        clientId: { not: undefined },
      },
      select: {
        academicLevel: true,
      },
    });

    const distribution: Record<string, number> = {};
    assignments.forEach((assignment) => {
      const level = assignment.academicLevel || "OTHER";
      distribution[level] = (distribution[level] || 0) + 1;
    });

    const clientDistribution = Object.entries(distribution).map(([category, value]) => ({
      category,
      value,
    }));

    // Recent client activity (simplified)
    const recentActivities = await prisma.user.findMany({
      where: {
        role: "CLIENT",
      },
      select: {
        id: true,
        name: true,
        updatedAt: true,
      },
      orderBy: {
        updatedAt: "desc",
      },
      take: 10,
    });

    const clientActivity = recentActivities.map((activity) => ({
      id: activity.id,
      name: activity.name || "Anonymous",
      action: "Logged in",
      timestamp: activity.updatedAt,
    }));

    const analyticsData: ClientAnalytics = {
      totalClients,
      activeClients,
      newClients,
      churnRate,
      clientGrowth,
      topClients: formattedTopClients,
      clientsByMonth,
      clientActivity,
      clientDistribution,
    };

    return NextResponse.json(analyticsData);
  } catch (error) {
    console.error("Error fetching client analytics:", error);
    return NextResponse.json(
      { error: "Failed to fetch client analytics" },
      { status: 500 }
    );
  }
}
