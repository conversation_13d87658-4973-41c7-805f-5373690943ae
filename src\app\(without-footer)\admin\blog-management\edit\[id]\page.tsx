"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { ArrowLeft, Save, Eye } from "lucide-react";
import Link from "next/link";
import HybridQuillEditor from "@/components/quill-editor/hybrid-editor";
import { BlogImageUpload } from "@/components/blog-image-upload";

interface Author {
  id: string;
  name: string;
  qualifications: string;
}

interface Category {
  id: string;
  name: string;
  slug: string;
}

interface FAQ {
  question: string;
  answer: string;
}

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  body: string;
  metaTitle: string;
  metaDescription: string;
  imageUrl: string;
  imageAlt: string;
  keywords: string[];
  faqs: string[];
  createdAt: string;
  updatedAt: string;
  pageViews: number;
  author: Author;
  category: Category;
  authorId: string;
  categoryId: string;
}

interface EditBlogPageProps {
  params: Promise<{ id: string }>;
}

export default function EditBlogPage({ params }: EditBlogPageProps) {
  const router = useRouter();
  const [blogId, setBlogId] = useState<string>("");
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  
  // Form state
  const [title, setTitle] = useState("");
  const [slug, setSlug] = useState("");
  const [metaTitle, setMetaTitle] = useState("");
  const [metaDescription, setMetaDescription] = useState("");
  const [imageUrl, setImageUrl] = useState("");
  const [imageAlt, setImageAlt] = useState("");
  const [authorId, setAuthorId] = useState("");
  const [categoryId, setCategoryId] = useState("");
  const [keywords, setKeywords] = useState<string[]>([]);
  const [keywordInput, setKeywordInput] = useState("");
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [faqQuestion, setFaqQuestion] = useState("");
  const [faqAnswer, setFaqAnswer] = useState("");
  const [body, setBody] = useState<string>("");
  
  // Reference data
  const [authors, setAuthors] = useState<Author[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [originalSlug, setOriginalSlug] = useState("");

  const fetchBlogPost = useCallback(async (id: string) => {
    try {
      const response = await fetch(`/api/blog/${id}`);
      if (response.ok) {
        const blog: BlogPost = await response.json();

        // Populate form with existing data
        setTitle(blog.title);
        setSlug(blog.slug);
        setOriginalSlug(blog.slug);
        setMetaTitle(blog.metaTitle);
        setMetaDescription(blog.metaDescription);
        setImageUrl(blog.imageUrl);
        setImageAlt(blog.imageAlt || '');
        setAuthorId(blog.authorId);
        setCategoryId(blog.categoryId);
        setKeywords(blog.keywords);
        setBody(blog.body);

        // Parse FAQs from string format back to objects
        const parsedFaqs = blog.faqs.map(faqString => {
          const [question, answer] = faqString.split('|');
          return { question: question || '', answer: answer || '' };
        });
        setFaqs(parsedFaqs);
      } else {
        toast.error("Failed to fetch blog post");
        router.push("/admin/blog-management");
      }
    } catch (error) {
      console.error("Error fetching blog post:", error);
      toast.error("Failed to fetch blog post");
      router.push("/admin/blog-management");
    }
  }, [router]);

  const fetchAuthors = useCallback(async () => {
    try {
      const response = await fetch("/api/blog/authors");
      if (response.ok) {
        const data = await response.json();
        setAuthors(data);
      }
    } catch (error) {
      console.error("Error fetching authors:", error);
    }
  }, []);

  const fetchCategories = useCallback(async () => {
    try {
      const response = await fetch("/api/blog/categories");
      if (response.ok) {
        const data = await response.json();
        setCategories(data);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  }, []);

  useEffect(() => {
    const initializePage = async () => {
      const resolvedParams = await params;
      setBlogId(resolvedParams.id);
      await Promise.all([
        fetchBlogPost(resolvedParams.id),
        fetchAuthors(),
        fetchCategories()
      ]);
      setLoading(false);
    };

    initializePage();
  }, [params, fetchBlogPost, fetchAuthors, fetchCategories]);



  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const handleTitleChange = (newTitle: string) => {
    setTitle(newTitle);
    if (!slug || slug === generateSlug(title)) {
      setSlug(generateSlug(newTitle));
    }
  };

  const handleAddKeyword = () => {
    if (!keywordInput.trim()) return;

    // Split by commas and process each keyword
    const newKeywords = keywordInput
      .split(',')
      .map(kw => kw.trim())
      .filter(kw => kw.length > 0 && !keywords.includes(kw));

    if (newKeywords.length > 0) {
      setKeywords([...keywords, ...newKeywords]);
      setKeywordInput("");

      if (newKeywords.length === 1) {
        toast.success(`Added keyword: "${newKeywords[0]}"`);
      } else {
        toast.success(`Added ${newKeywords.length} keywords`);
      }
    } else if (keywordInput.trim()) {
      toast.error("Keyword(s) already exist or are invalid");
    }
  };

  const handleRemoveKeyword = (kw: string) => setKeywords(keywords.filter(k => k !== kw));

  const handleAddFaq = () => {
    if (faqQuestion && faqAnswer) {
      const exists = faqs.some(faq => faq.question.toLowerCase() === faqQuestion.toLowerCase());
      if (!exists) {
        setFaqs([...faqs, { question: faqQuestion, answer: faqAnswer }]);
        setFaqQuestion("");
        setFaqAnswer("");
      } else {
        toast.error("This question already exists");
      }
    } else {
      toast.error("Please fill in both question and answer");
    }
  };

  const handleRemoveFaq = (index: number) => {
    setFaqs(faqs.filter((_, i) => i !== index));
  };

  const handleImageUpload = (url: string, altText: string) => {
    setImageUrl(url);
    setImageAlt(altText);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!title || !slug || !metaTitle || !metaDescription || !imageUrl || !imageAlt || !authorId || !categoryId) {
      toast.error("Please fill in all required fields");
      return;
    }

    if (keywords.length === 0) {
      toast.error("Please add at least one keyword");
      return;
    }

    if (faqs.length === 0) {
      toast.error("Please add at least one FAQ");
      return;
    }

    if (!body || body.replace(/<[^>]+>/g, '').trim().length < 10) {
      toast.error("Blog body must be at least 10 characters");
      return;
    }

    setSaving(true);
    try {
      // Convert FAQ objects to strings in the format "Question|Answer"
      const faqStrings = faqs.map(faq => `${faq.question}|${faq.answer}`);

      const response = await fetch(`/api/blog/${blogId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          title,
          body,
          slug,
          metaTitle,
          metaDescription,
          imageUrl,
          imageAlt,
          categoryId,
          authorId,
          keywords,
          faqs: faqStrings,
        }),
      });

      if (response.ok) {
        toast.success("Blog post updated successfully!");
        router.push("/admin/blog-management");
      } else {
        const error = await response.json();
        if (response.status === 409) {
          toast.error("Slug already exists. Please choose a different slug.");
        } else {
          toast.error(error.error || "Failed to update blog post");
        }
      }
    } catch (error) {
      toast.error("Failed to update blog post. Please try again.");
      console.error(error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4 max-w-4xl">
      <div className="flex flex-col lg:flex-row items-start lg:items-center gap-4 mb-8">
        <Link href="/admin/blog-management">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Blog Management
          </Button>
        </Link>
        <div className="flex-1">
          <h1 className="text-2xl lg:text-3xl font-bold">Edit Blog Post</h1>
          <p className="text-muted-foreground mt-1">
            Update your blog post content and settings
          </p>
        </div>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 w-full lg:w-auto">
          <Link href={`/blog/${originalSlug}`} target="_blank" className="w-full sm:w-auto">
            <Button variant="outline" size="sm" className="w-full sm:w-auto">
              <Eye className="h-4 w-4 mr-2" />
              Preview
            </Button>
          </Link>
          <Button
            type="submit"
            form="blog-edit-form"
            disabled={saving}
            className="flex items-center justify-center gap-2 w-full sm:w-auto"
          >
            <Save className="h-4 w-4" />
            {saving ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </div>

      <form id="blog-edit-form" onSubmit={handleSubmit} className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block mb-2 font-medium">
                  Title <span className="text-red-500">*</span>
                </label>
                <Input
                  value={title}
                  onChange={(e) => handleTitleChange(e.target.value)}
                  placeholder="Enter blog title"
                  required
                />
              </div>
              <div>
                <label className="block mb-2 font-medium">
                  Slug <span className="text-red-500">*</span>
                </label>
                <Input
                  value={slug}
                  onChange={(e) => setSlug(e.target.value)}
                  placeholder="blog-post-url"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block mb-2 font-medium">
                  Author <span className="text-red-500">*</span>
                </label>
                <Select value={authorId} onValueChange={setAuthorId} required>
                  <SelectTrigger>
                    <SelectValue placeholder="Select author" />
                  </SelectTrigger>
                  <SelectContent>
                    {authors.map(author => (
                      <SelectItem key={author.id} value={author.id}>
                        {author.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="block mb-2 font-medium">
                  Category <span className="text-red-500">*</span>
                </label>
                <Select value={categoryId} onValueChange={setCategoryId} required>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map(category => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        <Separator className="my-6" />

        <Card>
          <CardHeader>
            <CardTitle>SEO & Meta Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <label className="block mb-2 font-medium">
                Meta Title <span className="text-red-500">*</span>
              </label>
              <Input
                value={metaTitle}
                onChange={(e) => setMetaTitle(e.target.value)}
                placeholder="SEO optimized title"
                required
              />
            </div>
            <div>
              <label className="block mb-2 font-medium">
                Meta Description <span className="text-red-500">*</span>
              </label>
              <Textarea
                value={metaDescription}
                onChange={(e) => setMetaDescription(e.target.value)}
                placeholder="Brief description for search engines"
                rows={3}
                required
              />
            </div>
          </CardContent>
        </Card>

        <BlogImageUpload
          onImageUpload={handleImageUpload}
          currentImageUrl={imageUrl}
          currentAltText={imageAlt}
          required={true}
        />

        <Card>
          <CardHeader>
            <CardTitle>Keywords & SEO</CardTitle>
          </CardHeader>
          <CardContent>
            <div>
              <label className="block mb-2 font-medium">
                Keywords <span className="text-red-500">*</span>
              </label>
              <div className="flex gap-2 mb-3">
                <div className="flex-1">
                  <Input
                    value={keywordInput}
                    onChange={(e) => setKeywordInput(e.target.value)}
                    placeholder="Add keywords (separate multiple with commas: keyword1, keyword2, keyword3)"
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddKeyword())}
                    className="w-full"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    💡 Tip: You can add multiple keywords at once by separating them with commas
                  </p>
                </div>
                <Button type="button" onClick={handleAddKeyword} variant="secondary" className="shrink-0">
                  Add Keywords
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {keywords.map(kw => (
                  <Badge key={kw} variant="secondary" className="flex items-center gap-1">
                    {kw}
                    <button
                      type="button"
                      onClick={() => handleRemoveKeyword(kw)}
                      className="ml-1 text-red-500 hover:text-red-700"
                    >
                      &times;
                    </button>
                  </Badge>
                ))}
              </div>
              {keywords.length === 0 && (
                <p className="text-sm text-muted-foreground mt-2">
                  Please add at least one keyword for better SEO
                </p>
              )}
              {keywords.length > 0 && (
                <p className="text-xs text-green-600 mt-2">
                  ✅ {keywords.length} keyword{keywords.length !== 1 ? 's' : ''} added
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Frequently Asked Questions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block mb-2 font-medium">Question</label>
                  <Input
                    value={faqQuestion}
                    onChange={(e) => setFaqQuestion(e.target.value)}
                    placeholder="Enter FAQ question"
                  />
                </div>
                <div>
                  <label className="block mb-2 font-medium">Answer</label>
                  <Input
                    value={faqAnswer}
                    onChange={(e) => setFaqAnswer(e.target.value)}
                    placeholder="Enter FAQ answer"
                  />
                </div>
              </div>
              <Button type="button" onClick={handleAddFaq} variant="secondary" className="w-full md:w-auto">
                Add FAQ
              </Button>

              {faqs.length > 0 && (
                <div className="space-y-3">
                  <h4 className="font-medium text-sm text-gray-700">Added FAQs ({faqs.length})</h4>
                  {faqs.map((faq, index) => (
                    <Card key={index} className="p-3">
                      <CardContent className="p-0">
                        <div className="flex justify-between items-start gap-3">
                          <div className="flex-1 space-y-2">
                            <div>
                              <p className="text-sm font-medium text-gray-700">Q: {faq.question}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">A: {faq.answer}</p>
                            </div>
                          </div>
                          <Button
                            type="button"
                            onClick={() => handleRemoveFaq(index)}
                            variant="ghost"
                            size="sm"
                            className="text-red-500 hover:text-red-700 hover:bg-red-50"
                          >
                            &times;
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
              {faqs.length === 0 && (
                <p className="text-sm text-muted-foreground">
                  Please add at least one FAQ to help users understand your content better
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Content</CardTitle>
          </CardHeader>
          <CardContent>
            <div>
              <label className="block mb-2 font-medium">
                Blog Body <span className="text-red-500">*</span>
              </label>
              <HybridQuillEditor
                value={body}
                onChange={setBody}
                placeholder="Write your blog content here..."
                className="min-h-[400px]"
                showHeadlessToolbar={true}
              />
              {(!body || body.replace(/<[^>]+>/g, '').trim().length < 10) && (
                <p className="text-sm text-muted-foreground mt-2">
                  Please write at least 10 characters of content
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        <div className="flex flex-col sm:flex-row justify-end gap-4">
          <Link href="/admin/blog-management" className="w-full sm:w-auto">
            <Button variant="outline" type="button" className="w-full sm:w-auto">
              Cancel
            </Button>
          </Link>
          <Button
            type="submit"
            disabled={saving}
            className="min-w-[120px] w-full sm:w-auto"
          >
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
