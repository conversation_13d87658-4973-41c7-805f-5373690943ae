// src/components/dashboard/writer/bid-dialog.tsx
"use client";

import React, { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent } from "@/components/ui/card";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  User,
  MessageSquare,
  Clock,
  FileText,
  BookOpen,
  Target,
  Send,
  Loader2,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import { toast } from "sonner";

// Hook to get the current user's MongoDB ID from session
function useCurrentUserId(): {
  userId: string | null;
  loading: boolean;
  error: string | null;
} {
  const { data: session, status } = useSession();

  const loading = status === "loading";
  const userId = session?.user?.id || null;
  const error = status === "unauthenticated" ? "User not authenticated" : null;

  return {
    userId,
    loading,
    error,
  };
}

interface Assignment {
  id: string;
  taskId: string;
  title: string;
  assignmentType: string;
  subject: string;
  pageCount: number;
  priority: "LOW" | "MEDIUM" | "HIGH";
  academicLevel: string;
  formatStyle: string;
  hoursRemaining: number;
}

interface BidDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  assignment: Assignment | null;
}

export function BidDialog({ open, onOpenChange, assignment }: BidDialogProps) {
  const [bidMessage, setBidMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { data: session } = useSession();
  const { userId, loading: userLoading, error: userError } = useCurrentUserId();

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (!open) {
      setBidMessage("");
      setIsSubmitting(false);
    }
  }, [open]);

  // Get writer's display name and initials
  const writerName = session?.user?.name || session?.user?.email || "Writer";
  const writerInitials = writerName
    .split(" ")
    .map((n) => n[0])
    .join("")
    .toUpperCase()
    .slice(0, 2);

  // Format assignment type for display
  const getTypeDisplay = (type: string) => {
    switch (type) {
      case "RESEARCH_PAPER":
        return "Research Paper";
      case "TERM_PAPER":
        return "Term Paper";
      case "BOOK_REVIEW":
        return "Book Review";
      case "ARTICLE_REVIEW":
        return "Article Review";
      case "CASE_STUDY":
        return "Case Study";
      default:
        return type.toLowerCase().replace("_", " ");
    }
  };

  // Get priority color and icon
  const getPriorityDisplay = (priority: string) => {
    switch (priority) {
      case "LOW":
        return {
          color: "bg-gray-50 text-gray-700 border-gray-200",
          icon: "↓",
          label: "Low Priority",
        };
      case "MEDIUM":
        return {
          color: "bg-blue-50 text-blue-700 border-blue-200",
          icon: "→",
          label: "Medium Priority",
        };
      case "HIGH":
        return {
          color: "bg-red-50 text-red-700 border-red-200",
          icon: "↑",
          label: "High Priority",
        };
      default:
        return {
          color: "bg-gray-50 text-gray-700 border-gray-200",
          icon: "→",
          label: "Normal Priority",
        };
    }
  };

  // Format hours remaining display
  const formatTimeRemaining = (hours: number) => {
    if (hours <= 0) return "Overdue";
    if (hours < 24) return `${hours}h remaining`;
    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;
    return `${days}d ${remainingHours}h remaining`;
  };

  // Get urgency color
  const getUrgencyColor = (hours: number) => {
    if (hours <= 0) return "text-red-600";
    if (hours <= 24) return "text-orange-600";
    if (hours <= 48) return "text-yellow-600";
    return "text-green-600";
  };

  const handleSubmit = async () => {
    if (!bidMessage.trim() || !assignment || !userId) return;

    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/assignments/${assignment.id}/bids`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          message: bidMessage.trim(),
          writerId: userId,
          assignmentId: assignment.id,
        }),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || "Failed to submit bid");
      }

      // Show success toast
      toast.success("Bid submitted successfully");

      // Close dialog on success
      onOpenChange(false);
    } catch (error) {
      console.error("Failed to submit bid:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to submit bid"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!assignment) return null;

  const priorityDisplay = getPriorityDisplay(assignment.priority);

  return (
    <TooltipProvider>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader className="space-y-4">
            <div className="flex items-center gap-3">
              <Avatar className="h-12 w-12 border-2 border-primary/20">
                <AvatarImage src={session?.user?.image || undefined} />
                <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                  {writerInitials}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <DialogTitle className="text-xl font-semibold flex items-center gap-2">
                  <User className="h-5 w-5 text-primary" />
                  Submit Bid as {writerName}
                </DialogTitle>
                <DialogDescription className="text-sm text-muted-foreground">
                  Place your bid for this assignment opportunity
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          <Separator className="my-4" />

          {/* Assignment Overview Card */}
          <Card className="border-2 border-dashed border-muted bg-muted/20">
            <CardContent className="p-4 space-y-3">
              <div className="flex items-start justify-between gap-3">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge variant="outline" className="text-xs font-medium">
                      {assignment.taskId}
                    </Badge>
                    <Badge
                      variant="outline"
                      className={`text-xs ${priorityDisplay.color}`}
                    >
                      <span className="mr-1">{priorityDisplay.icon}</span>
                      {priorityDisplay.label}
                    </Badge>
                  </div>
                  <h3 className="font-semibold text-lg leading-tight mb-2 text-foreground">
                    {assignment.title}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {getTypeDisplay(assignment.assignmentType)} •{" "}
                    {assignment.subject}
                  </p>
                </div>
                <Tooltip>
                  <TooltipTrigger>
                    <div
                      className={`flex items-center gap-1 text-sm font-medium ${getUrgencyColor(
                        assignment.hoursRemaining
                      )}`}
                    >
                      <Clock className="h-4 w-4" />
                      {formatTimeRemaining(assignment.hoursRemaining)}
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Assignment deadline urgency</p>
                  </TooltipContent>
                </Tooltip>
              </div>

              <div className="grid grid-cols-3 gap-4 pt-2">
                <div className="flex items-center gap-2 text-sm">
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{assignment.pageCount}</span>
                  <span className="text-muted-foreground">pages</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <BookOpen className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">
                    {assignment.academicLevel.replace("_", " ")}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Target className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{assignment.formatStyle}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Bid Message Section */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5 text-primary" />
              <Label htmlFor="bid-message" className="text-base font-semibold">
                Your Bid Message
              </Label>
            </div>
            <Textarea
              id="bid-message"
              placeholder="Share your relevant experience, approach to this assignment, and why you're the best fit for this project..."
              value={bidMessage}
              onChange={(e) => setBidMessage(e.target.value)}
              rows={5}
              className="resize-none focus:ring-2 focus:ring-primary/20 border-2"
              disabled={isSubmitting}
            />
            <div className="flex items-center justify-between">
              <p className="text-xs text-muted-foreground">
                Tip: Highlight your expertise and unique value proposition
              </p>
              <p className="text-xs text-muted-foreground">
                {bidMessage.length}/500 characters
              </p>
            </div>
          </div>

          {/* Success/Error/Loading States */}
          {userError && (
            <div className="flex items-center gap-2 p-3 rounded-lg bg-red-50 border border-red-200">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <p className="text-sm text-red-600">{userError}</p>
            </div>
          )}

          {!userError && !userLoading && userId && (
            <div className="flex items-center gap-2 p-3 rounded-lg bg-green-50 border border-green-200">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <p className="text-sm text-green-600">Ready to submit your bid</p>
            </div>
          )}

          <DialogFooter className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={
                !bidMessage.trim() ||
                isSubmitting ||
                userLoading ||
                !!userError ||
                bidMessage.length > 500
              }
              className="min-w-[120px]"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Submitting...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Submit Bid
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </TooltipProvider>
  );
}
