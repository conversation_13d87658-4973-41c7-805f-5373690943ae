// Add these models to your existing schema.prisma file

// Chat-related enums
enum MessageType {
  TEXT
  EMOJI
  SYSTEM
}

enum ChatParticipantRole {
  ADMIN
  CLIENT
  WRITER
}

// Chat models
model Chat {
  id           String    @id @default(auto()) @map("_id") @db.ObjectId
  assignmentId String    @db.ObjectId
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  assignment   Assignment @relation(fields: [assignmentId], references: [id], onDelete: Cascade)
  messages     Message[]
  participants ChatParticipant[]

  @@unique([assignmentId])
}

model ChatParticipant {
  id     String              @id @default(auto()) @map("_id") @db.ObjectId
  chatId String              @db.ObjectId
  userId String              @db.ObjectId
  role   ChatParticipantRole
  joinedAt DateTime          @default(now())

  chat Chat @relation(fields: [chatId], references: [id], onDelete: Cascade)
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([chatId, userId])
}

model Message {
  id        String      @id @default(auto()) @map("_id") @db.ObjectId
  chatId    String      @db.ObjectId
  senderId  String      @db.ObjectId
  content   String
  type      MessageType @default(TEXT)
  isRead    Boolean     @default(false)
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt

  chat   Chat @relation(fields: [chatId], references: [id], onDelete: Cascade)
  sender User @relation(fields: [senderId], references: [id], onDelete: Cascade)
}

// Add these relations to the existing User model:
// chatParticipants ChatParticipant[]
// sentMessages     Message[]

// Add this relation to the existing Assignment model:
// chat             Chat?