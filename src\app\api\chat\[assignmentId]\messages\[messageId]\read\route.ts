// src/app/api/chat/[assignmentId]/messages/[messageId]/read/route.ts
import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authConfig } from "@/auth";

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ assignmentId: string; messageId: string }> }
) {
  try {
    const session = await getServerSession(authConfig);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { assignmentId, messageId } = await params;
    const userId = session.user.id;
    const userRole = session.user.role;

    // Verify message exists and user has access
    const message = await prisma.message.findUnique({
      where: { id: messageId },
      include: {
        chat: {
          include: {
            assignment: true,
          },
        },
      },
    });

    if (!message || message.chat.assignmentId !== assignmentId) {
      return NextResponse.json(
        { success: false, message: "Message not found" },
        { status: 404 }
      );
    }

    // Check access permissions
    const assignment = message.chat.assignment;
    const hasAccess =
      userRole === "ADMIN" ||
      (userRole === "CLIENT" && assignment.clientId === userId) ||
      (userRole === "WRITER" && assignment.assignedWriterId === userId);

    if (!hasAccess) {
      return NextResponse.json(
        { success: false, message: "Forbidden" },
        { status: 403 }
      );
    }

    // Don't allow marking own messages as read
    if (message.senderId === userId) {
      return NextResponse.json(
        { success: false, message: "Cannot mark own message as read" },
        { status: 400 }
      );
    }

    // Update message read status
    const updatedMessage = await prisma.message.update({
      where: { id: messageId },
      data: { isRead: true },
    });

    // Broadcast read status if you have SSE setup
    try {
      const { broadcastMessageRead } = await import("@/lib/chat-broadcast");
      await broadcastMessageRead(assignmentId, messageId, userId);
    } catch (error) {
      console.warn("Failed to broadcast read status:", error);
      // Don't fail the request if broadcasting fails
    }

    return NextResponse.json({
      success: true,
      message: "Message marked as read",
      data: updatedMessage,
    });
  } catch (error) {
    console.error("Error marking message as read:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}
