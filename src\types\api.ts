// src/types/api.ts
import {
  UserRole,
  AssignmentStatus,
  BidStatus,
  JobStatus,
  PaymentStatus,
  AssignmentType,
  Priority,
  AcademicLevel,
  Spacing,
  LanguageStyle,
  FormatStyle,
  NotificationType,
} from "@prisma/client";

// Base API Response
export type ApiResponse<T = undefined> = {
  success: boolean;
  message: string;
  data?: T;
};

// Error response
export type ApiError = {
  success: false;
  message: string;
  errors?: Record<string, string[]>;
  statusCode?: number;
};

// User types
export type UserBaseData = {
  email: string;
  name?: string | null;
  phone?: string | null;
  role: UserRole;
  accountId?: string | null;
};

export type UserCreateData = UserBaseData & {
  password: string;
};

export type UserUpdateData = Partial<UserBaseData> & {
  password?: string;
  isApproved?: boolean;
  emailVerified?: boolean;
  educationLevel?: string | null;
  // Add the missing writer profile fields
  professionalSummary?: string | null;
  experience?: string | null;
  competencies?: string[];
};

// Writer profile types
export type WriterProfileData = {
  professionalSummary?: string | null;
  experience?: string | null;
  competencies?: string[];
  educationLevel?: string | null;
  rating?: number | null;
};

export type WriterProfileUpdateData = Partial<WriterProfileData>;

export type UserResponse = {
  id: string;
  accountId: string | null;
  email: string;
  name: string | null;
  phone: string | null;
  role: UserRole;
  isApproved: boolean;
  emailVerified: boolean;
  professionalSummary: string | null;
  experience: string | null;
  competencies: string[];
  educationLevel: string | null;
  rating: number | null;
  createdAt: string;
  updatedAt: string;
  image: string | null;
};

// Todo types
export type TodoBaseData = {
  title: string;
  description?: string | null;
  priority?: Priority;
  category?: string | null;
  dueDate?: string | null;
  isCompleted?: boolean;
};

export type TodoCreateData = TodoBaseData;

export type TodoUpdateData = Partial<TodoBaseData>;

export type TodoResponse = {
  id: string;
  title: string;
  description: string | null;
  priority: Priority;
  category: string | null;
  dueDate: string | null;
  isCompleted: boolean;
  userId: string;
  createdAt: string;
  updatedAt: string;
  user?: {
    id: string;
    name: string | null;
    email: string;
    accountId: string | null;
  };
};

// CLAUDE ORDERS API: - Updated Assignment types to match new schema
// Assignment types
export type AssignmentBaseData = {
  taskId: string;
  title: string;
  description: string;
  assignmentType?: AssignmentType;
  subject: string;
  service: string;
  pageCount: number;
  price?: number;
  priority?: Priority;
  academicLevel?: AcademicLevel;
  spacing?: Spacing;
  languageStyle?: LanguageStyle;
  formatStyle?: FormatStyle;
  numSources?: number;
  guidelines?: string;
  estTime: string | Date;
  clientId: string;
  assignedWriterId?: string;
  status?: AssignmentStatus;
  paymentStatus?: PaymentStatus;
  paypalOrderId?: string;
  paypalPayerId?: string;
  paypalPaymentId?: string;
  // Coupon fields
  couponCode?: string;
  originalPrice?: number;
  discountAmount?: number;
};

export type AssignmentUpdateData = Partial<AssignmentBaseData>;

export type AssignmentResponse = {
  id: string;
  taskId: string;
  title: string;
  description: string;
  assignmentType: AssignmentType;
  subject: string;
  service: string;
  pageCount: number;
  price: number;
  priority: Priority;
  academicLevel: AcademicLevel;
  spacing: Spacing;
  languageStyle: LanguageStyle;
  formatStyle: FormatStyle;
  numSources: number;
  guidelines: string | null;
  estTime: string;
  clientId: string;
  assignedWriterId: string | null;
  status: AssignmentStatus;
  paymentStatus: PaymentStatus;
  paypalOrderId: string | null;
  paypalPayerId: string | null;
  paypalPaymentId: string | null;
  createdAt: string;
  updatedAt: string;
  // Coupon fields
  couponCode?: string | null;
  originalPrice?: number | null;
  discountAmount?: number | null;
  client?: {
    id: string;
    name: string | null;
    accountId: string | null;
    email: string;
  };
  assignedWriter?: {
    id: string;
    name: string | null;
    email: string;
    accountId: string | null;
    rating: number | null;
    competencies: string[];
  };
  _count?: {
    bids: number;
  };
};

// Bid types
export type BidBaseData = {
  message?: string | null;
  writerId: string;
  assignmentId: string;
  status?: BidStatus;
};

export type BidUpdateData = Partial<BidBaseData>;

export type BidResponse = {
  id: string;
  message: string | null;
  status: BidStatus;
  writerId: string;
  assignmentId: string;
  createdAt: string;
  updatedAt: string;
  writer?: {
    id: string;
    name: string | null;
    email: string;
    accountId: string | null;
    // rating: number | null;
    // competencies: string[];
    // professionalSummary: string | null;
    // experience: string | null;
  };
};

// Job Assignment types
export type JobAssignmentBaseData = {
  assignmentId: string;
  writerId: string;
  adminId: string;
  bidId: string;
  startDate: string | Date;
  deadline: string | Date;
  status?: JobStatus;
};

export type JobAssignmentUpdateData = Partial<JobAssignmentBaseData>;

export type JobAssignmentResponse = {
  id: string;
  assignmentId: string;
  writerId: string;
  adminId: string;
  bidId: string;
  startDate: string;
  deadline: string;
  status: JobStatus;
  createdAt: string;
  updatedAt: string;
  assignment?: AssignmentResponse;
  writer?: {
    id: string;
    name: string | null;
    email: string;
    accountId: string | null;
    // rating: number | null;
    // competencies: string[];
  };
};

// Pagination types
export type PaginationParams = {
  page?: number;
  limit?: number;
};

export type PaginatedResponse<T> = {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
};

// Filter types
export type TodoFilters = {
  isCompleted?: boolean;
  priority?: Priority;
  category?: string;
};

export type UserFilters = {
  role?: UserRole;
  isApproved?: boolean;
  accountId: string | null;
};

// Assessment types
export interface MultipleChoiceQuestion {
  question: string;
  options: string[];
  correctAnswer: string;
}

export interface EssayExam {
  topic: string;
  rubrics: string;
}

export interface WriterAnswer {
  writerId: string;
  multipleChoiceAnswers: { [key: number]: string };
  essayText: string;
}

export interface AssessmentPayload {
  title: string;
  multipleChoiceQuiz: MultipleChoiceQuestion[];
  essayExam: EssayExam;
  isActive?: boolean;
  writersAnswers?: WriterAnswer[];
}

// Newsletter subscription types
export interface NewsletterSubscriptionData {
  id: string;
  email: string;
  isActive: boolean;
  subscribedAt: Date;
  unsubscribedAt?: Date | null;
  source?: string | null;
}

export interface NewsletterSubscribeRequest {
  email: string;
  source?: string;
}

export interface NewsletterUnsubscribeRequest {
  email: string;
}

export interface NewsletterStatsResponse {
  totalSubscribers: number;
  totalUnsubscribed: number;
  recentSubscribers: number;
  subscribersBySource: Array<{
    source: string;
    count: number;
  }>;
  dailyStats: Record<string, number>;
}

export interface NewsletterResponse {
  success: boolean;
  message: string;
  data?: NewsletterSubscriptionData;
}

// Chat types
export type MessageBaseData = {
  content: string;
  assignmentId: string;
  senderId: string;
  recipientId: string;
};

export type MessageCreateData = MessageBaseData;

export type MessageUpdateData = {
  content?: string;
  isRead?: boolean;
};

export type MessageResponse = {
  id: string;
  content: string;
  assignmentId: string;
  senderId: string;
  recipientId: string;
  isRead: boolean;
  createdAt: string;
  updatedAt: string;
  sender: {
    id: string;
    name: string | null;
    email: string;
    role: UserRole;
  };
  recipient: {
    id: string;
    name: string | null;
    email: string;
    role: UserRole;
  };
};

// File attachment types
export type FileAttachmentBaseData = {
  assignmentId: string;
  fileName: string;
  originalName: string;
  fileUrl: string;
  fileSize: number;
  fileType: string;
};

export type FileAttachmentCreateData = FileAttachmentBaseData;

export type FileAttachmentResponse = {
  id: string;
  assignmentId: string;
  fileName: string;
  originalName: string;
  fileUrl: string;
  fileSize: number;
  fileType: string;
  uploadedAt: string;
};

export type ChatFilters = {
  assignmentId: string;
  senderId?: string;
  recipientId?: string;
  isRead?: boolean;
};

export type ChatData = {
  assignmentId: string;
  messages: MessageResponse[];
  participants: {
    client: {
      id: string;
      name: string | null;
      email: string;
    } | null;
    writer: {
      id: string;
      name: string | null;
      email: string;
    } | null;
    admin: {
      id: string;
      name: string | null;
      email: string;
    };
  };
};

export type UnreadCounts = {
  client: number;
  writer: number;
};

// Socket.IO event types
export type SocketEvents = {
  "join-assignment-chat": (assignmentId: string) => void;
  "send-message": (data: MessageCreateData) => void;
  "new-message": (message: MessageResponse) => void;
  "message-read": (data: { messageId: string }) => void;
  typing: (data: {
    assignmentId: string;
    userId: string;
    isTyping: boolean;
  }) => void;
};

// Notification types
export type NotificationBaseData = {
  type: NotificationType;
  title: string;
  message: string;
  taskId?: string;
  assignmentId?: string;
};

export type NotificationCreateData = {
  targetUserId: string;
  notification: NotificationBaseData;
};

export type NotificationResponse = {
  id: string;
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  taskId?: string;
  assignmentId?: string;
  read: boolean;
  createdAt: string;
  updatedAt: string;
};

export type NotificationQueryParams = {
  read?: boolean;
  type?: NotificationType;
  limit?: number;
  offset?: number;
};

// Contact form types
export interface ContactFormData {
  name: string;
  email: string;
  phone?: string | null;
  subject: string;
  message: string;
  urgency: "low" | "medium" | "high";
  category: "general" | "support" | "billing" | "technical" | "partnership";
  agreeToPrivacy: boolean;
}

export type ContactFormRequest = ContactFormData;

export interface ContactFormResponse {
  success: boolean;
  message: string;
  data?: {
    id: string;
    submittedAt: string;
  };
}

export type NotificationUpdateData = {
  notificationIds: string[];
};

// Pricing rule types
export type PricingRuleBaseData = {
  ruleType: string;
  academicLevel?: AcademicLevel;
  priority?: Priority;
  spacing?: Spacing;
  value: number;
  isActive?: boolean;
};

export type PricingRuleCreateData = PricingRuleBaseData;

export type PricingRuleUpdateData = {
  ruleType: string;
  value: number;
  academicLevel?: AcademicLevel;
  priority?: Priority;
  spacing?: Spacing;
};

export type PricingRuleResponse = {
  id: string;
  ruleType: string;
  academicLevel?: AcademicLevel;
  priority?: Priority;
  spacing?: Spacing;
  value: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
};

// Price calculation types
export type PriceBreakdownResponse = {
  basePrice: number;
  academicLevelMultiplier: number;
  priorityMultiplier: number;
  spacingMultiplier: number;
  subtotal: number;
  finalPrice: number;
  minimumPrice: number;
};

export type WriterCompensationResponse = {
  percentage: number;
  minimumPerPage: number;
  calculatedAmount: number;
  finalAmount: number;
};

export type PricePreviewRequest = {
  academicLevel: AcademicLevel;
  priority: Priority;
  spacing: Spacing;
  pageCount: number;
};

export type PricePreviewResponse = {
  priceBreakdown: PriceBreakdownResponse;
  writerCompensation: WriterCompensationResponse;
};

// Company Info types
export type CompanyInfoBaseData = {
  companyName: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  phone: string;
  tollFreePhone?: string | null;
  internationalPhone?: string | null;
  supportEmail: string;
  inquiriesEmail: string;
  businessHours: string;
  description?: string | null;
  website?: string | null;
};

export type CompanyInfoCreateData = CompanyInfoBaseData;

export type CompanyInfoUpdateData = Partial<CompanyInfoBaseData>;

export type CompanyInfoResponse = {
  id: string;
  companyName: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  phone: string;
  tollFreePhone?: string | null;
  internationalPhone?: string | null;
  supportEmail: string;
  inquiriesEmail: string;
  businessHours: string;
  description?: string | null;
  website?: string | null;
  createdAt: string;
  updatedAt: string;
};
