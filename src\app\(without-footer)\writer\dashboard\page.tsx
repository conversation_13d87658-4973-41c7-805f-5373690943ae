// src/app/(without-footer)/writer/dashboard/page.tsx

"use client";

import WriterAssignmentsChart from "@/components/dashboard/writer/dashboard-area-chart";
import WriterProfileLargeDash from "@/components/dashboard/writer/dashboard-large-card";
import WriterTodoList from "@/components/dashboard/writer/dashboard-todo-list";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";

export default function WriterDashboardPage() {
  const breadcrumbs = [
    { label: "Dashboard", href: "/writer/dashboard" },
    { label: "Account", isCurrentPage: true },
  ];

  return (
    <>
      <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <Breadcrumb>
            <BreadcrumbList>
              {breadcrumbs.map((item, index) => (
                <div key={index} className="flex items-center">
                  {index > 0 && (
                    <BreadcrumbSeparator className="hidden md:block" />
                  )}
                  <BreadcrumbItem className="hidden md:block">
                    {item.isCurrentPage ? (
                      <BreadcrumbPage>{item.label}</BreadcrumbPage>
                    ) : (
                      <BreadcrumbLink href={item.href || "#"}>
                        {item.label}
                      </BreadcrumbLink>
                    )}
                  </BreadcrumbItem>
                </div>
              ))}
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>

      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        {/* Main dashboard content */}
        <div className="grid gap-4 grid-cols-1 xl:grid-cols-3 min-h-[calc(100vh-8rem)]">
          {/* Left column - Two stacked cards */}
          <div className="flex flex-col gap-4 xl:col-span-1 h-full">
            {/* Top card - Chart section with fixed height */}
            <div className="bg-muted/50 rounded-xl border shadow-sm hover:shadow-md transition-shadow duration-200 h-80 md:h-96 shrink-0 overflow-hidden">
              {/* Chart will go here */}
              <WriterAssignmentsChart />
            </div>

            {/* Bottom card - Todo list section that expands to fill remaining space */}
            <div className="bg-muted/50 rounded-xl border shadow-sm hover:shadow-md transition-shadow duration-200 flex-1 min-h-[300px] overflow-hidden">
              {/* Todo list will go here */}
              <WriterTodoList />
            </div>
          </div>

          {/* Right column - Large card */}
          <div className="xl:col-span-2">
            <div className="bg-muted/50 h-full rounded-xl border shadow-sm hover:shadow-md transition-shadow duration-200 min-h-[400px] xl:min-h-full overflow-hidden">
              <WriterProfileLargeDash />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
