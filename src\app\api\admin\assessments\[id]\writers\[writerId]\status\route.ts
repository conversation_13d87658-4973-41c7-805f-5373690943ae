// src/app/api/admin/assessments/[id]/writers/[writerId]/status/route.ts

import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authConfig as authOptions } from "../../../../../../../../auth";
import prisma from "@/lib/prisma";
import { Prisma } from "@prisma/client";

// PATCH: Update writer's assessment status
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string; writerId: string }> }
): Promise<NextResponse> {
  const session = await getServerSession(authOptions);
  const { id, writerId } = await params;

  // Check if user is admin
  if (session?.user?.role !== "ADMIN") {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { status } = await req.json();

    // Validate status
    if (status !== "Passed" && status !== "Failed") {
      return NextResponse.json(
        { error: "Invalid status. Must be 'Passed' or 'Failed'" },
        { status: 400 }
      );
    }

    // Get current assessment
    const assessment = await prisma.assessment.findUnique({
      where: { id },
    });

    if (!assessment) {
      return NextResponse.json(
        { error: "Assessment not found" },
        { status: 404 }
      );
    }

    // Get and validate current writers answers
    const currentAnswers = assessment.writersAnswers;
    if (!Array.isArray(currentAnswers)) {
      return NextResponse.json(
        { error: "Invalid writers answers format" },
        { status: 400 }
      );
    }

    // Find and update the specific writer's answer
    const updatedAnswers = currentAnswers.map((answer) => {
      // Skip null values
      if (answer === null) {
        return {};
      }

      const writerAnswer = answer as Record<string, unknown>;

      if (
        typeof writerAnswer.writerId === "string" &&
        writerAnswer.writerId === writerId
      ) {
        return {
          ...writerAnswer,
          status,
        };
      }

      return writerAnswer;
    });

    // Update the assessment with the modified answers
    const updated = await prisma.assessment.update({
      where: { id },
      data: {
        writersAnswers: updatedAnswers as Prisma.InputJsonValue[],
      },
    });

    return NextResponse.json(updated);
  } catch (error) {
    console.error("Error updating writer status:", error);
    return NextResponse.json(
      { error: "Failed to update writer status" },
      { status: 500 }
    );
  }
}
