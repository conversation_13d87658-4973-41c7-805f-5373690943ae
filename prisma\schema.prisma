// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js" 
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

enum UserRole {
  ADMIN
  CLIENT
  WRITER
}

enum AssignmentStatus {
  DRAFT
  PENDING
  POSTED
  ASSIGNED
  COMPLETED
  REVISION
  CANCELLED
}

enum BidStatus {
  PENDING
  ACCEPTED
  REJECTED
}

enum JobStatus {
  IN_PROGRESS
  COMPLETED
  REVISION
  REJECTED
}

enum PaymentStatus {
  PENDING
  PAID
}

// CLAUDE ORDERS API: - Added new enums for assignment properties
enum AssignmentType {
  ARTICLE_REVIEW
  BOOK_REVIEW
  CASE_STUDY
  DISCUSSION
  DISSERTATION
  ESSAY
  LAB_REPORT
  LITERATURE_REVIEW
  PERSONAL_STATEMENT
  REFLECTION_PAPER
  RESEARCH_PAPER
  TERM_PAPER
  THESIS
  OTHER
}

enum Priority {
  LOW
  MEDIUM
  HIGH
}

enum AcademicLevel {
  HIGH_SCHOOL
  UNDERGRADUATE
  MASTERS
  PHD
  PROFESSIONAL
}

enum Spacing {
  SINGLE
  DOUBLE
}

enum LanguageStyle {
  ENGLISH_US
  ENGLISH_UK
  ENGLISH_AU
  OTHER
}

enum FormatStyle {
  APA
  MLA
  CHICAGO
  HARVARD
  IEEE
  OTHER
}

// Add NotificationType enum
enum NotificationType {
  ASSIGNMENT
  SYSTEM
  WRITER_ASSIGNED
  STATUS_CHANGE
  PAYMENT_RECEIVED
  PAYMENT_COMPLETED
  JOB_POSTED
  JOB_COMPLETED
}

model Account {
  id                 String  @id @default(auto()) @map("_id") @db.ObjectId
  userId             String  @db.ObjectId
  type               String
  provider           String
  providerAccountId  String
  refresh_token      String? @db.String
  access_token       String? @db.String
  expires_at         Int?
  token_type         String?
  scope              String?
  id_token           String? @db.String
  session_state      String?
  user               User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  sessionToken String   @unique
  userId       String   @db.ObjectId
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model User {
  id                 String    @id @default(auto()) @map("_id") @db.ObjectId
  accountId          String?  // No @unique constraint
  name               String?
  email              String    @unique
  emailVerified      Boolean   @default(false) // Changed from DateTime to Boolean
  phone              String?
  image              String?   // Added this field for NextAuth OAuth providers
  password           String?   // Nullable for social auth users
  role               UserRole  // role          UserRole  @default(CLIENT)
  isApproved         Boolean   @default(false) // CLAUDE API: - Added isApproved field for writers
  
  // Writer profile fields
  professionalSummary String?
  experience          String?
  competencies        String[] // Array of strings for skills/competencies
  educationLevel      String?
  rating              Float?   @default(0.0)
  
  createdAt          DateTime  @default(now())
  updatedAt          DateTime  @updatedAt

   // NextAuth relations
  accounts      Account[]
  sessions      Session[]

  // Relations
  clientAssignments    Assignment[]     @relation("ClientAssignments")
  assignedAssignments  Assignment[]     @relation("WriterAssignments")
  writerBids           Bid[]
  assignedJobs         JobAssignment[]  @relation("WriterAssignments")
  managedJobs          JobAssignment[]  @relation("AdminJobs")
  todos                Todo[]           // Relation to Todo model
  notifications         Notification[]
  emailVerificationTokens EmailVerificationToken[]
  passwordResetTokens   PasswordResetToken[]
  chatParticipants     ChatParticipant[]
  sentMessages         Message[]
  couponUsages         CouponUsage[]    // Relation to CouponUsage model
}

// Updated Assignment model with new fields
model Assignment {
  id              String           @id @default(auto()) @map("_id") @db.ObjectId
  taskId          String           @unique
  title           String
  description     String
  assignmentType  AssignmentType   @default(ESSAY)
  subject         String
  service         String
  pageCount       Int
  price           Float            @default(0.0)  // Adding price field with default value
  priority        Priority         @default(MEDIUM)
  academicLevel   AcademicLevel    @default(UNDERGRADUATE)
  spacing         Spacing          @default(DOUBLE)
  languageStyle   LanguageStyle    @default(ENGLISH_US)
  formatStyle     FormatStyle      @default(APA)
  numSources      Int              @default(0)
  guidelines      String?
  estTime         DateTime
  clientId        String           @db.ObjectId
  assignedWriterId String?         @db.ObjectId
  status          AssignmentStatus @default(POSTED)

  // PayPal payment fields (client payments)
  paymentStatus   PaymentStatus    @default(PENDING)
  paypalOrderId   String?          // PayPal order ID
  paypalPayerId   String?          // PayPal payer ID
  paypalPaymentId String?          // PayPal payment/capture ID

  // Writer payment fields
  isWriterPaid        Boolean      @default(false)
  writerCompensation  Float?       // Amount due to writer
  writerPaypalEmail   String?      // Writer's PayPal email (same as their account email)
  writerPaymentDate   DateTime?    // When payment was disbursed
  writerPaypalOrderId String?      // PayPal order ID for writer payment
  writerPaypalPaymentId String?    // PayPal payment/capture ID for writer payment

  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt

  client         User            @relation("ClientAssignments", fields: [clientId], references: [id])
  assignedWriter User?           @relation("WriterAssignments", fields: [assignedWriterId], references: [id])
  bids           Bid[]
  jobAssignment  JobAssignment[]
  notifications   Notification[]
  chat           Chat?
  fileAttachments FileAttachment[]
  couponUsages   CouponUsage[]   // Relation to CouponUsage model
}

model Bid {
  id           String    @id @default(auto()) @map("_id") @db.ObjectId
  message      String?
  status       BidStatus @default(PENDING)
  writerId     String    @db.ObjectId
  assignmentId String    @db.ObjectId
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  writer        User           @relation(fields: [writerId], references: [id])
  assignment    Assignment     @relation(fields: [assignmentId], references: [id])
  jobAssignment JobAssignment? // Keep as optional for 1:1
}

model JobAssignment {
  id           String    @id @default(auto()) @map("_id") @db.ObjectId
  assignmentId String    @db.ObjectId
  writerId     String    @db.ObjectId
  adminId      String    @db.ObjectId
  bidId        String    @unique @db.ObjectId // Fixed with unique
  startDate    DateTime
  deadline     DateTime
  status       JobStatus @default(IN_PROGRESS)
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  assignment Assignment @relation(fields: [assignmentId], references: [id])
  writer     User       @relation(fields: [writerId], references: [id], name: "WriterAssignments")
  admin      User       @relation(fields: [adminId], references: [id], name: "AdminJobs")
  bid        Bid        @relation(fields: [bidId], references: [id])
}


// New Todo model
model Todo {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  title       String
  description String?
  priority    Priority @default(MEDIUM)
  category    String?
  dueDate     DateTime?
  isCompleted Boolean  @default(false)
  userId      String   @db.ObjectId
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

// New Notification model
model Notification {
  id           String          @id @default(auto()) @map("_id") @db.ObjectId
  userId       String          @db.ObjectId
  type         NotificationType
  title        String
  message      String
  taskId       String?
  assignmentId String?         @db.ObjectId
  read         Boolean         @default(false)
  createdAt    DateTime        @default(now())
  updatedAt    DateTime        @updatedAt
  
  // Relations
  user         User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  assignment   Assignment?     @relation(fields: [assignmentId], references: [id], onDelete: SetNull)
}

// Enhanced pricing models for unified pricing system
model PricingRule {
  id            String       @id @default(auto()) @map("_id") @db.ObjectId
  ruleType      String       // 'base_price', 'academic_multiplier', 'priority_multiplier', 'spacing_multiplier', 'writer_percentage', 'minimum_price'
  academicLevel AcademicLevel? // For academic level multipliers
  priority      Priority?    // For priority multipliers
  spacing       Spacing?     // For spacing multipliers
  value         Float        // The actual value (price or multiplier)
  isActive      Boolean      @default(true)
  ruleKey       String       @unique // Computed unique key to handle null values properly
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
}

// Coupon system models
model Coupon {
  id            String       @id @default(auto()) @map("_id") @db.ObjectId
  code          String       @unique // Format: 897-786-786
  description   String       // Admin description of the coupon
  discountPercentage Float    // Percentage discount (0-100)
  isActive      Boolean      @default(true)
  maxUses       Int?         // Maximum number of times this coupon can be used (null = unlimited)
  currentUses   Int          @default(0) // Current number of uses
  expiresAt     DateTime?    // Expiration date (null = no expiration)
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt

  // Relations
  couponUsages  CouponUsage[]
}

model CouponUsage {
  id           String    @id @default(auto()) @map("_id") @db.ObjectId
  couponId     String    @db.ObjectId
  userId       String    @db.ObjectId
  assignmentId String?   @db.ObjectId // Optional: link to specific assignment
  discountAmount Float   // Actual discount amount applied
  originalPrice  Float   // Original price before discount
  finalPrice     Float   // Final price after discount
  usedAt       DateTime  @default(now())

  // Relations
  coupon       Coupon     @relation(fields: [couponId], references: [id], onDelete: Cascade)
  user         User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  assignment   Assignment? @relation(fields: [assignmentId], references: [id], onDelete: SetNull)

  // Ensure one coupon usage per user per coupon
  @@unique([couponId, userId])
}

// Legacy BasePrice model - kept for backward compatibility during migration
model BasePrice {
  id            String       @id @default(auto()) @map("_id") @db.ObjectId
  academicLevel AcademicLevel @unique
  price         Float
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
}

// File attachment model for assignments
model FileAttachment {
  id           String     @id @default(auto()) @map("_id") @db.ObjectId
  assignmentId String     @db.ObjectId
  fileName     String
  originalName String
  fileUrl      String
  fileSize     Int
  fileType     String
  uploadedAt   DateTime   @default(now())

  // Relations
  assignment   Assignment @relation(fields: [assignmentId], references: [id], onDelete: Cascade)

  @@map("file_attachments")
}

// =======================
// BLOG MODULE MODELS
// =======================

model EmailVerificationToken {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  userId    String   @db.ObjectId
  token     String   @unique
  expires   DateTime
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model PasswordResetToken {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  userId    String   @db.ObjectId
  code      String   @unique // 6-digit verification code
  expires   DateTime
  isUsed    Boolean  @default(false)
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Assessment {
  id                 String   @id @default(auto()) @map("_id") @db.ObjectId
  title              String
  multipleChoiceQuiz Json    // Array of { question, options, correctAnswer }
  essayExam          Json    // { topic, rubrics }
  writersAnswers     Json[]  // Array of { writerId, multipleChoiceAnswers, essayText }
  isActive           Boolean  @default(false) // Only one assessment can be active at a time
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
}

// Company Information Model
model CompanyInfo {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  companyName       String
  address           String
  city              String
  state             String
  zipCode           String
  country           String
  phone             String
  tollFreePhone     String?
  internationalPhone String?
  supportEmail      String
  inquiriesEmail    String
  businessHours     String
  description       String?
  website           String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@map("companyinfo")
}

model Blog {
  id              String         @id @default(auto()) @map("_id") @db.ObjectId
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt
  title           String
  body            String
  slug            String         @unique
  metaTitle       String
  metaDescription String
  imageUrl        String
  imageAlt        String         // Alt text for SEO and accessibility
  category        BlogCategory   @relation(fields: [categoryId], references: [id])
  categoryId      String         @db.ObjectId
  author          Author         @relation(fields: [authorId], references: [id])
  authorId        String         @db.ObjectId
  keywords        String[]
  faqs            String[]
  pageViews       Int            @default(0)
}

model FAQ {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  question    String
  answer      String
  category    String?  // Optional category for grouping FAQs
  isActive    Boolean  @default(true)
  order       Int      @default(0) // For custom ordering
}

model Author {
  id             String         @id @default(auto()) @map("_id") @db.ObjectId
  name           String         @unique
  qualifications String
  blogs          Blog[]
}

model BlogCategory {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  name        String   @unique
  description String?
  slug        String   @unique
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  blogs Blog[]

  @@map("blog_categories")
}

// Chat-related enums
enum MessageType {
  TEXT
  EMOJI
  SYSTEM
}

enum ChatParticipantRole {
  ADMIN
  CLIENT
  WRITER
}

// Chat models
model Chat {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  assignmentId String   @unique @db.ObjectId
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  assignment   Assignment        @relation(fields: [assignmentId], references: [id], onDelete: Cascade)
  messages     Message[]
  participants ChatParticipant[]

  @@map("chats")
}

model ChatParticipant {
  id     String              @id @default(auto()) @map("_id") @db.ObjectId
  chatId String              @db.ObjectId
  userId String              @db.ObjectId
  role   ChatParticipantRole

  // Relations
  chat Chat @relation(fields: [chatId], references: [id], onDelete: Cascade)
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([chatId, userId])
  @@map("chat_participants")
}

model Message {
  id        String      @id @default(auto()) @map("_id") @db.ObjectId
  chatId    String      @db.ObjectId
  senderId  String      @db.ObjectId
  content   String
  type      MessageType @default(TEXT)
  isRead    Boolean     @default(false)
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt

  // NEW FIELDS for conversation context
  targetParticipantId String?     // ID of the intended recipient (for admin messages)
  conversationType    String?     // "client" or "writer" (for admin messages)
  
  // Relations
  chat   Chat @relation(fields: [chatId], references: [id], onDelete: Cascade)
  sender User @relation(fields: [senderId], references: [id], onDelete: Cascade)

  @@map("messages")
}

// Newsletter subscription model
model NewsletterSubscription {
  id             String    @id @default(auto()) @map("_id") @db.ObjectId
  email          String    @unique
  isActive       Boolean   @default(true)
  subscribedAt   DateTime  @default(now())
  unsubscribedAt DateTime?
  source         String?   // Where they subscribed from (footer, blog, etc.)

  @@map("newsletter_subscriptions")
}

