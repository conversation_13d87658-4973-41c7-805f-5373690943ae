"use client";

import { useState } from "react";
import { toast } from "sonner";
import type { ContactFormData, ContactFormResponse } from "@/types/api";

interface UseContactFormOptions {
  onSuccess?: (data: ContactFormResponse) => void;
  onError?: (error: string) => void;
}

export function useContactForm(options: UseContactFormOptions = {}) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const submitContactForm = async (formData: ContactFormData): Promise<ContactFormResponse> => {
    setIsSubmitting(true);
    
    try {
      const response = await fetch("/api/contact", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      const result: ContactFormResponse = await response.json();

      if (!response.ok || !result.success) {
        throw new Error(result.message || "Failed to send message");
      }

      setIsSubmitted(true);
      toast.success(result.message || "Message sent successfully!");
      
      if (options.onSuccess) {
        options.onSuccess(result);
      }

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to send message";
      toast.error(errorMessage);
      
      if (options.onError) {
        options.onError(errorMessage);
      }
      
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setIsSubmitted(false);
  };

  return {
    submitContactForm,
    isSubmitting,
    isSubmitted,
    resetForm,
  };
}
