//writer-login-form.tsx
"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { signIn } from "next-auth/react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";

export function WriterLoginForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [formValues, setFormValues] = useState({
    email: "",
    password: "",
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormValues((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    if (!formValues.email || !formValues.password) {
      toast.error("Please fill in all required fields");
      setIsLoading(false);
      return;
    }

    try {
      // Check if user exists and their role before attempting login
      const roleCheckResponse = await fetch("/api/auth/check-user-role", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email: formValues.email }),
      });

      if (roleCheckResponse.ok) {
        const { exists, role } = await roleCheckResponse.json();

        if (exists && role !== "WRITER") {
          // User exists but has wrong role for writer login
          const correctLoginPage = role === "CLIENT" ? "/login/client" : "/login/admin";
          const roleName = role === "CLIENT" ? "Client" : "Admin";
          toast.error(`Please login through the ${roleName} dashboard at ${correctLoginPage}`);
          setIsLoading(false);
          return;
        }
      }

      const res = await signIn("credentials", {
        email: formValues.email,
        password: formValues.password,
        redirect: false,
      });
      if (res?.error) {
        console.error("❌ Login error detected:", res.error);

        // Check multiple variations of the email verification error
        const errorString = String(res.error).toLowerCase();
        const isEmailNotVerified =
          res.error === "EMAIL_NOT_VERIFIED" ||
          res.error === "Error: EMAIL_NOT_VERIFIED" ||
          res.error.includes("EMAIL_NOT_VERIFIED") ||
          (errorString.includes("email") &&
            errorString.includes("not") &&
            errorString.includes("verified")) ||
          errorString.includes("email not verified") ||
          errorString.includes("please verify your email");

        console.log("🔍 Error analysis:", {
          errorString,
          isEmailNotVerified,
          originalError: res.error,
        });

        if (isEmailNotVerified) {
          toast.error(
            <span>
              Please verify your email before logging in.
              <Button
                size="sm"
                variant="link"
                className="ml-2 px-0"
                onClick={async () => {
                  try {
                    const response = await fetch(
                      "/api/auth/send-verification-email",
                      {
                        method: "POST",
                        headers: { "Content-Type": "application/json" },
                        body: JSON.stringify({ email: formValues.email }),
                      }
                    );

                    if (response.ok) {
                      toast.success("Verification email resent!");
                    } else {
                      const errorData = await response.text();
                      console.error("📧 Verification email error:", errorData);
                      toast.error("Failed to resend verification email");
                    }
                  } catch (emailError) {
                    console.error("📧 Verification email error:", emailError);
                    toast.error("Failed to resend verification email");
                  }
                }}
              >
                Resend Verification Email
              </Button>
            </span>
          );
        } else if (res.error === "CredentialsSignin") {
          toast.error("Invalid email or password");
        } else {
          toast.error(res.error || "Invalid email or password");
        }
      } else {
        // Get the user's ID from the session
        const session = await fetch("/api/auth/session");
        const sessionData = await session.json();
        const userId = sessionData?.user?.id;

        if (!userId) {
          toast.error("Failed to get user session");
          setIsLoading(false);
          return;
        }

        // Check writer approval status
        try {
          const writerResponse = await fetch(`/api/users/writers/${userId}`);
          const writerData = await writerResponse.json();

          if (writerData.success && writerData.data) {
            const isApproved = writerData.data.isApproved;

            toast.success("Login successful!");

            // Redirect based on approval status
            if (isApproved) {
              router.push("/writer/dashboard");
            } else {
              toast.info(
                "Please complete the writer assessment to access your dashboard."
              );
              router.push("/writer-assessment");
            }
          } else {
            throw new Error("Failed to fetch writer status");
          }
        } catch (writerError) {
          console.error("Error checking writer status:", writerError);
          toast.error("Failed to verify writer status");
          setIsLoading(false);
          return;
        }
      }
    } catch (err) {
      console.error("Login error:", err);
      toast.error(`Login failed. Please try again. ${err}`);
    } finally {
      setIsLoading(false);
    }
  };
  // Handle social login with role validation
  const handleSocialLogin = async (provider: string) => {
    try {
      // For social login, we need to check if user exists and validate their role
      // Since we can't get email before social login, we'll handle this in the auth callback
      // But we can set the intended role in the state parameter
      // Pass intended role in callback URL for validation
      const callbackUrl = "/writer/dashboard?intended_role=WRITER&provider=" + provider;

      await signIn(provider, {
        callbackUrl,
        redirect: true,
      });
    } catch (error) {
      console.error("Social login error:", error);
      toast.error(`${provider} login failed. Please try again.`);
    }
  };

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card className="overflow-hidden">
        <CardContent className="grid p-0 xs:grid-cols-1 md:grid-cols-2">
          <form className="p-4 xs:p-6 md:p-8" onSubmit={handleSubmit}>
            <div className="flex flex-col gap-6">
              <div className="flex flex-col items-center text-center">
                <h1 className="text-2xl font-bold">Writer Portal</h1>
                <p className="text-balance text-muted-foreground">
                  Access your writer dashboard
                </p>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  required
                  className="h-10"
                  value={formValues.email}
                  onChange={handleInputChange}
                />
              </div>
              <div className="grid gap-2">
                <div className="flex items-center">
                  <Label htmlFor="password">Password</Label>
                  <Link
                    href="/forgot-password"
                    className="ml-auto text-sm text-primary underline-offset-2 hover:underline"
                  >
                    Forgot password?
                  </Link>
                </div>

                <div className="relative">
                  <Input
                    id="password"
                    name="password"
                    type={passwordVisible ? "text" : "password"}
                    required
                    className="h-10 pr-10"
                    value={formValues.password}
                    onChange={handleInputChange}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-10 px-3"
                    onClick={() => setPasswordVisible(!passwordVisible)}
                  >
                    {passwordVisible ? (
                      <EyeOffIcon className="h-4 w-4" />
                    ) : (
                      <EyeIcon className="h-4 w-4" />
                    )}
                    <span className="sr-only">
                      {passwordVisible ? "Hide password" : "Show password"}
                    </span>
                  </Button>
                </div>
              </div>
              <Button
                type="submit"
                className="w-full h-10"
                disabled={isLoading}
              >
                {isLoading ? "Logging in..." : "Log in"}
              </Button>
              <div className="relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t after:border-border">
                <span className="relative z-10 bg-card px-2 text-muted-foreground">
                  Or continue with
                </span>
              </div>
              <div className="grid grid-cols-3 gap-3 xs:gap-4">
                <Button
                  type="button"
                  variant="outline"
                  className="h-10 w-full hover:text-primary"
                  onClick={() => handleSocialLogin("twitter")}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    className="h-5 w-5"
                  >
                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                  </svg>
                  <span className="sr-only">Login with Twitter</span>
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => handleSocialLogin("google")}
                  className="h-10 w-full hover:text-primary"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    className="h-5 w-5"
                  >
                    <path
                      d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"
                      fill="currentColor"
                    />
                  </svg>
                  <span className="sr-only">Login with Google</span>
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => handleSocialLogin("facebook")}
                  className="h-10 w-full hover:text-primary"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    className="h-5 w-5"
                  >
                    <path
                      d="M22.675 0H1.325C.593 0 0 .593 0 1.325v21.351C0 23.407.593 24 1.325 24H12.82v-9.294H9.692v-3.622h3.128V8.413c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.795.143v3.24l-1.918.001c-1.504 0-1.795.715-1.795 1.763v2.313h3.587l-.467 3.622h-3.12V24h6.116c.73 0 1.323-.593 1.323-1.325V1.325C24 .593 23.407 0 22.675 0z"
                      fill="currentColor"
                    />
                  </svg>
                  <span className="sr-only">Login with Facebook</span>
                </Button>
              </div>
              <div className="text-center text-sm">
                Don&apos;t have an account?{" "}
                <Link
                  href="/register/writer"
                  className="text-primary underline underline-offset-4"
                >
                  Apply as writer
                </Link>
              </div>
            </div>
          </form>
          <div className="relative hidden bg-muted md:block">
            <svg
              className="absolute inset-0 h-full w-full text-primary/20"
              xmlns="http://www.w3.org/2000/svg"
              width="100%"
              height="100%"
              viewBox="0 0 100 100"
              preserveAspectRatio="xMidYMid slice"
            >
              <defs>
                <pattern
                  id="pattern"
                  width="10"
                  height="10"
                  patternUnits="userSpaceOnUse"
                >
                  <path
                    d="M 0 10 L 10 0 M -1 1 L 1 -1 M 9 11 L 11 9"
                    stroke="currentColor"
                    strokeWidth="1.5"
                  />
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#pattern)" />
            </svg>
            <div className="absolute inset-0 flex flex-col items-center justify-center p-6">
              <div className="w-full max-w-sm text-center">
                <div className="mb-6 flex justify-center">
                  <WriterLogoSVG className="h-16 w-16 text-primary" />
                </div>
                <h2 className="mb-2 text-3xl font-bold text-foreground">
                  Writer Portal
                </h2>
                <p className="mb-6 text-balance text-muted-foreground">
                  Create content, manage projects, and track your earnings in
                  one place.
                </p>
                <div className="flex flex-wrap justify-center gap-2">
                  {["Content Creation", "Project Management", "Analytics"].map(
                    (tag) => (
                      <span
                        key={tag}
                        className="inline-flex items-center rounded-full bg-primary/10 px-3 py-1 text-sm text-primary"
                      >
                        {tag}
                      </span>
                    )
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      <div className="text-balance text-center text-xs text-muted-foreground [&_a]:underline [&_a]:underline-offset-4 hover:[&_a]:text-primary mb-8">
        By clicking continue, you agree to our <Link href="#">Terms of Service</Link>{" "}
        and <Link href="#">Privacy Policy</Link>.
      </div>
    </div>
  );
}

function WriterLogoSVG({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      {/* Writer-themed logo - pen/document icon */}
      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
      <polyline points="14 2 14 8 20 8" />
      <line x1="16" y1="13" x2="8" y2="13" />
      <line x1="16" y1="17" x2="8" y2="17" />
      <line x1="10" y1="9" x2="8" y2="9" />
    </svg>
  );
}

function EyeIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z" />
      <circle cx="12" cy="12" r="3" />
    </svg>
  );
}

function EyeOffIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="M9.88 9.88a3 3 0 1 0 4.24 4.24" />
      <path d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68" />
      <path d="M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61" />
      <line x1="2" x2="22" y1="2" y2="22" />
    </svg>
  );
}
