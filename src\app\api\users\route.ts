// src/app/api/users/route.ts
import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import bcrypt from "bcryptjs";
// import { z } from "zod";
import {
  apiSuccess,
  apiError,
  parseRequestBody,
  checkPermission,
  safeJsonDate,
} from "@/lib/api-utils";
import { userCreateSchema, paginationSchema } from "@/lib/validations";
import { UserResponse } from "@/types/api";
import type { UserCreateData } from "@/types/api";

export async function GET(req: NextRequest): Promise<NextResponse> {
  // Check if user has admin permissions
  const permissionError = await checkPermission(["ADMIN"]);
  if (permissionError) return permissionError;

  try {
    // Parse query parameters
    const url = new URL(req.url);
    const pageParam = url.searchParams.get("page") || "1";
    const limitParam = url.searchParams.get("limit") || "10";

    const { page, limit } = paginationSchema.parse({
      page: parseInt(pageParam),
      limit: parseInt(limitParam),
    });

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get total count
    const totalCount = await prisma.user.count();

    // Get users with pagination
    const users = await prisma.user.findMany({
      skip,
      take: limit,
      select: {
        id: true,
        email: true,
        name: true,
        phone: true,
        role: true,
        isApproved: true,
        createdAt: true,
        updatedAt: true,
        image: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return apiSuccess({
      users: safeJsonDate(users) as UserResponse[],
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching users:", error);
    return apiError("Failed to fetch users", 500);
  }
}

export async function POST(req: NextRequest): Promise<NextResponse> {
  // Check if user has admin permissions
  const permissionError = await checkPermission(["ADMIN"]);
  if (permissionError) return permissionError;

  // Parse and validate request body
  const result = await parseRequestBody(req, userCreateSchema);
  if ("success" in result && result.success === false) {
    return apiError(result.message, 400, result.errors);
  }

  const userData = result as UserCreateData; // Cast to UserCreateData
  try {
    // Check if user with email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: userData.email },
    });

    if (existingUser) {
      return apiError("User with this email already exists", 409);
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(userData.password, 10);

    // Create new user
    const newUser = await prisma.user.create({
      data: {
        ...userData,
        password: hashedPassword,
      },
      select: {
        id: true,
        email: true,
        name: true,
        phone: true,
        role: true,
        isApproved: true,
        createdAt: true,
        updatedAt: true,
        image: true,
      },
    });

    return apiSuccess(
      safeJsonDate(newUser) as UserResponse,
      "User created successfully"
    );
  } catch (error) {
    console.error("Error creating user:", error);
    return apiError("Failed to create user", 500);
  }
}
