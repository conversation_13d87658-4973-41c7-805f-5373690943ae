"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ClientsAnalytics } from "./clients-analytics";
import { WritersAnalytics } from "./writers-analytics";
import { RevenueAnalytics } from "./revenue-analytics";
import { GrowthAnalytics } from "./growth-analytics";
import { IconUsers, IconCash, IconTrendingUp, IconPencil } from "@tabler/icons-react";

export function AnalyticsTabs() {
  return (
    <Tabs defaultValue="clients" className="w-full space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h1>
        <TabsList>
          <TabsTrigger value="clients" className="flex items-center gap-2">
            <IconUsers className="h-4 w-4" />
            <span className="hidden sm:inline">Clients</span>
          </TabsTrigger>
          <TabsTrigger value="writers" className="flex items-center gap-2">
            <IconPencil className="h-4 w-4" />
            <span className="hidden sm:inline">Writers</span>
          </TabsTrigger>
          <TabsTrigger value="revenue" className="flex items-center gap-2">
            <IconCash className="h-4 w-4" />
            <span className="hidden sm:inline">Revenue</span>
          </TabsTrigger>
          <TabsTrigger value="growth" className="flex items-center gap-2">
            <IconTrendingUp className="h-4 w-4" />
            <span className="hidden sm:inline">Growth</span>
          </TabsTrigger>
        </TabsList>
      </div>

      {/* Summary Cards - visible on all tabs */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <ClientsAnalytics variant="card" />
        <WritersAnalytics variant="card" />
        <RevenueAnalytics variant="card" />
        <GrowthAnalytics variant="card" />
      </div>

      {/* Clients Tab Content */}
      <TabsContent value="clients" className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2">
          <ClientsAnalytics variant="chart" />
          <RevenueAnalytics variant="chart" />
        </div>
        <ClientsAnalytics variant="full" />
      </TabsContent>

      {/* Writers Tab Content */}
      <TabsContent value="writers" className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2">
          <WritersAnalytics variant="chart" />
          <GrowthAnalytics variant="chart" />
        </div>
        <WritersAnalytics variant="full" />
      </TabsContent>

      {/* Revenue Tab Content */}
      <TabsContent value="revenue" className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2">
          <RevenueAnalytics variant="chart" />
          <ClientsAnalytics variant="chart" />
        </div>
        <RevenueAnalytics variant="full" />
      </TabsContent>

      {/* Growth Tab Content */}
      <TabsContent value="growth" className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2">
          <GrowthAnalytics variant="chart" />
          <WritersAnalytics variant="chart" />
        </div>
        <GrowthAnalytics variant="full" />
      </TabsContent>
    </Tabs>
  );
}
