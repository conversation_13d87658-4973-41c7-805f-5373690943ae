import { NextResponse } from "next/server";
import { apiSuccess, apiError, checkPermission } from "@/lib/api-utils";
import { pricingService } from "@/lib/pricing-service";

// Invalidate pricing cache (admin only)
export async function POST(): Promise<NextResponse> {
  try {
    // Only admins can invalidate pricing cache
    const permissionError = await checkPermission(["ADMIN"]);
    if (permissionError) return permissionError;

    // Clear the pricing cache to trigger real-time updates
    pricingService.clearCache();

    return apiSuccess({ message: "Pricing cache invalidated successfully" });
  } catch (error) {
    console.error("Error invalidating pricing cache:", error);
    return apiError("Failed to invalidate pricing cache", 500);
  }
}
