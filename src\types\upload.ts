export interface UploadResult {
    url: string;
    publicId: string;
    width: number;
    height: number;
    format: string;
    size: number;
  }

  export interface UploadError {
    error: string;
  }

  export interface UploadHookReturn {
    uploadImage: (file: File) => Promise<UploadResult>;
    isUploading: boolean;
    uploadProgress: number;
    error: string | null;
    resetError: () => void;
  }

  // Supabase file upload types
  export interface FileUploadResult {
    url: string;
    path: string;
    name: string;
    size: number;
    type: string;
    lastModified: number;
  }

  export interface FileUploadError {
    error: string;
  }

  export interface FileUploadHookReturn {
    uploadFile: (file: File, folder?: string, assignmentId?: string) => Promise<FileUploadResult>;
    isUploading: boolean;
    uploadProgress: number;
    error: string | null;
    resetError: () => void;
  }

  export interface UploadedFile {
    id: string;
    name: string;
    url: string;
    size: number;
    type: string;
    uploadedAt: string;
  }