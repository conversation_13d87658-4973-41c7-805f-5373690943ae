import React from "react";
import type { Metada<PERSON> } from "next";
import BlogMain from "./BlogMain";

// Site configuration
const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
const siteName = "Academic App";

// Author info
const author = {
  name: "Academic Team",
  url: `${baseUrl}/about`,
  qualifications: "PhD experts in various academic fields with extensive research experience"
};

// Blog metadata
const blogUrl = `${baseUrl}/blog`;
const blogImage = "/opengraph-image.png";
const blogTitle = "Welcome to our blog section: Get informed | Academic App";
const blogDescription = "Going through school is a lot of work and can be tiresome. Our blog posts are meant to make things easier.";
const datePublished = new Date().toISOString();
const dateModified = new Date().toISOString();

// Structured Data (JSON-LD)
const jsonLd = {
  "@context": "https://schema.org",
  "@type": "Blog",
  "name": blogTitle,
  "description": blogDescription,
  "url": blogUrl,
  "image": blogImage,
  "publisher": {
    "@type": "Organization",
    "name": siteName,
    "url": baseUrl,
    "logo": {
      "@type": "ImageObject",
      "url": `${baseUrl}/favicon.ico`
    }
  },
  "author": {
    "@type": "Person",
    "name": author.name,
    "url": author.url,
    "description": author.qualifications
  },
  "mainEntityOfPage": blogUrl,
  "datePublished": datePublished,
  "dateModified": dateModified,
  "inLanguage": "en-US"
};

const breadcrumbLd = {
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [
    {
      "@type": "ListItem",
      "position": 1,
      "name": "Home",
      "item": baseUrl
    },
    {
      "@type": "ListItem",
      "position": 2,
      "name": "Blog",
      "item": blogUrl
    }
  ]
};

export const metadata: Metadata = {
  title: blogTitle,
  description: blogDescription,
  keywords: [
    "academic writing",
    "essay help",
    "college essays",
    "study guides",
    "student resources",
    "research papers",
    "academic blog",
    "education tips",
    "university life",
    "homework help"
  ],
  alternates: {
    canonical: blogUrl
  },
  openGraph: {
    title: blogTitle,
    description: blogDescription,
    url: blogUrl,
    siteName: siteName,
    images: [
      {
        url: blogImage,
        width: 1200,
        height: 630,
        alt: "Academic App Blog Header Image"
      }
    ],
    type: "website",
    locale: "en_US"
  },
  twitter: {
    card: "summary_large_image",
    title: blogTitle,
    description: blogDescription,
    images: [
      {
        url: blogImage,
        alt: "Academic App Blog Header Image"
      }
    ],
    creator: "@academicapp"
  },
  authors: [{
    name: author.name,
    url: author.url
  }],
  creator: author.name,
  publisher: siteName,
  category: "Education",
  robots: {
    index: true,
    follow: true,
    nocache: false,
    googleBot: {
      index: true,
      follow: true,
      'max-image-preview': 'large',
      'max-snippet': -1,
      'max-video-preview': -1
    }
  },
  verification: {
    google: 'verification_token',
    yandex: 'verification_token',
    other: {
      me: [author.url]
    }
  },
  icons: {
    icon: "/favicon.ico",
    apple: "/apple-icon.png"
  },
  metadataBase: new URL(baseUrl)
};

export default function Page() {
  return (
    <>
      {/* In Next.js App Router, we don't need to use Head component for JSON-LD */}
      {/* The metadata export above handles most SEO needs */}
      {/* For JSON-LD, we use a client component or a special script tag */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(jsonLd)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbLd)
        }}
      />
      <BlogMain />
    </>
  );
}
