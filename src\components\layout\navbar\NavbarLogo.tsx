"use client"

import { FC } from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { useCompanyInfo } from "@/hooks/use-company-info";

interface NavbarLogoProps {
  className?: string;
}

const NavbarLogo: FC<NavbarLogoProps> = ({ className }) => {
  const { companyInfo } = useCompanyInfo();
  const companyName = companyInfo?.companyName || "Essay App";

  // Split company name into first and second words
  const words = companyName.split(' ');
  const firstWord = words[0] || "Essay";
  const secondWord = words.slice(1).join(' ') || "App";

  return (
    <Link href="/" className={cn("flex items-center gap-2", className)}>

        <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      width="24"
      height="24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="text-primary"
    >
      <path d="M22 10v6M2 10l10-5 10 5-10 5z" />
      <path d="M6 12v5c0 2 2 3 6 3s6-1 6-3v-5" />
    </svg>

      <span className="text-lg font-semibold text-foreground">
        <span>{firstWord}</span>
        <span className="text-primary ml-1">{secondWord}</span>
      </span>
    </Link>
  );
};

export { NavbarLogo };