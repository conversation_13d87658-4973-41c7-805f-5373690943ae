// src/app/(with-footer)/services/page.tsx
import React from "react";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  BookOpen,
  GraduationCap,
  FileText,
  BarChart3, // Changed from PresentationChart
  Calculator,
  Code,
  Heart,
  Briefcase,
  Scale,
  Globe,
  Microscope,
  PenTool,
  Star,
  Clock,
  Users,
  Award,
} from "lucide-react";

interface Service {
  icon: React.ReactNode;
  title: string;
  description: string;
  category: "writing" | "analysis" | "technical" | "specialized";
  popular?: boolean;
}

const ViewAllServices: React.FC = () => {
  const services: Service[] = [
    // Writing Services
    {
      icon: <FileText className="h-6 w-6" />,
      title: "Essay Writing",
      description:
        "Custom essays for all academic levels - argumentative, narrative, descriptive, and analytical essays",
      category: "writing",
      popular: true,
    },
    {
      icon: <BookOpen className="h-6 w-6" />,
      title: "Term Papers",
      description:
        "Comprehensive term papers with extensive research and analysis across all subjects",
      category: "writing",
      popular: true,
    },
    {
      icon: <GraduationCap className="h-6 w-6" />,
      title: "Dissertation Writing",
      description:
        "Complete dissertation support from proposal to defense, including all chapters and methodology",
      category: "writing",
    },
    {
      icon: <FileText className="h-6 w-6" />,
      title: "Thesis Writing",
      description:
        "Master&apos;s and undergraduate thesis writing with original research and analysis",
      category: "writing",
    },
    {
      icon: <PenTool className="h-6 w-6" />,
      title: "Research Papers",
      description:
        "Original research papers with primary and secondary data analysis",
      category: "writing",
      popular: true,
    },
    {
      icon: <BookOpen className="h-6 w-6" />,
      title: "Literature Reviews",
      description:
        "Systematic and narrative literature reviews with comprehensive source analysis",
      category: "writing",
    },
    {
      icon: <FileText className="h-6 w-6" />,
      title: "Case Studies",
      description:
        "In-depth case study analysis for business, psychology, medicine, and social sciences",
      category: "writing",
    },
    {
      icon: <PenTool className="h-6 w-6" />,
      title: "Lab Reports",
      description:
        "Scientific lab reports with proper methodology, results, and discussion sections",
      category: "technical",
    },

    // Analysis Services
    {
      icon: <Calculator className="h-6 w-6" />,
      title: "Statistical Analysis",
      description:
        "SPSS, R, SAS data analysis with interpretation and visualization",
      category: "analysis",
    },
    {
      icon: <BarChart3 className="h-6 w-6" />, // Changed from PresentationChart
      title: "Data Analysis",
      description:
        "Quantitative and qualitative data analysis with comprehensive reporting",
      category: "analysis",
    },
    {
      icon: <Microscope className="h-6 w-6" />,
      title: "Research Proposals",
      description:
        "PhD and Master&apos;s research proposals with methodology and literature review",
      category: "analysis",
    },

    // Technical Services
    {
      icon: <Code className="h-6 w-6" />,
      title: "Programming Assignments",
      description:
        "Code solutions in Python, Java, C++, JavaScript, and other programming languages",
      category: "technical",
    },
    {
      icon: <Calculator className="h-6 w-6" />,
      title: "Mathematics Solutions",
      description:
        "Advanced calculus, statistics, algebra, and applied mathematics problems",
      category: "technical",
    },
    {
      icon: <BarChart3 className="h-6 w-6" />, // Changed from PresentationChart
      title: "PowerPoint Presentations",
      description:
        "Professional academic and business presentations with engaging visuals",
      category: "technical",
    },

    // Specialized Services
    {
      icon: <Heart className="h-6 w-6" />,
      title: "Nursing Papers",
      description:
        "Evidence-based nursing papers, care plans, and clinical reflections",
      category: "specialized",
    },
    {
      icon: <Briefcase className="h-6 w-6" />,
      title: "Business Plans",
      description:
        "Comprehensive business plans with market analysis and financial projections",
      category: "specialized",
    },
    {
      icon: <Scale className="h-6 w-6" />,
      title: "Legal Writing",
      description: "Legal briefs, case analyses, and law school assignments",
      category: "specialized",
    },
    {
      icon: <Globe className="h-6 w-6" />,
      title: "Online Courses",
      description:
        "Complete online course assistance and assignment completion",
      category: "specialized",
    },
    {
      icon: <FileText className="h-6 w-6" />,
      title: "Admission Essays",
      description:
        "College and graduate school admission essays that stand out",
      category: "specialized",
    },
    {
      icon: <PenTool className="h-6 w-6" />,
      title: "Creative Writing",
      description:
        "Short stories, poems, scripts, and other creative writing projects",
      category: "specialized",
    },
  ];

  const stats = [
    {
      icon: <Star className="h-8 w-8 text-primary" />,
      value: "15,000+",
      label: "Completed Projects",
    },
    {
      icon: <Clock className="h-8 w-8 text-primary" />,
      value: "98%",
      label: "On-Time Delivery",
    },
    {
      icon: <Users className="h-8 w-8 text-primary" />,
      value: "500+",
      label: "Expert Writers",
    },
    {
      icon: <Award className="h-8 w-8 text-primary" />,
      value: "4.9/5",
      label: "Customer Rating",
    },
  ];

  const getServicesByCategory = (category: string) => {
    return services.filter((service) => service.category === category);
  };

  const popularServices = services.filter((service) => service.popular);

  const processSteps = [
    {
      step: "1",
      title: "Submit Requirements",
      description:
        "Upload your assignment details, deadline, and any specific instructions",
    },
    {
      step: "2",
      title: "Get Writer Bids",
      description:
        "Qualified writers bid on your project with their rates and timelines",
    },
    {
      step: "3",
      title: "Choose Your Writer",
      description:
        "Select the best writer based on credentials, reviews, and price",
    },
    {
      step: "4",
      title: "Receive Your Work",
      description:
        "Get your completed assignment on time with free revisions if needed",
    },
  ];

  const renderServiceGrid = (
    serviceList: Service[],
    showPopularBadge = false
  ) => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {serviceList.map((service, index) => (
        <Card
          key={index}
          className="hover:shadow-lg transition-shadow relative"
        >
          {showPopularBadge && service.popular && (
            <Badge className="absolute top-4 right-4 bg-primary text-primary-foreground">
              Popular
            </Badge>
          )}
          <CardHeader>
            <div className="flex items-center space-x-3">
              <div className="text-primary">{service.icon}</div>
              <CardTitle className="text-lg">{service.title}</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <CardDescription className="mb-4">
              {service.description}
            </CardDescription>
            <Link href="/create-order">
              <Button
                className={
                  showPopularBadge && service.popular ? "w-full" : "w-full"
                }
                variant={
                  showPopularBadge && service.popular ? "default" : "outline"
                }
              >
                {showPopularBadge && service.popular ? "Order Now" : "Get Quote"}
              </Button>
            </Link>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-12 max-w-7xl">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <Badge variant="secondary" className="mb-4">
            <BookOpen className="h-4 w-4 mr-2" />
            Complete Academic Solutions
          </Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
            All Academic Writing Services
          </h1>
          <p className="text-xl text-muted-foreground max-w-4xl mx-auto mb-8">
            From essays to dissertations, research papers to technical
            assignments - we provide comprehensive academic writing support
            across all subjects and academic levels. Our expert writers are
            ready to help you succeed in your educational journey.
          </p>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
          {stats.map((stat, index) => (
            <Card key={index} className="text-center">
              <CardContent className="p-6">
                <div className="flex justify-center mb-4">{stat.icon}</div>
                <div className="text-2xl font-bold text-foreground mb-2">
                  {stat.value}
                </div>
                <div className="text-muted-foreground text-sm">
                  {stat.label}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Services Section */}
        <Tabs defaultValue="popular" className="w-full mb-12">
          <TabsList className="grid w-full grid-cols-5 mb-8">
            <TabsTrigger value="popular" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300">Popular</TabsTrigger>
            <TabsTrigger value="writing" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300">Writing</TabsTrigger>
            <TabsTrigger value="analysis" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300">Analysis</TabsTrigger>
            <TabsTrigger value="technical" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300">Technical</TabsTrigger>
            <TabsTrigger value="specialized" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300">Specialized</TabsTrigger>
          </TabsList>

          <TabsContent value="popular">
            {renderServiceGrid(popularServices, true)}
          </TabsContent>

          <TabsContent value="writing">
            {renderServiceGrid(getServicesByCategory("writing"))}
          </TabsContent>

          <TabsContent value="analysis">
            {renderServiceGrid(getServicesByCategory("analysis"))}
          </TabsContent>

          <TabsContent value="technical">
            {renderServiceGrid(getServicesByCategory("technical"))}
          </TabsContent>

          <TabsContent value="specialized">
            {renderServiceGrid(getServicesByCategory("specialized"))}
          </TabsContent>
        </Tabs>

        {/* Encouragement Section */}
        <Card className="bg-gradient-to-br from-primary/10 via-primary/5 to-secondary/10 border-primary/20 mb-12">
          <CardContent className="p-8 md:p-12">
            <div className="text-center max-w-4xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
                Don&apos;t Let Challenging Assignments Hold You Back
              </h2>
              <div className="space-y-4 text-muted-foreground text-lg leading-relaxed mb-8">
                <p>
                  We understand that academic life can be overwhelming. Between
                  multiple courses, part-time jobs, family responsibilities, and
                  social commitments, finding time to produce high-quality
                  academic work can feel impossible. You&apos;re not alone in
                  this struggle.
                </p>
                <p>
                  Every successful student knows when to seek help. Whether
                  you&apos;re dealing with writer&apos;s block, struggling with
                  complex research methodologies, or simply running out of time,
                  our expert academic writers are here to support your
                  educational journey.
                </p>
                <p className="font-semibold text-foreground">
                  Your success is our mission. Let us help you achieve the
                  grades you deserve while maintaining a healthy work-life
                  balance.
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" className="text-lg px-8">
                  Get Help Now
                </Button>
                <Button size="lg" variant="outline" className="text-lg px-8">
                  Chat with Expert
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Process Section */}
        <Card className="mb-12">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl md:text-3xl">
              How Our Service Works
            </CardTitle>
            <CardDescription className="text-lg">
              Getting expert academic help has never been easier
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {processSteps.map((item, index) => (
                <div key={index} className="text-center">
                  <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-primary-foreground font-bold text-lg mx-auto mb-4">
                    {item.step}
                  </div>
                  <h3 className="font-semibold text-foreground mb-2">
                    {item.title}
                  </h3>
                  <p className="text-muted-foreground text-sm">
                    {item.description}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Final CTA */}
        <Card className="bg-primary text-primary-foreground">
          <CardContent className="p-8 text-center">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Ready to Excel in Your Studies?
            </h3>
            <p className="text-primary-foreground/90 mb-6 max-w-2xl mx-auto text-lg">
              Join thousands of students who trust us with their academic
              success. Get started today and experience the difference expert
              help can make.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/create-order">
                <Button size="lg" variant="secondary" className="text-lg px-8">
                  Place Your Order
                </Button>
              </Link>
              <Link href="/contact-us">
                <Button
                  size="lg"
                  variant="outline"
                  className="text-lg px-8 border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary"
                >
                  Get Free Quote
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ViewAllServices;
