import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Shield, Users, BookOpen, AlertTriangle, CheckCircle, XCircle, Scale, MessageSquare } from 'lucide-react';

interface ConductSectionProps {
  title: string;
  icon: React.ReactNode;
  children: React.ReactNode;
}

const ConductSection: React.FC<ConductSectionProps> = ({ title, icon, children }) => (
  <Card className="mb-6">
    <CardHeader>
      <CardTitle className="flex items-center gap-3 text-xl">
        {icon}
        {title}
      </CardTitle>
    </CardHeader>
    <CardContent className="space-y-4">
      {children}
    </CardContent>
  </Card>
);

interface PolicyItemProps {
  type: 'allowed' | 'prohibited';
  children: React.ReactNode;
}

const PolicyItem: React.FC<PolicyItemProps> = ({ type, children }) => (
  <div className="flex items-start gap-3 p-3 rounded-lg bg-muted/50">
    {type === 'allowed' ? (
      <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
    ) : (
      <XCircle className="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
    )}
    <div className="text-sm leading-relaxed">{children}</div>
  </div>
);

const CodeOfConductPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-gradient-to-r from-primary/10 via-primary/5 to-background border-b">
        <div className="container mx-auto px-4 py-12 max-w-4xl">
          <div className="text-center space-y-4">
            <div className="flex justify-center">
              <Shield className="h-16 w-16 text-primary" />
            </div>
            <h1 className="text-4xl font-bold tracking-tight">Code of Conduct</h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Our commitment to maintaining a professional, ethical, and respectful academic writing community
            </p>
            <div className="flex justify-center gap-2 flex-wrap">
              <Badge variant="secondary">Effective Date: January 2025</Badge>
              <Badge variant="outline">Version 2.1</Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Introduction */}
        <div className="mb-8 p-6 bg-card rounded-lg border">
          <h2 className="text-2xl font-semibold mb-4">Our Mission</h2>
          <p className="text-muted-foreground leading-relaxed">
            We are committed to fostering a professional, respectful, and ethical environment where students can receive legitimate academic assistance and writers can provide quality educational support. This Code of Conduct applies to all users of our platform, including students, writers, and administrators.
          </p>
        </div>

        {/* Core Principles */}
        <ConductSection 
          title="Core Principles" 
          icon={<Scale className="h-6 w-6 text-primary" />}
        >
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <h4 className="font-semibold">Academic Integrity</h4>
              <p className="text-sm text-muted-foreground">
                All work must be original, properly cited, and used as a learning aid rather than direct submission.
              </p>
            </div>
            <div className="space-y-3">
              <h4 className="font-semibold">Professional Conduct</h4>
              <p className="text-sm text-muted-foreground">
                Maintain respectful, professional communication and behavior at all times.
              </p>
            </div>
            <div className="space-y-3">
              <h4 className="font-semibold">Quality Standards</h4>
              <p className="text-sm text-muted-foreground">
                Deliver high-quality work that meets academic standards and requirements.
              </p>
            </div>
            <div className="space-y-3">
              <h4 className="font-semibold">Fair Practice</h4>
              <p className="text-sm text-muted-foreground">
                Engage in honest pricing, transparent communication, and fair business practices.
              </p>
            </div>
          </div>
        </ConductSection>

        {/* Student Responsibilities */}
        <ConductSection 
          title="Student Responsibilities" 
          icon={<BookOpen className="h-6 w-6 text-primary" />}
        >
          <div className="space-y-3">
            <h4 className="font-semibold text-green-700 dark:text-green-400">✓ Expected Behavior</h4>
            <div className="space-y-2">
              <PolicyItem type="allowed">
                Use completed work as reference material, study guides, or learning aids
              </PolicyItem>
              <PolicyItem type="allowed">
                Provide clear, detailed assignment instructions and requirements
              </PolicyItem>
              <PolicyItem type="allowed">
                Communicate respectfully with writers and maintain professional dialogue
              </PolicyItem>
              <PolicyItem type="allowed">
                Pay agreed amounts promptly upon satisfactory completion of work
              </PolicyItem>
              <PolicyItem type="allowed">
                Report any issues or concerns through proper channels
              </PolicyItem>
            </div>
          </div>

          <Separator />

          <div className="space-y-3">
            <h4 className="font-semibold text-red-700 dark:text-red-400">✗ Prohibited Actions</h4>
            <div className="space-y-2">
              <PolicyItem type="prohibited">
                Submitting purchased work directly without modification or attribution
              </PolicyItem>
              <PolicyItem type="prohibited">
                Sharing login credentials or allowing others to use your account
              </PolicyItem>
              <PolicyItem type="prohibited">
                Harassment, discrimination, or abusive behavior toward writers
              </PolicyItem>
              <PolicyItem type="prohibited">
                Attempting to bypass payment systems or engage in fraudulent activities
              </PolicyItem>
              <PolicyItem type="prohibited">
                Requesting work that violates academic policies or institutional guidelines
              </PolicyItem>
            </div>
          </div>
        </ConductSection>

        {/* Writer Responsibilities */}
        <ConductSection 
          title="Writer Responsibilities" 
          icon={<Users className="h-6 w-6 text-primary" />}
        >
          <div className="space-y-3">
            <h4 className="font-semibold text-green-700 dark:text-green-400">✓ Expected Behavior</h4>
            <div className="space-y-2">
              <PolicyItem type="allowed">
                Produce original, high-quality work that meets academic standards
              </PolicyItem>
              <PolicyItem type="allowed">
                Provide proper citations and references for all sources used
              </PolicyItem>
              <PolicyItem type="allowed">
                Meet agreed deadlines and communicate proactively about any delays
              </PolicyItem>
              <PolicyItem type="allowed">
                Maintain confidentiality of client information and assignments
              </PolicyItem>
              <PolicyItem type="allowed">
                Offer reasonable revisions to ensure client satisfaction
              </PolicyItem>
            </div>
          </div>

          <Separator />

          <div className="space-y-3">
            <h4 className="font-semibold text-red-700 dark:text-red-400">✗ Prohibited Actions</h4>
            <div className="space-y-2">
              <PolicyItem type="prohibited">
                Plagiarizing content from any source, including AI-generated text without disclosure
              </PolicyItem>
              <PolicyItem type="prohibited">
                Reselling the same work to multiple clients
              </PolicyItem>
              <PolicyItem type="prohibited">
                Bidding on assignments outside your area of expertise
              </PolicyItem>
              <PolicyItem type="prohibited">
                Sharing or distributing client work without explicit permission
              </PolicyItem>
              <PolicyItem type="prohibited">
                Engaging in discriminatory practices based on race, gender, religion, or nationality
              </PolicyItem>
            </div>
          </div>
        </ConductSection>

        {/* Communication Guidelines */}
        <ConductSection 
          title="Communication Guidelines" 
          icon={<MessageSquare className="h-6 w-6 text-primary" />}
        >
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h4 className="font-semibold">Professional Communication</h4>
              <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
                <li>Use respectful, professional language at all times</li>
                <li>Respond to messages within 24 hours during business days</li>
                <li>Keep discussions focused on the academic work</li>
                <li>Use clear, concise language to avoid misunderstandings</li>
              </ul>
            </div>
            <div className="space-y-3">
              <h4 className="font-semibold">Prohibited Communication</h4>
              <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
                <li>Harassment, threats, or abusive language</li>
                <li>Sharing personal contact information</li>
                <li>Discussing ways to violate academic integrity</li>
                <li>Spam or irrelevant promotional content</li>
              </ul>
            </div>
          </div>
        </ConductSection>

        {/* Enforcement */}
        <ConductSection 
          title="Enforcement & Consequences" 
          icon={<AlertTriangle className="h-6 w-6 text-primary" />}
        >
          <div className="space-y-4">
            <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-800">
              <h4 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">Violation Consequences</h4>
              <div className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                <p><strong>First Offense:</strong> Written warning and mandatory code of conduct review</p>
                <p><strong>Second Offense:</strong> Temporary account suspension (7-30 days)</p>
                <p><strong>Serious Violations:</strong> Immediate account termination and potential legal action</p>
              </div>
            </div>
            
            <div className="space-y-3">
              <h4 className="font-semibold">Reporting Process</h4>
              <p className="text-sm text-muted-foreground">
                If you witness or experience conduct violations, please report them immediately through our support system. All reports are treated confidentially and investigated thoroughly.
              </p>
              <div className="flex flex-wrap gap-2">
                <Badge variant="outline">Email: <EMAIL></Badge>
                <Badge variant="outline">Support Ticket System</Badge>
                <Badge variant="outline">24/7 Reporting Available</Badge>
              </div>
            </div>
          </div>
        </ConductSection>

        {/* Updates and Contact */}
        <div className="mt-8 p-6 bg-muted rounded-lg">
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold mb-2">Policy Updates</h3>
              <p className="text-sm text-muted-foreground">
                This Code of Conduct may be updated periodically. Users will be notified of significant changes via email and platform notifications. Continued use of the platform constitutes acceptance of updates.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Questions & Support</h3>
              <p className="text-sm text-muted-foreground mb-3">
                If you have questions about this Code of Conduct or need clarification on any policies, please contact our support team.
              </p>
              <div className="space-y-1 text-sm">
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Hours:</strong> Monday-Friday, 9 AM - 6 PM EST</p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-8 pt-6 border-t">
          <p className="text-sm text-muted-foreground">
            By using our platform, you agree to abide by this Code of Conduct and contribute to a positive academic community.
          </p>
        </div>
      </div>
    </div>
  );
};

export default CodeOfConductPage;