import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { WriterAnalytics } from "@/types/analytics";
import { PaymentStatus } from "@prisma/client";

export async function GET() {
  try {
    // Get total writers
    const totalWriters = await prisma.user.count({
      where: { role: "WRITER" },
    });

    // Get active writers (writers who logged in within the last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const activeWriters = await prisma.user.count({
      where: {
        role: "WRITER",
        updatedAt: {
          gte: thirtyDaysAgo,
        },
      },
    });

    // Get new writers in the last 30 days
    const newWriters = await prisma.user.count({
      where: {
        role: "WRITER",
        createdAt: {
          gte: thirtyDaysAgo,
        },
      },
    });

    // Get all writers with their PAID assignments
    const writersWithAssignments = await prisma.user.findMany({
      where: {
        role: "WRITER",
      },
      select: {
        id: true,
        name: true,
        email: true,
        rating: true,
        assignedAssignments: {
          select: {
            id: true,
            price: true,
            paymentStatus: true,
          },
          where: {
            paymentStatus: PaymentStatus.PAID,
          },
        },
      },
    });

    // Calculate stats for each writer and sort by completed assignments
    const writersWithStats = writersWithAssignments
      .map((writer) => {
        const paidAssignments = writer.assignedAssignments;
        const completedAssignments = paidAssignments.length;
        const earnings = paidAssignments.reduce((sum, assignment) => sum + assignment.price, 0);

        return {
          id: writer.id,
          name: writer.name || "Anonymous",
          email: writer.email,
          completedAssignments,
          averageRating: writer.rating || 0,
          earnings,
        };
      })
      .filter((writer) => writer.completedAssignments > 0) // Only include writers with paid assignments
      .sort((a, b) => b.completedAssignments - a.completedAssignments) // Sort by completed assignments desc
      .slice(0, 5); // Take top 5

    const formattedTopWriters = writersWithStats;

    // Generate writers by month data (last 12 months)
    const writersByMonth = [];
    for (let i = 11; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
      const endOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0);
      
      const count = await prisma.user.count({
        where: {
          role: "WRITER",
          createdAt: {
            gte: startOfMonth,
            lte: endOfMonth,
          },
        },
      });
      
      writersByMonth.push({
        date: `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`,
        value: count,
      });
    }

    // Generate writer distribution by education level
    const writers = await prisma.user.findMany({
      where: {
        role: "WRITER",
      },
      select: {
        educationLevel: true,
      },
    });

    const distribution: Record<string, number> = {};
    writers.forEach((writer) => {
      const level = writer.educationLevel || "Not Specified";
      distribution[level] = (distribution[level] || 0) + 1;
    });

    const writerDistribution = Object.entries(distribution).map(([category, value]) => ({
      category,
      value,
    }));

    // Recent writer activity (simplified)
    const recentActivities = await prisma.user.findMany({
      where: {
        role: "WRITER",
      },
      select: {
        id: true,
        name: true,
        updatedAt: true,
      },
      orderBy: {
        updatedAt: "desc",
      },
      take: 10,
    });

    const writerActivity = recentActivities.map((activity) => ({
      id: activity.id,
      name: activity.name || "Anonymous",
      action: "Logged in",
      timestamp: activity.updatedAt,
    }));

    // Writer performance data - based on PAID assignments
    const writerPerformanceData = await prisma.user.findMany({
      where: {
        role: "WRITER",
      },
      select: {
        id: true,
        name: true,
        assignedAssignments: {
          select: {
            id: true,
            paymentStatus: true,
            estTime: true,
            updatedAt: true,
            status: true,
          },
          where: {
            paymentStatus: PaymentStatus.PAID,
          },
        },
      },
      take: 5,
    });

    const formattedWriterPerformance = writerPerformanceData.map((writer) => {
      const paidAssignments = writer.assignedAssignments;

      const onTime = paidAssignments.filter(assignment =>
        new Date(assignment.updatedAt) <= new Date(assignment.estTime)
      ).length;

      const late = paidAssignments.filter(assignment =>
        new Date(assignment.updatedAt) > new Date(assignment.estTime)
      ).length;

      const rejected = 0; // For now, since we're only looking at PAID assignments

      return {
        id: writer.id,
        name: writer.name || "Anonymous",
        onTime,
        late,
        rejected,
      };
    });

    const analyticsData: WriterAnalytics = {
      totalWriters,
      activeWriters,
      newWriters,
      topWriters: formattedTopWriters,
      writersByMonth,
      writerActivity,
      writerDistribution,
      writerPerformance: formattedWriterPerformance,
    };

    return NextResponse.json(analyticsData);
  } catch (error) {
    console.error("Error fetching writer analytics:", error);
    return NextResponse.json(
      { error: "Failed to fetch writer analytics" },
      { status: 500 }
    );
  }
}
