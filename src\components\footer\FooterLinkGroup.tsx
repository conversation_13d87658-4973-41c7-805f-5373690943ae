// components/footer/FooterLinkGroup.tsx
import Link from 'next/link';

interface FooterLinkGroupProps {
  title: string;
  links: Array<{
    label: string;
    href: string;
  }>;
}

export function FooterLinkGroup({ title, links }: FooterLinkGroupProps) {
  return (
    <div className="mb-8 md:mb-0">
      <h3 className="font-medium text-lg mb-4 footer-text">{title}</h3>
      <ul className="space-y-3">
        {links.map((link, idx) => (
          <li key={`${link.href}-${link.label}-${idx}`}>
            <Link 
              href={link.href}
              className="text-sm hover:text-primary transition-colors"
            >
              {link.label}
            </Link>
          </li>
        ))}
      </ul>
    </div>
  );
}