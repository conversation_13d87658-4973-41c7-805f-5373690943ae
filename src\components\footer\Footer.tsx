// components/footer/Footer.tsx
"use client";

import { SocialIcons } from "./SocialIcons";
import { SubscribeForm } from "./SubscribeForm";
import { FooterLinkGroup } from "./FooterLinkGroup";
import { Logo } from "./Logo";
import { DisclaimerBanner } from "./DisclaimerBanner";
import { LanguageSelector } from "./LanguageSelector";
import { useCompanyInfo } from "@/hooks/use-company-info";

const legalLinks = [
  { label: "Terms & Conditions", href: "/terms" },
  { label: "Refund policy", href: "/refund-policy" },
  { label: "Privacy policy", href: "/privacy-policy" },
  { label: "Cookies policy", href: "/cookies-policy" },
  { label: "Code of conduct", href: "/code-of-conduct" },
];

const productLinks = [
  { label: "Plagiarism checker", href: "/services" },
  { label: "Essay writing app", href: "/services" },
  { label: "Citation generator", href: "/services" },
  { label: "Homework planner", href: "/services" },
  { label: "AI humanizer", href: "/services" },
  { label: "AI detector", href: "/services" },
];

const serviceLinks = [
  { label: "Dissertation writing", href: "/services/dissertation" },
  { label: "Research paper writing", href: "/services/research-paper" },
  { label: "Term paper writing", href: "/services/term-paper" },
  { label: "Custom essay writing", href: "/services/custom-essay-writing" },
  { label: "Literature review", href: "/services/literature-review" },
];

const otherLinks = [
  { label: "FAQ", href: "/faqs" },
  { label: "Contact us", href: "/contact-us" },
  { label: "Blog", href: "/blog" },
  { label: "Become a Writer", href: "/login/writer" },
];

export function Footer() {
  const { companyInfo } = useCompanyInfo();

  return (
    <footer className="footer-bg border-t border-border">
      <div className="container mx-auto px-4 py-12 footer-text">
        <div className="flex flex-col lg:flex-row gap-8 lg:gap-16 mb-12">
          <div className="w-full lg:w-1/3 space-y-6 ">
            <Logo />
            <DisclaimerBanner />
            <div className="space-y-8">
              <LanguageSelector />
              <SocialIcons />
            </div>
          </div>

          <div className="w-full lg:w-2/3">
            <div className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-4 gap-6">
              <FooterLinkGroup title="Legal & Policies" links={legalLinks} />
              <FooterLinkGroup
                title={`${companyInfo?.companyName || "Essay App"} products`}
                links={productLinks}
              />
              <FooterLinkGroup title="Popular services" links={serviceLinks} />
              <FooterLinkGroup title="Other" links={otherLinks} />
            </div>

            <div className="mt-12">
              <SubscribeForm />
            </div>
          </div>
        </div>

        <div className="border-t border-border pt-6 text-sm text-muted-foreground text-center">
          <p>
            © {new Date().getFullYear()} {companyInfo?.companyName || "Essay App"}. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}
