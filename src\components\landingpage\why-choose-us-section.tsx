// components/WhyChooseUs.tsx
export const WhyChooseUs = () => {
  const features = [
    {
      title: "Easy to Use",
      description: "Just tell us what you need and we'll take care of the rest",
    },
    {
      title: "Top Quality",
      description:
        "We only work with the best writers and tutors to ensure you get the highest quality work",
    },
    {
      title: "Fast Turnaround",
      description: "You can trust us to deliver your work on time, every time",
    },
  ];

  return (
    <section className="py-16 px-4 bg-[oklch(var(--muted))]">
      <div className="max-w-6xl mx-auto">
        <h2 className="text-3xl font-bold mb-12 text-center">Why Choose Us</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="p-6 rounded-xl bg-[oklch(var(--background))] border border-[oklch(var(--border))]"
            >
              <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
              <p className="text-[oklch(var(--muted-foreground))]">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
