// src/app/(with-footer)/services/literature-review/page.tsx
import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>ert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import {
  BookOpen,
  Search,
  FileText,
  Network,
  Filter,
  CheckCircle,
  Lightbulb,
  BarChart3,
  Archive,
  Layers,
  TrendingUp,
  Eye,
  Brain,
  Link,
} from "lucide-react";

const LiteratureReviewService: React.FC = () => {
  const reviewTypes = [
    {
      type: "Systematic Review",
      description:
        "Comprehensive analysis following strict methodology protocols",
      icon: <BarChart3 className="h-5 w-5" />,
    },
    {
      type: "Narrative Review",
      description: "Broad overview providing background and context",
      icon: <BookOpen className="h-5 w-5" />,
    },
    {
      type: "Scoping Review",
      description: "Mapping key concepts and evidence in a research area",
      icon: <Network className="h-5 w-5" />,
    },
    {
      type: "Meta-Analysis",
      description: "Statistical synthesis of quantitative research findings",
      icon: <TrendingUp className="h-5 w-5" />,
    },
  ];

  const researchSteps = [
    {
      step: "Database Search",
      icon: <Search className="h-6 w-6" />,
      description:
        "Comprehensive search across academic databases like PubMed, JSTOR, Google Scholar",
    },
    {
      step: "Source Evaluation",
      icon: <Eye className="h-6 w-6" />,
      description:
        "Critical assessment of relevance, credibility, and methodological quality",
    },
    {
      step: "Thematic Analysis",
      icon: <Brain className="h-6 w-6" />,
      description: "Identification of key themes, patterns, and research gaps",
    },
    {
      step: "Synthesis & Writing",
      icon: <Link className="h-6 w-6" />,
      description:
        "Coherent integration of findings with critical analysis and conclusions",
    },
  ];

  const qualityFeatures = [
    "Access to premium academic databases",
    "Systematic search strategy development",
    "Critical appraisal of source quality",
    "Thematic analysis and gap identification",
    "Proper citation and referencing",
    "Visual representation of findings",
    "Adherence to review guidelines",
    "Plagiarism-free original analysis",
  ];

  const commonChallenges = [
    {
      challenge: "Information Overload",
      solution:
        "We filter and organize vast amounts of literature systematically",
    },
    {
      challenge: "Source Credibility",
      solution:
        "Expert evaluation ensures only high-quality, peer-reviewed sources",
    },
    {
      challenge: "Synthesis Complexity",
      solution:
        "Professional writers create coherent narratives from diverse sources",
    },
    {
      challenge: "Time Constraints",
      solution:
        "Efficient research processes meet your deadlines without quality compromise",
    },
  ];

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-12 max-w-6xl">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <Badge variant="secondary" className="mb-4">
            <Archive className="h-4 w-4 mr-2" />
            Research Foundation
          </Badge>
          <h1 className="text-4xl font-bold text-foreground mb-4">
            Professional Literature Review Services
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Comprehensive literature reviews that form the foundation of your
            research. Our experts synthesize existing knowledge to identify gaps
            and guide your academic work.
          </p>
        </div>

        {/* What is a Literature Review */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-6 w-6 text-primary" />
              What is a Literature Review?
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground leading-relaxed mb-4">
              A literature review is a comprehensive survey and critical
              analysis of existing research publications on a specific topic or
              research question. It serves as the foundation for academic
              research by systematically examining, evaluating, and synthesizing
              relevant scholarly sources to provide context and justify the need
              for new research.
            </p>
            <p className="text-muted-foreground leading-relaxed mb-4">
              Unlike a simple summary of sources, a literature review critically
              analyzes the methodology, findings, and contributions of existing
              studies, identifies patterns and themes, highlights research gaps,
              and establishes the theoretical framework for future research
              endeavors.
            </p>
            <Alert>
              <Lightbulb className="h-4 w-4" />
              <AlertDescription>
                <strong>Key Purpose:</strong> Literature reviews don&apos;t just
                describe what others have found—they critically evaluate the
                quality of research, identify controversies, and reveal areas
                where additional investigation is needed.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        {/* Types of Literature Reviews */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Types of Literature Reviews We Provide</CardTitle>
            <CardDescription>
              Different approaches for different research needs and academic
              requirements
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              {reviewTypes.map((review, index) => (
                <div
                  key={index}
                  className="flex gap-4 p-4 rounded-lg bg-muted/50 hover:bg-muted/70 transition-colors"
                >
                  <div className="text-primary flex-shrink-0 mt-1">
                    {review.icon}
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">{review.type}</h4>
                    <p className="text-sm text-muted-foreground">
                      {review.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Service Tabs */}
        <Tabs defaultValue="process" className="mb-8">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="process" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300">Our Process</TabsTrigger>
            <TabsTrigger value="features" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300">Quality Features</TabsTrigger>
            <TabsTrigger value="challenges" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300">Common Challenges</TabsTrigger>
          </TabsList>

          <TabsContent value="process" className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              {researchSteps.map((step, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="text-primary">{step.icon}</div>
                      <h4 className="font-semibold">{step.step}</h4>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {step.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="features" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>
                  What Makes Our Literature Reviews Exceptional
                </CardTitle>
                <CardDescription>
                  Comprehensive quality assurance and professional standards
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-4">
                  {qualityFeatures.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="challenges" className="space-y-6">
            <div className="grid gap-6">
              {commonChallenges.map((item, index) => (
                <Card key={index}>
                  <CardContent className="pt-6">
                    <div className="flex items-start gap-4">
                      <div className="bg-destructive/10 text-destructive p-2 rounded-lg">
                        <Filter className="h-4 w-4" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold mb-2 text-destructive">
                          Challenge: {item.challenge}
                        </h4>
                        <p className="text-sm text-muted-foreground mb-2">
                          {item.solution}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Structure and Components */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Layers className="h-6 w-6 text-primary" />
              Literature Review Structure
            </CardTitle>
            <CardDescription>
              Professional structure that meets academic standards across all
              disciplines
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h4 className="font-semibold mb-4">Core Components</h4>
                <div className="space-y-3">
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <span className="font-medium text-sm">
                        Introduction & Scope
                      </span>
                      <p className="text-xs text-muted-foreground">
                        Clear research question and review parameters
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <span className="font-medium text-sm">Methodology</span>
                      <p className="text-xs text-muted-foreground">
                        Search strategy and selection criteria
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <span className="font-medium text-sm">
                        Thematic Analysis
                      </span>
                      <p className="text-xs text-muted-foreground">
                        Organized discussion of key themes and findings
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <span className="font-medium text-sm">
                        Critical Evaluation
                      </span>
                      <p className="text-xs text-muted-foreground">
                        Assessment of methodological quality and limitations
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <span className="font-medium text-sm">
                        Synthesis & Gaps
                      </span>
                      <p className="text-xs text-muted-foreground">
                        Integration of findings and identification of research
                        gaps
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <h4 className="font-semibold mb-4">Academic Standards</h4>
                <div className="bg-muted/50 p-4 rounded-lg space-y-2">
                  <p className="text-sm">
                    <strong>Citation Style:</strong> APA, MLA, Chicago, Harvard,
                    or custom requirements
                  </p>
                  <p className="text-sm">
                    <strong>Source Quality:</strong> Peer-reviewed journals,
                    books, and credible publications
                  </p>
                  <p className="text-sm">
                    <strong>Currency:</strong> Emphasis on recent publications
                    with historical context
                  </p>
                  <p className="text-sm">
                    <strong>Scope:</strong> Comprehensive coverage within
                    defined parameters
                  </p>
                  <p className="text-sm">
                    <strong>Analysis Depth:</strong> Critical evaluation beyond
                    mere summary
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Why Choose Us */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Why Choose Our Literature Review Services?</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="bg-primary/10 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Search className="h-6 w-6 text-primary" />
                </div>
                <h4 className="font-semibold mb-2">Expert Research Skills</h4>
                <p className="text-sm text-muted-foreground">
                  Our researchers are trained in advanced search techniques and
                  have access to premium academic databases
                </p>
              </div>
              <div className="text-center">
                <div className="bg-primary/10 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Brain className="h-6 w-6 text-primary" />
                </div>
                <h4 className="font-semibold mb-2">Critical Analysis</h4>
                <p className="text-sm text-muted-foreground">
                  We don&apos;t just summarize—we provide deep critical analysis
                  that adds value to your research
                </p>
              </div>
              <div className="text-center">
                <div className="bg-primary/10 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <FileText className="h-6 w-6 text-primary" />
                </div>
                <h4 className="font-semibold mb-2">Academic Writing</h4>
                <p className="text-sm text-muted-foreground">
                  Professional academic writing that meets the highest standards
                  of clarity and scholarly rigor
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Success Metrics */}
        <Card className="mb-8 bg-gradient-to-r from-primary/5 to-secondary/5">
          <CardContent className="pt-6">
            <div className="grid md:grid-cols-4 gap-6 text-center">
              <div>
                <div className="text-2xl font-bold text-primary mb-1">500+</div>
                <div className="text-sm text-muted-foreground">
                  Literature Reviews Completed
                </div>
              </div>
              <div>
                <div className="text-2xl font-bold text-primary mb-1">50+</div>
                <div className="text-sm text-muted-foreground">
                  Academic Disciplines
                </div>
              </div>
              <div>
                <div className="text-2xl font-bold text-primary mb-1">98%</div>
                <div className="text-sm text-muted-foreground">
                  Client Satisfaction Rate
                </div>
              </div>
              <div>
                <div className="text-2xl font-bold text-primary mb-1">24/7</div>
                <div className="text-sm text-muted-foreground">
                  Research Support
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* CTA Section */}
        <Card className="text-center bg-primary/5 border-primary/20">
          <CardContent className="pt-6">
            <h3 className="text-2xl font-bold mb-4">
              Ready to Build Your Research Foundation?
            </h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Let our expert researchers create a comprehensive literature
              review that establishes the context for your research and
              identifies opportunities for original contribution to your field.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="px-8">
                Order Literature Review
              </Button>
              <Button size="lg" variant="outline" className="px-8">
                View Sample Reviews
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default LiteratureReviewService;
