import { PrismaClient, AcademicLevel, Priority, Spacing } from "@prisma/client";

const prisma = new PrismaClient();

// Helper function to generate rule key
function generateRuleKey(
  ruleType: string,
  academicLevel?: AcademicLevel,
  priority?: Priority,
  spacing?: Spacing
): string {
  const parts = [
    ruleType,
    academicLevel || 'null',
    priority || 'null',
    spacing || 'null'
  ];
  return parts.join(':');
}

async function seedPricingRules() {
  try {
    console.log("Seeding unified pricing rules...");

    // Delete existing rules first
    await prisma.pricingRule.deleteMany({});

    // Create all pricing rules with ruleKey
    const rules = [
      // Base pricing
      {
        ruleType: "base_price",
        value: 15.0,
        ruleKey: generateRuleKey("base_price")
      },
      {
        ruleType: "minimum_price",
        value: 10.0,
        ruleKey: generateRuleKey("minimum_price")
      },

      // Academic level multipliers
      {
        ruleType: "academic_multiplier",
        academicLevel: AcademicLevel.HIGH_SCHOOL,
        value: 1.0,
        ruleKey: generateRuleKey("academic_multiplier", AcademicLevel.HIGH_SCHOOL)
      },
      {
        ruleType: "academic_multiplier",
        academicLevel: AcademicLevel.UNDERGRADUATE,
        value: 1.2,
        ruleKey: generateRuleKey("academic_multiplier", AcademicLevel.UNDERGRADUATE)
      },
      {
        ruleType: "academic_multiplier",
        academicLevel: AcademicLevel.MASTERS,
        value: 1.5,
        ruleKey: generateRuleKey("academic_multiplier", AcademicLevel.MASTERS)
      },
      {
        ruleType: "academic_multiplier",
        academicLevel: AcademicLevel.PHD,
        value: 2.0,
        ruleKey: generateRuleKey("academic_multiplier", AcademicLevel.PHD)
      },
      {
        ruleType: "academic_multiplier",
        academicLevel: AcademicLevel.PROFESSIONAL,
        value: 1.8,
        ruleKey: generateRuleKey("academic_multiplier", AcademicLevel.PROFESSIONAL)
      },

      // Priority multipliers
      {
        ruleType: "priority_multiplier",
        priority: Priority.LOW,
        value: 1.0,
        ruleKey: generateRuleKey("priority_multiplier", undefined, Priority.LOW)
      },
      {
        ruleType: "priority_multiplier",
        priority: Priority.MEDIUM,
        value: 1.5,
        ruleKey: generateRuleKey("priority_multiplier", undefined, Priority.MEDIUM)
      },
      {
        ruleType: "priority_multiplier",
        priority: Priority.HIGH,
        value: 2.0,
        ruleKey: generateRuleKey("priority_multiplier", undefined, Priority.HIGH)
      },

      // Spacing multipliers
      {
        ruleType: "spacing_multiplier",
        spacing: Spacing.DOUBLE,
        value: 1.0,
        ruleKey: generateRuleKey("spacing_multiplier", undefined, undefined, Spacing.DOUBLE)
      },
      {
        ruleType: "spacing_multiplier",
        spacing: Spacing.SINGLE,
        value: 2.0,
        ruleKey: generateRuleKey("spacing_multiplier", undefined, undefined, Spacing.SINGLE)
      },

      // Writer compensation
      {
        ruleType: "writer_percentage",
        value: 0.35,
        ruleKey: generateRuleKey("writer_percentage")
      },
      {
        ruleType: "writer_minimum_per_page",
        value: 3.0,
        ruleKey: generateRuleKey("writer_minimum_per_page")
      },
    ];

    for (const rule of rules) {
      await prisma.pricingRule.create({
        data: rule,
      });
    }

    console.log("Unified pricing rules seeded successfully!");
  } catch (error) {
    console.error("Warning: Error seeding pricing rules:", error);
    console.log("The application will use default pricing configuration if database seeding failed");
  }
}

async function seedCoupons() {
  try {
    console.log("Seeding welcome coupons...");

    // Check if welcome coupon already exists
    const existingWelcomeCoupon = await prisma.coupon.findUnique({
      where: { code: "WELCOME10" }
    });

    if (!existingWelcomeCoupon) {
      // Create welcome coupon for first-time users
      const welcomeCoupon = await prisma.coupon.create({
        data: {
          code: "WELCOME10",
          description: "Welcome discount for first-time users - 10% off your first order!",
          discountPercentage: 10.0,
          isActive: true,
          maxUses: null, // Unlimited uses
          currentUses: 0,
          expiresAt: null, // No expiration
        },
      });

      console.log(`Welcome coupon created: ${welcomeCoupon.code} (${welcomeCoupon.discountPercentage}% off)`);
    } else {
      console.log("Welcome coupon already exists, skipping creation");
    }

    // Create a limited-time promotional coupon
    const existingPromoCoupon = await prisma.coupon.findUnique({
      where: { code: "SAVE15" }
    });

    if (!existingPromoCoupon) {
      // Create promotional coupon with expiration
      const promoCoupon = await prisma.coupon.create({
        data: {
          code: "SAVE15",
          description: "Limited time offer - 15% off any order!",
          discountPercentage: 15.0,
          isActive: true,
          maxUses: 100, // Limited to 100 uses
          currentUses: 0,
          expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // Expires in 30 days
        },
      });

      console.log(`Promotional coupon created: ${promoCoupon.code} (${promoCoupon.discountPercentage}% off, expires in 30 days)`);
    } else {
      console.log("Promotional coupon already exists, skipping creation");
    }

    console.log("Coupons seeded successfully!");
  } catch (error) {
    console.error("Warning: Error seeding coupons:", error);
    console.log("The application will continue without initial coupons");
  }
}

async function seedBasePrices() {
  try {
    const basePrices = [
      { academicLevel: AcademicLevel.HIGH_SCHOOL, price: 15.0 },
      { academicLevel: AcademicLevel.UNDERGRADUATE, price: 18.0 },
      { academicLevel: AcademicLevel.MASTERS, price: 22.5 },
      { academicLevel: AcademicLevel.PHD, price: 30.0 },
      { academicLevel: AcademicLevel.PROFESSIONAL, price: 27.0 },
    ];

    console.log("Seeding legacy base prices (for backward compatibility)...");

    for (const basePrice of basePrices) {
      await prisma.basePrice.upsert({
        where: { academicLevel: basePrice.academicLevel },
        update: { price: basePrice.price },
        create: basePrice,
      });
      console.log(
        `Base price for ${basePrice.academicLevel} set to $${basePrice.price}`
      );
    }

    console.log("Legacy base prices seeded successfully");
  } catch (error) {
    console.error("Warning: Error seeding base prices:", error);
    console.log("The application will use default prices if database seeding failed");
  }
}

async function seedAuthors() {
  const authors = [
    { name: "Jane Doe", qualifications: "PhD, English Literature" },
    { name: "John Smith", qualifications: "MA, Creative Writing" },
    { name: "Alice Johnson", qualifications: "MSc, Education" },
  ];
  console.log("Seeding authors...");
  for (const author of authors) {
    await prisma.author.upsert({
      where: { name: author.name },
      update: { qualifications: author.qualifications },
      create: author,
    });
  }
  console.log("Authors seeded successfully");
}

async function seedBlogCategories() {
  const now = new Date();
  const categories = [
    { name: "All Posts", slug: "all", createdAt: now, updatedAt: now },
    {
      name: "Essay Writing Guides",
      slug: "guides",
      createdAt: now,
      updatedAt: now,
    },
    { name: "Reviews", slug: "reviews", createdAt: now, updatedAt: now },
    {
      name: "Colleges and Universities",
      slug: "colleges",
      createdAt: now,
      updatedAt: now,
    },
    { name: "Lifestyle", slug: "lifestyle", createdAt: now, updatedAt: now },
    { name: "Subjects", slug: "subjects", createdAt: now, updatedAt: now },
  ];
  console.log("Seeding blog categories...");
  for (const category of categories) {
    await prisma.blogCategory.upsert({
      where: { slug: category.slug },
      update: { name: category.name, createdAt: now, updatedAt: now },
      create: category,
    });
  }
  console.log("Blog categories seeded successfully");
}

async function seedAssessments() {
  console.log("Seeding assessments...");

  const assessmentTitle = "Academic Writing Assessment";

  // Check if assessment already exists
  const existingAssessment = await prisma.assessment.findFirst({
    where: { title: assessmentTitle },
  });

  const assessmentData = {
    title: assessmentTitle,
    isActive: true, // Make the seeded assessment active by default
    multipleChoiceQuiz: [
      {
        question:
          "APA Citation Format\nIn APA 7th edition, which of the following is the correct in-text citation for a direct quote from page 45 of a work by Johnson and Smith published in 2023?",
        options: [
          "A) (Johnson & Smith, 2023, p. 45)",
          "B) (Johnson and Smith, 2023: 45)",
          "C) (Johnson & Smith 2023, pg. 45)",
          "D) [Johnson & Smith, 2023, p. 45]",
        ],
        correctAnswer: "A) (Johnson & Smith, 2023, p. 45)",
      },
      {
        question:
          "MLA Works Cited\nAccording to MLA 9th edition guidelines, which reference entry is correctly formatted for a journal article accessed online?",
        options: [
          "A) Martinez, Ana. 'Digital Literacy in Modern Education.' Educational Review, vol. 34, no. 2, 2023, pp. 123-145. Web. 15 Jan. 2024.",
          "B) Martinez, Ana. 'Digital Literacy in Modern Education.' Educational Review, vol. 34, no. 2, 2023, pp. 123-145, doi:10.1234/er.2023.456.",
          "C) Ana Martinez. 'Digital Literacy in Modern Education.' Educational Review 34.2 (2023): 123-145. DOI: 10.1234/er.2023.456.",
          "D) Martinez, A. (2023). Digital literacy in modern education. Educational Review, 34(2), 123-145.",
        ],
        correctAnswer:
          "B) Martinez, Ana. 'Digital Literacy in Modern Education.' Educational Review, vol. 34, no. 2, 2023, pp. 123-145, doi:10.1234/er.2023.456.",
      },
      {
        question:
          "Harvard Referencing System\nIn Harvard style, what is the correct format for citing a book with three authors in the reference list?",
        options: [
          "A) Thompson, J., Wilson, K. & Davis, M. (2022) Research Methods in Psychology, 3rd edn, London: Academic Press.",
          "B) Thompson, J., Wilson, K., and Davis, M. 2022. Research Methods in Psychology. 3rd ed. London: Academic Press.",
          "C) Thompson, J.; Wilson, K.; Davis, M. (2022). Research Methods in Psychology (3rd ed.). Academic Press.",
          "D) Thompson, J., K. Wilson, and M. Davis. Research Methods in Psychology. 3rd edition. London: Academic Press, 2022.",
        ],
        correctAnswer:
          "A) Thompson, J., Wilson, K. & Davis, M. (2022) Research Methods in Psychology, 3rd edn, London: Academic Press.",
      },
      {
        question:
          "Academic Integrity\nA student asks you to write their entire research paper because they 'don't have time.' What is the most appropriate professional response?",
        options: [
          "A) Accept the job but charge a premium rate for the urgency",
          "B) Decline and explain that this constitutes academic dishonesty, but offer to provide tutoring or editing services instead",
          "C) Write the paper but advise the student to make minor changes before submission",
          "D) Accept but require the student to sign a disclaimer acknowledging the work is for 'reference purposes only'",
        ],
        correctAnswer:
          "B) Decline and explain that this constitutes academic dishonesty, but offer to provide tutoring or editing services instead",
      },
      {
        question:
          "Chicago Style Footnotes\nIn Chicago Manual of Style (Notes-Bibliography system), which footnote format is correct for a first reference to a book chapter?",
        options: [
          "A) ¹Sarah Chen, 'Climate Change Adaptation,' in Environmental Policy Today, ed. Michael Brown (New York: Green Publishers, 2023), 78-92.",
          "B) ¹Chen, Sarah. 'Climate Change Adaptation.' In Environmental Policy Today, edited by Michael Brown, 78-92. New York: Green Publishers, 2023.",
          "C) ¹Sarah Chen, 'Climate Change Adaptation' (Environmental Policy Today, ed. Michael Brown, New York: Green Publishers, 2023), pp. 78-92.",
          "D) ¹Chen, S. 'Climate Change Adaptation,' Environmental Policy Today, M. Brown (ed.), Green Publishers: New York, 2023, 78-92.",
        ],
        correctAnswer:
          "A) ¹Sarah Chen, 'Climate Change Adaptation,' in Environmental Policy Today, ed. Michael Brown (New York: Green Publishers, 2023), 78-92.",
      },
      {
        question:
          "Paraphrasing vs. Plagiarism\nA client submits a draft where they've changed a few words from the original source but kept the same sentence structure and ideas without citation. How should you address this?",
        options: [
          "A) It's acceptable since they changed some words",
          "B) Explain that this is still plagiarism and help them learn proper paraphrasing techniques with appropriate citation",
          "C) Simply add quotation marks around the borrowed sections",
          "D) Suggest they use a paraphrasing tool to make more changes",
        ],
        correctAnswer:
          "B) Explain that this is still plagiarism and help them learn proper paraphrasing techniques with appropriate citation",
      },
      {
        question:
          "APA Headings Hierarchy\nIn APA format, which heading level should be used for major sections of a research paper (like 'Method' or 'Results')?",
        options: [
          "A) Level 1: Centered, Bold, Title Case Heading",
          "B) Level 2: Flush Left, Bold, Title Case Heading",
          "C) Level 3: Flush Left, Bold Italic, Title Case Heading",
          "D) Level 4: Indented, Bold, Title Case Heading, Ending With a Period.",
        ],
        correctAnswer: "A) Level 1: Centered, Bold, Title Case Heading",
      },
      {
        question:
          "Ethical Boundaries\nA regular client asks you to complete their dissertation methodology section, claiming their advisor 'basically expects students to get help with technical writing.' What should you do?",
        options: [
          "A) Complete the work since the advisor apparently approves of assistance",
          "B) Refuse entirely and end the professional relationship",
          "C) Offer to review and provide feedback on their draft instead of writing it for them",
          "D) Complete it but advise them to check with their advisor first",
        ],
        correctAnswer:
          "C) Offer to review and provide feedback on their draft instead of writing it for them",
      },
    ],
    essayExam: {
      topic:
        "Navigating the Ethical Landscape of Academic Writing Support: Establishing Professional Boundaries in Educational Assistance Services",
      rubrics:
        "Write a comprehensive essay (500-850 words) that demonstrates your understanding of professional academic writing practices and ethical considerations in providing educational support services.",
    },
  };

  if (existingAssessment) {
    // Deactivate all other assessments first if this one should be active
    if (assessmentData.isActive) {
      await prisma.assessment.updateMany({
        where: {
          id: { not: existingAssessment.id },
          isActive: true
        },
        data: { isActive: false },
      });
    }

    // Update existing assessment
    await prisma.assessment.update({
      where: { id: existingAssessment.id },
      data: assessmentData,
    });
    console.log("Assessment updated successfully");
  } else {
    // Deactivate all other assessments first if this one should be active
    if (assessmentData.isActive) {
      await prisma.assessment.updateMany({
        where: { isActive: true },
        data: { isActive: false },
      });
    }

    // Create new assessment
    await prisma.assessment.create({
      data: assessmentData,
    });
    console.log("Assessment created successfully");
  }
}

async function seedCompanyInfo() {
  try {
    console.log("Seeding company information...");

    // Check if company info already exists
    const existingCompanyInfo = await prisma.companyInfo.findFirst();

    if (!existingCompanyInfo) {
      // Create default company information
      const companyInfo = await prisma.companyInfo.create({
        data: {
          companyName: "Essay App",
          address: "1234 Academic Way, Suite 500",
          city: "New York",
          state: "NY",
          zipCode: "10001",
          country: "United States",
          phone: "+****************",
          tollFreePhone: "+****************",
          internationalPhone: "+****************",
          supportEmail: "<EMAIL>",
          inquiriesEmail: "<EMAIL>",
          businessHours: "24/7 Customer Support - Business hours: Mon-Fri 9am-6pm EST",
          description: "Professional academic writing services for students at all levels. Get expert help with essays, research papers, dissertations, and more.",
          website: "https://essayapp.com",
        },
      });

      console.log(`Company information created: ${companyInfo.companyName}`);
    } else {
      console.log("Company information already exists, skipping creation");
    }

    console.log("Company information seeded successfully");
  } catch (error) {
    console.error("Warning: Error seeding company information:", error);
    console.log("The application will use default company information if database seeding failed");
  }
}

async function seedBlogs() {
  console.log("Seeding sample blog posts...");

  try {
    // Get authors and categories for relationships
    const authors = await prisma.author.findMany();
    const categories = await prisma.blogCategory.findMany();

    if (authors.length === 0 || categories.length === 0) {
      console.log("No authors or categories found, skipping blog seeding");
      return;
    }

    const sampleBlogs = [
      {
        title: "How to Write a Perfect Essay: A Comprehensive Guide",
        slug: "how-to-write-perfect-essay-guide",
        body: `<h2>Introduction</h2>
<p>Writing a perfect essay is a skill that every student needs to master. Whether you're in high school or pursuing higher education, the ability to craft compelling, well-structured essays is crucial for academic success.</p>

<h2>Understanding Essay Structure</h2>
<p>A well-written essay follows a clear structure that guides readers through your argument. The basic structure includes:</p>
<ul>
<li><strong>Introduction:</strong> Hook the reader and present your thesis statement</li>
<li><strong>Body paragraphs:</strong> Develop your arguments with evidence and analysis</li>
<li><strong>Conclusion:</strong> Summarize your points and reinforce your thesis</li>
</ul>

<h2>Research and Planning</h2>
<p>Before you start writing, invest time in thorough research and planning. This foundation will make the writing process smoother and more effective.</p>

<h2>Writing Tips for Success</h2>
<p>Here are some proven strategies to improve your essay writing:</p>
<ol>
<li>Start with a compelling hook</li>
<li>Use clear, concise language</li>
<li>Support your arguments with credible sources</li>
<li>Maintain consistent tone throughout</li>
<li>Proofread and edit carefully</li>
</ol>

<h2>Conclusion</h2>
<p>Mastering essay writing takes practice, but with these guidelines, you'll be well on your way to creating outstanding academic papers.</p>`,
        metaTitle: "How to Write a Perfect Essay: Complete Guide for Students",
        metaDescription: "Learn the essential steps to write outstanding essays with our comprehensive guide. From structure to research tips, master academic writing today.",
        imageUrl: "https://images.unsplash.com/photo-1455390582262-044cdead277a?w=1200&h=630&fit=crop",
        imageAlt: "Student writing an essay with pen and paper, surrounded by books and notes on a wooden desk",
        keywords: ["essay writing", "academic writing", "student guide", "writing tips", "essay structure"],
        faqs: [
          "How long should an essay be?|Essay length depends on the assignment requirements, but typically ranges from 500-2000 words for most academic essays.",
          "What makes a good thesis statement?|A good thesis statement is clear, specific, arguable, and presents the main argument of your essay in one or two sentences.",
          "How many sources should I use?|The number of sources varies by assignment, but generally use 3-5 credible sources for shorter essays and more for longer research papers."
        ],
        authorId: authors[0].id,
        categoryId: categories.find(c => c.slug === "guides")?.id || categories[0].id,
      },
      {
        title: "Top 10 Universities for Computer Science in 2024",
        slug: "top-universities-computer-science-2024",
        body: `<h2>Introduction</h2>
<p>Choosing the right university for computer science is a crucial decision that can shape your career. Here's our comprehensive ranking of the top 10 universities for computer science in 2024.</p>

<h2>Ranking Methodology</h2>
<p>Our ranking considers factors such as:</p>
<ul>
<li>Academic reputation and faculty quality</li>
<li>Research opportunities and funding</li>
<li>Industry partnerships and internship programs</li>
<li>Graduate employment rates and starting salaries</li>
<li>Campus facilities and resources</li>
</ul>

<h2>Top 10 Universities</h2>
<h3>1. Massachusetts Institute of Technology (MIT)</h3>
<p>MIT consistently ranks as the world's leading institution for computer science, with cutting-edge research and exceptional faculty.</p>

<h3>2. Stanford University</h3>
<p>Located in Silicon Valley, Stanford offers unparalleled industry connections and entrepreneurship opportunities.</p>

<h3>3. Carnegie Mellon University</h3>
<p>Known for its rigorous computer science program and strong emphasis on practical applications.</p>

<h2>Making Your Choice</h2>
<p>When selecting a university, consider your specific interests, career goals, and personal preferences beyond just rankings.</p>`,
        metaTitle: "Top 10 Universities for Computer Science 2024 - Complete Rankings",
        metaDescription: "Discover the best universities for computer science in 2024. Our comprehensive ranking helps you choose the perfect program for your career goals.",
        imageUrl: "https://images.unsplash.com/photo-1562774053-701939374585?w=1200&h=630&fit=crop",
        imageAlt: "Modern university campus with students walking between academic buildings and computer science facilities",
        keywords: ["computer science universities", "university rankings", "CS programs", "college selection", "STEM education"],
        faqs: [
          "What should I look for in a computer science program?|Look for strong faculty, research opportunities, industry partnerships, modern facilities, and good job placement rates.",
          "Do I need programming experience before starting?|While helpful, most programs start with fundamentals. Focus on problem-solving skills and mathematical aptitude.",
          "What career opportunities are available?|CS graduates can pursue careers in software development, data science, cybersecurity, AI/ML, and many other tech fields."
        ],
        authorId: authors[1].id,
        categoryId: categories.find(c => c.slug === "colleges")?.id || categories[0].id,
      },
      {
        title: "The Art of Academic Research: Finding Credible Sources",
        slug: "academic-research-credible-sources-guide",
        body: `<h2>Introduction</h2>
<p>In the digital age, information is abundant, but not all sources are created equal. Learning to identify and use credible sources is fundamental to academic success.</p>

<h2>What Makes a Source Credible?</h2>
<p>Credible sources share several key characteristics:</p>
<ul>
<li><strong>Authority:</strong> Written by experts in the field</li>
<li><strong>Accuracy:</strong> Factually correct and well-researched</li>
<li><strong>Currency:</strong> Up-to-date and relevant</li>
<li><strong>Objectivity:</strong> Presents balanced, unbiased information</li>
<li><strong>Coverage:</strong> Comprehensive treatment of the topic</li>
</ul>

<h2>Types of Academic Sources</h2>
<h3>Primary Sources</h3>
<p>Original research, documents, or firsthand accounts that provide direct evidence about your topic.</p>

<h3>Secondary Sources</h3>
<p>Analyses, interpretations, or evaluations of primary sources by other researchers.</p>

<h3>Tertiary Sources</h3>
<p>Compilations of primary and secondary sources, such as encyclopedias and textbooks.</p>

<h2>Research Strategies</h2>
<p>Effective research requires a systematic approach:</p>
<ol>
<li>Start with your institution's library databases</li>
<li>Use academic search engines like Google Scholar</li>
<li>Consult subject-specific databases</li>
<li>Evaluate sources using the CRAAP test</li>
<li>Keep detailed records of your sources</li>
</ol>

<h2>Conclusion</h2>
<p>Mastering the art of finding credible sources will enhance the quality of your academic work and prepare you for lifelong learning.</p>`,
        metaTitle: "Academic Research Guide: How to Find Credible Sources",
        metaDescription: "Master academic research with our comprehensive guide to finding credible sources. Learn evaluation techniques and research strategies for better papers.",
        imageUrl: "https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=1200&h=630&fit=crop",
        imageAlt: "Academic library with open books, research papers, and laptop computer for scholarly research",
        keywords: ["academic research", "credible sources", "research methods", "source evaluation", "library research"],
        faqs: [
          "How do I know if a website is credible?|Check the author's credentials, publication date, domain (.edu, .gov, .org), and whether the information is cited and peer-reviewed.",
          "Can I use Wikipedia for academic research?|While Wikipedia isn't suitable for citation, it's useful for background information and finding primary sources in the references section.",
          "How many sources do I need for my paper?|The number varies by assignment length and level, but generally aim for 1-2 sources per page for undergraduate work."
        ],
        authorId: authors[2].id,
        categoryId: categories.find(c => c.slug === "guides")?.id || categories[0].id,
      }
    ];

    for (const blog of sampleBlogs) {
      const existingBlog = await prisma.blog.findUnique({
        where: { slug: blog.slug }
      });

      if (!existingBlog) {
        await prisma.blog.create({
          data: blog
        });
        console.log(`Created blog: ${blog.title}`);
      } else {
        console.log(`Blog already exists: ${blog.title}`);
      }
    }

    console.log("Sample blog posts seeded successfully");
  } catch (error) {
    console.error("Error seeding blog posts:", error);
  }
}

async function seedFAQs() {
  try {
    console.log("Seeding FAQs...");

    const faqs = [
      {
        question: "How does your academic writing service work?",
        answer: "Our service connects students with qualified academic writers. Simply submit your assignment requirements, receive bids from writers, choose your preferred writer, and receive your completed work within the deadline. We ensure quality through our review process and offer revisions if needed.",
        category: "General",
        order: 1,
        isActive: true,
      },
      {
        question: "What types of academic papers do you handle?",
        answer: "We handle a wide range of academic papers including essays, research papers, dissertations, thesis papers, case studies, literature reviews, term papers, coursework, and more. Our writers specialize in various academic levels from high school to PhD.",
        category: "Services",
        order: 2,
        isActive: true,
      },
      {
        question: "How do you ensure the quality of the work?",
        answer: "We maintain quality through several measures: all writers undergo a rigorous assessment process, every paper is checked for plagiarism, we have a quality assurance team that reviews completed work, and we offer unlimited revisions within the specified timeframe to ensure your satisfaction.",
        category: "Quality",
        order: 3,
        isActive: true,
      },
      {
        question: "Is the work plagiarism-free?",
        answer: "Absolutely! All our work is 100% original and written from scratch. We use advanced plagiarism detection software to ensure originality, and every paper comes with a free plagiarism report. We have a zero-tolerance policy for plagiarism.",
        category: "Quality",
        order: 4,
        isActive: true,
      },
      {
        question: "What are your pricing and payment options?",
        answer: "Our pricing is competitive and depends on factors like academic level, deadline, and paper complexity. We offer transparent pricing with no hidden fees. Payment options include PayPal, credit cards, and for Kenyan writers, M-Pesa. We also offer discount coupons for new customers.",
        category: "Pricing",
        order: 5,
        isActive: true,
      },
      {
        question: "How do you handle revisions and refunds?",
        answer: "We offer unlimited free revisions within 14 days of delivery if the work doesn't meet your original requirements. If you're still not satisfied, we have a comprehensive refund policy. Refunds are processed based on the stage of work completion and adherence to our terms.",
        category: "Policies",
        order: 6,
        isActive: true,
      },
      {
        question: "How do I communicate with my assigned writer?",
        answer: "We provide a secure messaging system within your dashboard where you can communicate directly with your assigned writer. You can share additional instructions, ask questions, and track progress. All communication is monitored to ensure professionalism and quality service.",
        category: "Communication",
        order: 7,
        isActive: true,
      },
      {
        question: "What if I need urgent assistance or have a tight deadline?",
        answer: "We offer urgent delivery options with deadlines as short as 3 hours for certain types of assignments. Rush orders may have premium pricing, but we ensure the same quality standards. Our support team is available 24/7 to assist with urgent requests and questions.",
        category: "Support",
        order: 8,
        isActive: true,
      },
    ];

    for (const faq of faqs) {
      const existingFAQ = await prisma.fAQ.findFirst({
        where: { question: faq.question }
      });

      if (!existingFAQ) {
        await prisma.fAQ.create({
          data: faq
        });
        console.log(`Created FAQ: ${faq.question.substring(0, 50)}...`);
      } else {
        console.log(`FAQ already exists: ${faq.question.substring(0, 50)}...`);
      }
    }

    console.log("FAQs seeded successfully");
  } catch (error) {
    console.error("Error seeding FAQs:", error);
  }
}

async function main() {
  console.log("Starting seeding...");
  await seedPricingRules();
  await seedBasePrices();
  await seedCoupons();
  await seedAuthors();
  await seedBlogCategories();
  await seedBlogs(); // Add blog seeding
  await seedAssessments();
  await seedCompanyInfo();
  await seedFAQs(); // Add FAQ seeding
  console.log("Seeding completed.");
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
