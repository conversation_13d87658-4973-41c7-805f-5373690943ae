"use client";
import React, {  useEffect, useState } from "react";
import { BlogPostCard, BlogPostCardProps } from "./BlogPostCard";
import { Skeleton } from "@/components/ui/skeleton";

type BlogApiResponse = {
  id: string;
  slug: string;
  category?: { name?: string };
  title: string;
  createdAt: string;
  readTime?: string;
  imageUrl?: string;
  metaDescription?: string;
  body?: string;
};

// Fetch blog posts from API
async function fetchBlogs(): Promise<BlogPostCardProps[]> {
  const res = await fetch('/api/blog');
  if (!res.ok) return [];
  const blogs = await res.json();
  function stripHtml(html: string): string {
    if (!html) return '';
    return html.replace(/<[^>]+>/g, '');
  }
  return blogs.map((blog: BlogApiResponse) => ({
    id: blog.id,
    slug: blog.slug || blog.id, // Use slug if available, fallback to id for backward compatibility
    category: blog.category?.name || 'Uncategorized',
    title: blog.title,
    date: new Date(blog.createdAt).toLocaleDateString(),
    readTime: blog.readTime || '8 min', // fallback if not present
    imageUrl: blog.imageUrl || '/assets/blog.jpg',
    description: blog.metaDescription?.slice(0, 160) || stripHtml(blog.body ?? '').slice(0, 160) || '',
  }));
}

export function BlogPostsGrid() {
  const [posts, setPosts] = useState<BlogPostCardProps[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchBlogs().then(data => {
      setPosts(data);
      setLoading(false);
    });
  }, []);

  return (
    <section className="py-8">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {posts.map((post) => (
          <BlogPostCard key={post.id} {...post} />
        ))}
        {loading && Array.from({ length: 4 }).map((_, i) => (
          <Skeleton key={i} className="h-80 w-full rounded-lg" />
        ))}
      </div>
    </section>
  );
}
