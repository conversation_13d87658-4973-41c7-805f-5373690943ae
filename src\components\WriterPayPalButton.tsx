/* eslint-disable @typescript-eslint/no-unused-vars */

declare global {
  interface Window {
    paypal?: PayPalNamespace;
  }
}

interface PayPalNamespace {
  Buttons: (options: PayPalButtonsOptions) => PayPalButtonsInstance;
}

interface PayPalButtonsOptions {
  createOrder?: (...args: unknown[]) => unknown;
  onApprove?: (...args: unknown[]) => unknown;
  onError?: (...args: unknown[]) => unknown;
  onCancel?: (...args: unknown[]) => unknown;
}

interface PayPalButtonsInstance {
  render: (container: HTMLElement | null) => void;
}

import { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";

// Type for PayPal window object
interface PayPalWindow extends Window {
  paypal?: typeof window.paypal;
}

// Props for the WriterPayPalButton component
export interface WriterPayPalButtonProps {
  orderId: string; // The assignment ID
  amount: number; // Amount to pay to writer
  payeeEmail: string; // Writer's PayPal email
  onSuccess: (details: { id: string; status: string; paymentID: string }) => void;
  onError?: (error: Error) => void;
  onCancel?: () => void;
}

const WriterPayPalButton: React.FC<WriterPayPalButtonProps> = ({
  orderId,
  amount,
  payeeEmail,
  onSuccess,
  onError,
  onCancel,
}) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [payoutStatus, setPayoutStatus] = useState<string | null>(null);
  const paypalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Create PayPal script
    const script = document.createElement("script");
    script.src = "https://www.paypal.com/sdk/js?client-id=" + process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID + "&currency=USD";
    script.async = true;

    script.onload = () => {
      const win = window as PayPalWindow;
      if (win.paypal && paypalRef.current) {
        win.paypal.Buttons({
          createOrder: async (...args: unknown[]) => {
            try {
              console.log("[WriterPayPalButton] Creating order for writer payment");
              // Call Next.js API to create writer payment order
              const res = await fetch("/api/payments/writer-payments/create-order", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ 
                  orderId, 
                  amount, 
                  payeeEmail 
                }),
              });
              
              const data = await res.json();
              console.log("[WriterPayPalButton] Create order response:", data);
              
              if (!res.ok || !data.id) {
                throw new Error(data.message || "Failed to create PayPal order");
              }
              
              return data.id;
            } catch (error) {
              console.error("[WriterPayPalButton] Create order error:", error);
              if (onError) {
                onError(error instanceof Error ? error : new Error(String(error)));
              }
              throw error;
            }
          },
          
          onApprove: async (...args: unknown[]) => {
            try {
              const data = args[0] as { orderID: string };
              console.log("[WriterPayPalButton] onApprove called with orderID:", data.orderID);

              // Capture the writer payment order
              const res = await fetch("/api/payments/writer-payments/capture-order", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ 
                  orderId, 
                  orderID: data.orderID 
                }),
              });

              console.log("[WriterPayPalButton] Capture response status:", res.status);
              const details = await res.json();
              console.log("[WriterPayPalButton] Capture response data:", details);

              if (!res.ok) {
                const errorMsg = details.message || "Payment capture failed";
                console.error("[WriterPayPalButton] Capture failed:", errorMsg);

                // Check if this is a sandbox transaction refused error
                if (res.status === 422 && errorMsg.includes("PayPal Sandbox Transaction Refused")) {
                  throw new Error("PayPal Sandbox Limitation: Please use the M-Pesa option below instead for testing.");
                }

                throw new Error(errorMsg);
              }

              console.log("[WriterPayPalButton] Writer payment successful, calling onSuccess");
              onSuccess({
                id: details.id,
                status: "COMPLETED",
                paymentID: details.paymentID,
              });
            } catch (error) {
              console.error("[WriterPayPalButton] onApprove error:", error);
              if (onError) {
                onError(error instanceof Error ? error : new Error(String(error)));
              }
            }
          },

          onError: (...args: unknown[]) => {
            console.error("[WriterPayPalButton] PayPal SDK onError:", args);
            const err = args[0] instanceof Error ? args[0] : new Error(String(args[0]));
            if (onError) onError(err);
          },

          onCancel: () => {
            console.log("[WriterPayPalButton] Payment cancelled");
            if (onCancel) onCancel();
          },
        }).render(paypalRef.current);
      }
    };

    script.onerror = () => {
      console.error("[WriterPayPalButton] Failed to load PayPal SDK");
      if (onError) {
        onError(new Error("Failed to load PayPal SDK"));
      }
    };

    document.body.appendChild(script);

    return () => {
      try {
        document.body.removeChild(script);
      } catch (error) {
        // Script might have already been removed
        console.warn("[WriterPayPalButton] Script cleanup warning:", error);
      }
    };
  }, [orderId, amount, payeeEmail, onSuccess, onError, onCancel]);

  return (
    <div>
      <div ref={paypalRef} />
      <p className="text-xs text-muted-foreground mt-2">
        Payment will be sent to: {payeeEmail}
      </p>
    </div>
  );
};

export default WriterPayPalButton;
