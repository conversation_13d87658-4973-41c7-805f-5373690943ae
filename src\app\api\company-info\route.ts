import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { apiSuccess, apiError } from "@/lib/api-utils";

// GET /api/company-info - Get company information (public endpoint)
export async function GET(): Promise<NextResponse> {
  try {
    const companyInfo = await prisma.companyInfo.findFirst({
      select: {
        companyName: true,
        supportEmail: true,
        inquiriesEmail: true,
        phone: true,
        tollFreePhone: true,
        internationalPhone: true,
        businessHours: true,
        website: true,
        description: true,
      },
    });

    if (!companyInfo) {
      return apiError("Company information not found", 404);
    }

    return apiSuccess(companyInfo);
  } catch (error) {
    console.error("Error fetching company info:", error);
    return apiError("Failed to fetch company information", 500);
  }
}
