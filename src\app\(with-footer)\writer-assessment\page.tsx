"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Clock,
  FileText,
  CheckCircle,
  AlertTriangle,
  PenTool,
} from "lucide-react";
import { useCurrentUserId } from "@/hooks/use-session-user-id";
import { toast } from "sonner";

// Types
interface MultipleChoiceQuestion {
  question: string;
  options: string[];
  correctAnswer: string;
}

interface EssayExam {
  topic: string;
  rubrics: string;
}

interface AssessmentData {
  id: string;
  title: string;
  multipleChoiceQuiz: MultipleChoiceQuestion[];
  essayExam: EssayExam;
  createdAt: string;
  updatedAt: string;
}

export default function WriterAssessmentPage() {
  const { userId, loading: userLoading, error: userError } = useCurrentUserId();
  const [assessment, setAssessment] = useState<AssessmentData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("multiple-choice");
  const [showTimeDialog, setShowTimeDialog] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Multiple choice state
  const [selectedAnswers, setSelectedAnswers] = useState<{
    [key: number]: string;
  }>({});
  const [mcProgress, setMcProgress] = useState(0);

  // Essay state
  const [essayText, setEssayText] = useState("");
  const [wordCount, setWordCount] = useState(0);
  const [charCount, setCharCount] = useState(0);

  // Fetch assessment data from API
  useEffect(() => {
    const fetchAssessment = async () => {
      try {
        console.log("🔄 [WriterAssessment] Starting to fetch assessment...");

        const response = await fetch("/api/admin/assessments", {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include", // Add this to include session cookie
        });

        console.log("📝 [WriterAssessment] Response status:", response.status);

        if (!response.ok) {
          const errorData = await response.json();
          console.error("❌ [WriterAssessment] Error response:", errorData);
          throw new Error(
            `HTTP error! status: ${response.status} - ${errorData.error || "Unknown error"}`
          );
        }

        const assessments = await response.json();
        console.log("✅ [WriterAssessment] Fetched assessments:", assessments);

        // Get the active assessment (API now returns only active assessments for writers)
        if (assessments && assessments.length > 0) {
          console.log(
            "📋 [WriterAssessment] Setting active assessment:",
            assessments[0]
          );
          setAssessment(assessments[0]);
        } else {
          console.warn("⚠️ [WriterAssessment] No active assessments available");
          setError(
            "No active assessments available. Please contact your administrator."
          );
        }
      } catch (err) {
        console.error("❌ [WriterAssessment] Error fetching assessment:", err);
        setError("Failed to load assessment data. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchAssessment();
  }, []);

  // Update progress when answers change
  useEffect(() => {
    if (assessment) {
      const answeredCount = Object.keys(selectedAnswers).length;
      const totalQuestions = assessment.multipleChoiceQuiz.length;
      setMcProgress((answeredCount / totalQuestions) * 100);
    }
  }, [selectedAnswers, assessment]);

  // Update word and character count
  useEffect(() => {
    const words = essayText
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0);
    setWordCount(words.length);
    setCharCount(essayText.length);
  }, [essayText]);

  const handleAnswerSelect = (questionIndex: number, answer: string) => {
    setSelectedAnswers((prev) => ({
      ...prev,
      [questionIndex]: answer,
    }));
  };

  const handleNextToEssay = () => {
    setActiveTab("essay");
  };

  const handleSubmitAssessment = async () => {
    try {
      // Reset error state
      setSubmitError(null);

      // Validate required data
      if (!userId) {
        throw new Error("User not authenticated");
      }

      if (!assessment?.id) {
        toast.error("No assessment loaded");
        throw new Error("No assessment loaded");
      }

      // Validate answers
      if (
        Object.keys(selectedAnswers).length <
        (assessment?.multipleChoiceQuiz?.length || 0)
      ) {
        toast.error("Please answer all multiple choice questions");
        throw new Error("Please answer all multiple choice questions");
      }

      if (!essayText.trim()) {
        toast.error("Please complete the essay section");
        throw new Error("Please complete the essay section");
      }

      // Check word count
      if (wordCount < 500 || wordCount > 850) {
        toast.error("Essay must be between 500 and 850 words");
        throw new Error("Essay must be between 500 and 850 words");
      }

      setIsSubmitting(true);

      // Show loading toast
      const loadingToast = toast.loading("Submitting assessment...");

      const writerAnswer = {
        writerId: userId,
        multipleChoiceAnswers: selectedAnswers,
        essayText: essayText,
      };

      const response = await fetch(`/api/admin/assessments/${assessment.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          writersAnswers: [writerAnswer],
        }),
        credentials: "include",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to submit assessment");
      }

      // Dismiss loading toast and show success
      toast.dismiss(loadingToast);
      toast.success("Assessment submitted successfully!", {
        description: "Thank you for completing the assessment.",
      });

      // Mark as submitted
      setIsSubmitted(true);
      setError(null);
    } catch (error) {
      console.error("Error submitting assessment:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Failed to submit assessment";
      setSubmitError(errorMessage);
      toast.error("Failed to submit assessment", {
        description: errorMessage,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (userLoading || loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">Loading assessment...</p>
        </div>
      </div>
    );
  }

  if (userError || error || !assessment) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <Alert className="max-w-md">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {userError ||
              error ||
              "Assessment not ready. Please check back later or contact support."}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const allMcAnswered =
    assessment.multipleChoiceQuiz.length ===
    Object.keys(selectedAnswers).length;

  return (
    <div className="min-h-screen bg-background">
      {/* Time Limit Dialog */}
      <Dialog open={showTimeDialog} onOpenChange={setShowTimeDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-primary" />
              Assessment Time Limit
            </DialogTitle>
            <DialogDescription className="space-y-2">
              <p>
                You have <strong>72 hours (3 days)</strong> to complete this
                assessment.
              </p>
              <p>
                The assessment includes multiple choice questions and an essay
                exam. Make sure to save your progress regularly.
              </p>
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end">
            <Button onClick={() => setShowTimeDialog(false)}>
              Got it, Let&apos;s Start
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      <div className="container mx-auto py-8 px-4">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-primary/10 rounded-lg">
              <PenTool className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                {assessment.title}
              </h1>
              <p className="text-muted-foreground">
                Complete both sections to finish your assessment
              </p>
            </div>
          </div>

          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <Badge variant="outline" className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              72 hours remaining
            </Badge>
            <span>•</span>
            <span>
              {assessment.multipleChoiceQuiz.length} Multiple Choice Questions
            </span>
            <span>•</span>
            <span>1 Essay Question</span>
          </div>
        </div>

        {/* Assessment Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-8">
            <TabsTrigger
              value="multiple-choice"
              className="flex items-center gap-2"
            >
              <CheckCircle className="h-4 w-4" />
              Multiple Choice
              {mcProgress === 100 && (
                <Badge variant="secondary" className="ml-2">
                  Complete
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="essay" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Essay Exam
            </TabsTrigger>
          </TabsList>

          {/* Multiple Choice Tab */}
          <TabsContent value="multiple-choice" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Multiple Choice Questions</CardTitle>
                    <CardDescription>
                      Select the best answer for each question
                    </CardDescription>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-muted-foreground mb-1">
                      Progress: {Object.keys(selectedAnswers).length}/
                      {assessment.multipleChoiceQuiz.length}
                    </div>
                    <Progress value={mcProgress} className="w-32" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-8">
                {assessment.multipleChoiceQuiz.map((question, index) => (
                  <div key={index} className="space-y-4">
                    <div className="flex items-start gap-3">
                      <Badge variant="outline" className="mt-1 shrink-0">
                        Q{index + 1}
                      </Badge>
                      <div className="flex-1">
                        <h3 className="font-medium mb-4 whitespace-pre-line leading-relaxed">
                          {question.question}
                        </h3>
                        <div className="space-y-3">
                          {question.options.map((option, optionIndex) => (
                            <div key={optionIndex}>
                              <label className="flex items-start gap-3 p-4 rounded-lg border cursor-pointer hover:bg-accent/50 transition-colors">
                                <input
                                  type="radio"
                                  name={`question-${index}`}
                                  value={option}
                                  checked={selectedAnswers[index] === option}
                                  onChange={() =>
                                    handleAnswerSelect(index, option)
                                  }
                                  className="mt-1 h-4 w-4 text-primary focus:ring-primary"
                                />
                                <span className="flex-1 text-sm leading-relaxed">
                                  {option}
                                </span>
                              </label>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                    {index < assessment.multipleChoiceQuiz.length - 1 && (
                      <Separator className="my-6" />
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>

            <div className="flex justify-end">
              <Button
                onClick={handleNextToEssay}
                disabled={!allMcAnswered}
                size="lg"
                className="px-8"
              >
                Next: Essay Exam
                <FileText className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </TabsContent>

          {/* Essay Tab */}
          <TabsContent value="essay" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Essay Examination</CardTitle>
                <CardDescription>
                  Write a comprehensive response to the prompt below
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Essay Topic */}
                <div className="space-y-3">
                  <Label className="text-base font-medium">Essay Topic</Label>
                  <div className="p-4 bg-muted/50 rounded-lg border-l-4 border-l-primary">
                    <p className="font-medium leading-relaxed">
                      {assessment.essayExam.topic}
                    </p>
                  </div>
                </div>

                {/* Rubrics */}
                <div className="space-y-3">
                  <Label className="text-base font-medium">
                    Instructions & Rubrics
                  </Label>
                  <div className="p-4 bg-accent/20 rounded-lg">
                    <p className="text-sm leading-relaxed">
                      {assessment.essayExam.rubrics}
                    </p>
                  </div>
                </div>

                {/* Essay Input */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label
                      htmlFor="essay-text"
                      className="text-base font-medium"
                    >
                      Your Essay
                    </Label>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span>
                        Words: <strong>{wordCount}</strong>
                      </span>
                      <span>
                        Characters: <strong>{charCount}</strong>
                      </span>
                    </div>
                  </div>
                  <Textarea
                    id="essay-text"
                    placeholder="Begin writing your essay here..."
                    value={essayText}
                    onChange={(e) => setEssayText(e.target.value)}
                    className="min-h-[400px] resize-none"
                  />

                  {/* Word count guidance */}
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <div className="flex items-center gap-1">
                      {wordCount >= 500 && wordCount <= 850 ? (
                        <CheckCircle className="h-3 w-3 text-green-500" />
                      ) : (
                        <AlertTriangle className="h-3 w-3 text-amber-500" />
                      )}
                      <span>
                        Target: 500-850 words
                        {wordCount < 500 &&
                          ` (${500 - wordCount} words needed)`}
                        {wordCount > 850 &&
                          ` (${wordCount - 850} words over limit)`}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Submit Button */}
                <div className="pt-6 border-t">
                  <div className="flex justify-end">
                    <Button
                      onClick={handleSubmitAssessment}
                      size="lg"
                      className="px-8"
                      disabled={
                        isSubmitting ||
                        isSubmitted ||
                        wordCount < 500 ||
                        wordCount > 850
                      }
                    >
                      {isSubmitting ? (
                        <>
                          Submitting...
                          <div className="animate-spin ml-2 h-4 w-4 border-b-2 border-white rounded-full" />
                        </>
                      ) : isSubmitted ? (
                        <>
                          Submitted
                          <CheckCircle className="ml-2 h-4 w-4" />
                        </>
                      ) : (
                        <>Submit Assessment</>
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {submitError && (
        <Alert variant="destructive" className="mt-4">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}
    </div>
  );
}
