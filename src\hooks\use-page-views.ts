"use client";

import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";

interface UsePageViewsReturn {
  pageViews: number;
  isAdmin: boolean;
  loading: boolean;
  error: string | null;
  trackPageView: () => Promise<void>;
  refreshPageViews: () => Promise<void>;
}

export function usePageViews(blogSlug: string): UsePageViewsReturn {
  const { data: session, status } = useSession();
  const [pageViews, setPageViews] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasTracked, setHasTracked] = useState(false);

  // Check if user is admin
  const isAdmin = session?.user?.role === "ADMIN";

  // Function to fetch current page views
  const fetchPageViews = useCallback(async () => {
    if (!blogSlug) return;

    try {
      setError(null);
      const response = await fetch(`/api/blog/by-slug/${blogSlug}?action=views`);
      
      if (!response.ok) {
        throw new Error("Failed to fetch page views");
      }

      const data: { success: boolean; pageViews: number; title: string } = await response.json();
      
      if (data.success) {
        setPageViews(data.pageViews);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch page views";
      setError(errorMessage);
      console.error("Error fetching page views:", err);
    } finally {
      setLoading(false);
    }
  }, [blogSlug]);

  // Function to track a page view
  const trackPageView = useCallback(async () => {
    if (!blogSlug || hasTracked) return;

    try {
      const response = await fetch(`/api/blog/by-slug/${blogSlug}?action=track-view`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to track page view");
      }

      const data: { success: boolean; pageViews: number } = await response.json();
      
      if (data.success) {
        setPageViews(data.pageViews);
        setHasTracked(true);
      }
    } catch (err) {
      console.error("Error tracking page view:", err);
      // Don't set error state for tracking failures to avoid disrupting user experience
    }
  }, [blogSlug, hasTracked]);

  // Function to refresh page views (for admin use)
  const refreshPageViews = useCallback(async () => {
    setLoading(true);
    await fetchPageViews();
  }, [fetchPageViews]);

  // Initial fetch of page views
  useEffect(() => {
    if (status === "loading") return;
    
    fetchPageViews();
  }, [fetchPageViews, status]);

  // Track page view on mount (only once per session)
  useEffect(() => {
    if (status === "loading" || hasTracked) return;

    // Add a small delay to ensure the page has loaded
    const timer = setTimeout(() => {
      trackPageView();
    }, 1000);

    return () => clearTimeout(timer);
  }, [trackPageView, status, hasTracked]);

  return {
    pageViews,
    isAdmin,
    loading,
    error,
    trackPageView,
    refreshPageViews,
  };
}

// Hook specifically for admin page views display
export function useAdminPageViews(blogSlug: string) {
  const { pageViews, isAdmin, loading, error, refreshPageViews } = usePageViews(blogSlug);

  return {
    pageViews,
    loading,
    error,
    refreshPageViews,
    shouldShow: isAdmin, // Only show for admins
  };
}
