// src/hooks/use-todos.ts
"use client";

import { useState, useEffect, useCallback } from "react";
import { TodoResponse, TodoCreateData, TodoUpdateData } from "@/types/api";

interface TodosApiResponse {
  success: boolean;
  message: string;
  data: {
    todos: TodoResponse[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      pages: number;
    };
  };
}

interface UseTodosReturn {
  todos: TodoResponse[];
  loading: boolean;
  error: string | null;
  createTodo: (todoData: Omit<TodoCreateData, "userId">) => Promise<boolean>;
  updateTodo: (id: string, updateData: TodoUpdateData) => Promise<boolean>;
  deleteTodo: (id: string) => Promise<boolean>;
  toggleTodo: (id: string) => Promise<boolean>;
  refetch: () => Promise<void>;
  totalCount: number;
}

export function useTodos(): UseTodosReturn {
  const [todos, setTodos] = useState<TodoResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  const fetchTodos = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch("/api/todos?limit=100");
      const data: TodosApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to fetch todos");
      }

      if (data.success && data.data) {
        setTodos(data.data.todos);
        setTotalCount(data.data.pagination.total);
      } else {
        throw new Error(data.message || "Invalid response format");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch todos";
      setError(errorMessage);
      console.error("Error fetching todos:", err);
    } finally {
      setLoading(false);
    }
  }, []);

  const createTodo = useCallback(
    async (todoData: TodoCreateData): Promise<boolean> => {
      try {
        const response = await fetch("/api/todos", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(todoData),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.message || "Failed to create todo");
        }

        if (data.success) {
          // Add the new todo to the list
          setTodos((prev) => [data.data, ...prev]);
          setTotalCount((prev) => prev + 1);
          return true;
        } else {
          throw new Error(data.message || "Failed to create todo");
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to create todo";
        setError(errorMessage);
        console.error("Error creating todo:", err);
        return false;
      }
    },
    []
  );

  const updateTodo = useCallback(
    async (id: string, updateData: TodoUpdateData): Promise<boolean> => {
      try {
        const response = await fetch(`/api/todos/${id}`, {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(updateData),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.message || "Failed to update todo");
        }

        if (data.success) {
          // Update the todo in the list
          setTodos((prev) =>
            prev.map((todo) => (todo.id === id ? data.data : todo))
          );
          return true;
        } else {
          throw new Error(data.message || "Failed to update todo");
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to update todo";
        setError(errorMessage);
        console.error("Error updating todo:", err);
        return false;
      }
    },
    []
  );

  const deleteTodo = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/todos/${id}`, {
        method: "DELETE",
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to delete todo");
      }

      if (data.success) {
        // Remove the todo from the list
        setTodos((prev) => prev.filter((todo) => todo.id !== id));
        setTotalCount((prev) => prev - 1);
        return true;
      } else {
        throw new Error(data.message || "Failed to delete todo");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to delete todo";
      setError(errorMessage);
      console.error("Error deleting todo:", err);
      return false;
    }
  }, []);

  const toggleTodo = useCallback(
    async (id: string): Promise<boolean> => {
      const todo = todos.find((t) => t.id === id);
      if (!todo) return false;

      return updateTodo(id, { isCompleted: !todo.isCompleted });
    },
    [todos, updateTodo]
  );

  const refetch = useCallback(async () => {
    await fetchTodos();
  }, [fetchTodos]);

  // Initial fetch
  useEffect(() => {
    fetchTodos();
  }, [fetchTodos]);

  return {
    todos,
    loading,
    error,
    createTodo,
    updateTodo,
    deleteTodo,
    toggleTodo,
    refetch,
    totalCount,
  };
}
