// src/hooks/use-writer-assignments.ts
"use client";

import { useState, useEffect, useCallback } from "react";
import { useCurrentUserId } from "./use-session-user-id";

interface Assignment {
  id: string;
  taskId: string;
  title: string;
  status: string;
  assignedWriterId: string | null;
  createdAt: string;
  updatedAt: string;
}

interface AssignmentsApiResponse {
  success: boolean;
  message: string;
  data: {
    assignments: Assignment[];
    totalCount: number;
    pagination: {
      total: number;
      page: number;
      limit: number;
      pages: number;
    };
  };
}

interface MonthlyData {
  month: string;
  assignments: number;
}

interface UseWriterAssignmentsReturn {
  assignments: Assignment[];
  monthlyData: MonthlyData[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  totalCompleted: number;
}

export function useWriterAssignments(): UseWriterAssignmentsReturn {
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [monthlyData, setMonthlyData] = useState<MonthlyData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCompleted, setTotalCompleted] = useState(0);

  const { userId } = useCurrentUserId();

  const processMonthlyData = useCallback((assignments: Assignment[]) => {
    const monthlyMap = new Map<string, number>();
    
    // Initialize last 12 months
    const months = [];
    for (let i = 11; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthKey = date.toISOString().slice(0, 7); // YYYY-MM format
      const monthName = date.toLocaleDateString('en-US', { month: 'short' });
      months.push({ key: monthKey, name: monthName });
      monthlyMap.set(monthKey, 0);
    }

    // Count assignments by month
    assignments.forEach((assignment) => {
      const monthKey = assignment.updatedAt.slice(0, 7); // YYYY-MM format
      if (monthlyMap.has(monthKey)) {
        monthlyMap.set(monthKey, monthlyMap.get(monthKey)! + 1);
      }
    });

    // Convert to chart data format
    return months.map(({ key, name }) => ({
      month: name,
      assignments: monthlyMap.get(key) || 0,
    }));
  }, []);

  const fetchAssignments = useCallback(async () => {
    if (!userId) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Fetch all assignments with a high limit to get complete data
      const response = await fetch("/api/assignments?limit=10000");
      const data: AssignmentsApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to fetch assignments");
      }

      if (data.success && data.data) {
        console.log("Total assignments fetched:", data.data.assignments.length);
        console.log("Current user ID:", userId);

        // Filter assignments for the current writer that are COMPLETED or CANCELLED
        const writerAssignments = data.data.assignments.filter(
          (assignment) =>
            assignment.assignedWriterId === userId &&
            (assignment.status === "COMPLETED" || assignment.status === "CANCELLED")
        );

        console.log("Writer assignments found:", writerAssignments.length);
        console.log("Writer assignments:", writerAssignments);

        setAssignments(writerAssignments);
        setTotalCompleted(writerAssignments.length);
        setMonthlyData(processMonthlyData(writerAssignments));
      } else {
        throw new Error(data.message || "Invalid response format");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch assignments";
      setError(errorMessage);
      console.error("Error fetching writer assignments:", err);
    } finally {
      setLoading(false);
    }
  }, [userId, processMonthlyData]);

  const refetch = useCallback(async () => {
    await fetchAssignments();
  }, [fetchAssignments]);

  // Initial fetch
  useEffect(() => {
    fetchAssignments();
  }, [fetchAssignments]);

  return {
    assignments,
    monthlyData,
    loading,
    error,
    refetch,
    totalCompleted,
  };
}
