// components/blog/single/RelatedPosts.tsx
import Link from 'next/link';
import Image from 'next/image';
import { Calendar, ArrowRight } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface RelatedPost {
  id: string;
  title: string;
  slug: string;
  imageUrl: string;
  imageAlt?: string;
  createdAt: Date;
  metaDescription: string;
  author: {
    name: string;
  };
}

interface RelatedPostsProps {
  posts: RelatedPost[];
}

export default function RelatedPosts({ posts }: RelatedPostsProps) {
  const formatDate = (date: Date): string => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };

  if (posts.length === 0) return null;

  return (
    <section className="mt-12">
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">You might also like</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {posts.map((post) => (
              <Link
                key={post.id}
                href={`/blog/${post.slug}`}
                className="group block"
              >
                <article className="h-full bg-muted/20 rounded-lg overflow-hidden hover:bg-muted/40 transition-all duration-300 hover:shadow-md">
                  {/* Image */}
                  <div className="relative aspect-[16/9] overflow-hidden">
                    <Image
                      src={post.imageUrl}
                      alt={post.imageAlt || post.title}
                      fill
                      className="object-cover transition-transform duration-300 group-hover:scale-105"
                      sizes="(max-width: 768px) 100vw, 50vw"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent" />
                  </div>

                  {/* Content */}
                  <div className="p-4">
                    <div className="space-y-2">
                      {/* Meta */}
                      <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                        <Calendar className="w-3 h-3" />
                        <time dateTime={post.createdAt.toISOString()}>
                          {formatDate(post.createdAt)}
                        </time>
                        <span>•</span>
                        <span>By {post.author.name}</span>
                      </div>

                      {/* Title */}
                      <h3 className="font-semibold text-foreground line-clamp-2 group-hover:text-primary transition-colors">
                        {post.title}
                      </h3>

                      {/* Description */}
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {post.metaDescription}
                      </p>

                      {/* Read More */}
                      <div className="flex items-center space-x-1 text-xs text-primary font-medium pt-2">
                        <span>Read more</span>
                        <ArrowRight className="w-3 h-3 transition-transform group-hover:translate-x-1" />
                      </div>
                    </div>
                  </div>
                </article>
              </Link>
            ))}
          </div>

          {/* View All Link */}
          <div className="mt-6 text-center">
            <Link
              href="/blog"
              className="inline-flex items-center space-x-2 text-sm font-medium text-primary hover:text-primary/80 transition-colors"
            >
              <span>View all articles</span>
              <ArrowRight className="w-4 h-4" />
            </Link>
          </div>
        </CardContent>
      </Card>
    </section>
  );
}