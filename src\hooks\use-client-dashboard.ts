// src/hooks/use-client-dashboard.ts
"use client";

import { useState, useEffect, useCallback } from "react";
import { UserResponse } from "@/types/api";
import { useCurrentUserId } from "./use-session-user-id";

// Client-specific data interface for the dashboard
export interface ClientDashboardData {
  id: string;
  accountId: string | null;
  name: string | null;
  email: string;
  phone: string | null;
  isEmailVerified: boolean;
  createdAt: string;
  updatedAt: string;
  image: string | null;
}

export interface ClientDashboardUpdateData {
  name?: string | null;
  phone?: string | null;
}

interface UseClientDashboardReturn {
  clientData: ClientDashboardData | null;
  loading: boolean;
  error: string | null;
  updateClient: (updateData: ClientDashboardUpdateData) => Promise<boolean>;
  isUpdating: boolean;
  refetch: () => Promise<void>;
}

export function useClientDashboard(): UseClientDashboardReturn {
  const [clientData, setClientData] = useState<ClientDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);

  const {
    userId,
    loading: sessionLoading,
    error: sessionError,
  } = useCurrentUserId();

  // Transform API response to dashboard data format
  const transformApiResponseToDashboard = useCallback(
    (apiData: UserResponse): ClientDashboardData => {
      return {
        id: apiData.id,
        accountId: apiData.accountId,
        name: apiData.name,
        email: apiData.email,
        phone: apiData.phone,
        isEmailVerified: apiData.emailVerified,
        createdAt: apiData.createdAt,
        updatedAt: apiData.updatedAt,
        image: apiData.image,
      };
    },
    []
  );

  const fetchClientData = useCallback(async () => {
    if (!userId) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log("Fetching client data for userId:", userId);
      const response = await fetch(`/api/users/${userId}`);
      console.log("Response status:", response.status);
      console.log("Response headers:", response.headers);

      const responseText = await response.text();
      console.log("Raw response:", responseText);

      let result;
      try {
        result = JSON.parse(responseText);
      } catch (parseError) {
        console.error("JSON parse error:", parseError);
        throw new Error(`Invalid JSON response: ${responseText.substring(0, 100)}...`);
      }

      if (!response.ok) {
        throw new Error(result.message || "Failed to fetch client data");
      }

      if (!result.success) {
        throw new Error(result.message || "API request failed");
      }

      const transformedData = transformApiResponseToDashboard(result.data);
      setClientData(transformedData);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to fetch client data";
      setError(errorMessage);
      console.error("Error fetching client data:", err);
    } finally {
      setLoading(false);
    }
  }, [userId, transformApiResponseToDashboard]);

  const updateClient = useCallback(
    async (updateData: ClientDashboardUpdateData): Promise<boolean> => {
      if (!userId) {
        console.error("No user session available");
        return false;
      }

      try {
        setIsUpdating(true);
        setError(null);

        const response = await fetch(`/api/users/${userId}`, {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(updateData),
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.message || "Failed to update client data");
        }

        if (!result.success) {
          throw new Error(result.message || "Update failed");
        }

        // Update local state with new data
        const transformedData = transformApiResponseToDashboard(result.data);
        setClientData(transformedData);

        return true;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to update client data";
        setError(errorMessage);
        console.error("Error updating client data:", err);
        return false;
      } finally {
        setIsUpdating(false);
      }
    },
    [userId, transformApiResponseToDashboard]
  );

  const refetch = useCallback(async () => {
    await fetchClientData();
  }, [fetchClientData]);

  // Initial fetch when userId is available
  useEffect(() => {
    if (!sessionLoading && !sessionError && userId) {
      fetchClientData();
    } else if (!sessionLoading && sessionError) {
      setError(sessionError);
      setLoading(false);
    }
  }, [userId, sessionLoading, sessionError, fetchClientData]);

  return {
    clientData,
    loading: loading || sessionLoading,
    error: error || sessionError,
    updateClient,
    isUpdating,
    refetch,
  };
}
