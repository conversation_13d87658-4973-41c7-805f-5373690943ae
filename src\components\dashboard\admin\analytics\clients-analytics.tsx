"use client";

import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AnalyticsCard } from "./analytics-card";
import { ClientAnalytics } from "@/types/analytics";
import { AreaChartComponent } from "./charts/area-chart";
import { PieChartComponent } from "./charts/pie-chart";
import { DataTable } from "@/components/ui/data-table";
import { ColumnDef } from "@tanstack/react-table";
import { IconTrendingDown, IconTrendingUp, IconUsers } from "@tabler/icons-react";
import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";

// Define table columns for top clients
const topClientsColumns: ColumnDef<ClientAnalytics["topClients"][0]>[] = [
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "email",
    header: "Email",
  },
  {
    accessorKey: "totalOrders",
    header: "Total Orders",
  },
  {
    accessorKey: "totalSpent",
    header: "Total Spent",
    cell: ({ row }) => {
      const value = row.getValue("totalSpent") as number;
      return `$${value.toFixed(2)}`;
    },
  },
  {
    accessorKey: "lastActive",
    header: "Last Active",
    cell: ({ row }) => {
      const value = row.getValue("lastActive");
      // Handle both Date objects and date strings
      return value ? new Date(value as string).toLocaleDateString() : "N/A";
    },
  },
];

// Define table columns for client activity
const clientActivityColumns: ColumnDef<ClientAnalytics["clientActivity"][0]>[] = [
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "action",
    header: "Action",
  },
  {
    accessorKey: "timestamp",
    header: "Timestamp",
    cell: ({ row }) => {
      const value = row.getValue("timestamp");
      // Handle both Date objects and date strings
      return value ? new Date(value as string).toLocaleString() : "N/A";
    },
  },
];

interface ClientsAnalyticsProps {
  variant: "card" | "chart" | "full";
  className?: string;
}

export function ClientsAnalytics({ variant, className }: ClientsAnalyticsProps) {
  const [data, setData] = useState<ClientAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch("/api/analytics/clients");
        if (!response.ok) {
          throw new Error(`Error fetching client analytics: ${response.statusText}`);
        }
        const result = await response.json();
        setData(result);
      } catch (err) {
        setError((err as Error).message || "Failed to fetch client analytics");
        console.error("Error fetching client analytics:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle><Skeleton className="h-4 w-[200px]" /></CardTitle>
          <CardDescription><Skeleton className="h-4 w-[300px]" /></CardDescription>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[300px] w-full" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle>Error Loading Client Analytics</CardTitle>
          <CardDescription>{error}</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  if (!data) {
    return null;
  }

  // Card variant - summary statistics
  if (variant === "card") {
    return (
      <AnalyticsCard
        title="Total Clients"
        value={data.totalClients}
        description="Active clients this month"
        icon={<IconUsers className="h-4 w-4" />}
        trend={{
          value: data.clientGrowth,
          isPositive: data.clientGrowth > 0,
        }}
        className={className}
      />
    );
  }

  // Chart variant - clients by month chart
  if (variant === "chart") {
    return (
      <AreaChartComponent
        title="Client Growth"
        description="New clients registered per month"
        data={data.clientsByMonth.map(item => ({
          ...item,
          date: `${item.date}-01`, // Convert to YYYY-MM-DD format
        }))}
        categories={[{ name: "value", color: "hsl(var(--primary))" }]}
        className={className}
      />
    );
  }

  // Full variant - comprehensive client analytics
  return (
    <div className={cn("space-y-6", className)}>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <AnalyticsCard
          title="Total Clients"
          value={data.totalClients}
          icon={<IconUsers className="h-4 w-4" />}
        />
        <AnalyticsCard
          title="Active Clients"
          value={data.activeClients}
          description="Active in the last 30 days"
          icon={<IconUsers className="h-4 w-4" />}
        />
        <AnalyticsCard
          title="New Clients"
          value={data.newClients}
          description="Joined in the last 30 days"
          icon={<IconTrendingUp className="h-4 w-4" />}
          trend={{
            value: data.clientGrowth,
            isPositive: data.clientGrowth > 0,
          }}
        />
        <AnalyticsCard
          title="Churn Rate"
          value={`${data.churnRate.toFixed(1)}%`}
          description="Clients lost in the last 30 days"
          icon={<IconTrendingDown className="h-4 w-4" />}
          trend={{
            value: data.churnRate,
            isPositive: false,
          }}
        />
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <AreaChartComponent
          title="Client Growth"
          description="New clients registered per month"
          data={data.clientsByMonth.map(item => ({
            ...item,
            date: `${item.date}-01`, // Convert to YYYY-MM-DD format
          }))}
          categories={[{ name: "value", color: "hsl(var(--primary))" }]}
        />
        <PieChartComponent
          title="Client Distribution"
          description="Distribution by academic level"
          data={data.clientDistribution.map(({ category, value }) => ({ name: category, value }))}
        />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Top Clients</CardTitle>
          <CardDescription>Clients with the highest spending</CardDescription>
        </CardHeader>
        <CardContent>
          <DataTable columns={topClientsColumns} data={data.topClients} />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Recent Client Activity</CardTitle>
          <CardDescription>Latest actions from clients</CardDescription>
        </CardHeader>
        <CardContent>
          <DataTable columns={clientActivityColumns} data={data.clientActivity} />
        </CardContent>
      </Card>
    </div>
  );
}
