import { NextRequest } from "next/server";
import { getSession } from "@/lib/auth-utils";
import { apiSuccess, apiError } from "@/lib/api-utils";
import prisma from "@/lib/prisma";

export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return apiError("Authentication required", 401);
    }
    const currentUserId = session.user.id;

    // Await the params to resolve the Promise
    const { id: notificationId } = await params;

    // Delete notification, ensuring it belongs to the current user
    const deletedNotification = await prisma.notification.deleteMany({
      where: {
        id: notificationId,
        userId: currentUserId,
      },
    });

    if (!deletedNotification.count) {
      return apiError("Notification not found or access denied", 404);
    }

    return apiSuccess(undefined, "Notification deleted successfully");
  } catch (error) {
    console.error("Error deleting notification:", error);
    const message =
      error instanceof Error ? error.message : "Failed to delete notification";
    return apiError(message, 500);
  }
}
