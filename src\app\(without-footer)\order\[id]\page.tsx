// src/app/(without-footer)/order/[id]/page.tsx
"use client";

import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { Label } from "@/components/ui/label";
import {
  CheckCircle,
  Circle,
  Clock,
  XCircle,
  AlertCircle,
  FileText,
  User,
  ArrowLeft,
  Calendar,
  BookOpen,
  FileType,
  Users,
  MessageSquare,
  Upload,
  Gavel,
} from "lucide-react";

// Import the useUserRole hook
import { useUserRole } from "@/hooks/use-session-user-id";
import { BidDialog } from "@/components/dashboard/writer/bid-dialog";
import { ChatDialog } from "@/components/chat/chat-dialog";
import { toast } from "sonner";
import { useNotifications } from "@/hooks/use-notifications";
import { FileUpload } from "@/components/file-upload";
import { UploadedFile } from "@/types/upload";

// - Added types for assignment data structure
type Assignment = {
  id: string;
  taskId: string;
  title: string;
  description: string;
  assignmentType:
    | "ARTICLE_REVIEW"
    | "BOOK_REVIEW"
    | "CASE_STUDY"
    | "DISCUSSION"
    | "DISSERTATION"
    | "ESSAY"
    | "LAB_REPORT"
    | "LITERATURE_REVIEW"
    | "PERSONAL_STATEMENT"
    | "REFLECTION_PAPER"
    | "RESEARCH_PAPER"
    | "TERM_PAPER"
    | "THESIS"
    | "OTHER";
  subject: string;
  service: string;
  pageCount: number;
  priority: "LOW" | "MEDIUM" | "HIGH";
  academicLevel:
    | "HIGH_SCHOOL"
    | "UNDERGRADUATE"
    | "MASTERS"
    | "PHD"
    | "PROFESSIONAL";
  spacing: "SINGLE" | "DOUBLE";
  languageStyle: "ENGLISH_US" | "ENGLISH_UK" | "ENGLISH_AU" | "OTHER";
  formatStyle: "APA" | "MLA" | "CHICAGO" | "HARVARD" | "IEEE" | "OTHER";
  numSources: number;
  guidelines?: string;
  estTime: string;
  clientId: string;
  assignedWriterId?: string;
  status:
    | "DRAFT"
    | "PENDING"
    | "POSTED"
    | "ASSIGNED"
    | "COMPLETED"
    | "REVISION"
    | "CANCELLED";
  paymentStatus: "PENDING" | "PAID";
  paypalOrderId?: string;
  paypalPayerId?: string;
  paypalPaymentId?: string;
  createdAt: string;
  updatedAt: string;
  client?: {
    id: string;
    name: string | null;
    email: string;
  };
  assignedWriter?: {
    id: string;
    name: string | null;
    email: string;
  };
  bidCount: number;
  hoursRemaining: number;
  price?: number;
};

// Remove the demo writer data and types
type Bid = {
  id: string;
  message: string | null;
  status: string;
  writerId: string;
  assignmentId: string;
  createdAt: string;
  updatedAt: string;
  writer: {
    id: string;
    name: string | null;
    email: string;
  };
};

export default function OrderDetailsPage() {
  const params = useParams();
  const router = useRouter();

  // Use the useUserRole hook to get the user's role
  const { userRole, userId } = useUserRole();
  const [assignment, setAssignment] = useState<Assignment | null>(null);
  const [loading, setLoading] = useState(true);
  // Add these state variables at the top with other states
  const [cancellingOrder, setCancellingOrder] = useState(false);
  const [completing, setCompleting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedWriter, setSelectedWriter] = useState<string>("");
  const [bidDialogOpen, setBidDialogOpen] = useState(false);
  const [bids, setBids] = useState<Bid[]>([]);
  const [loadingBids, setLoadingBids] = useState(false);
  const [bidError, setBidError] = useState<string | null>(null);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [loadingFiles, setLoadingFiles] = useState(false);
  const { addNotification } = useNotifications();

  // - Extract order ID from URL params
  const orderId = params?.id as string;

  // Check if user is admin or client
  const isAdmin = userRole === "ADMIN";
  const isClient = userRole === "CLIENT";
  const isWriter = userRole === "WRITER";

  // - Fetch assignment details on component mount
  useEffect(() => {
    const fetchAssignment = async () => {
      if (!orderId) return;

      try {
        setLoading(true);
        const response = await fetch(`/api/assignments/${orderId}`);

        if (!response.ok) {
          throw new Error(`Failed to fetch assignment: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.success) {
          // Calculate hoursRemaining based on estTime and current time
          const estTime = new Date(data.data.estTime);
          const now = new Date();
          const hoursRemaining = Math.max(
            0,
            Math.round((estTime.getTime() - now.getTime()) / (1000 * 60 * 60))
          );
          setAssignment({ ...data.data, hoursRemaining });
        } else {
          throw new Error(data.message || "Failed to fetch assignment");
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "An error occurred");
      } finally {
        setLoading(false);
      }
    };

    fetchAssignment();
  }, [orderId]);

  // Fetch bids when component mounts
  useEffect(() => {
    const fetchBids = async () => {
      if (!orderId || !isAdmin) return;

      try {
        setLoadingBids(true);
        setBidError(null);
        const response = await fetch(`/api/assignments/${orderId}/bids`);
        const data = await response.json();

        if (!data.success) {
          throw new Error(data.message || "Failed to fetch bids");
        }

        setBids(data.data.bids);
      } catch (error) {
        console.error("Error fetching bids:", error);
        setBidError(
          error instanceof Error ? error.message : "Failed to fetch bids"
        );
      } finally {
        setLoadingBids(false);
      }
    };

    fetchBids();
  }, [orderId, isAdmin]);

  // Fetch files when component mounts
  useEffect(() => {
    const fetchFiles = async () => {
      if (!orderId) return;

      try {
        setLoadingFiles(true);
        const response = await fetch(`/api/assignments/${orderId}/files`);
        const data = await response.json();

        if (data.success) {
          setUploadedFiles(data.data);
        }
      } catch (error) {
        console.error('Error fetching files:', error);
      } finally {
        setLoadingFiles(false);
      }
    };

    fetchFiles();
  }, [orderId]);

  // File management functions
  const handleFileUpload = async (file: UploadedFile) => {
    // File is already uploaded by the FileUpload component
    // We just need to add it to our local state
    setUploadedFiles((prev) => [...prev, file]);
  };

  const handleFileRemove = async (fileId: string) => {
    try {
      const response = await fetch(`/api/assignments/${orderId}/files?fileId=${fileId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setUploadedFiles((prev) => prev.filter((file) => file.id !== fileId));
        toast.success('File removed successfully');
      } else {
        throw new Error('Failed to remove file');
      }
    } catch (error) {
      console.error('Error removing file:', error);
      toast.error('Failed to remove file');
    }
  };

  // - Helper functions for display formatting
  const getStatusDisplay = (status: string) => {
    const statusConfig = {
      POSTED: {
        label: "Posted",
        icon: Circle,
        color: "bg-muted text-muted-foreground border-border",
      },
      PENDING: {
        label: "In-Progress",
        icon: Clock,
        color:
          "bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800",
      },
      ASSIGNED: {
        label: "Assigned",
        icon: User,
        color:
          "bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300 dark:border-yellow-800",
      },
      COMPLETED: {
        label: "Completed",
        icon: CheckCircle,
        color:
          "bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300 dark:border-green-800",
      },
      REVISION: {
        label: "Revision",
        icon: AlertCircle,
        color:
          "bg-orange-50 text-orange-700 border-orange-200 dark:bg-orange-950 dark:text-orange-300 dark:border-orange-800",
      },
      CANCELLED: {
        label: "Cancelled",
        icon: XCircle,
        color:
          "bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300 dark:border-red-800",
      },
      DRAFT: {
        label: "Draft",
        icon: FileText,
        color: "bg-muted text-muted-foreground border-border",
      },
    };
    return (
      statusConfig[status as keyof typeof statusConfig] || statusConfig.DRAFT
    );
  };

  const getPriorityDisplay = (priority: string) => {
    const priorityConfig = {
      LOW: {
        label: "Low",
        symbol: "↓",
        color: "bg-muted text-muted-foreground border-border",
      },
      MEDIUM: {
        label: "Medium",
        symbol: "→",
        color:
          "bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800",
      },
      HIGH: {
        label: "High",
        symbol: "↑",
        color:
          "bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300 dark:border-red-800",
      },
    };
    return (
      priorityConfig[priority as keyof typeof priorityConfig] ||
      priorityConfig.MEDIUM
    );
  };

  const getTypeDisplay = (type: string) => {
    switch (type) {
      case "RESEARCH_PAPER":
        return "Research Paper";
      case "TERM_PAPER":
        return "Term Paper";
      case "BOOK_REVIEW":
        return "Book Review";
      case "ARTICLE_REVIEW":
        return "Article Review";
      case "CASE_STUDY":
        return "Case Study";
      case "DISCUSSION":
        return "Discussion";
      case "LAB_REPORT":
        return "Lab Report";
      case "LITERATURE_REVIEW":
        return "Literature Review";
      case "PERSONAL_STATEMENT":
        return "Personal Statement";
      case "REFLECTION_PAPER":
        return "Reflection Paper";
      default:
        return type.toLowerCase().replace("_", " ");
    }
  };

  const getAcademicLevelDisplay = (level: string) => {
    switch (level) {
      case "HIGH_SCHOOL":
        return "High School";
      case "UNDERGRADUATE":
        return "Undergraduate";
      case "MASTERS":
        return "Masters";
      case "PHD":
        return "PhD";
      case "PROFESSIONAL":
        return "Professional";
      default:
        return level;
    }
  };

  const handleAssignWriter = async () => {
    if (!selectedWriter || !assignment) return;

    try {
      // First, accept the selected bid
      const bidToAccept = bids.find((bid) => bid.writer.id === selectedWriter);
      if (!bidToAccept) return;

      const bidResponse = await fetch(
        `/api/assignments/${orderId}/bids/${bidToAccept.id}`,
        {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            status: "ACCEPTED",
          }),
        }
      );

      const bidData = await bidResponse.json();

      if (!bidData.success) {
        throw new Error(bidData.message || "Failed to accept bid");
      }

      // Show success message
      toast.success(
        `Writer ${
          bidToAccept.writer.name || bidToAccept.writer.email
        } has been assigned`
      );

      // Create notification for the assigned writer using the new API
      await addNotification(
        selectedWriter, // targetUserId is now the first parameter
        {
          type: "ASSIGNMENT",
          title: "New Assignment",
          message: `You have been assigned to ${assignment.title}`,
          taskId: assignment.taskId,
          assignmentId: assignment.id,
        }
      );

      // Update local assignment state to reflect the change
      setAssignment((prev) =>
        prev
          ? {
              ...prev,
              status: "ASSIGNED",
              assignedWriterId: selectedWriter,
              assignedWriter: bidToAccept.writer,
            }
          : null
      );

      // Refresh the page to ensure all data is in sync
      router.refresh();
    } catch (error) {
      console.error("Error assigning writer:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to assign writer"
      );
    }
  };

  const handleCancelOrder = async () => {
    try {
      setCancellingOrder(true);
      const response = await fetch(`/api/assignments/${orderId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          status: "CANCELLED",
        }),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || "Failed to cancel order");
      }

      // Update local state
      setAssignment((prev) => (prev ? { ...prev, status: "CANCELLED" } : null));
      toast.success("Order cancelled successfully");
    } catch (error) {
      console.error("Error cancelling order:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to cancel order"
      );
    } finally {
      setCancellingOrder(false);
    }
  };

  const handleMarkAsComplete = async () => {
    try {
      setCompleting(true);
      toast.message("Redirecting to payments...");

      // Add a small delay to show the loading state
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Redirect to payments page
      router.push(`/complete-payment/${orderId}`);
    } catch (error) {
      console.error("Error completing order:", error);
      toast.error("Failed to process completion");
      setCompleting(false);
    }
  };

  const handlePlaceBid = () => {
    setBidDialogOpen(true);
  };

  // Utility function to trim long bid messages
  const trimBidMessage = (message: string | null, maxLength: number = 100): string => {
    if (!message) return "No message provided";
    if (message.length <= maxLength) return message;
    return message.substring(0, maxLength) + "...";
  };



  // - Loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading order details...</p>
        </div>
      </div>
    );
  }

  // - Error state
  if (error || !assignment) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <XCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <p className="text-destructive mb-2">Error loading order</p>
          <p className="text-muted-foreground text-sm mb-4">
            {error || "Order not found"}
          </p>
          <Button onClick={() => router.back()} variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  const statusInfo = getStatusDisplay(assignment.status);
  const priorityInfo = getPriorityDisplay(assignment.priority);
  const StatusIcon = statusInfo.icon;

  return (
    <div className="min-h-screen bg-background ">
      <div className="w-[98%] p-6 border rounded mx-auto">
        {/* - Main content */}
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Basic Information Section */}
          <div>
            <h2 className="text-xl font-bold text-center mb-2 flex items-center justify-center space-x-2 text-foreground">
              <FileText className="h-5 w-5" />
              <span>Assignment Information</span>
            </h2>
            <p className="text-center text-muted-foreground mb-6 text-primary">
              {assignment.taskId}
            </p>
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold mb-2 text-foreground">
                  {assignment.title}
                </h3>
                <Badge variant="outline" className="mb-3">
                  {getTypeDisplay(assignment.assignmentType)}
                </Badge>
                {assignment.description && (
                  <p className="text-foreground leading-relaxed">
                    {assignment.description}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t border-border">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <BookOpen className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium text-foreground">
                      Subject:
                    </span>
                    <span className="text-sm text-foreground">
                      {assignment.subject}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <FileType className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium text-foreground">
                      Service:
                    </span>
                    <span className="text-sm text-foreground">
                      {assignment.service}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-foreground">
                      Pages:
                    </span>
                    <span className="text-sm text-foreground">
                      {assignment.pageCount}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-foreground">
                      Sources:
                    </span>
                    <span className="text-sm text-foreground">
                      {assignment.numSources}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-foreground">
                      Price:
                    </span>
                    <span className="text-sm text-foreground">
                      ${assignment.price?.toFixed(2) || "0.00"}
                    </span>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-foreground">
                      Academic Level:
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {getAcademicLevelDisplay(assignment.academicLevel)}
                    </Badge>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-foreground">
                      Format:
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {assignment.formatStyle}
                    </Badge>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-foreground">
                      Spacing:
                    </span>
                    <span className="text-sm text-foreground">
                      {assignment.spacing.toLowerCase()}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-foreground">
                      Language:
                    </span>
                    <span className="text-sm text-foreground">
                      {assignment.languageStyle.replace("_", " ")}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <hr className="border-border" />

          {/* Status and Priority Section */}
          <div>
            <h2 className="text-xl font-bold text-center mb-6 flex items-center justify-center space-x-2 text-foreground">
              <StatusIcon className="h-5 w-5" />
              <span>Status & Priority</span>
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div>
                <span className="text-sm font-medium block mb-2 text-foreground">
                  Status
                </span>
                <Badge
                  variant="outline"
                  className={`flex items-center gap-1 w-fit ${statusInfo.color}`}
                >
                  <StatusIcon className="h-3.5 w-3.5" />
                  <span>{statusInfo.label}</span>
                </Badge>
              </div>
              <div>
                <span className="text-sm font-medium block mb-2 text-foreground">
                  Priority
                </span>
                <Badge
                  variant="outline"
                  className={`flex items-center gap-1 w-fit ${priorityInfo.color}`}
                >
                  <span className="inline-block h-2 w-2">
                    {priorityInfo.symbol}
                  </span>
                  <span>{priorityInfo.label}</span>
                </Badge>
              </div>
              <div>
                <span className="text-sm font-medium block mb-2 text-foreground">
                  Payment
                </span>
                <Badge
                  variant="outline"
                  className={`flex items-center gap-1 w-fit ${
                    assignment.paymentStatus === "PAID"
                      ? "bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300 dark:border-green-800"
                      : "bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300 dark:border-yellow-800"
                  }`}
                >
                  {assignment.paymentStatus === "PAID" ? (
                    <CheckCircle className="h-3.5 w-3.5" />
                  ) : (
                    <Clock className="h-3.5 w-3.5" />
                  )}
                  <span>{assignment.paymentStatus}</span>
                </Badge>
              </div>
              {isAdmin && (
                <div>
                  <span className="text-sm font-medium block mb-2 text-foreground">
                    Bids
                  </span>
                  <div className="flex items-center space-x-1">
                    <MessageSquare className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-foreground">
                      {assignment.bidCount || 0} bids
                    </span>
                  </div>
                </div>
              )}
            </div>

            {/* Writer Selection - Admin Only */}
            {isAdmin && (
              <div className="space-y-4">
                <div>
                  <Label
                    htmlFor="writer-select"
                    className="text-sm font-medium text-center block text-foreground"
                  >
                    Assign Writer
                  </Label>
                  <div className="flex gap-2 mt-2">
                    <Select
                      value={assignment.assignedWriterId || selectedWriter}
                      onValueChange={setSelectedWriter}
                      disabled={assignment.status === "ASSIGNED"}
                    >
                      <SelectTrigger className="flex-1">
                        <SelectValue
                          placeholder={
                            assignment.status === "ASSIGNED"
                              ? `Assigned to ${
                                  assignment.assignedWriter?.name ||
                                  assignment.assignedWriter?.email
                                }`
                              : "Select a writer"
                          }
                        />
                      </SelectTrigger>
                      <SelectContent>
                        {loadingBids ? (
                          <div className="p-2 text-center text-muted-foreground">
                            Loading bids...
                          </div>
                        ) : bidError ? (
                          <div className="p-2 text-center text-destructive">
                            {bidError}
                          </div>
                        ) : bids.length === 0 ? (
                          <div className="p-2 text-center text-muted-foreground">
                            No bids available
                          </div>
                        ) : (
                          bids.map((bid) => (
                            <SelectItem
                              key={bid.id}
                              value={bid.writer.id}
                              disabled={
                                assignment.status === "ASSIGNED" &&
                                assignment.assignedWriterId !== bid.writer.id
                              }
                            >
                              <div className="flex flex-col w-full space-y-2">
                                <div className="flex items-center justify-between w-full">
                                  <div className="flex flex-col">
                                    <span className="font-medium">
                                      {bid.writer.name || bid.writer.email}
                                      {assignment.assignedWriterId ===
                                        bid.writer.id && " (Assigned)"}
                                    </span>
                                    <span className="text-xs text-muted-foreground">
                                      {bid.writer.email}
                                    </span>
                                  </div>
                                </div>
                                {/* Bid Message Section */}
                                <div className="border-t border-border pt-2">
                                  <span className="text-xs font-medium text-muted-foreground block mb-1">
                                    Bid Message:
                                  </span>
                                  <p className="text-xs text-foreground bg-muted/50 p-2 rounded text-left leading-relaxed">
                                    {trimBidMessage(bid.message, 120)}
                                  </p>
                                </div>
                              </div>
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <Button
                      onClick={handleAssignWriter}
                      disabled={
                        !selectedWriter ||
                        loadingBids ||
                        assignment.status === "ASSIGNED"
                      }
                      className="px-6"
                    >
                      {assignment.status === "ASSIGNED" ? "Assigned" : "Assign"}
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>

          <hr className="border-border" />

          {/* Timeline Section - Admin Only */}
          {isAdmin && (
            <>
              <div>
                <h2 className="text-xl font-bold text-center mb-6 flex items-center justify-center space-x-2 text-foreground">
                  <Calendar className="h-5 w-5" />
                  <span>Timeline</span>
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <span className="text-sm font-medium block mb-1 text-foreground">
                      Created
                    </span>
                    <span className="text-sm text-muted-foreground">
                      {new Date(assignment.createdAt).toLocaleDateString(
                        "en-US",
                        {
                          year: "numeric",
                          month: "short",
                          day: "numeric",
                          hour: "2-digit",
                          minute: "2-digit",
                        }
                      )}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm font-medium block mb-1 text-foreground">
                      Last Updated
                    </span>
                    <span className="text-sm text-muted-foreground">
                      {new Date(assignment.updatedAt).toLocaleDateString(
                        "en-US",
                        {
                          year: "numeric",
                          month: "short",
                          day: "numeric",
                          hour: "2-digit",
                          minute: "2-digit",
                        }
                      )}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm font-medium block mb-1 text-foreground">
                      Deadline
                    </span>
                    <span className="text-sm text-muted-foreground">
                      {new Date(assignment.estTime).toLocaleDateString(
                        "en-US",
                        {
                          year: "numeric",
                          month: "short",
                          day: "numeric",
                          hour: "2-digit",
                          minute: "2-digit",
                        }
                      )}
                    </span>
                  </div>
                </div>
              </div>
              <hr className="border-border" />
            </>
          )}

          {/* People Section - Admin Only */}
          {isAdmin && (assignment.client || assignment.assignedWriter) && (
            <>
              <div>
                <h2 className="text-xl font-bold text-center mb-6 flex items-center justify-center space-x-2 text-foreground">
                  <Users className="h-5 w-5" />
                  <span>People</span>
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {assignment.client && (
                    <div className="p-4 rounded-lg border border-border bg-card">
                      <span className="text-sm font-medium block mb-3 text-foreground">
                        Client
                      </span>
                      <div className="flex flex-col space-y-2">
                        <span className="text-sm text-foreground">
                          {assignment.client.name || "N/A"}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {assignment.client.email}
                        </span>
                      </div>
                    </div>
                  )}
                  <div className="p-4 rounded-lg border border-border bg-card">
                    <span className="text-sm font-medium block mb-3 text-foreground">
                      Assigned Writer
                    </span>
                    {assignment.assignedWriter ? (
                      <div className="flex flex-col space-y-2">
                        <span className="text-sm text-foreground">
                          {assignment.assignedWriter.name || "N/A"}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {assignment.assignedWriter.email}
                        </span>
                      </div>
                    ) : (
                      <span className="text-sm text-muted-foreground">
                        Unassigned
                      </span>
                    )}
                  </div>
                </div>
              </div>
              <hr className="border-border" />
            </>
          )}

          {/* Guidelines Section */}
          {assignment.guidelines && (
            <>
              <div>
                <h2 className="text-xl font-bold text-center mb-6 flex items-center justify-center space-x-2 text-foreground">
                  <FileText className="h-5 w-5" />
                  <span>Guidelines</span>
                </h2>
                <div className="prose prose-sm max-w-none">
                  <p className="text-foreground leading-relaxed whitespace-pre-wrap">
                    {assignment.guidelines}
                  </p>
                </div>
              </div>
              <hr className="border-border" />
            </>
          )}

          {/* File Upload Section */}
          <div>
            <h2 className="text-xl font-bold text-center mb-6 flex items-center justify-center space-x-2 text-foreground">
              <Upload className="h-5 w-5" />
              <span>Assignment Files</span>
            </h2>
            {loadingFiles ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-muted-foreground">Loading files...</p>
              </div>
            ) : (
              <FileUpload
                onFileUpload={handleFileUpload}
                onFileRemove={handleFileRemove}
                uploadedFiles={uploadedFiles}
                folder={`assignments`}
                multiple={true}
                maxFiles={20}
                assignmentId={orderId}
              />
            )}
          </div>

          {/* Action Buttons Section */}
          <div className="mt-8 flex justify-end gap-4">
            {/* Chat Dialog - Available for all authorized users */}
            {(isAdmin ||
              isClient ||
              (isWriter && assignment.assignedWriterId === userId)) && (
              <>
                
                <ChatDialog
                  assignmentId={assignment.id}
                  clientId={assignment.clientId}
                  writerId={assignment.assignedWriterId}
                  userRole={userRole as "ADMIN" | "CLIENT" | "WRITER"}
                  assignmentStatus={assignment.status}
                />
              </>
            )}

            {(isAdmin || isClient) && (
              <>
                <Button
                  variant="outline"
                  onClick={handleCancelOrder}
                  disabled={
                    cancellingOrder || assignment.status === "CANCELLED"
                  }
                  className="text-destructive hover:text-destructive hover:bg-destructive/10"
                >
                  {cancellingOrder ? (
                    <>
                      <span className="animate-spin mr-2">⏳</span>
                      Cancelling...
                    </>
                  ) : (
                    <>
                      <XCircle className="h-4 w-4 mr-2" />
                      Cancel Order
                    </>
                  )}
                </Button>
                <Button
                  onClick={handleMarkAsComplete}
                  disabled={
                    completing ||
                    assignment.status === "CANCELLED" ||
                    assignment.paymentStatus === "PAID"
                  }
                  className={`${
                    assignment.paymentStatus === "PAID"
                      ? "bg-gray-400 cursor-not-allowed"
                      : "bg-green-600 hover:bg-green-700"
                  } text-white`}
                >
                  {completing ? (
                    <>
                      <span className="animate-spin mr-2">⏳</span>
                      Redirecting...
                    </>
                  ) : assignment.paymentStatus === "PAID" ? (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Payment Completed
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Complete Payment
                    </>
                  )}
                </Button>
              </>
            )}

            {isWriter && assignment?.status === "POSTED" && (
              <>
                <Button
                  onClick={handlePlaceBid}
                  className="bg-primary text-primary-foreground hover:bg-primary/90"
                >
                  <Gavel className="h-4 w-4 mr-2" />
                  Place Bid
                </Button>

                <BidDialog
                  open={bidDialogOpen}
                  onOpenChange={setBidDialogOpen}
                  assignment={assignment}
                />
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
