// src/hooks/user-writer-profile.ts
"use client";
import { useState, useEffect, useCallback } from "react";
import { UserResponse, UserUpdateData } from "@/types/api";
import { useCurrentUserId } from "./use-session-user-id";

// Writer-specific data interface for the dashboard
export interface WriterDashboardData {
  id: string;
  accountId: string | null;
  name: string | null;
  email: string;
  phone: string | null;
  educationLevel: string | null;
  rating: number | null;
  professionalSummary: string | null;
  experience: string | null;
  competencies: string[];
  isEmailVerified: boolean;
  createdAt: string;
  updatedAt: string;
  image: string | null;
}

// Update data interface - matches what the dashboard component needs
export interface WriterDashboardUpdateData {
  name?: string | null;
  email?: string;
  phone?: string | null;
  educationLevel?: string | null;
  professionalSummary?: string | null; // will be mapped to professionalSummary for API
  experience?: string | null;
  competencies?: string[];
  password?: string;
}

interface UseWriterDashboardReturn {
  writerData: WriterDashboardData | null;
  loading: boolean;
  error: string | null;
  updateWriter: (data: WriterDashboardUpdateData) => Promise<boolean>;
  refreshWriter: () => Promise<void>;
  isUpdating: boolean;
  // Convenience methods for common operations
  updateProfileField: (
    field: keyof WriterDashboardUpdateData,
    value: WriterDashboardUpdateData[keyof WriterDashboardUpdateData]
  ) => Promise<boolean>;
  addCompetency: (competency: string) => Promise<boolean>;
  removeCompetency: (competency: string) => Promise<boolean>;
  updateCompetencies: (competencies: string[]) => Promise<boolean>;
}

interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: Record<string, string[]>;
}

export function useWriterDashboard(): UseWriterDashboardReturn {
  const [writerData, setWriterData] = useState<WriterDashboardData | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);

  const {
    userId,
    loading: sessionLoading,
    error: sessionError,
  } = useCurrentUserId();

  // Transform API response to dashboard data format
  const transformApiResponseToDashboard = useCallback(
    (apiData: UserResponse): WriterDashboardData => {
      return {
        id: apiData.id,
        accountId: apiData.accountId,
        name: apiData.name,
        email: apiData.email,
        phone: apiData.phone,
        educationLevel: apiData.educationLevel,
        rating: apiData.rating,
        professionalSummary: apiData.professionalSummary,
        experience: apiData.experience,
        competencies: apiData.competencies || [],
        isEmailVerified: apiData.emailVerified,
        createdAt: apiData.createdAt,
        updatedAt: apiData.updatedAt,
        image: apiData.image,
      };
    },
    []
  );

  // Transform dashboard update data to API format
  const transformDashboardUpdateToApi = useCallback(
    (dashboardData: WriterDashboardUpdateData): UserUpdateData => {
      const apiData: UserUpdateData = {};

      if (dashboardData.name !== undefined) apiData.name = dashboardData.name;
      if (dashboardData.email !== undefined)
        apiData.email = dashboardData.email;
      if (dashboardData.phone !== undefined)
        apiData.phone = dashboardData.phone;
      if (dashboardData.educationLevel !== undefined)
        apiData.educationLevel = dashboardData.educationLevel;
      if (dashboardData.password !== undefined)
        apiData.password = dashboardData.password;

      if (dashboardData.professionalSummary !== undefined) {
        apiData.professionalSummary = dashboardData.professionalSummary;
      }
      if (dashboardData.experience !== undefined)
        apiData.experience = dashboardData.experience;
      if (dashboardData.competencies !== undefined)
        apiData.competencies = dashboardData.competencies;

      return apiData;
    },
    []
  );

  // Fetch writer data from API
  const fetchWriterData = useCallback(async () => {
    if (!userId) {
      setError("User session not available");
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/users/writers/${userId}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

        try {
          const errorData: ApiResponse = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch {
          // If JSON parsing fails, use the default error message
        }

        throw new Error(errorMessage);
      }

      const result: ApiResponse<UserResponse> = await response.json();

      if (!result.success || !result.data) {
        throw new Error(result.message || "Invalid response from server");
      }

      const transformedData = transformApiResponseToDashboard(result.data);
      setWriterData(transformedData);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to fetch writer data";
      setError(errorMessage);
      console.error("Error fetching writer data:", err);
    } finally {
      setLoading(false);
    }
  }, [userId, transformApiResponseToDashboard]);

  // Add detailed logging utility
  const logError = (prefix: string, error: unknown) => {
    console.error(`DASH POST ERROR:- ${prefix}:`, error);
  };

  // Update the updateWriter function
  const updateWriter = useCallback(
    async (updateData: WriterDashboardUpdateData): Promise<boolean> => {
      if (!userId) {
        logError("UpdateWriter", "No user session available");
        return false;
      }

      try {
        setIsUpdating(true);
        setError(null);

        const apiUpdateData = transformDashboardUpdateToApi(updateData);

        const response = await fetch(`/api/users/writers/${userId}`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(apiUpdateData),
        });

        const result: ApiResponse = await response.json();

        if (!response.ok) {
          logError(
            "API Response",
            `HTTP ${response.status}: ${response.statusText}`
          );
          throw new Error(result.message || `HTTP ${response.status}`);
        }

        if (!result.success) {
          logError("API Result", result.message || "Update operation failed");
          return false;
        }

        // Refresh data after successful update
        await fetchWriterData();
        return true;
      } catch (err) {
        logError("UpdateWriter", err);
        setError(
          err instanceof Error ? err.message : "Failed to update writer profile"
        );
        return false;
      } finally {
        setIsUpdating(false);
      }
    },
    [userId, transformDashboardUpdateToApi, fetchWriterData]
  );

  // Refresh writer data
  const refreshWriter = useCallback(async () => {
    await fetchWriterData();
  }, [fetchWriterData]);

  // Convenience method to update a single field
  const updateProfileField = useCallback(
    async (
      field: keyof WriterDashboardUpdateData,
      value: WriterDashboardUpdateData[keyof WriterDashboardUpdateData]
    ): Promise<boolean> => {
      return updateWriter({ [field]: value });
    },
    [updateWriter]
  );

  // Update competency handlers
  const addCompetency = useCallback(
    async (competency: string): Promise<boolean> => {
      if (!writerData) {
        logError("AddCompetency", "No writer data available");
        return false;
      }

      const trimmedCompetency = competency.trim();
      if (!trimmedCompetency) {
        logError("AddCompetency", "Empty competency provided");
        return false;
      }

      try {
        setIsUpdating(true);
        const currentCompetencies = writerData.competencies || [];

        // Case-insensitive check for duplicates
        if (
          currentCompetencies.some(
            (c) => c.toLowerCase() === trimmedCompetency.toLowerCase()
          )
        ) {
          logError("AddCompetency", "Competency already exists");
          return false;
        }

        const updatedCompetencies = [...currentCompetencies, trimmedCompetency];

        return await updateWriter({ competencies: updatedCompetencies });
      } catch (err) {
        logError("AddCompetency", err);
        return false;
      } finally {
        setIsUpdating(false);
      }
    },
    [writerData, updateWriter]
  );

  // Remove a competency
  const removeCompetency = useCallback(
    async (competency: string): Promise<boolean> => {
      if (!writerData) return false;

      try {
        setIsUpdating(true);
        const currentCompetencies = writerData.competencies || [];
        const updatedCompetencies = currentCompetencies.filter(
          (c) => c !== competency
        );

        const success = await updateWriter({
          competencies: updatedCompetencies,
        });

        if (success) {
          await refreshWriter(); // Refresh data after successful update
        }

        return success;
      } finally {
        setIsUpdating(false);
      }
    },
    [writerData, updateWriter, refreshWriter]
  );

  // Update all competencies at once
  const updateCompetencies = useCallback(
    async (competencies: string[]): Promise<boolean> => {
      return updateWriter({ competencies });
    },
    [updateWriter]
  );

  // Handle session loading and errors
  useEffect(() => {
    if (sessionLoading) {
      return; // Wait for session to load
    }

    if (sessionError) {
      setError(sessionError);
      setLoading(false);
      return;
    }

    if (userId) {
      fetchWriterData();
    } else {
      setError("No user session found");
      setLoading(false);
    }
  }, [userId, sessionLoading, sessionError, fetchWriterData]);

  return {
    writerData,
    loading: loading || sessionLoading,
    error: error || sessionError,
    updateWriter,
    refreshWriter,
    isUpdating,
    updateProfileField,
    addCompetency,
    removeCompetency,
    updateCompetencies,
  };
}
