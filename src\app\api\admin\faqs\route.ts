import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { apiSuccess, apiError, checkPermission, parseRequestBody } from "@/lib/api-utils";
import { faqCreateSchema } from "@/lib/validations";
import { UserRole } from "@prisma/client";

// GET /api/admin/faqs - Get all FAQs for admin management
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Check admin permission
    const permissionError = await checkPermission([UserRole.ADMIN]);
    if (permissionError) return permissionError;

    const url = new URL(request.url);
    const category = url.searchParams.get("category");
    const isActive = url.searchParams.get("isActive");

    // Build where clause
    const where: {
      category?: string;
      isActive?: boolean;
    } = {};

    if (category) {
      where.category = category;
    }

    if (isActive !== null) {
      where.isActive = isActive === "true";
    }

    // Fetch FAQs
    const faqs = await prisma.fAQ.findMany({
      where,
      orderBy: [
        { order: "asc" },
        { createdAt: "desc" }
      ],
    });

    // Get unique categories
    const categories = await prisma.fAQ.findMany({
      select: { category: true },
      distinct: ["category"],
    });

    const uniqueCategories = categories
      .map(c => c.category)
      .filter(Boolean)
      .sort();

    return apiSuccess({
      faqs,
      categories: uniqueCategories,
      total: faqs.length,
    });
  } catch (error) {
    console.error("Error fetching FAQs for admin:", error);
    return apiError("Failed to fetch FAQs", 500);
  }
}

// POST /api/admin/faqs - Create new FAQ
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Check admin permission
    const permissionError = await checkPermission([UserRole.ADMIN]);
    if (permissionError) return permissionError;

    // Parse and validate request body
    const parseResult = await parseRequestBody(request, faqCreateSchema);
    if ("message" in parseResult) {
      return apiError(parseResult.message, 400, parseResult.errors);
    }

    const data = parseResult;

    // Create FAQ
    const faq = await prisma.fAQ.create({
      data,
    });

    return apiSuccess(faq, "FAQ created successfully");
  } catch (error) {
    console.error("Error creating FAQ:", error);
    return apiError("Failed to create FAQ", 500);
  }
}
