// types/react-quill.d.ts
/* eslint-disable @typescript-eslint/no-explicit-any */
import type { FC } from "react";

declare module "react-quill" {
  export interface ReactQuillProps {
    value: string;
    onChange: (content: string, delta: any, source: string, editor: any) => void;
    modules?: any;
    theme?: string;
    placeholder?: string;
    readOnly?: boolean;
    [key: string]: any;
  }

  const ReactQuill: FC<ReactQuillProps>;
  export default ReactQuill;
}

declare module "react-quill" {
  const whatever: any;
  export = whatever;
}