"use client";

import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AnalyticsCard } from "./analytics-card";
import type { GrowthAnalytics } from "@/types/analytics";
import { LineChartComponent } from "./charts/line-chart";
import { BarChartComponent } from "./charts/bar-chart";
import { PieChartComponent } from "./charts/pie-chart";
import { DataTable } from "@/components/ui/data-table";
import { ColumnDef } from "@tanstack/react-table";
import { IconTrendingUp, IconPercentage, IconRefresh } from "@tabler/icons-react";
import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";
import { BarChartNegative } from "./charts/bar-chart-negative";

// Define table columns for marketing effectiveness
const marketingColumns: ColumnDef<GrowthAnalytics["marketingEffectiveness"][0]>[] = [
  {
    accessorKey: "channel",
    header: "Channel",
  },
  {
    accessorKey: "visitors",
    header: "Visitors",
  },
  {
    accessorKey: "conversions",
    header: "Conversions",
  },
  {
    accessorKey: "conversionRate",
    header: "Conv. Rate",
    cell: ({ row }) => {
      const value = row.getValue("conversionRate") as number;
      return `${value.toFixed(2)}%`;
    },
  },
  {
    accessorKey: "cost",
    header: "Cost",
    cell: ({ row }) => {
      const value = row.getValue("cost") as number;
      return `$${value.toFixed(2)}`;
    },
  },
  {
    accessorKey: "roi",
    header: "ROI",
    cell: ({ row }) => {
      const value = row.getValue("roi") as number;
      return `${value.toFixed(2)}%`;
    },
  },
];

interface GrowthAnalyticsProps {
  variant: "card" | "chart" | "full";
  className?: string;
}

export function GrowthAnalytics({ variant, className }: GrowthAnalyticsProps) {
  const [data, setData] = useState<GrowthAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch("/api/analytics/growth");
        if (!response.ok) {
          throw new Error(`Error fetching growth analytics: ${response.statusText}`);
        }
        const result = await response.json();
        setData(result);
      } catch (err) {
        setError((err as Error).message || "Failed to fetch growth analytics");
        console.error("Error fetching growth analytics:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle><Skeleton className="h-4 w-[200px]" /></CardTitle>
          <CardDescription><Skeleton className="h-4 w-[300px]" /></CardDescription>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[300px] w-full" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle>Error Loading Growth Analytics</CardTitle>
          <CardDescription>{error}</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  if (!data) {
    return null;
  }

  // Card variant - summary statistics
  if (variant === "card") {
    return (
      <AnalyticsCard
        title="Growth Rate"
        value={`${data.growthRate.toFixed(1)}%`}
        description="Month-over-month"
        icon={<IconTrendingUp className="h-4 w-4" />}
        trend={{
          value: data.growthRate,
          isPositive: data.growthRate > 0,
        }}
        className={className}
      />
    );
  }

  // Chart variant - growth by month chart
  if (variant === "chart") {
    return (
      <LineChartComponent
        title="User Growth Trends"
        description="Monthly growth in users, clients, and writers"
        data={data.growthByMonth.map(item => ({
          ...item,
          date: `${item.date}-01`, // Convert to YYYY-MM-DD format
        }))}
        categories={[
          { name: "users", color: "hsl(var(--primary))" },
          { name: "clients", color: "hsl(var(--secondary))" },
          { name: "writers", color: "hsl(var(--accent))" },
        ]}
        className={className}
      />
    );
  }

  // Full variant - comprehensive growth analytics
  return (
    <div className={cn("space-y-6", className)}>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <AnalyticsCard
          title="User Growth"
          value={`Clients: ${data.userGrowth.clients}, Writers: ${data.userGrowth.writers}`}
          description="New users this month"
          icon={<IconTrendingUp className="h-4 w-4" />}
          trend={{
            value: data.growthRate,
            isPositive: data.growthRate > 0,
          }}
        />
        <AnalyticsCard
          title="Growth Rate"
          value={`${data.growthRate.toFixed(1)}%`}
          description="Month-over-month"
          icon={<IconPercentage className="h-4 w-4" />}
          trend={{
            value: data.growthRate,
            isPositive: data.growthRate > 0,
          }}
        />
        <AnalyticsCard
          title="Conversion Rate"
          value={`${data.conversionRate.toFixed(1)}%`}
          description="Visitors to registered users"
          icon={<IconPercentage className="h-4 w-4" />}
        />
        <AnalyticsCard
          title="Retention Rate"
          value={`${data.retentionRate.toFixed(1)}%`}
          description="User retention after 30 days"
          icon={<IconRefresh className="h-4 w-4" />}
        />
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <LineChartComponent
          title="User Growth Trends"
          description="Monthly growth in users, clients, and writers"
          data={data.growthByMonth.map(item => ({
            ...item,
            date: `${item.date}-01`, // Convert to YYYY-MM-DD format
          }))}
          categories={[
            { name: "users", color: "hsl(var(--primary))" },
            { name: "clients", color: "hsl(var(--secondary))" },
            { name: "writers", color: "hsl(var(--accent))" },
          ]}
        />
        <BarChartComponent
          title="Growth by Acquisition Channel"
          description="New users by acquisition source"
          data={data.marketingEffectiveness.map(item => ({
            name: item.channel,
            visitors: item.visitors,
            conversions: item.conversions,
          }))}
          categories={[
            { name: "visitors", color: "hsl(var(--primary))" },
            { name: "conversions", color: "hsl(var(--secondary))" },
          ]}
        />
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Retention vs Churn</CardTitle>
            <CardDescription>Monthly user retention and churn rates</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <BarChartNegative
                title=""
                data={data.growthByMonth.map(item => ({
                  name: item.date,
                  value: item.clients > 0 ? item.clients : -Math.abs(item.clients),
                }))}
              />
            </div>
          </CardContent>
        </Card>
        <PieChartComponent
          title="Traffic Sources"
          description="Visitors by referral source"
          data={data.marketingEffectiveness.map(item => ({
            name: item.channel,
            value: item.visitors,
          }))}
        />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Marketing Effectiveness</CardTitle>
          <CardDescription>ROI by marketing channel</CardDescription>
        </CardHeader>
        <CardContent>
          <DataTable columns={marketingColumns} data={data.marketingEffectiveness} />
        </CardContent>
      </Card>
    </div>
  );
}
