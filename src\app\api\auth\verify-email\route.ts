import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

export async function GET(req: NextRequest): Promise<NextResponse> {
  const { searchParams } = new URL(req.url);
  const token = searchParams.get("token");

  // Get base URL from environment or request
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || `${req.nextUrl.protocol}//${req.nextUrl.host}`;

  if (!token) {
    return NextResponse.redirect(new URL("/login?error=MissingToken", baseUrl));
  }

  const record = await prisma.emailVerificationToken.findUnique({ where: { token } });
  if (!record || record.expires < new Date()) {
    return NextResponse.redirect(new URL("/login?error=InvalidOrExpiredToken", baseUrl));
  }

  console.log("✅ Email verification - updating user:", { userId: record.userId, token });

  // Mark user as verified
  const updatedUser = await prisma.user.update({
    where: { id: record.userId },
    data: { emailVerified: true },
  });

  console.log("✅ User email verification updated successfully:", {
    userId: updatedUser.id,
    email: updatedUser.email,
    emailVerified: updatedUser.emailVerified,
    role: updatedUser.role
  });

  // Delete token
  await prisma.emailVerificationToken.delete({ where: { token } });

  // Fetch user to determine role and redirect appropriately
  const user = await prisma.user.findUnique({ where: { id: record.userId } });
  let redirectPath = "/login?success=EmailVerified";

  if (user?.role === "WRITER") {
    // For writers, check if they are approved to determine redirect path
    const isApproved = user.isApproved;
    redirectPath = isApproved ? "/writer/dashboard" : "/writer-assessment";
  } else if (user?.role === "CLIENT") {
    redirectPath = "/client/dashboard";
  } else if (user?.role === "ADMIN") {
    redirectPath = "/admin/dashboard";
  }

  // Add a query parameter to indicate successful verification
  // This will help the frontend know to refresh the session
  const redirectUrl = new URL(redirectPath, baseUrl);
  redirectUrl.searchParams.set('emailVerified', 'true');

  console.log("🔄 Redirecting to:", redirectUrl.toString());

  // Use absolute URL for redirect
  return NextResponse.redirect(redirectUrl);
}
