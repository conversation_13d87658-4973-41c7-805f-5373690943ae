"use client";

import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { Clock } from "lucide-react";

export function HowItWorksTimeline() {
  return (
    <div className="container mx-auto px-4">
      <div className="max-w-4xl mx-auto text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <Badge 
            variant="secondary" 
            className="px-4 py-2 text-sm font-medium bg-primary/10 text-primary border-primary/20 mb-4"
          >
            <Clock className="w-4 h-4 mr-2" />
            Timeline
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
            From Order to Delivery
          </h2>
          <p className="text-lg text-muted-foreground">
            Track your order progress with our transparent timeline system
          </p>
        </motion.div>
      </div>
    </div>
  );
}
