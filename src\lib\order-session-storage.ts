import { OrderSessionData } from "@/types/order";

const ORDER_STORAGE_KEY = "pending_order_data";
const ORDER_EXPIRY_HOURS = 24; // Order data expires after 24 hours

/**
 * Utility class for managing order data in session storage
 * Used to persist order data while user registers
 */
export class OrderSessionStorage {
  /**
   * Save order data to session storage
   */
  static saveOrderData(orderData: OrderSessionData): void {
    try {
      const dataWithTimestamp = {
        ...orderData,
        timestamp: Date.now(),
      };
      
      if (typeof window !== "undefined") {
        sessionStorage.setItem(ORDER_STORAGE_KEY, JSON.stringify(dataWithTimestamp));
      }
    } catch (error) {
      console.error("Failed to save order data to session storage:", error);
    }
  }

  /**
   * Retrieve order data from session storage
   */
  static getOrderData(): OrderSessionData | null {
    try {
      if (typeof window === "undefined") {
        return null;
      }

      const storedData = sessionStorage.getItem(ORDER_STORAGE_KEY);
      if (!storedData) {
        return null;
      }

      const parsedData: OrderSessionData = JSON.parse(storedData);
      
      // Check if data has expired
      if (this.isOrderDataExpired(parsedData.timestamp)) {
        this.clearOrderData();
        return null;
      }

      return parsedData;
    } catch (error) {
      console.error("Failed to retrieve order data from session storage:", error);
      this.clearOrderData(); // Clear corrupted data
      return null;
    }
  }

  /**
   * Clear order data from session storage
   */
  static clearOrderData(): void {
    try {
      if (typeof window !== "undefined") {
        sessionStorage.removeItem(ORDER_STORAGE_KEY);
      }
    } catch (error) {
      console.error("Failed to clear order data from session storage:", error);
    }
  }

  /**
   * Check if order data exists in session storage
   */
  static hasOrderData(): boolean {
    try {
      if (typeof window === "undefined") {
        return false;
      }

      const storedData = sessionStorage.getItem(ORDER_STORAGE_KEY);
      if (!storedData) {
        return false;
      }

      const parsedData: OrderSessionData = JSON.parse(storedData);
      
      // Check if data has expired
      if (this.isOrderDataExpired(parsedData.timestamp)) {
        this.clearOrderData();
        return false;
      }

      return true;
    } catch (error) {
      console.error("Failed to check order data existence:", error);
      this.clearOrderData(); // Clear corrupted data
      return false;
    }
  }

  /**
   * Update specific fields in stored order data
   */
  static updateOrderData(updates: Partial<OrderSessionData>): void {
    try {
      const existingData = this.getOrderData();
      if (!existingData) {
        console.warn("No existing order data to update");
        return;
      }

      const updatedData = {
        ...existingData,
        ...updates,
        timestamp: Date.now(), // Update timestamp
      };

      this.saveOrderData(updatedData);
    } catch (error) {
      console.error("Failed to update order data:", error);
    }
  }

  /**
   * Check if order data has expired
   */
  private static isOrderDataExpired(timestamp: number): boolean {
    const now = Date.now();
    const expiryTime = timestamp + (ORDER_EXPIRY_HOURS * 60 * 60 * 1000);
    return now > expiryTime;
  }

  /**
   * Get remaining time before order data expires (in minutes)
   */
  static getRemainingTime(): number {
    try {
      const orderData = this.getOrderData();
      if (!orderData) {
        return 0;
      }

      const now = Date.now();
      const expiryTime = orderData.timestamp + (ORDER_EXPIRY_HOURS * 60 * 60 * 1000);
      const remainingMs = expiryTime - now;
      
      return Math.max(0, Math.floor(remainingMs / (60 * 1000))); // Convert to minutes
    } catch (error) {
      console.error("Failed to calculate remaining time:", error);
      return 0;
    }
  }

  /**
   * Create order data from form data and uploaded files
   */
  static createOrderSessionData(
    formData: Partial<OrderSessionData>,
    uploadedFiles: Array<{
      id: string;
      name: string;
      url: string;
      size: number;
      type: string;
    }>,
    calculatedPrice: number
  ): OrderSessionData {
    return {
      ...formData,
      uploadedFiles,
      calculatedPrice,
      timestamp: Date.now(),
    } as OrderSessionData;
  }

  /**
   * Validate order data structure
   */
  static validateOrderData(data: unknown): data is OrderSessionData {
    try {
      return (
        data !== null &&
        data !== undefined &&
        typeof data === "object" &&
        typeof (data as Record<string, unknown>).title === "string" &&
        typeof (data as Record<string, unknown>).description === "string" &&
        typeof (data as Record<string, unknown>).pageCount === "number" &&
        typeof (data as Record<string, unknown>).calculatedPrice === "number" &&
        typeof (data as Record<string, unknown>).timestamp === "number" &&
        Array.isArray((data as Record<string, unknown>).uploadedFiles)
      );
    } catch (error) {
      console.error("Failed to validate order data:", error);
      return false;
    }
  }

  /**
   * Get order summary for display
   */
  static getOrderSummary(): {
    title: string;
    pageCount: number;
    price: number;
    fileCount: number;
    timeRemaining: number;
  } | null {
    try {
      const orderData = this.getOrderData();
      if (!orderData) {
        return null;
      }

      return {
        title: orderData.title,
        pageCount: orderData.pageCount,
        price: orderData.calculatedPrice,
        fileCount: orderData.uploadedFiles.length,
        timeRemaining: this.getRemainingTime(),
      };
    } catch (error) {
      console.error("Failed to get order summary:", error);
      return null;
    }
  }
}

/**
 * Hook for using order session storage in React components
 */
export function useOrderSessionStorage() {
  const saveOrder = (orderData: OrderSessionData) => {
    OrderSessionStorage.saveOrderData(orderData);
  };

  const getOrder = () => {
    return OrderSessionStorage.getOrderData();
  };

  const clearOrder = () => {
    OrderSessionStorage.clearOrderData();
  };

  const hasOrder = () => {
    return OrderSessionStorage.hasOrderData();
  };

  const updateOrder = (updates: Partial<OrderSessionData>) => {
    OrderSessionStorage.updateOrderData(updates);
  };

  const getOrderSummary = () => {
    return OrderSessionStorage.getOrderSummary();
  };

  const getRemainingTime = () => {
    return OrderSessionStorage.getRemainingTime();
  };

  return {
    saveOrder,
    getOrder,
    clearOrder,
    hasOrder,
    updateOrder,
    getOrderSummary,
    getRemainingTime,
  };
}
