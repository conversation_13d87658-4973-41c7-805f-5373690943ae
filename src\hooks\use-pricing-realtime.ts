"use client";

import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { AcademicLevel, Priority, Spacing } from '@prisma/client';

export interface PricingParams {
  academicLevel: AcademicLevel;
  priority: Priority;
  spacing: Spacing;
  pageCount: number;
}

export interface PriceBreakdown {
  basePrice: number;
  academicLevelMultiplier: number;
  priorityMultiplier: number;
  spacingMultiplier: number;
  subtotal: number;
  finalPrice: number;
  minimumPrice: number;
}

export interface WriterCompensation {
  percentage: number;
  minimumPerPage: number;
  calculatedAmount: number;
  finalAmount: number;
}

export interface PricingData {
  priceBreakdown: PriceBreakdown;
  writerCompensation: WriterCompensation;
  finalPrice: number;
}

// Global event emitter for real-time updates
class PricingEventEmitter {
  private listeners: Set<() => void> = new Set();

  subscribe(callback: () => void): () => void {
    this.listeners.add(callback);
    return () => {
      this.listeners.delete(callback);
    };
  }

  emit(): void {
    this.listeners.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('Error in pricing update callback:', error);
      }
    });
  }
}

const pricingEventEmitter = new PricingEventEmitter();

// Expose global function for triggering updates
if (typeof window !== 'undefined') {
  (window as Window & { triggerPricingUpdate?: () => void }).triggerPricingUpdate = () => {
    pricingEventEmitter.emit();
  };
}

export function usePricingRealtime(params: PricingParams) {
  const [pricingData, setPricingData] = useState<PricingData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Memoize params to prevent unnecessary re-renders
  const memoizedParams = useMemo(() => ({
    academicLevel: params.academicLevel,
    priority: params.priority,
    spacing: params.spacing,
    pageCount: params.pageCount,
  }), [params.academicLevel, params.priority, params.spacing, params.pageCount]);

  // Debounce timer ref
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const lastParamsRef = useRef<string>('');

  const calculatePricing = useCallback(async (forceUpdate = false) => {
    try {
      // Create a stable key for params comparison
      const paramsKey = JSON.stringify(memoizedParams);

      // Skip if params haven't changed and it's not a forced update
      if (!forceUpdate && lastParamsRef.current === paramsKey) {
        return;
      }

      lastParamsRef.current = paramsKey;

      setLoading(true);
      setError(null);

      const response = await fetch("/api/pricing/calculate", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(memoizedParams),
      });

      if (!response.ok) {
        throw new Error("Failed to calculate pricing");
      }

      const data = await response.json();
      setPricingData(data.data);
      setLastUpdated(new Date());
    } catch (err) {
      console.error('Error calculating pricing:', err);
      setError(err instanceof Error ? err.message : 'Failed to calculate pricing');
    } finally {
      setLoading(false);
    }
  }, [memoizedParams]);

  // Debounced calculation for parameter changes
  const debouncedCalculatePricing = useCallback(() => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    debounceTimerRef.current = setTimeout(() => {
      calculatePricing();
    }, 300); // 300ms debounce
  }, [calculatePricing]);

  // Initial calculation and parameter change handling
  useEffect(() => {
    debouncedCalculatePricing();

    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [debouncedCalculatePricing]);

  // Subscribe to pricing updates for real-time changes (admin updates)
  useEffect(() => {
    const unsubscribe = pricingEventEmitter.subscribe(() => {
      console.log('🔄 Pricing update triggered - recalculating...');
      calculatePricing(true); // Force update for admin changes
    });

    return unsubscribe;
  }, [calculatePricing]);

  return {
    pricingData,
    loading,
    error,
    lastUpdated,
    refetch: calculatePricing,
  };
}

// Hook for just price calculation without writer compensation
export function usePriceCalculation(params: PricingParams) {
  const [priceBreakdown, setPriceBreakdown] = useState<PriceBreakdown | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Memoize params to prevent unnecessary re-renders
  const memoizedParams = useMemo(() => ({
    academicLevel: params.academicLevel,
    priority: params.priority,
    spacing: params.spacing,
    pageCount: params.pageCount,
  }), [params.academicLevel, params.priority, params.spacing, params.pageCount]);

  // Debounce timer ref
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const lastParamsRef = useRef<string>('');

  const calculatePrice = useCallback(async (forceUpdate = false) => {
    try {
      // Create a stable key for params comparison
      const paramsKey = JSON.stringify(memoizedParams);

      // Skip if params haven't changed and it's not a forced update
      if (!forceUpdate && lastParamsRef.current === paramsKey) {
        return;
      }

      lastParamsRef.current = paramsKey;

      setLoading(true);
      setError(null);

      const response = await fetch("/api/pricing/calculate", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(memoizedParams),
      });

      if (!response.ok) {
        throw new Error("Failed to calculate price");
      }

      const data = await response.json();
      setPriceBreakdown(data.data.priceBreakdown);
    } catch (err) {
      console.error('Error calculating price:', err);
      setError(err instanceof Error ? err.message : 'Failed to calculate price');
    } finally {
      setLoading(false);
    }
  }, [memoizedParams]);

  // Debounced calculation for parameter changes
  const debouncedCalculatePrice = useCallback(() => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    debounceTimerRef.current = setTimeout(() => {
      calculatePrice();
    }, 300); // 300ms debounce
  }, [calculatePrice]);

  // Initial calculation and parameter change handling
  useEffect(() => {
    debouncedCalculatePrice();

    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [debouncedCalculatePrice]);

  // Subscribe to pricing updates for real-time changes (admin updates)
  useEffect(() => {
    const unsubscribe = pricingEventEmitter.subscribe(() => {
      calculatePrice(true); // Force update for admin changes
    });

    return unsubscribe;
  }, [calculatePrice]);

  return {
    priceBreakdown,
    loading,
    error,
    refetch: calculatePrice,
  };
}

// Hook for real-time pricing rules (for admin interface)
export function usePricingRules() {
  const [rules, setRules] = useState<Array<{
    id: string;
    ruleType: string;
    academicLevel?: AcademicLevel;
    priority?: Priority;
    spacing?: Spacing;
    value: number;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
  }>>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchRules = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/admin/pricing');
      if (!response.ok) {
        throw new Error('Failed to fetch pricing rules');
      }

      const data = await response.json();
      setRules(data.data || []);
    } catch (err) {
      console.error('Error fetching pricing rules:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch pricing rules');
    } finally {
      setLoading(false);
    }
  }, []);

  // Initial fetch
  useEffect(() => {
    fetchRules();
  }, [fetchRules]);

  // Subscribe to pricing updates for real-time changes
  useEffect(() => {
    const unsubscribe = pricingEventEmitter.subscribe(() => {
      fetchRules();
    });

    return unsubscribe;
  }, [fetchRules]);

  return {
    rules,
    loading,
    error,
    refetch: fetchRules,
  };
}

// Export function to trigger pricing updates globally
export function triggerPricingUpdate() {
  pricingEventEmitter.emit();
}
