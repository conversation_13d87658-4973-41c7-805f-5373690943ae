
'use client';

import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import { DynamicBreadcrumbs } from "@/components/layout/DynamicBreadcrumbs";

// Custom labels for specific breadcrumbs paths
const pathMap = {
  blog: "Blog",
  // We'll dynamically add the blog post title
};

// Exclude paths that shouldn't appear in breadcrumbs
const excludePaths = [
  "api",
  "_next",
  "static",
  "public",
  "favicon.ico",
  "robots.txt",
  "sitemap.xml",
  "images",
  "fonts",
  "styles",
  "scripts",
  "assets",
];

export default function BlogPostLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const [dynamicPathMap, setDynamicPathMap] = useState(pathMap);

  useEffect(() => {
    // Extract the blog post slug from the URL
    const segments = pathname?.split('/') || [];
    const slug = segments[segments.length - 1];
    
    if (slug) {
      // Fetch the blog post title
      const fetchPostTitle = async () => {
        try {
          const response = await fetch(`/api/blog/by-slug/${slug}`);
          if (response.ok) {
            const post = await response.json();
            
            // Update the path map with the post title
            setDynamicPathMap(prev => ({
              ...prev,
              [slug]: post.title
            }));
          }
        } catch (error) {
          console.error('Error fetching post title:', error);
        }
      };
      
      fetchPostTitle();
    }
  }, [pathname]);

  return (
    <>
      <div className="ml-2 md:ml-8">
        <DynamicBreadcrumbs pathMap={dynamicPathMap} excludePaths={excludePaths} />
      </div>
      {children}
    </>
  );
}
