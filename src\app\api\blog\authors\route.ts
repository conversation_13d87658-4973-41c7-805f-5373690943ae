import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { z } from "zod";
import { checkPermission, apiSuccess, apiError } from "@/lib/api-utils";

// Validation schema for author
const AuthorSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").max(100, "Name must be less than 100 characters"),
  qualifications: z.string().min(10, "Qualifications must be at least 10 characters").max(500, "Qualifications must be less than 500 characters"),
});

export async function GET() {
  try {
    // Fetch all authors
    const authors = await prisma.author.findMany({
      select: {
        id: true,
        name: true,
        qualifications: true,
        _count: {
          select: {
            blogs: true,
          },
        },
      },
      orderBy: { name: "asc" },
    });

    return NextResponse.json(authors);
  } catch (error) {
    console.error("Error fetching authors:", error);
    return apiError("Failed to fetch authors", 500);
  }
}

export async function POST(req: NextRequest) {
  try {
    // Check admin permission
    const permissionError = await checkPermission(["ADMIN"]);
    if (permissionError) return permissionError;

    const body = await req.json();
    const parsed = AuthorSchema.safeParse(body);

    if (!parsed.success) {
      return apiError("Invalid input data", 400, parsed.error.flatten().fieldErrors);
    }

    const { name, qualifications } = parsed.data;

    // Check if author with same name already exists
    const existingAuthor = await prisma.author.findUnique({
      where: { name },
    });

    if (existingAuthor) {
      return apiError("Author with this name already exists", 409);
    }

    const author = await prisma.author.create({
      data: {
        name,
        qualifications,
      },
    });

    return apiSuccess(author, "Author created successfully");
  } catch (error) {
    console.error("Error creating author:", error);
    return apiError("Failed to create author", 500);
  }
}
