// src/components/landing-page/types.ts
import { ReactNode } from "react";

export interface TestimonialType {
  id: number;
  name: string;
  role: string;
  content: string;
  rating: number;
}

export interface ServiceType {
  id: number;
  title: string;
  description: string;
  icon: ReactNode;
  link: string;
}

export interface FeatureType {
  id: number;
  title: string;
  description: string;
  icon: ReactNode;
  stat?: {
    value: string;
    label: string;
  };
}

export interface ProgramType {
  id: number;
  title: string;
  description: string;
  category: string;
  features: string[];
  image?: string;
  link: string;
}

export interface FAQType {
  question: string;
  answer: string;
}