import { NextRequest, NextResponse } from "next/server";
import  prisma  from "@/lib/prisma";
import { Resend } from "resend";
import { newsletterSubscribeSchema } from "@/lib/validations";
import { parseRequestBody, apiSuccess, apiError } from "@/lib/api-utils";
import type { NewsletterSubscribeRequest } from "@/types/api";

const resend = new Resend(process.env.RESEND_API_KEY);
const fromAddress = process.env.RESEND_VERIFIED_DOMAIN
  ? `noreply@${process.env.RESEND_VERIFIED_DOMAIN}`
  : "<EMAIL>";

function getWelcomeEmailTemplate(email: string): string {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Welcome to Our Newsletter</title>
      <style>
        body {
          margin: 0;
          padding: 0;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
          line-height: 1.6;
          color: #333333;
          background-color: #f8f9fa;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          background-color: #ffffff;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          padding: 40px 30px;
          text-align: center;
        }
        .header h1 {
          color: #ffffff;
          margin: 0;
          font-size: 28px;
          font-weight: 600;
        }
        .content {
          padding: 40px 30px;
        }
        .welcome-text {
          font-size: 18px;
          color: #2d3748;
          margin-bottom: 20px;
          text-align: center;
        }
        .description {
          font-size: 16px;
          color: #4a5568;
          margin-bottom: 30px;
          text-align: center;
          line-height: 1.7;
        }
        .benefits {
          background-color: #f7fafc;
          padding: 20px;
          border-radius: 6px;
          margin: 20px 0;
        }
        .benefits h3 {
          color: #2d3748;
          margin-top: 0;
        }
        .benefits ul {
          margin: 0;
          padding-left: 20px;
        }
        .benefits li {
          color: #4a5568;
          margin-bottom: 8px;
        }
        .footer {
          background-color: #f7fafc;
          padding: 30px;
          text-align: center;
          border-top: 1px solid #e2e8f0;
        }
        .footer p {
          margin: 0;
          font-size: 14px;
          color: #718096;
        }
        .unsubscribe {
          margin-top: 20px;
          font-size: 12px;
          color: #a0aec0;
        }
        .unsubscribe a {
          color: #667eea;
          text-decoration: none;
        }
        @media (max-width: 600px) {
          .container {
            margin: 0 10px;
          }
          .header, .content, .footer {
            padding: 20px;
          }
          .header h1 {
            font-size: 24px;
          }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🎉 Welcome to Our Newsletter!</h1>
        </div>
        
        <div class="content">
          <p class="welcome-text">Thank you for subscribing!</p>
          
          <p class="description">
            You've successfully joined our newsletter community. We're excited to share valuable content, 
            updates, and insights directly to your inbox.
          </p>
          
          <div class="benefits">
            <h3>What to expect:</h3>
            <ul>
              <li>📚 Latest academic writing tips and guides</li>
              <li>🎓 Educational resources and study materials</li>
              <li>💡 Expert insights and industry updates</li>
              <li>🎁 Exclusive offers and promotions</li>
              <li>📰 Weekly digest of our best content</li>
            </ul>
          </div>
          
          <p class="description">
            We respect your inbox and will only send you valuable content. 
            You can unsubscribe at any time using the link below.
          </p>
        </div>
        
        <div class="footer">
          <p>
            This email was sent to ${email}<br>
            Academic App Newsletter
          </p>
          <div class="unsubscribe">
            <a href="${process.env.NEXT_PUBLIC_BASE_URL}/newsletter/unsubscribe?email=${encodeURIComponent(email)}">
              Unsubscribe from this newsletter
            </a>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
}

export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Parse and validate request body
    const body = await parseRequestBody(req, newsletterSubscribeSchema);
    if ('success' in body && !body.success) {
      return apiError(body.message, 400, body.errors);
    }

    const { email, source } = body as NewsletterSubscribeRequest;

    // Check if email already exists
    const existingSubscription = await prisma.newsletterSubscription.findUnique({
      where: { email }
    });

    let subscription;

    if (existingSubscription) {
      if (existingSubscription.isActive) {
        return apiError("Email is already subscribed to our newsletter", 409);
      } else {
        // Reactivate subscription
        subscription = await prisma.newsletterSubscription.update({
          where: { email },
          data: {
            isActive: true,
            subscribedAt: new Date(),
            unsubscribedAt: null,
            source: source || "unknown"
          }
        });
      }
    } else {
      // Create new subscription
      subscription = await prisma.newsletterSubscription.create({
        data: {
          email,
          source: source || "unknown"
        }
      });
    }

    // Send welcome email
    try {
      await resend.emails.send({
        from: fromAddress,
        to: email,
        subject: "Welcome to Our Newsletter! 🎉",
        html: getWelcomeEmailTemplate(email),
      });
    } catch (emailError) {
      console.error("Failed to send welcome email:", emailError);
      // Don't fail the subscription if email fails
    }

    return apiSuccess(subscription, "Successfully subscribed to newsletter");

  } catch (error) {
    console.error("Newsletter subscription error:", error);
    return apiError("Internal server error", 500);
  }
}
