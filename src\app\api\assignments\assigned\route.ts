// src/app/api/assignments/assigned/route.ts
import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import {
  apiSuccess,
  apiError,
  getCurrentUserId,
  getCurrentUserRole,
} from "@/lib/api-utils";
import { paginationSchema } from "@/lib/validations";
import type { AssignmentResponse } from "@/types/api";
import {
  AssignmentStatus,
  AssignmentType,
  Prisma,
  UserRole,
} from "@prisma/client";

// Get assignments assigned to the current writer
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    const currentUserId = await getCurrentUserId();
    const userRole = await getCurrentUserRole();

    if (!currentUserId) {
      return apiError("Authentication required", 401);
    }

    // Only writers can access this endpoint
    if (userRole !== UserRole.WRITER) {
      return apiError("Access denied. Writers only.", 403);
    }

    // Extract pagination and search parameters
    const url = new URL(req.url);
    const pageParam = url.searchParams.get("page") ?? "1";
    const limitParam =
      url.searchParams.get("perPage") ?? url.searchParams.get("limit") ?? "10";
    const search = url.searchParams.get("search") ?? "";
    const status = url.searchParams.get("status");
    const assignmentType = url.searchParams.get("type");
    const subject = url.searchParams.get("subject");

    // New parameter for filtering by specific statuses (comma-separated)
    const statusFilter = url.searchParams.get("statusFilter"); // e.g., "COMPLETED" or "COMPLETED,REJECTED"

    // API REVISION: New parameter to exclude completed/rejected from main page
    const excludeCompleted =
      url.searchParams.get("excludeCompleted") === "true";

    // Parse and validate pagination parameters
    let parsedPage: number;
    let parsedLimit: number;

    try {
      parsedPage = parseInt(pageParam, 10);
      parsedLimit = parseInt(limitParam, 10);

      // Validate page and limit values
      if (isNaN(parsedPage) || parsedPage < 1) {
        parsedPage = 1;
      }
      if (isNaN(parsedLimit) || parsedLimit < 1) {
        parsedLimit = 10;
      }

      // Set reasonable limits to prevent abuse
      if (parsedLimit > 100) {
        parsedLimit = 100;
      }
    } catch (error) {
      console.error("Error parsing pagination parameters:", error);
      parsedPage = 1;
      parsedLimit = 10;
    }

    const { page, limit } = paginationSchema.parse({
      page: parsedPage,
      limit: parsedLimit,
    });

    const skip = (page - 1) * limit;

    // Build where conditions - ONLY assignments assigned to current writer
    const whereConditions: Prisma.AssignmentWhereInput = {
      assignedWriterId: currentUserId, // This is the key filter
    };

    // Add search condition if provided
    if (search) {
      whereConditions.OR = [
        { title: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
        { subject: { contains: search, mode: "insensitive" } },
      ];
    }

    // API REVISION: Handle exclusion of completed/rejected statuses for main page
    if (excludeCompleted) {
      // For main page: only show PENDING, IN_PROGRESS, and REVISION statuses
      whereConditions.status = {
        in: [
          AssignmentStatus.PENDING,
          AssignmentStatus.ASSIGNED,
          AssignmentStatus.REVISION,
        ],
      };
    } else {
      // Handle status filtering - priority order: statusFilter > status (existing logic)
      if (statusFilter) {
        // Parse comma-separated status values for specific filtering (e.g., "COMPLETED,REJECTED")
        const statusArray = statusFilter
          .split(",")
          .map((s) => s.trim().toUpperCase())
          .filter((s) =>
            Object.values(AssignmentStatus).includes(s as AssignmentStatus)
          ) as AssignmentStatus[];

        if (statusArray.length > 0) {
          if (statusArray.length === 1) {
            whereConditions.status = statusArray[0];
          } else {
            whereConditions.status = { in: statusArray };
          }
        }
      } else if (
        // Fallback to single status filter if no statusFilter provided
        status &&
        Object.values(AssignmentStatus).includes(status as AssignmentStatus)
      ) {
        whereConditions.status = status as AssignmentStatus;
      }
    }

    // Add assignmentType filter if provided
    if (
      assignmentType &&
      Object.values(AssignmentType).includes(assignmentType as AssignmentType)
    ) {
      whereConditions.assignmentType = assignmentType as AssignmentType;
    }

    // Add subject filter if provided
    if (subject) {
      whereConditions.subject = subject;
    }

    // Get total count FIRST before applying pagination
    const totalCount = await prisma.assignment.count({
      where: whereConditions,
    });

    // Get assignments with the correct limit value
    const assignments = await prisma.assignment.findMany({
      where: whereConditions,
      skip,
      take: limit,
      orderBy: { createdAt: "desc" },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            email: true,
            accountId: true, // Add this
          },
        },
        assignedWriter: {
          select: {
            id: true,
            name: true,
            email: true,
            accountId: true,
            rating: true,
            competencies: true,
          },
        },
        _count: {
          select: { bids: true },
        },
      },
    });

    // Format the response
    const formattedAssignments: AssignmentResponse[] = assignments.map(
      (assignment) => ({
        id: assignment.id,
        taskId: assignment.taskId,
        title: assignment.title,
        description: assignment.description,
        assignmentType: assignment.assignmentType,
        subject: assignment.subject,
        service: assignment.service,
        pageCount: assignment.pageCount,
        priority: assignment.priority,
        academicLevel: assignment.academicLevel,
        spacing: assignment.spacing,
        languageStyle: assignment.languageStyle,
        formatStyle: assignment.formatStyle,
        numSources: assignment.numSources,
        guidelines: assignment.guidelines,
        estTime: assignment.estTime.toISOString(),
        price: assignment.price,
        clientId: assignment.clientId,
        assignedWriterId: assignment.assignedWriterId,
        status: assignment.status,
        paymentStatus: assignment.paymentStatus,
        paypalOrderId: assignment.paypalOrderId,
        paypalPayerId: assignment.paypalPayerId,
        paypalPaymentId: assignment.paypalPaymentId,
        assignedWriter: assignment.assignedWriter
          ? {
              id: assignment.assignedWriter.id,
              name: assignment.assignedWriter.name,
              email: assignment.assignedWriter.email,
              accountId: assignment.assignedWriter.accountId,
              rating: assignment.assignedWriter.rating,
              competencies: assignment.assignedWriter.competencies,
            }
          : undefined,
        updatedAt: assignment.updatedAt.toISOString(),
        createdAt: assignment.createdAt.toISOString(),
        client: assignment.client,
        bidCount: assignment._count.bids,
      })
    );

    // Return the data with correct pagination info
    return apiSuccess({
      assignments: formattedAssignments,
      totalCount: totalCount,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching assigned assignments:", error);
    return apiError("Failed to fetch assigned assignments", 500);
  }
}
