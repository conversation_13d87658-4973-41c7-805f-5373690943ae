import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authConfig as authOptions } from "@/auth";
import prisma from "@/lib/prisma";
import { assessmentToggleActiveSchema } from "@/lib/validations";

// PATCH: Toggle assessment active status
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  const session = await getServerSession(authOptions);
  const { id } = await params;

  // Check if user is admin
  if (session?.user?.role !== "ADMIN") {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await req.json();
    const validatedData = assessmentToggleActiveSchema.parse(body);

    // If setting this assessment to active, first deactivate all other assessments
    if (validatedData.isActive) {
      await prisma.assessment.updateMany({
        where: { 
          id: { not: id },
          isActive: true 
        },
        data: { isActive: false },
      });
    }

    // Update the target assessment
    const updatedAssessment = await prisma.assessment.update({
      where: { id },
      data: { isActive: validatedData.isActive },
    });

    return NextResponse.json(updatedAssessment);
  } catch (error) {
    console.error("Error toggling assessment active status:", error);
    return NextResponse.json(
      { error: "Failed to toggle assessment active status" },
      { status: 500 }
    );
  }
}
