// src/lib/api-utils.ts
import { NextRequest, NextResponse } from "next/server";
import { Zod<PERSON><PERSON><PERSON>, Zod<PERSON>chema, ZodIssue } from "zod";
import { ApiError, ApiResponse, UserFilters, TodoFilters } from "@/types/api";
import { getSession } from "@/lib/auth-utils";
import { UserRole, Priority, AcademicLevel, Spacing } from "@prisma/client";

// Coupon types
export interface Coupon {
  id: string;
  code: string;
  description: string;
  discountPercentage: number;
  isActive: boolean;
  maxUses?: number;
  currentUses: number;
  expiresAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  usageCount?: number;
}

// Company Info types
export interface CompanyInfo {
  id: string;
  companyName: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  phone: string;
  tollFreePhone?: string | null;
  internationalPhone?: string | null;
  supportEmail: string;
  inquiriesEmail: string;
  businessHours: string;
  description?: string | null;
  website?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface CouponUsage {
  id: string;
  couponId: string;
  userId: string;
  assignmentId?: string;
  discountAmount: number;
  originalPrice: number;
  finalPrice: number;
  usedAt: Date;
  coupon?: {
    code: string;
    description: string;
    discountPercentage: number;
  };
  assignment?: {
    id: string;
    title: string;
    taskId: string;
  };
}

// Pricing rule types
export interface PricingRule {
  id: string;
  ruleType: string;
  academicLevel?: AcademicLevel;
  priority?: Priority;
  spacing?: Spacing;
  value: number;
  isActive: boolean;
  ruleKey: string;
  createdAt: Date;
  updatedAt: Date;
}

// Writer payment types
export interface WriterPayment {
  id: string;
  assignmentId: string;
  writerId: string;
  writerCompensation: number;
  isWriterPaid: boolean;
  writerPaypalEmail?: string;
  writerPaymentDate?: Date;
  writerPaypalOrderId?: string;
  writerPaypalPaymentId?: string;
  assignment: {
    id: string;
    title: string;
    taskId: string;
    status: string;
    createdAt: Date;
    updatedAt: Date;
  };
  writer: {
    id: string;
    name: string | null;
    email: string;
    phone: string | null;
  };
}

// Type for Prisma where clauses
type UserWhereClause = {
  role?: UserRole;
  isApproved?: boolean;
  accountId?: string | null;
};

type TodoWhereClause = {
  userId?: string;
  isCompleted?: boolean;
  priority?: Priority;
  category?: {
    contains: string;
    mode: "insensitive";
  };
};

// Type for user data with password field (for sanitization)
type UserWithPassword = {
  password?: string;
  [key: string]: unknown;
};

// CLAUDE BUILD ERROR: - Ensuring type definitions are compatible with Next.js 15
export async function parseRequestBody<T>(
  request: NextRequest,
  schema: ZodSchema<T>
): Promise<T | ApiError> {
  try {
    const body = await request.json();
    return schema.parse(body);
  } catch (error) {
    if (error instanceof ZodError) {
      const errors: Record<string, string[]> = {};

      for (const issue of error.errors) {
        const path = issue.path.join(".");
        if (!errors[path]) {
          errors[path] = [];
        }
        errors[path].push(issue.message);
      }

      return {
        success: false,
        message: "Validation error",
        errors,
      };
    }

    return {
      success: false,
      message: "Failed to parse request body",
    };
  }
}

// Parse query parameters for filtering
export function parseQueryFilters(searchParams: URLSearchParams): {
  userFilters: UserFilters;
  todoFilters: TodoFilters;
  pagination: { page: number; limit: number };
} {
  const userFilters: UserFilters = {
    accountId: searchParams.get("accountId") || null,
  };
  const todoFilters: TodoFilters = {};

  // User filters
  const role = searchParams.get("role");
  if (role && Object.values(UserRole).includes(role as UserRole)) {
    userFilters.role = role as UserRole;
  }

  const isApproved = searchParams.get("isApproved");
  if (isApproved !== null) {
    userFilters.isApproved = isApproved === "true";
  }

  // Todo filters
  const isCompleted = searchParams.get("isCompleted");
  if (isCompleted !== null) {
    todoFilters.isCompleted = isCompleted === "true";
  }

  const priority = searchParams.get("priority");
  if (priority && Object.values(Priority).includes(priority as Priority)) {
    todoFilters.priority = priority as Priority;
  }

  const category = searchParams.get("category");
  if (category) {
    todoFilters.category = category;
  }

  // Pagination
  const page = Math.max(1, parseInt(searchParams.get("page") || "1"));
  const limit = Math.max(
    1,
    Math.min(100, parseInt(searchParams.get("limit") || "10"))
  );

  return {
    userFilters,
    todoFilters,
    pagination: { page, limit },
  };
}

// Create successful API response
export function apiSuccess<T>(
  data: T,
  message = "Success"
): NextResponse<ApiResponse<T>> {
  return NextResponse.json({
    success: true,
    message,
    data,
  });
}

// Transform Zod errors to API error format
export function formatZodErrors(zodErrors: ZodIssue[]): Record<string, string[]> {
  const formattedErrors: Record<string, string[]> = {};
  zodErrors.forEach((error) => {
    const path = error.path.length > 0 ? error.path.join('.') : 'root';
    if (!formattedErrors[path]) {
      formattedErrors[path] = [];
    }
    formattedErrors[path].push(error.message);
  });
  return formattedErrors;
}

// Create error API response
export function apiError(
  message: string,
  status = 400,
  errors?: Record<string, string[]>
): NextResponse<ApiError> {
  return NextResponse.json(
    {
      success: false,
      message,
      errors,
    },
    { status }
  );
}

export function isApiError(x: unknown): x is ApiError {
  return (
    typeof x === "object" &&
    x !== null &&
    "success" in x &&
    (x as ApiError).success === false
  );
}

// Check permissions for current user
export async function checkPermission(
  allowedRoles: UserRole[] | string[],
  errorMessage = "Unauthorized"
): Promise<NextResponse | null> {
  const session = await getSession();

  if (!session || !session.user) {
    return apiError("Authentication required", 401);
  }

  // Convert string roles to UserRole enum if needed
  const roles = allowedRoles.map(role =>
    typeof role === 'string' ? role as UserRole : role
  );

  // Check if user has one of the allowed roles
  const hasPermission = roles.some((role) => session.user.role === role);

  if (!hasPermission) {
    return apiError(errorMessage, 403);
  }

  return null;
}

// Check if user owns resource or is admin
export async function checkResourceOwnership(
  resourceUserId: string,
  errorMessage = "Access denied"
): Promise<NextResponse | null> {
  const session = await getSession();

  if (!session || !session.user) {
    return apiError("Authentication required", 401);
  }

  // Allow access if user owns the resource or is an admin
  if (
    session.user.id !== resourceUserId &&
    session.user.role !== UserRole.ADMIN
  ) {
    return apiError(errorMessage, 403);
  }

  return null;
}

// Extract user ID from session
export async function getCurrentUserId(): Promise<string | null> {
  const session = await getSession();
  return session?.user?.id || null;
}

// Extract user role from session
export async function getCurrentUserRole(): Promise<UserRole | null> {
  const session = await getSession();
  return session?.user?.role || null;
}

// Get current user session data
export async function getCurrentUser(): Promise<{
  id: string;
  role: UserRole;
  email: string;
  name?: string;
} | null> {
  const session = await getSession();
  if (!session || !session.user) {
    return null;
  }

  // Type guard to ensure email is defined
  if (!session.user.email) {
    return null;
  }

  return {
    id: session.user.id,
    role: session.user.role,
    email: session.user.email,
    name: session.user.name || undefined,
  };
}

// Check if current user is writer and approved
export async function checkApprovedWriter(): Promise<NextResponse | null> {
  const session = await getSession();

  if (!session || !session.user) {
    return apiError("Authentication required", 401);
  }

  if (session.user.role !== UserRole.WRITER) {
    return apiError("Writer access required", 403);
  }

  // Note: You may want to check isApproved status here if needed
  // This would require fetching user data from database

  return null;
}

// Validate date string format
export function validateDateString(dateString: string): Date | null {
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return null;
    }
    return date;
  } catch {
    return null;
  }
}

// Format date for API response
export function formatDateForApi(date: Date): string {
  return date.toISOString();
}

// Safe JSON date converter (for Date objects)
export function safeJsonDate(obj: unknown): unknown {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (obj instanceof Date) {
    return obj.toISOString();
  }

  if (Array.isArray(obj)) {
    return obj.map(safeJsonDate);
  }

  if (typeof obj === "object") {
    return Object.fromEntries(
      Object.entries(obj as Record<string, unknown>).map(([key, value]) => [
        key,
        safeJsonDate(value),
      ])
    );
  }

  return obj;
}

// Generate pagination metadata
export function createPaginationMeta(
  total: number,
  page: number,
  limit: number
) {
  return {
    page,
    limit,
    total,
    totalPages: Math.ceil(total / limit),
    hasNextPage: page < Math.ceil(total / limit),
    hasPreviousPage: page > 1,
  };
}

// Build Prisma where clause for filtering
export function buildUserWhereClause(filters: UserFilters): UserWhereClause {
  const where: UserWhereClause = {};

  if (filters.accountId) {
    where.accountId = filters.accountId;
  }

  if (filters.role) {
    where.role = filters.role;
  }

  if (filters.isApproved !== undefined) {
    where.isApproved = filters.isApproved;
  }

  return where;
}

export function buildTodoWhereClause(
  filters: TodoFilters,
  userId?: string
): TodoWhereClause {
  const where: TodoWhereClause = {};

  if (userId) {
    where.userId = userId;
  }

  if (filters.isCompleted !== undefined) {
    where.isCompleted = filters.isCompleted;
  }

  if (filters.priority) {
    where.priority = filters.priority;
  }

  if (filters.category) {
    where.category = {
      contains: filters.category,
      mode: "insensitive",
    };
  }

  return where;
}

// Sanitize user data for API response (remove sensitive fields)
export function sanitizeUserForApi<T extends UserWithPassword>(
  user: T
): Omit<T, "password"> {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { password, ...sanitizedUser } = user;
  return sanitizedUser;
}

// Convert Prisma model to API response format
export function convertToApiResponse<T>(data: T): T {
  return safeJsonDate(data) as T;
}

// Validate and parse pagination parameters
export function parsePaginationParams(searchParams: URLSearchParams) {
  const page = Math.max(1, parseInt(searchParams.get("page") || "1"));
  const limit = Math.max(
    1,
    Math.min(100, parseInt(searchParams.get("limit") || "10"))
  );

  return { page, limit };
}

// Chat-specific error handling
export function handleChatError(error: unknown): ApiError {
  console.error("Chat error:", error);

  if (error instanceof Error) {
    // Handle specific chat-related errors
    if (error.message.includes("Assignment not found")) {
      return {
        success: false,
        message: "Assignment not found",
        statusCode: 404,
      };
    }

    if (error.message.includes("Unauthorized")) {
      return {
        success: false,
        message: "You are not authorized to access this chat",
        statusCode: 403,
      };
    }

    if (error.message.includes("Message not found")) {
      return {
        success: false,
        message: "Message not found",
        statusCode: 404,
      };
    }
  }

  return {
    success: false,
    message: "An error occurred while processing your chat request",
    statusCode: 500,
  };
}

// Validate chat permissions
export async function validateChatAccess(
  userId: string,
  assignmentId: string,
  userRole: UserRole
): Promise<{ hasAccess: boolean; error?: ApiError }> {
  try {
    // Admin can access all chats
    if (userRole === UserRole.ADMIN) {
      return { hasAccess: true };
    }

    // For non-admin users, they can only access chats for assignments they're involved in
    // This would typically involve checking the assignment's clientId or writerId
    // Implementation depends on your specific business logic

    return { hasAccess: true };
  } catch (error) {
    console.error("Chat access validation error:", error);
    return {
      hasAccess: false,
      error: {
        success: false,
        message: "Failed to validate chat access",
        statusCode: 500,
      },
    };
  }
}

// File attachment utility functions
export function validateFileType(fileType: string): boolean {
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'application/rtf',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ];

  return allowedTypes.includes(fileType);
}

export function validateFileSize(fileSize: number, maxSizeInMB: number = 50): boolean {
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
  return fileSize <= maxSizeInBytes;
}

export function getFileExtension(fileName: string): string {
  return fileName.split('.').pop() || '';
}

export function sanitizeFileName(fileName: string): string {
  // Remove or replace invalid characters for Supabase Storage
  return fileName
    .replace(/['"]/g, '') // Remove quotes
    .replace(/[^\w\s.-]/g, '') // Keep only word characters, spaces, dots, and hyphens
    .replace(/\s+/g, '_') // Replace spaces with underscores
    .replace(/_{2,}/g, '_') // Replace multiple underscores with single
    .replace(/^_+|_+$/g, '') // Remove leading/trailing underscores
    .toLowerCase(); // Convert to lowercase for consistency
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Validate file upload permissions
export async function validateFileUploadAccess(
  userId: string,
  assignmentId: string,
  userRole: UserRole
): Promise<{ hasAccess: boolean; error?: ApiError }> {
  try {
    // Admin can upload files to any assignment
    if (userRole === UserRole.ADMIN) {
      return { hasAccess: true };
    }

    // For non-admin users, they can only upload files to assignments they're involved in
    // This would typically involve checking the assignment's clientId or writerId
    // Implementation depends on your specific business logic

    return { hasAccess: true };
  } catch (error) {
    console.error("File upload access validation error:", error);
    return {
      hasAccess: false,
      error: {
        success: false,
        message: "Failed to validate file upload access",
        statusCode: 500,
      },
    };
  }
}

// Export pricing-related schemas
export { basePriceSchema, pricingRuleSchema, pricingRuleUpdateSchema } from "./validations";

// Export assessment-related schemas
export { assessmentCreateSchema, assessmentUpdateSchema, assessmentToggleActiveSchema } from "./validations";

// Newsletter subscription types
export interface NewsletterSubscription {
  id: string;
  email: string;
  isActive: boolean;
  subscribedAt: Date;
  unsubscribedAt?: Date | null;
  source?: string | null;
}



// Export newsletter-related schemas
export { newsletterSubscribeSchema, newsletterUnsubscribeSchema, newsletterStatsSchema } from "./validations";

// Export contact-related schemas
export { contactFormSchema } from "./validations";

// Password reset types
export interface PasswordResetToken {
  id: string;
  userId: string;
  code: string;
  expires: Date;
  isUsed: boolean;
  createdAt: Date;
  user?: {
    id: string;
    email: string;
    name?: string | null;
  };
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  code: string;
  newPassword: string;
  confirmPassword: string;
}

export interface VerifyResetCodeRequest {
  code: string;
}

// Export password reset schemas
export { forgotPasswordSchema, resetPasswordSchema, verifyResetCodeSchema } from "./validations";

// Newsletter utility functions
export function validateEmailFormat(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function sanitizeNewsletterData(subscription: NewsletterSubscription): NewsletterSubscription {
  return {
    ...subscription,
    subscribedAt: new Date(subscription.subscribedAt),
    unsubscribedAt: subscription.unsubscribedAt ? new Date(subscription.unsubscribedAt) : null,
  };
}

// Password reset utility functions
export function generateResetCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

export function isResetCodeExpired(expiresAt: Date): boolean {
  return new Date() > expiresAt;
}

export function sanitizePasswordResetToken(token: PasswordResetToken): Omit<PasswordResetToken, 'code'> {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { code, ...sanitizedToken } = token;
  return sanitizedToken;
}
