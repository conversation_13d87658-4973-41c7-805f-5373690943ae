import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { z } from "zod";
import { checkPermission, apiSuccess, apiError } from "@/lib/api-utils";

// Validation schema for blog category
const BlogCategorySchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").max(100, "Name must be less than 100 characters"),
  description: z.string().optional().nullable(),
  slug: z.string().min(2, "Slug must be at least 2 characters").max(100, "Slug must be less than 100 characters")
    .regex(/^[a-z0-9-]+$/, "Slug must contain only lowercase letters, numbers, and hyphens"),
});

export async function GET() {
  try {
    // Fetch all blog categories
    const categories = await prisma.blogCategory.findMany({
      select: {
        id: true,
        name: true,
        description: true,
        slug: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            blogs: true,
          },
        },
      },
      orderBy: { name: "asc" },
    });

    return NextResponse.json(categories);
  } catch (error) {
    console.error("Error fetching categories:", error);
    return apiError("Failed to fetch categories", 500);
  }
}

export async function POST(req: NextRequest) {
  try {
    // Check admin permission
    const permissionError = await checkPermission(["ADMIN"]);
    if (permissionError) return permissionError;

    const body = await req.json();
    const parsed = BlogCategorySchema.safeParse(body);

    if (!parsed.success) {
      return apiError("Invalid input data", 400, parsed.error.flatten().fieldErrors);
    }

    const { name, description, slug } = parsed.data;

    // Check if category with same name or slug already exists
    const existingCategory = await prisma.blogCategory.findFirst({
      where: {
        OR: [
          { name },
          { slug },
        ],
      },
    });

    if (existingCategory) {
      if (existingCategory.name === name) {
        return apiError("Category with this name already exists", 409);
      }
      if (existingCategory.slug === slug) {
        return apiError("Category with this slug already exists", 409);
      }
    }

    const category = await prisma.blogCategory.create({
      data: {
        name,
        description,
        slug,
      },
    });

    return apiSuccess(category, "Category created successfully");
  } catch (error) {
    console.error("Error creating category:", error);
    return apiError("Failed to create category", 500);
  }
}
