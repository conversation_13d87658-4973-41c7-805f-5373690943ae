// Type definitions for PayPal integration

export interface CreateOrderRequest {
  orderId: string;
  amount: number;
}

export interface CreateOrderResponse {
  id: string; // PayPal order ID
}

export interface CaptureOrderRequest {
  orderId: string;
  orderID: string; // PayPal order ID
}

export interface CaptureOrderResponse {
  id: string; // PayPal order ID
  payerID: string;
  paymentID: string;
}

export interface PayPalApiError {
  message: string;
}
