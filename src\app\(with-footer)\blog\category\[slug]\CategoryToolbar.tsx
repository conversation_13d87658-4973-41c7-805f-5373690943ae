"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { BlogToolbar } from "@/components/blog/BlogToolbar";

interface CategoryToolbarProps {
  selectedCategory: string;
}

export function CategoryToolbar({ selectedCategory }: CategoryToolbarProps) {
  const [searchValue, setSearchValue] = useState("");
  const router = useRouter();

  const handleCategoryChange = (category: string) => {
    if (category === "all") {
      router.push("/blog");
    } else {
      router.push(`/blog/category/${category}`);
    }
  };

  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    // You can implement search functionality here
    // For example, you could add a query parameter to the URL
    // router.push(`/blog/category/${selectedCategory}?search=${value}`);
  };

  return (
    <BlogToolbar
      selectedCategory={selectedCategory}
      onCategoryChange={handleCategoryChange}
      searchValue={searchValue}
      onSearchChange={handleSearchChange}
    />
  );
}
