/**
 * Utility functions for handling Est Time calculations and filtering
 * for the academic writing platform
 */

/**
 * Calculate hours remaining until deadline from a given date
 * @param deadlineDate The deadline date (ISO string or Date object)
 * @param uploadedAt Optional upload date to measure from (defaults to current time)
 * @returns Number of hours remaining until deadline (can be negative if deadline passed)
 */
export function calculateHoursUntilDeadline(
  deadlineDate: string | Date,
  uploadedAt: string | Date = new Date()
): number {
  const deadline = new Date(deadlineDate);
  const startDate = new Date(uploadedAt);
  const currentTime = new Date();

  // Use the later of upload date or current time as the reference point
  // This ensures we're always showing the current remaining time
  const referenceTime = currentTime > startDate ? currentTime : startDate;

  // Calculate time difference in milliseconds
  const timeDiffMs = deadline.getTime() - referenceTime.getTime();

  // Convert to hours
  const hoursRemaining = timeDiffMs / (1000 * 60 * 60);

  // Return rounded value (can be negative if deadline has passed)
  return Math.ceil(hoursRemaining);
}

/**
 * Format hours for display, converting to days/weeks as needed
 * @param hours Number of hours
 * @returns Formatted string (e.g., "24h", "3d 12h", "1w 2d")
 */
export function formatHoursDisplay(hours: number): string {
  if (hours <= 0) {
    return "0h";
  }

  if (hours >= 168) {
    // If 1 week or more
    const weeks = Math.floor(hours / 168);
    const remainingHours = hours % 168;

    if (remainingHours === 0) {
      return `${weeks}w`;
    }

    const days = Math.floor(remainingHours / 24);
    const finalHours = remainingHours % 24;

    if (finalHours === 0) {
      return `${weeks}w ${days}d`;
    }

    return `${weeks}w ${days}d ${finalHours}h`;
  } else if (hours >= 24) {
    // If 1 day or more
    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;

    if (remainingHours === 0) {
      return `${days}d`;
    }

    return `${days}d ${remainingHours}h`;
  }

  // Less than a day
  return `${hours}h`;
}

/**
 * Determine if an assignment is urgent based on hours remaining
 * @param hours Number of hours remaining
 * @param urgencyThreshold Threshold in hours (default: 48)
 * @returns Boolean indicating if the assignment is urgent
 */
export function isUrgentAssignment(
  hours: number,
  urgencyThreshold: number = 48
): boolean {
  return hours >= 0 && hours <= urgencyThreshold;
}

/**
 * Get the appropriate color class for displaying deadline urgency
 * @param hours Number of hours remaining
 * @returns Tailwind CSS color class
 */
export function getDeadlineColorClass(hours: number): string {
  if (hours <= 0) {
    return "text-red-600 font-bold"; // Overdue
  } else if (hours <= 12) {
    return "text-red-500"; // Very urgent (less than 12 hours)
  } else if (hours <= 24) {
    return "text-orange-500"; // Urgent (less than 1 day)
  } else if (hours <= 48) {
    return "text-yellow-500"; // Approaching deadline (less than 2 days)
  } else {
    return "text-gray-600"; // Plenty of time
  }
}

/**
 * Convert a stored deadline date to hours remaining for data table usage
 * This is used when loading data to ensure the table shows current remaining hours
 * @param estTime Original deadline date (ISO string)
 * @param createdAt When the assignment was created/uploaded (ISO string)
 * @returns Number of hours remaining until deadline
 */
export function convertDeadlineToHours(
  estTime: string | Date,
  createdAt: string | Date
): number {
  return calculateHoursUntilDeadline(estTime, createdAt);
}

/**
 * Process an array of assignments to update their estTime field
 * from date format to hours remaining
 * @param assignments Array of assignment objects with estTime as date
 * @returns Updated assignments with estTime as hours remaining
 */
export function processAssignmentsEstTime<
  T extends { estTime: string | Date; createdAt: string | Date }
>(assignments: T[]): (Omit<T, "estTime"> & { estTime: number })[] {
  return assignments.map((assignment) => {
    const hoursRemaining = convertDeadlineToHours(
      assignment.estTime,
      assignment.createdAt
    );

    return {
      ...assignment,
      estTime: hoursRemaining,
    };
  });
}
