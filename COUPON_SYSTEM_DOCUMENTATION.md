# Coupon System Implementation Documentation

## Overview

This document provides a comprehensive overview of the coupon system implementation for the academic writing platform. The system allows administrators to create discount coupons and enforces one-time usage per user with real-time validation.

## 🏗️ Architecture Overview

The coupon system is built with the following components:

### **Database Schema**
- **Coupon Model**: Stores coupon details and configuration
- **CouponUsage Model**: Tracks individual coupon usage with user enforcement
- **Unique Constraints**: Ensures one coupon usage per user per coupon

### **Backend Services**
- **CouponService**: Core business logic for coupon management
- **API Endpoints**: RESTful APIs for CRUD operations and validation
- **Real-time Validation**: Instant coupon validation with caching

### **Frontend Components**
- **Admin Interface**: Coupon management dashboard
- **User Interface**: Coupon input components for order forms
- **Custom Hooks**: Reusable logic for coupon operations

## 📊 Database Models

### Coupon Model
```prisma
model Coupon {
  id                 String       @id @default(auto()) @map("_id") @db.ObjectId
  code               String       @unique // Format: 897-786-786
  description        String       // Admin description
  discountPercentage Float        // Percentage discount (0-100)
  isActive           Boolean      @default(true)
  maxUses            Int?         // Maximum uses (null = unlimited)
  currentUses        Int          @default(0)
  expiresAt          DateTime?    // Expiration date (null = no expiration)
  createdAt          DateTime     @default(now())
  updatedAt          DateTime     @updatedAt
  
  couponUsages       CouponUsage[]
}
```

### CouponUsage Model
```prisma
model CouponUsage {
  id             String      @id @default(auto()) @map("_id") @db.ObjectId
  couponId       String      @db.ObjectId
  userId         String      @db.ObjectId
  assignmentId   String?     @db.ObjectId
  discountAmount Float       // Actual discount applied
  originalPrice  Float       // Price before discount
  finalPrice     Float       // Price after discount
  usedAt         DateTime    @default(now())
  
  coupon         Coupon      @relation(fields: [couponId], references: [id], onDelete: Cascade)
  user           User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  assignment     Assignment? @relation(fields: [assignmentId], references: [id], onDelete: SetNull)
  
  @@unique([couponId, userId]) // Ensures one usage per user per coupon
}
```

## 🔧 Core Features

### **1. Coupon Code Generation**
- **Format**: XXX-XXX-XXX (e.g., 897-786-786)
- **Uniqueness**: Automatic collision detection with retry mechanism
- **Generation**: Random 3-digit segments for easy sharing

### **2. One-Time Usage Enforcement**
- **Database Constraint**: Unique index on (couponId, userId)
- **Validation Logic**: Pre-check before application
- **Error Handling**: Clear messaging for already-used coupons

### **3. Real-Time Validation**
- **Instant Feedback**: Validates coupons as users type
- **Caching**: 2-minute cache for performance optimization
- **Comprehensive Checks**: Active status, expiration, usage limits

### **4. Discount Calculation**
- **Percentage-Based**: Configurable discount percentages
- **Minimum Price**: Ensures prices don't go below zero
- **Transparent Pricing**: Shows original price, discount, and final price

## 🎯 Implementation Details

### **CouponService Class**

The core service handles all coupon operations:

```typescript
class CouponService {
  // Generate unique coupon codes
  generateCouponCode(): string
  
  // CRUD operations
  createCoupon(data: CouponCreateData): Promise<Result>
  updateCoupon(id: string, data: CouponUpdateData): Promise<Result>
  deleteCoupon(id: string): Promise<Result>
  getAllCoupons(): Promise<Coupon[]>
  
  // Validation and application
  validateCoupon(code: string, userId: string, originalPrice: number): Promise<ValidationResult>
  applyCoupon(code: string, userId: string, originalPrice: number, assignmentId?: string): Promise<ApplicationResult>
  
  // Usage tracking
  hasUserUsedCoupon(userId: string, couponCode: string): Promise<boolean>
  getUserCouponHistory(userId: string): Promise<CouponUsage[]>
}
```

### **API Endpoints**

#### Admin Endpoints (Admin Only)
- `GET /api/admin/coupons` - List all coupons with usage statistics
- `POST /api/admin/coupons` - Create new coupon
- `PUT /api/admin/coupons/[id]` - Update coupon
- `DELETE /api/admin/coupons/[id]` - Delete coupon

#### User Endpoints (Authenticated Users)
- `POST /api/coupons/validate` - Validate coupon code
- `POST /api/coupons/apply` - Apply coupon and create usage record

### **Custom Hooks**

#### useCoupon Hook
```typescript
const {
  // State
  couponState,
  isValidating,
  isApplying,
  validationError,
  
  // Actions
  validateCoupon,
  applyCoupon,
  removeCoupon,
  resetCoupon,
  updateOriginalPrice,
  
  // Computed values
  getDiscountPercentage,
  getSavingsAmount,
  
  // Convenience getters
  isApplied,
  finalPrice,
  originalPrice,
  discountAmount,
  couponCode,
} = useCoupon(initialPrice);
```

#### useAdminCoupons Hook
```typescript
const {
  coupons,
  loading,
  error,
  fetchCoupons,
  createCoupon,
  updateCoupon,
  deleteCoupon,
} = useAdminCoupons();
```

## 🎨 User Interface Components

### **Admin Interface**
- **Coupon Management Dashboard**: Full CRUD operations
- **Statistics Cards**: Total coupons, usage metrics, active rates
- **Real-time Updates**: Instant reflection of changes
- **Bulk Operations**: Toggle active status, delete multiple

### **User Interface**
- **CouponInput Component**: Full-featured coupon input with validation
- **InlineCouponInput Component**: Compact version for quick forms
- **Real-time Feedback**: Instant validation and error messages
- **Visual Indicators**: Clear success/error states

### **Integration Points**
- **Order Form**: Main order creation page
- **Admin Quick Create**: Admin assignment creation
- **Client Quick Create**: Client assignment creation

## 🔒 Security & Validation

### **Input Validation**
- **Zod Schemas**: Type-safe validation for all inputs
- **Sanitization**: Proper data cleaning and formatting
- **Error Handling**: Comprehensive error messages

### **Authorization**
- **Role-Based Access**: Admin-only coupon management
- **User Ownership**: Users can only apply coupons to their own orders
- **Session Validation**: Proper authentication checks

### **Data Integrity**
- **Database Constraints**: Unique indexes and foreign keys
- **Transaction Safety**: Atomic operations for coupon application
- **Audit Trail**: Complete usage history tracking

## 📈 Performance Optimizations

### **Caching Strategy**
- **Service-Level Cache**: 2-minute cache for validation results
- **Cache Invalidation**: Automatic clearing on updates
- **Memory Efficient**: Map-based caching with expiration

### **Database Optimization**
- **Indexed Queries**: Optimized lookups on coupon codes
- **Efficient Joins**: Minimal data fetching with proper includes
- **Pagination**: Proper pagination for large datasets

## 🧪 Testing Considerations

### **Unit Tests**
- **Service Methods**: Test all CouponService methods
- **Validation Logic**: Test edge cases and error conditions
- **Hook Behavior**: Test custom hook state management

### **Integration Tests**
- **API Endpoints**: Test all CRUD operations
- **Database Operations**: Test constraints and relationships
- **User Flows**: Test complete coupon application process

### **Edge Cases**
- **Expired Coupons**: Proper handling of expired coupons
- **Usage Limits**: Test maximum usage enforcement
- **Concurrent Usage**: Test race conditions in coupon application

## 🚀 Deployment & Monitoring

### **Environment Variables**
No additional environment variables required - uses existing database connection.

### **Database Migration**
```bash
npx prisma db push
```

### **Database Seeding**
The system includes pre-configured coupons that are automatically seeded:

```bash
npx tsx prisma/seed-new.ts
```

**Seeded Coupons:**
- **WELCOME10** - 10% discount for first-time users (unlimited uses, no expiration)
- **SAVE15** - 15% promotional discount (limited to 100 uses, expires in 30 days)

These coupons are created automatically during the seeding process and can be used immediately for testing and production.

### **Monitoring Points**
- **Coupon Usage Rates**: Track popular coupons
- **Error Rates**: Monitor validation failures
- **Performance Metrics**: Track response times

## 🔄 Future Enhancements

### **Potential Features**
- **Bulk Coupon Generation**: Generate multiple coupons at once
- **Usage Analytics**: Detailed reporting on coupon performance
- **Conditional Coupons**: Minimum order value requirements
- **Time-Limited Coupons**: Specific validity periods
- **Category-Specific Coupons**: Coupons for specific assignment types

### **Scalability Considerations**
- **Redis Caching**: For high-traffic scenarios
- **Background Jobs**: For bulk operations
- **API Rate Limiting**: Prevent abuse of validation endpoints

## 📝 Best Practices Implemented

1. **Type Safety**: No `any` types used throughout the implementation
2. **Error Handling**: Comprehensive error handling with user-friendly messages
3. **Code Reusability**: Modular components and hooks for easy reuse
4. **Performance**: Efficient caching and database queries
5. **Security**: Proper validation and authorization checks
6. **User Experience**: Real-time feedback and clear visual indicators
7. **Maintainability**: Well-documented code with clear separation of concerns

## 🎉 Conclusion

The coupon system provides a robust, secure, and user-friendly way to offer discounts on the academic writing platform. With comprehensive validation, one-time usage enforcement, and seamless integration across all order creation flows, it enhances the user experience while maintaining data integrity and security.

The implementation follows modern best practices with TypeScript safety, proper error handling, and efficient performance optimizations. The modular architecture ensures easy maintenance and future enhancements.
