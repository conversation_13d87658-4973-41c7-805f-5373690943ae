import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";

// Props for the WriterPayPalButton component
export interface WriterPayPalButtonProps {
  orderId: string; // The assignment ID
  amount: number; // Amount to pay to writer
  payeeEmail: string; // Writer's PayPal email
  onSuccess: (details: { id: string; status: string; paymentID: string }) => void;
  onError?: (error: Error) => void;
  onCancel?: () => void;
}

const WriterPayPalButton: React.FC<WriterPayPalButtonProps> = ({
  orderId,
  amount,
  payeeEmail,
  onSuccess,
  onError,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  onCancel,
}) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [payoutStatus, setPayoutStatus] = useState<string | null>(null);

  const handleDirectPayout = async () => {
    try {
      setIsProcessing(true);
      setPayoutStatus("Creating payout...");
      
      console.log("[WriterPayPalButton] Initiating direct payout to:", payeeEmail);
      
      // Create payout directly from business account to writer
      const createRes = await fetch("/api/payments/writer-payments/create-order", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ 
          orderId, 
          amount, 
          payeeEmail 
        }),
      });
      
      const createData = await createRes.json();
      console.log("[WriterPayPalButton] Create payout response:", createData);
      
      if (!createRes.ok) {
        throw new Error(createData.message || "Failed to create PayPal payout");
      }

      setPayoutStatus("Checking payout status...");

      // Check payout status
      const checkRes = await fetch("/api/payments/writer-payments/capture-order", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ 
          orderId, 
          batchId: createData.batch_id,
          payoutItemId: createData.payout_item_id
        }),
      });

      const checkData = await checkRes.json();
      console.log("[WriterPayPalButton] Payout status response:", checkData);

      if (!checkRes.ok) {
        throw new Error(checkData.message || "Failed to process payout");
      }

      // Provide status-specific messaging
      if (checkData.status === "UNCLAIMED") {
        setPayoutStatus("Payout sent! Writer will receive an email to claim the payment.");
      } else if (checkData.status === "PENDING") {
        setPayoutStatus("Payout is being processed by PayPal.");
      } else {
        setPayoutStatus("Payout completed successfully!");
      }

      console.log("[WriterPayPalButton] Payout successful:", checkData);
      onSuccess(checkData);
      
    } catch (error) {
      console.error("[WriterPayPalButton] Payout error:", error);
      setPayoutStatus(null);
      if (onError) {
        onError(error as Error);
      }
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-3">
      <Button
        onClick={handleDirectPayout}
        disabled={isProcessing}
        className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 text-base shadow-lg hover:shadow-xl transition-all duration-200"
      >
        {isProcessing ? (
          <div className="flex items-center gap-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            <span>Processing Payout...</span>
          </div>
        ) : (
          `💰 Send $${amount.toFixed(2)} via PayPal`
        )}
      </Button>
      
      {payoutStatus && (
        <div className="bg-blue-50 dark:bg-blue-950/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
          <p className="text-sm text-blue-700 dark:text-blue-300">
            {payoutStatus}
          </p>
        </div>
      )}
      
      <p className="text-xs text-muted-foreground">
        Payment will be sent directly to: <strong>{payeeEmail}</strong>
      </p>
      
      <div className="bg-green-50 dark:bg-green-950/20 p-3 rounded-lg border border-green-200 dark:border-green-800">
        <p className="text-xs text-green-700 dark:text-green-300">
          <strong>How it works:</strong> This sends money directly from your business PayPal account to the writer&apos;s account.
          No checkout required - it&apos;s a direct payout!
        </p>
        <p className="text-xs text-green-600 dark:text-green-400 mt-2">
          <strong>Note:</strong> If the writer doesn&apos;t have a PayPal account, they&apos;ll receive an email to create one and claim the payment.
        </p>
      </div>
    </div>
  );
};

export default WriterPayPalButton;
