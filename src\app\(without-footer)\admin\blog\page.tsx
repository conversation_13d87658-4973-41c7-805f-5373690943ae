"use client";

import * as React from "react";
import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Toaster, toast } from "sonner";
import HybridQuillEditor from "@/components/quill-editor/hybrid-editor";
import { Card, CardContent } from "@/components/ui/card";
import { BlogImageUpload } from "@/components/blog-image-upload";

interface Author {
  id: string;
  name: string;
  qualifications: string;
}
interface Category {
  id: string;
  name: string;
  slug: string;
}

interface FAQ {
  question: string;
  answer: string;
}

export default function BlogPage() {
  const [title, setTitle] = useState("");
  const [slug, setSlug] = useState("");
  const [metaTitle, setMetaTitle] = useState("");
  const [metaDescription, setMetaDescription] = useState("");
  const [imageUrl, setImageUrl] = useState("");
  const [imageAlt, setImageAlt] = useState("");
  const [authorId, setAuthorId] = useState("");
  const [categoryId, setCategoryId] = useState("");
  const [keywords, setKeywords] = useState<string[]>([]);
  const [keywordInput, setKeywordInput] = useState("");
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [faqQuestion, setFaqQuestion] = useState("");
  const [faqAnswer, setFaqAnswer] = useState("");
  // Blog body as HTML string for Quill
  const [body, setBody] = useState<string>("");
  const [authors, setAuthors] = useState<Author[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetch("/api/blog/authors").then(res => res.json()).then(setAuthors);
    fetch("/api/blog/categories").then(res => res.json()).then(setCategories);
  }, []);

  useEffect(() => {
    // Auto-generate slug from title
    setSlug(title.toLowerCase().replace(/[^a-z0-9]+/g, "-").replace(/(^-|-$)/g, ""));
  }, [title]);

  const handleAddKeyword = () => {
    if (!keywordInput.trim()) return;

    // Split by commas and process each keyword
    const newKeywords = keywordInput
      .split(',')
      .map(kw => kw.trim())
      .filter(kw => kw.length > 0 && !keywords.includes(kw));

    if (newKeywords.length > 0) {
      setKeywords([...keywords, ...newKeywords]);
      setKeywordInput("");

      if (newKeywords.length === 1) {
        toast.success(`Added keyword: "${newKeywords[0]}"`);
      } else {
        toast.success(`Added ${newKeywords.length} keywords`);
      }
    } else if (keywordInput.trim()) {
      toast.error("Keyword(s) already exist or are invalid");
    }
  };

  const handleRemoveKeyword = (kw: string) => setKeywords(keywords.filter(k => k !== kw));

  const handleAddFaq = () => {
    if (faqQuestion && faqAnswer) {
      // Check if question already exists
      const exists = faqs.some(faq => faq.question.toLowerCase() === faqQuestion.toLowerCase());
      if (!exists) {
        setFaqs([...faqs, { question: faqQuestion, answer: faqAnswer }]);
        setFaqQuestion("");
        setFaqAnswer("");
      } else {
        toast.error("This question already exists");
      }
    } else {
      toast.error("Please fill in both question and answer");
    }
  };

  const handleRemoveFaq = (index: number) => {
    setFaqs(faqs.filter((_, i) => i !== index));
  };

  const handleImageUpload = (url: string, altText: string) => {
    setImageUrl(url);
    setImageAlt(altText);
  };

  const validateForm = () => {
    const missingFields: string[] = [];

    // Check required text fields
    if (!title.trim()) missingFields.push("Title");
    if (!slug.trim()) missingFields.push("Slug");
    if (!metaTitle.trim()) missingFields.push("Meta Title");
    if (!metaDescription.trim()) missingFields.push("Meta Description");

    // Check blog body content
    const html = body;
    if (!html || html.replace(/<[^>]+>/g, '').trim().length < 10) {
      missingFields.push("Blog Content (minimum 10 characters)");
    }

    // Check image upload
    if (!imageUrl) missingFields.push("Featured Image");
    if (!imageAlt.trim()) missingFields.push("Image Alt Text");

    // Check category selection
    if (!categoryId) missingFields.push("Blog Category");

    // Check author selection
    if (!authorId) missingFields.push("Blog Author");

    // Check keywords
    if (keywords.length === 0) missingFields.push("Keywords (at least 1)");

    // Check FAQs
    if (faqs.length === 0) missingFields.push("FAQs (at least 1)");

    if (missingFields.length > 0) {
      if (missingFields.length === 1) {
        toast.error(`Please provide: ${missingFields[0]}`);
      } else if (missingFields.length <= 3) {
        toast.error(`Please provide: ${missingFields.join(", ")}`);
      } else {
        toast.error(`Please complete ${missingFields.length} missing fields: ${missingFields.slice(0, 3).join(", ")} and ${missingFields.length - 3} more`);
      }
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form before submission
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      // Convert FAQ objects to strings in the format "Question|Answer"
      const faqStrings = faqs.map(faq => `${faq.question}|${faq.answer}`);

      const res = await fetch("/api/blog", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          title,
          body,
          slug,
          metaTitle,
          metaDescription,
          imageUrl,
          imageAlt,
          categoryId,
          authorId,
          keywords,
          faqs: faqStrings,
        }),
      });

      if (!res.ok) {
        const err = await res.json();
        // Handle specific API errors
        if (err.error && typeof err.error === 'object') {
          // Zod validation errors
          const errorMessages = Object.values(err.error.fieldErrors || {}).flat();
          toast.error(`Validation error: ${errorMessages.join(', ')}`);
        } else if (err.error === "Slug already exists") {
          toast.error("This slug already exists. Please choose a different slug.");
        } else {
          toast.error(err.error || "Failed to create blog post");
        }
        console.error(err);
        setLoading(false);
        return;
      }

      toast.success("Blog post created successfully!");

      // Reset form
      setTitle("");
      setSlug("");
      setMetaTitle("");
      setMetaDescription("");
      setImageUrl("");
      setImageAlt("");
      setAuthorId("");
      setCategoryId("");
      setKeywords([]);
      setFaqs([]);
      setBody("");
    } catch (err) {
      toast.error("Failed to create blog post. Please try again.");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-2xl sm:text-3xl font-bold mb-4">Create Blog Post</h1>

        {/* Validation Summary */}
        <div className="mb-6 p-4 bg-muted/30 rounded-lg">
          <h2 className="text-sm font-medium mb-2">Required Fields Checklist:</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-xs">
          <div className={`flex items-center gap-1 ${title ? 'text-green-600' : 'text-red-500'}`}>
            {title ? '✅' : '❌'} Title
          </div>
          <div className={`flex items-center gap-1 ${slug ? 'text-green-600' : 'text-red-500'}`}>
            {slug ? '✅' : '❌'} Slug
          </div>
          <div className={`flex items-center gap-1 ${metaTitle ? 'text-green-600' : 'text-red-500'}`}>
            {metaTitle ? '✅' : '❌'} Meta Title
          </div>
          <div className={`flex items-center gap-1 ${metaDescription ? 'text-green-600' : 'text-red-500'}`}>
            {metaDescription ? '✅' : '❌'} Meta Description
          </div>
          <div className={`flex items-center gap-1 ${imageUrl && imageAlt ? 'text-green-600' : 'text-red-500'}`}>
            {imageUrl && imageAlt ? '✅' : '❌'} Featured Image & Alt Text
          </div>
          <div className={`flex items-center gap-1 ${authorId ? 'text-green-600' : 'text-red-500'}`}>
            {authorId ? '✅' : '❌'} Author
          </div>
          <div className={`flex items-center gap-1 ${categoryId ? 'text-green-600' : 'text-red-500'}`}>
            {categoryId ? '✅' : '❌'} Category
          </div>
          <div className={`flex items-center gap-1 ${keywords.length > 0 ? 'text-green-600' : 'text-red-500'}`}>
            {keywords.length > 0 ? '✅' : '❌'} Keywords ({keywords.length})
          </div>
          <div className={`flex items-center gap-1 ${body && body.replace(/<[^>]+>/g, '').trim().length >= 10 ? 'text-green-600' : 'text-red-500'}`}>
            {body && body.replace(/<[^>]+>/g, '').trim().length >= 10 ? '✅' : '❌'} Blog Content
          </div>
          <div className={`flex items-center gap-1 ${faqs.length > 0 ? 'text-green-600' : 'text-red-500'}`}>
            {faqs.length > 0 ? '✅' : '❌'} FAQs ({faqs.length})
          </div>
        </div>
      </div>

      <form className="space-y-6" onSubmit={handleSubmit}>
        <div>
          <label className="block mb-1 font-medium">
            Title <span className="text-red-500">*</span>
          </label>
          <Input
            value={title}
            onChange={e => setTitle(e.target.value)}
            placeholder="Enter blog title"
            required
          />
        </div>
        <div>
          <label className="block mb-1 font-medium">
            Slug <span className="text-red-500">*</span>
          </label>
          <Input
            value={slug}
            onChange={e => setSlug(e.target.value)}
            placeholder="Enter URL slug (e.g., my-blog-post)"
            required
          />
        </div>
        <div>
          <label className="block mb-1 font-medium">
            Meta Title <span className="text-red-500">*</span>
          </label>
          <Input
            value={metaTitle}
            onChange={e => setMetaTitle(e.target.value)}
            placeholder="Enter meta title for SEO"
            required
          />
        </div>
        <div>
          <label className="block mb-1 font-medium">
            Meta Description <span className="text-red-500">*</span>
          </label>
          <Textarea
            value={metaDescription}
            onChange={e => setMetaDescription(e.target.value)}
            placeholder="Enter meta description for SEO (150-160 characters recommended)"
            required
          />
        </div>
        <BlogImageUpload
          onImageUpload={handleImageUpload}
          currentImageUrl={imageUrl}
          currentAltText={imageAlt}
          required={true}
        />
        <div>
          <label className="block mb-1 font-medium">
            Author <span className="text-red-500">*</span>
          </label>
          <Select value={authorId} onValueChange={setAuthorId}>
            <SelectTrigger className={`w-full ${!authorId ? "border-red-200" : ""}`} aria-label="Select author">
              <SelectValue placeholder="Select author" />
            </SelectTrigger>
            <SelectContent>
              {authors.map(a => (
                <SelectItem key={a.id} value={a.id}>
                  <span className="flex flex-col items-start">
                    <span className="font-medium">{a.name}</span>
                    <span className="text-xs text-muted-foreground">{a.qualifications}</span>
                  </span>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {!authorId && (
            <p className="text-sm text-muted-foreground mt-1">
              Please select an author for this blog post
            </p>
          )}
        </div>
        <div>
          <label className="block mb-1 font-medium">
            Category <span className="text-red-500">*</span>
          </label>
          <Select value={categoryId} onValueChange={setCategoryId}>
            <SelectTrigger className={`w-full ${!categoryId ? "border-red-200" : ""}`} aria-label="Select category">
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map(c => (
                <SelectItem key={c.id} value={c.id}>{c.name}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          {!categoryId && (
            <p className="text-sm text-muted-foreground mt-1">
              Please select a category for this blog post
            </p>
          )}
        </div>
        <div>
          <label className="block mb-1 font-medium">
            Keywords <span className="text-red-500">*</span>
          </label>
          <div className="flex gap-2 mb-2">
            <div className="flex-1">
              <Input
                value={keywordInput}
                onChange={e => setKeywordInput(e.target.value)}
                onKeyDown={e => { if (e.key === 'Enter') { e.preventDefault(); handleAddKeyword(); }}}
                placeholder="Add keywords (separate multiple with commas: keyword1, keyword2, keyword3)"
                className="w-full"
              />
              <p className="text-xs text-muted-foreground mt-1">
                💡 Tip: You can add multiple keywords at once by separating them with commas
              </p>
            </div>
            <Button type="button" onClick={handleAddKeyword} variant="secondary" className="shrink-0">
              Add Keywords
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {keywords.map(kw => (
              <span key={kw} className="bg-muted px-2 py-1 rounded text-xs flex items-center gap-1">
                {kw}
                <button type="button" onClick={() => handleRemoveKeyword(kw)} className="ml-1 text-red-500 hover:text-red-700">&times;</button>
              </span>
            ))}
          </div>
          {keywords.length === 0 && (
            <p className="text-sm text-muted-foreground mt-1">
              Please add at least one keyword for better SEO
            </p>
          )}
          {keywords.length > 0 && (
            <p className="text-xs text-green-600 mt-1">
              ✅ {keywords.length} keyword{keywords.length !== 1 ? 's' : ''} added
            </p>
          )}
        </div>
        <div>
          <label className="block mb-1 font-medium">
            Blog Body <span className="text-red-500">*</span>
          </label>
          <HybridQuillEditor
            value={body}
            onChange={setBody}
            placeholder="Write your blog content here..."
            className="min-h-[400px]"
            showHeadlessToolbar={true}
          />
          {(!body || body.replace(/<[^>]+>/g, '').trim().length < 10) && (
            <p className="text-sm text-muted-foreground mt-1">
              Please write at least 10 characters of content
            </p>
          )}
        </div>
        
        {/* FAQ Section - Moved to bottom */}
        <div>
          <label className="block mb-3 font-medium text-lg">
            Frequently Asked Questions <span className="text-red-500">*</span>
          </label>
          {faqs.length === 0 && (
            <p className="text-sm text-muted-foreground mb-3">
              Please add at least one FAQ to improve user engagement
            </p>
          )}
          <div className="space-y-4">
            <div className="grid gap-3">
              <div>
                <label className="block mb-1 text-sm font-medium text-gray-600">Question</label>
                <Input
                  value={faqQuestion}
                  onChange={e => setFaqQuestion(e.target.value)}
                  placeholder="Enter FAQ question"
                />
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium text-gray-600">Answer</label>
                <Textarea
                  value={faqAnswer}
                  onChange={e => setFaqAnswer(e.target.value)}
                  placeholder="Enter FAQ answer"
                  rows={3}
                />
              </div>
              <Button type="button" onClick={handleAddFaq} variant="secondary" className="w-fit">
                Add FAQ
              </Button>
            </div>
            
            {faqs.length > 0 && (
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-gray-700">Added FAQs ({faqs.length})</h4>
                {faqs.map((faq, index) => (
                  <Card key={index} className="p-3">
                    <CardContent className="p-0">
                      <div className="flex justify-between items-start gap-3">
                        <div className="flex-1 space-y-2">
                          <div>
                            <p className="text-sm font-medium text-gray-700">Q: {faq.question}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">A: {faq.answer}</p>
                          </div>
                        </div>
                        <Button
                          type="button"
                          onClick={() => handleRemoveFaq(index)}
                          variant="ghost"
                          size="sm"
                          className="text-red-500 hover:text-red-700 hover:bg-red-50"
                        >
                          &times;
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>

        <Button
          type="submit"
          disabled={loading}
          className={`w-full ${
            title && slug && metaTitle && metaDescription && imageUrl && imageAlt && authorId && categoryId &&
            keywords.length > 0 && faqs.length > 0 && body && body.replace(/<[^>]+>/g, '').trim().length >= 10
              ? 'bg-green-600 hover:bg-green-700'
              : ''
          }`}
        >
          {loading ? "Creating..." : "Create Blog Post"}
        </Button>
      </form>
      <Toaster richColors />
      </div>
    </div>
  );
}