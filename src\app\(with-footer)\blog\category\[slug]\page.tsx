import { notFound } from "next/navigation";
import prisma from "@/lib/prisma";
import { Metadata } from "next";
import { BlogCategoryHeader } from "@/components/blog/category/BlogCategoryHeader";
import { CategoryBlogPostsGrid } from "./CategoryBlogPostsGrid";
import { CategoryToolbar } from "./CategoryToolbar";

interface CategoryPageProps {
  params: Promise<{ slug: string }>;
}

// Generate metadata for SEO
export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const category = await getCategoryBySlug(resolvedParams.slug);
  
  if (!category) {
    return {
      title: "Category Not Found",
      description: "The requested blog category could not be found.",
    };
  }

  return {
    title: `${category.name} - Blog Category`,
    description: `Browse all blog posts in the ${category.name} category.`,
    openGraph: {
      title: `${category.name} - Blog Category`,
      description: `Browse all blog posts in the ${category.name} category.`,
      type: "website",
      url: `/blog/category/${category.slug}`,
    },
  };
}

// Get category by slug
async function getCategoryBySlug(slug: string) {
  return await prisma.blogCategory.findUnique({
    where: { slug },
  });
}

// Get posts by category
async function getPostsByCategory(categoryId: string) {
  return await prisma.blog.findMany({
    where: { categoryId: categoryId },
    include: {
      category: true,
      author: true,
    },
    orderBy: {
      createdAt: "desc",
    },
  });
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const resolvedParams = await params;
  const category = await getCategoryBySlug(resolvedParams.slug);

  if (!category) {
    notFound();
  }

  const posts = await getPostsByCategory(category.id);

  return (
    <div className="container mx-auto px-4 py-8">
      <BlogCategoryHeader category={category} postCount={posts.length} />
      
      <div className="mt-8">
        {/* Client-side component with default selected category */}
        <CategoryToolbar selectedCategory={category.slug} />
      </div>
      
      <div className="mt-8">
        {/* We'll create a server-rendered version below */}
        <CategoryBlogPostsGrid posts={posts} />
      </div>
    </div>
  );
}
