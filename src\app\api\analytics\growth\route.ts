import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { GrowthAnalytics } from "@/types/analytics";

export async function GET() {
  try {
    // Get current user counts
    const totalClients = await prisma.user.count({
      where: { role: "CLIENT" },
    });

    const totalWriters = await prisma.user.count({
      where: { role: "WRITER" },
    });

    // Calculate growth rates over the past year
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);

    const clientsOneYearAgo = await prisma.user.count({
      where: {
        role: "CLIENT",
        createdAt: {
          lte: oneYearAgo,
        },
      },
    });

    const writersOneYearAgo = await prisma.user.count({
      where: {
        role: "WRITER",
        createdAt: {
          lte: oneYearAgo,
        },
      },
    });

    const clientGrowthRate = clientsOneYearAgo > 0
      ? ((totalClients - clientsOneYearAgo) / clientsOneYearAgo) * 100
      : 100;

    const writerGrowthRate = writersOneYearAgo > 0
      ? ((totalWriters - writersOneYearAgo) / writersOneYearAgo) * 100
      : 100;

    // Overall growth rate (average of client and writer growth)
    const growthRate = (clientGrowthRate + writerGrowthRate) / 2;

    // Calculate conversion rate (simplified - assignments completed / total clients)
    const completedAssignments = await prisma.assignment.count({
      where: {
        status: "COMPLETED",
      },
    });

    const conversionRate = totalClients > 0
      ? (completedAssignments / totalClients) * 100
      : 0;

    // Calculate retention rate (simplified - active clients / total clients)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const activeClients = await prisma.user.count({
      where: {
        role: "CLIENT",
        updatedAt: {
          gte: thirtyDaysAgo,
        },
      },
    });

    const retentionRate = totalClients > 0
      ? (activeClients / totalClients) * 100
      : 0;

    // Generate growth by month data (last 12 months)
    const growthByMonth = [];
    for (let i = 11; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
      const endOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0);
      
      const clientsCount = await prisma.user.count({
        where: {
          role: "CLIENT",
          createdAt: {
            gte: startOfMonth,
            lte: endOfMonth,
          },
        },
      });
      
      const writersCount = await prisma.user.count({
        where: {
          role: "WRITER",
          createdAt: {
            gte: startOfMonth,
            lte: endOfMonth,
          },
        },
      });
      
      growthByMonth.push({
        date: `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`,
        clients: clientsCount,
        writers: writersCount,
      });
    }

    // Generate marketing effectiveness data (mocked data - would come from analytics integration)
    const marketingEffectiveness = [
      {
        channel: "Organic Search",
        visitors: 2450,
        conversions: 128,
        conversionRate: 5.22,
        cost: 1200,
        roi: 320.83,
      },
      {
        channel: "Social Media",
        visitors: 1830,
        conversions: 94,
        conversionRate: 5.14,
        cost: 950,
        roi: 296.84,
      },
      {
        channel: "Referrals",
        visitors: 940,
        conversions: 68,
        conversionRate: 7.23,
        cost: 350,
        roi: 582.86,
      },
      {
        channel: "Direct",
        visitors: 1250,
        conversions: 82,
        conversionRate: 6.56,
        cost: 0,
        roi: 0,
      },
      {
        channel: "Email Marketing",
        visitors: 780,
        conversions: 53,
        conversionRate: 6.79,
        cost: 420,
        roi: 378.57,
      },
    ];

    const analyticsData: GrowthAnalytics = {
      userGrowth: {
        clients: totalClients,
        writers: totalWriters,
      },
      growthRate,
      conversionRate,
      retentionRate,
      growthByMonth,
      marketingEffectiveness,
    };

    return NextResponse.json(analyticsData);
  } catch (error) {
    console.error("Error fetching growth analytics:", error);
    return NextResponse.json(
      { error: "Failed to fetch growth analytics" },
      { status: 500 }
    );
  }
}
