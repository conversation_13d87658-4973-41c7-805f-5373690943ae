# academic-app
An academic writing website created using the latest technologies (Shadcn, Tailwind V4, Next.js, and Mongodb)


Technical Requirements
-Role-based dashboards (3 distinct UIs)
-Real-time bid notifications
-Assignment lifecycle tracking
-Document upload/review system
-Payment integration (future phase)


Next Critical Components to Build
1. Authentication System
-NextAuth.js with role-based session management
-Protected routes for each user type

2. Assignment Submission Flow
-Client-facing form with rich text editor
-File upload capability for reference materials

3. Bidding System
-Real-time bid updates using WebSockets
-Admin approval interface

4. Job Board
-Dynamic filtering (deadline, bid status, assignment type)
-Writer bidding interface with bid history

-----------
Set up environment variables for social login providers

To fully implement these changes, you'll need to:

Add the suggested fields to your User model in your Prisma schema for password reset functionality
Create a forgot-password page that connects to the API
Set up email sending functionality

## RATING LOGIC

The platform implements an objective, performance-based rating system for writers that automatically adjusts based on measurable metrics rather than subjective feedback.

### Rating Scale
- Scale ranges from 0.0 to 5.0 stars (with one decimal place precision)
- New writers start with a default rating of 3.0

### Rating Calculation Factors

#### Positive Factors
- **Completed Orders**: Rating increases by 0.1 for every 25 completed orders
- **On-Time Completion**: Small rating boost based on the percentage of orders completed before deadline

#### Negative Factors
- **Rejected Orders**: Rating decreases by 0.2 for every 5 rejected orders
- **Revisions**: Minor rating penalty based on the percentage of orders requiring revision

### Rating Levels

| Rating Range | Label         | Color  |
|--------------|---------------|--------|
| 4.5 - 5.0    | Exceptional   | Emerald|
| 4.0 - 4.4    | Excellent     | Green  |
| 3.5 - 3.9    | Very Good     | Lime   |
| 3.0 - 3.4    | Good          | Yellow |
| 2.5 - 2.9    | Satisfactory  | Amber  |
| 2.0 - 2.4    | Fair          | Orange |
| 1.0 - 1.9    | Poor          | Red    |
| 0.0 - 0.9    | Unsatisfactory| Red    |

### Implementation

The rating system is implemented in `src/lib/rating-utils.ts` and provides functions for:
- Calculating writer ratings based on performance metrics
- Determining color coding for visual representation
- Providing descriptive labels for each rating level

Ratings are automatically updated whenever a job status changes (completion, rejection, revision) without requiring manual input from clients or administrators.
-----------------------------------------------
#SOCKET.IO SETUP
##Necessary files
use-persistent-socket.ts MAIN
chat-dialog MAIN
socket-server.mjs MAIN