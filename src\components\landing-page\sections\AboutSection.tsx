// src/components/landing-page/sections/aboutsection.tsx
"use client";

import React, { useState } from "react";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import {
  CheckCircle,
  Trophy,
  Users,
  BookOpen,
  Award,
  Shield,
  Clock,
  Star,
  TrendingUp,
  Globe,
} from "lucide-react";

export const AboutOverviewSection = () => {
  const [activeTab, setActiveTab] = useState("overview");

  const achievements = [
    {
      icon: Trophy,
      value: "98.5%",
      label: "Client Satisfaction",
      color: "text-amber-500",
    },
    {
      icon: Users,
      value: "150K+",
      label: "Students Served",
      color: "text-blue-500",
    },
    {
      icon: BookOpen,
      value: "500K+",
      label: "Papers Delivered",
      color: "text-green-500",
    },
    {
      icon: Star,
      value: "4.9/5",
      label: "Average Rating",
      color: "text-purple-500",
    },
  ];

  const certifications = [
    "ISO 27001 Certified",
    "GDPR Compliant",
    "SSL Secured",
    "PCI DSS Level 1",
    "SOC 2 Type II",
  ];

  const milestones = [
    {
      year: "2015",
      event: "Platform Launch",
      description: "Started with 50 expert writers",
    },
    {
      year: "2018",
      event: "Global Expansion",
      description: "Expanded to serve 25+ countries",
    },
    {
      year: "2020",
      event: "AI Integration",
      description: "Implemented advanced matching algorithms",
    },
    {
      year: "2022",
      event: "Mobile App Launch",
      description: "Released iOS and Android applications",
    },
    {
      year: "2024",
      event: "Quality Milestone",
      description: "Achieved 98%+ satisfaction rate",
    },
  ];

  const teamHighlights = [
    {
      role: "PhD Writers",
      count: "750+",
      expertise: "Advanced Research & Analysis",
    },
    {
      role: "Subject Experts",
      count: "300+",
      expertise: "Specialized Knowledge Areas",
    },
    {
      role: "Quality Analysts",
      count: "50+",
      expertise: "Content Review & Enhancement",
    },
    {
      role: "Support Team",
      count: "100+",
      expertise: "24/7 Customer Assistance",
    },
  ];

  return (
    <section
      id="about-overview"
      className="py-8 md:py-12 bg-gradient-to-b from-slate-50 to-white dark:from-slate-950 dark:to-slate-900"
      aria-labelledby="about-heading"
    >
      <div className="container mx-auto px-2 sm:px-4 md:px-6">
        {/* Header */}
        <div className="text-center max-w-4xl mx-auto mb-16">
          <Badge
            variant="outline"
            className="mb-4 px-4 py-2 text-sm font-medium bg-blue-50 border-blue-200 text-blue-700 dark:bg-blue-950 dark:border-blue-800 dark:text-blue-300"
          >
            <Award className="w-4 h-4 mr-2" />
            Trusted Academic Partner Since 2015
          </Badge>

          <h2
            id="about-heading"
            className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6 leading-tight"
          >
            Empowering Academic Success Through
            <span className="text-blue-600 dark:text-blue-400">
              {" "}
              Expert Collaboration
            </span>
          </h2>

          <p className="text-lg md:text-xl text-gray-600 dark:text-gray-300 leading-relaxed">
            We&apos;ve revolutionized academic writing by creating a platform
            where students connect with qualified experts, ensuring quality,
            originality, and timely delivery for every project.
          </p>
        </div>

        {/* Achievement Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {achievements.map((achievement, index) => {
            const Icon = achievement.icon;
            return (
              <Card
                key={index}
                className="text-center p-6 border-0 shadow-lg bg-white dark:bg-slate-800 hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
              >
                <CardContent className="p-0">
                  <div className="mb-4">
                    <Icon className={`w-8 h-8 mx-auto ${achievement.color}`} />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                    {achievement.value}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-300 font-medium">
                    {achievement.label}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 md:grid-cols-4 mb-12 bg-muted p-1 rounded-xl dark:bg-muted/50">
            <TabsTrigger
              value="overview"
              className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300 data-[state=active]:shadow-sm"
            >
              <Globe className="w-4 h-4 mr-2" />
              Overview
            </TabsTrigger>
            <TabsTrigger
              value="mission"
              className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300 data-[state=active]:shadow-sm"
            >
              <TrendingUp className="w-4 h-4 mr-2" />
              Mission
            </TabsTrigger>
            <TabsTrigger
              value="team"
              className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300 data-[state=active]:shadow-sm"
            >
              <Users className="w-4 h-4 mr-2" />
              Our Team
            </TabsTrigger>
            <TabsTrigger
              value="timeline"
              className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300 data-[state=active]:shadow-sm"
            >
              <Clock className="w-4 h-4 mr-2" />
              Journey
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-8">
            <div className="grid md:grid-cols-2 gap-8">
              <Card className="p-8 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/50 dark:to-indigo-950/50 border-blue-200 dark:border-blue-800">
                <CardHeader className="p-0 mb-6">
                  <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                    <Shield className="w-6 h-6 mr-3 text-blue-600" />
                    Our Promise
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-0 space-y-4">
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    We connect students with verified academic experts through
                    our secure platform, ensuring every project meets the
                    highest standards of quality and originality.
                  </p>
                  <div className="grid grid-cols-2 gap-3">
                    {[
                      "100% Original Work",
                      "Timely Delivery",
                      "24/7 Support",
                      "Money-Back Guarantee",
                    ].map((feature, i) => (
                      <div
                        key={i}
                        className="flex items-center text-sm text-gray-600 dark:text-gray-400"
                      >
                        <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                        {feature}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="p-8 bg-gradient-to-br from-amber-50 to-orange-50 dark:from-amber-950/50 dark:to-orange-950/50 border-amber-200 dark:border-amber-800">
                <CardHeader className="p-0 mb-6">
                  <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                    <Award className="w-6 h-6 mr-3 text-amber-600" />
                    Certifications & Security
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <p className="text-gray-700 dark:text-gray-300 mb-4 leading-relaxed">
                    Your data and privacy are protected by industry-leading
                    security standards.
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {certifications.map((cert, i) => (
                      <Badge
                        key={i}
                        variant="secondary"
                        className="bg-amber-100 dark:bg-amber-900/50 text-amber-800 dark:text-amber-200 border-amber-300 dark:border-amber-700"
                      >
                        {cert}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="mission" className="space-y-8">
            <Card className="p-8 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950/50 dark:to-pink-950/50 border-purple-200 dark:border-purple-800">
              <CardContent className="p-0">
                <div className="text-center mb-8">
                  <h3 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                    Transforming Academic Writing
                  </h3>
                  <p className="text-lg text-gray-700 dark:text-gray-300 leading-relaxed max-w-3xl mx-auto">
                    Our mission is to democratize access to high-quality
                    academic assistance, helping students achieve their
                    educational goals while learning from expert guidance.
                  </p>
                </div>

                <Separator className="my-8 bg-purple-200 dark:bg-purple-800" />

                <div className="grid md:grid-cols-3 gap-8">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
                      <BookOpen className="w-8 h-8 text-purple-600 dark:text-purple-400" />
                    </div>
                    <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                      Quality First
                    </h4>
                    <p className="text-gray-600 dark:text-gray-400">
                      Every piece of work undergoes rigorous quality checks and
                      plagiarism screening.
                    </p>
                  </div>

                  <div className="text-center">
                    <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Users className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                    </div>
                    <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                      Expert Network
                    </h4>
                    <p className="text-gray-600 dark:text-gray-400">
                      Our platform connects you with PhD-qualified writers and
                      subject matter experts.
                    </p>
                  </div>

                  <div className="text-center">
                    <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Shield className="w-8 h-8 text-green-600 dark:text-green-400" />
                    </div>
                    <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                      Secure & Confidential
                    </h4>
                    <p className="text-gray-600 dark:text-gray-400">
                      Your privacy is protected with enterprise-grade security
                      and strict confidentiality.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="team" className="space-y-8">
            <div className="grid md:grid-cols-2 gap-8">
              {teamHighlights.map((team, index) => (
                <Card
                  key={index}
                  className="p-6 hover:shadow-lg transition-shadow duration-300"
                >
                  <CardContent className="p-0">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="text-xl font-semibold text-gray-900 dark:text-white">
                        {team.role}
                      </h4>
                      <Badge
                        variant="outline"
                        className="text-lg font-bold px-3 py-1"
                      >
                        {team.count}
                      </Badge>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                      {team.expertise}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>

            <Card className="p-8 bg-gradient-to-r from-green-50 to-teal-50 dark:from-green-950/50 dark:to-teal-950/50 border-green-200 dark:border-green-800">
              <CardContent className="p-0 text-center">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Join Our Network of Experts
                </h3>
                <p className="text-gray-700 dark:text-gray-300 mb-6 leading-relaxed">
                  Are you a qualified academic professional? Join our platform
                  and help students achieve their goals while earning
                  competitive compensation for your expertise.
                </p>
                <Link href="/register/writer">
                  <Button
                    size="lg"
                    className="bg-green-600 hover:bg-green-700 text-white px-8 py-3"
                  >
                    Become a Writer
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="timeline" className="space-y-8">
            <div className="relative">
              <div className="absolute left-4 md:left-1/2 top-0 bottom-0 w-0.5 bg-gradient-to-b from-blue-400 to-purple-600 md:transform md:-translate-x-1/2"></div>

              {milestones.map((milestone, index) => (
                <div
                  key={index}
                  className={`relative flex items-center mb-12 ${index % 2 === 0 ? "md:flex-row" : "md:flex-row-reverse"}`}
                >
                  <div
                    className={`flex-1 ${index % 2 === 0 ? "md:pr-8 pl-12 md:pl-0" : "md:pl-8 pl-12 md:pr-0"}`}
                  >
                    <Card className="p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
                      <CardContent className="p-0">
                        <div className="flex items-center mb-3">
                          <Badge className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 mr-3">
                            {milestone.year}
                          </Badge>
                          <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                            {milestone.event}
                          </h4>
                        </div>
                        <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                          {milestone.description}
                        </p>
                      </CardContent>
                    </Card>
                  </div>

                  <div className="absolute left-0 md:left-1/2 w-8 h-8 bg-blue-500 rounded-full border-4 border-white dark:border-slate-900 shadow-lg md:transform md:-translate-x-1/2 flex items-center justify-center">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </section>
  );
};

export default AboutOverviewSection;
