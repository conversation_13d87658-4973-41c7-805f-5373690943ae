// src/app/api/users/writers/route.ts
import { NextRequest } from "next/server";
import type { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import bcrypt from "bcryptjs";
import {
  apiSuccess,
  apiError,
  parseRequestBody,
  checkPermission,
  safeJsonDate,
} from "@/lib/api-utils";
import { userCreateSchema, paginationSchema } from "@/lib/validations";
import type { UserResponse, UserCreateData } from "@/types/api";
import { Prisma } from "@prisma/client";

// Helper function to generate random 6-digit accountId
const generateAccountId = (): string => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Helper function to generate unique accountId
const generateUniqueAccountId = async (): Promise<string> => {
  let attempts = 0;
  const maxAttempts = 10;
  
  while (attempts < maxAttempts) {
    const accountId = generateAccountId();
    
    // Check if this accountId already exists
    const existing = await prisma.user.findFirst({
      where: { accountId },
      select: { id: true }
    });
    
    if (!existing) {
      return accountId;
    }
    
    attempts++;
  }
  
  // If we couldn't generate a unique ID after 10 attempts, throw an error
  throw new Error("Failed to generate unique account ID after multiple attempts");
};

// GPT WRITERS: - List and create writers, default isApproved to false
export async function GET(req: NextRequest): Promise<NextResponse> {
  // allow ADMIN & CLIENT
  const permissionError = await checkPermission(["ADMIN", "CLIENT"]);
  if (permissionError) return permissionError;

  try {
    const url = new URL(req.url);
    const pageParam = url.searchParams.get("page") ?? "1";
    const limitParam = url.searchParams.get("limit") ?? "10";
    const search = url.searchParams.get("search") ?? "";
    const approvedOnly = url.searchParams.get("approvedOnly") === "true";
    const accountId = url.searchParams.get("accountId"); // Added accountId filter

    const { page, limit } = paginationSchema.parse({
      page: parseInt(pageParam, 10),
      limit: parseInt(limitParam, 10),
    });
    const skip = (page - 1) * limit;

    const where: Prisma.UserWhereInput = { role: "WRITER" };
    if (approvedOnly) where.isApproved = true;
    if (accountId) where.accountId = accountId;
    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { email: { contains: search, mode: "insensitive" } },
        { accountId: { contains: search, mode: "insensitive" } },
      ];
    }

    const total = await prisma.user.count({ where });
    const rows = await prisma.user.findMany({
      where,
      skip,
      take: limit,
      select: {
        id: true,
        accountId: true,
        email: true,
        name: true,
        phone: true,
        role: true,
        isApproved: true,
        emailVerified: true,
        professionalSummary: true,
        experience: true,
        competencies: true,
        educationLevel: true,
        createdAt: true,
        updatedAt: true,
        image: true,
        _count: { select: { writerBids: true, assignedJobs: true } },
      },
      orderBy: { createdAt: "desc" },
    });

    const writers = rows.map((w) => ({
      id: w.id,
      accountId: w.accountId,
      email: w.email,
      name: w.name,
      phone: w.phone,
      role: w.role,
      isApproved: w.isApproved,
      emailVerified: w.emailVerified,
      professionalSummary: w.professionalSummary,
      experience: w.experience,
      competencies: w.competencies,
      educationLevel: w.educationLevel,
      createdAt: w.createdAt.toISOString(),
      updatedAt: w.updatedAt.toISOString(),
      image: w.image,
      bidCount: w._count.writerBids,
      assignmentCount: w._count.assignedJobs,
    })) as (UserResponse & { bidCount: number; assignmentCount: number })[];

    return apiSuccess({
      writers: safeJsonDate(writers),
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (err) {
    console.error("Error fetching writers:", err);
    return apiError("Failed to fetch writers", 500);
  }
}

export async function POST(req: NextRequest): Promise<NextResponse> {
  // only ADMIN can create
  const permissionError = await checkPermission(["ADMIN"]);
  if (permissionError) return permissionError;

  const parsed = await parseRequestBody(req, userCreateSchema);
  if ("success" in parsed && !parsed.success) {
    return apiError(parsed.message, 400, parsed.errors);
  }
  const data = parsed as UserCreateData;

  try {
    if (data.role !== "WRITER") {
      return apiError("Role must be WRITER for this endpoint", 400);
    }

    // Check if email already exists
    const existsEmail = await prisma.user.findUnique({
      where: { email: data.email },
    });
    if (existsEmail) {
      return apiError("User with this email already exists", 409);
    }

    // Generate unique accountId automatically (ignore any provided accountId)
    let uniqueAccountId: string;
    try {
      uniqueAccountId = await generateUniqueAccountId();
    } catch (err) {
      console.error("Error generating unique account ID:", err);
      return apiError("Failed to generate unique account ID", 500);
    }

    const hashed = await bcrypt.hash(data.password, 10);
    const newW = await prisma.user.create({
      data: {
        ...data,
        accountId: uniqueAccountId, // Use auto-generated accountId
        password: hashed,
        isApproved: false, // GPT WRITERS: - default false
      },
      select: {
        id: true,
        accountId: true,
        email: true,
        name: true,
        phone: true,
        role: true,
        isApproved: true,
        createdAt: true,
        updatedAt: true,
        image: true,
      },
    });

    return apiSuccess(
      safeJsonDate(newW) as UserResponse,
      "Writer created successfully"
    );
  } catch (err) {
    console.error("Error creating writer:", err);
    return apiError("Failed to create writer", 500);
  }
}