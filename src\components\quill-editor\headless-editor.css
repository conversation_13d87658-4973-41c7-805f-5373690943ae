/* Headless Quill Editor Styles */

/* Hide the default toolbar since we're using a headless approach */
.headless-quill .ql-toolbar {
  display: none !important;
}

/* Hybrid editor keeps the toolbar visible */
.hybrid-quill .ql-toolbar {
  display: block !important;
  border-bottom: 1px solid hsl(var(--border));
  background-color: hsl(var(--background));
}

/* Improve the editor container styling */
.headless-quill .ql-container {
  border-top: 1px solid hsl(var(--border)) !important;
  font-family: inherit;
}

.hybrid-quill .ql-container {
  font-family: inherit;
}

/* Better focus styling */
.headless-quill .ql-editor:focus,
.hybrid-quill .ql-editor:focus {
  outline: 2px solid hsl(var(--ring)) !important;
  outline-offset: 2px !important;
}

/* Improve placeholder styling */
.headless-quill .ql-editor.ql-blank::before {
  color: hsl(var(--muted-foreground));
  font-style: normal;
  opacity: 0.7;
}

/* Better selection styling */
.headless-quill .ql-editor ::selection {
  background-color: hsl(var(--primary) / 0.2);
}

/* Responsive padding */
.headless-quill .ql-editor,
.hybrid-quill .ql-editor {
  padding: 16px !important;
  line-height: 1.6 !important;
  font-size: 14px !important;
}

@media (min-width: 640px) {
  .headless-quill .ql-editor,
  .hybrid-quill .ql-editor {
    padding: 20px !important;
    font-size: 16px !important;
  }
}

/* Improve list styling */
.headless-quill .ql-editor ul,
.headless-quill .ql-editor ol {
  padding-left: 1.5rem;
}

.headless-quill .ql-editor li {
  margin-bottom: 0.25rem;
}

/* Better blockquote styling */
.headless-quill .ql-editor blockquote {
  border-left: 4px solid hsl(var(--primary));
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: hsl(var(--muted-foreground));
}

/* Code block styling */
.headless-quill .ql-editor pre.ql-syntax {
  background-color: hsl(var(--muted));
  border: 1px solid hsl(var(--border));
  border-radius: 0.375rem;
  padding: 1rem;
  margin: 1rem 0;
  overflow-x: auto;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

/* Heading styles */
.headless-quill .ql-editor h1 {
  font-size: 2rem;
  font-weight: 700;
  margin: 1.5rem 0 1rem 0;
  line-height: 1.2;
}

.headless-quill .ql-editor h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 1.25rem 0 0.75rem 0;
  line-height: 1.3;
}

.headless-quill .ql-editor h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 1rem 0 0.5rem 0;
  line-height: 1.4;
}

/* Link styling */
.headless-quill .ql-editor a {
  color: hsl(var(--primary));
  text-decoration: underline;
  text-decoration-color: hsl(var(--primary) / 0.3);
  text-underline-offset: 2px;
}

.headless-quill .ql-editor a:hover {
  text-decoration-color: hsl(var(--primary));
}

/* Image styling */
.headless-quill .ql-editor img {
  max-width: 100%;
  height: auto;
  border-radius: 0.375rem;
  margin: 1rem 0;
}

/* Video styling */
.headless-quill .ql-editor iframe {
  max-width: 100%;
  border-radius: 0.375rem;
  margin: 1rem 0;
}

/* Text alignment */
.headless-quill .ql-editor .ql-align-center,
.hybrid-quill .ql-editor .ql-align-center {
  text-align: center !important;
}

.headless-quill .ql-editor .ql-align-right,
.hybrid-quill .ql-editor .ql-align-right {
  text-align: right !important;
}

.headless-quill .ql-editor .ql-align-justify,
.hybrid-quill .ql-editor .ql-align-justify {
  text-align: justify !important;
}

/* Color formatting - Allow inline styles to work properly */
.headless-quill .ql-editor [style*="color:"],
.hybrid-quill .ql-editor [style*="color:"] {
  /* Allow inline color styles to work */
}

.headless-quill .ql-editor [style*="background-color:"],
.hybrid-quill .ql-editor [style*="background-color:"] {
  /* Allow inline background-color styles to work */
}

/* Specific color classes that Quill might use */
.headless-quill .ql-editor .ql-color-red,
.hybrid-quill .ql-editor .ql-color-red {
  color: #e60000 !important;
}

.headless-quill .ql-editor .ql-color-blue,
.hybrid-quill .ql-editor .ql-color-blue {
  color: #0066cc !important;
}

.headless-quill .ql-editor .ql-color-green,
.hybrid-quill .ql-editor .ql-color-green {
  color: #008a00 !important;
}

.headless-quill .ql-editor .ql-bg-red,
.hybrid-quill .ql-editor .ql-bg-red {
  background-color: #facccc !important;
}

.headless-quill .ql-editor .ql-bg-blue,
.hybrid-quill .ql-editor .ql-bg-blue {
  background-color: #cce0f5 !important;
}

.headless-quill .ql-editor .ql-bg-green,
.hybrid-quill .ql-editor .ql-bg-green {
  background-color: #cce8cc !important;
}

/* Improve mobile experience */
@media (max-width: 640px) {
  .headless-quill .ql-editor {
    padding: 12px;
    font-size: 16px; /* Prevent zoom on iOS */
  }
  
  .headless-quill .ql-editor h1 {
    font-size: 1.75rem;
  }
  
  .headless-quill .ql-editor h2 {
    font-size: 1.375rem;
  }
  
  .headless-quill .ql-editor h3 {
    font-size: 1.125rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .headless-quill .ql-editor.ql-blank::before {
    color: hsl(var(--muted-foreground));
  }
  
  .headless-quill .ql-editor blockquote {
    color: hsl(var(--muted-foreground));
  }
  
  .headless-quill .ql-editor pre.ql-syntax {
    background-color: hsl(var(--muted));
    color: hsl(var(--foreground));
  }
}

/* Animation for toolbar appearance */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate(-50%, -90%);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -100%);
  }
}

.headless-toolbar-enter {
  animation: fadeInUp 0.2s ease-out;
  /* Ensure maximum visibility */
  z-index: 2147483647 !important;
  position: fixed !important;
  pointer-events: auto !important;
  isolation: isolate !important;
  will-change: transform !important;
  /* Force horizontal layout */
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  justify-content: center !important;
  flex-wrap: nowrap !important;
  white-space: nowrap !important;
}

/* Active toolbar button styling */
.headless-toolbar-button-active {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
}

.headless-toolbar-button-active:hover {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  opacity: 0.9 !important;
}

/* Ensure proper z-index stacking */
.headless-quill,
.hybrid-quill {
  position: relative;
  z-index: 1;
  /* Prevent overflow from clipping toolbar */
  overflow: visible !important;
}

/* Ensure form containers don't clip the toolbar */
.headless-quill form,
.hybrid-quill form,
.headless-quill .form,
.hybrid-quill .form {
  overflow: visible !important;
}

/* Prevent any parent containers from clipping */
.headless-quill *,
.hybrid-quill * {
  /* Allow toolbar to escape container bounds */
  contain: none !important;
}

/* Selection highlight animation */
.headless-quill .ql-editor .ql-cursor {
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}
