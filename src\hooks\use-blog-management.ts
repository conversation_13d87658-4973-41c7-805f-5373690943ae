"use client";

import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";

interface Author {
  id: string;
  name: string;
  qualifications: string;
}

interface Category {
  id: string;
  name: string;
  slug: string;
}

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  body: string;
  metaTitle: string;
  metaDescription: string;
  imageUrl: string;
  imageAlt: string;
  keywords: string[];
  faqs: string[];
  createdAt: string;
  updatedAt: string;
  pageViews: number;
  author: Author;
  category: Category;
  authorId: string;
  categoryId: string;
}

interface BlogFormData {
  title: string;
  slug: string;
  body: string;
  metaTitle: string;
  metaDescription: string;
  imageUrl: string;
  imageAlt: string;
  authorId: string;
  categoryId: string;
  keywords: string[];
  faqs: Array<{ question: string; answer: string }>;
}

export function useBlogManagement() {
  const [blogs, setBlogs] = useState<BlogPost[]>([]);
  const [authors, setAuthors] = useState<Author[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchBlogs = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch("/api/blog");
      if (response.ok) {
        const data = await response.json();
        setBlogs(data);
      } else {
        throw new Error("Failed to fetch blog posts");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch blog posts";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchAuthors = useCallback(async () => {
    try {
      const response = await fetch("/api/blog/authors");
      if (response.ok) {
        const data = await response.json();
        setAuthors(data);
      } else {
        throw new Error("Failed to fetch authors");
      }
    } catch (err) {
      console.error("Error fetching authors:", err);
    }
  }, []);

  const fetchCategories = useCallback(async () => {
    try {
      const response = await fetch("/api/blog/categories");
      if (response.ok) {
        const data = await response.json();
        setCategories(data);
      } else {
        throw new Error("Failed to fetch categories");
      }
    } catch (err) {
      console.error("Error fetching categories:", err);
    }
  }, []);

  const fetchBlogPost = useCallback(async (id: string): Promise<BlogPost | null> => {
    try {
      const response = await fetch(`/api/blog/${id}`);
      if (response.ok) {
        const blog: BlogPost = await response.json();
        return blog;
      } else {
        throw new Error("Failed to fetch blog post");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch blog post";
      toast.error(errorMessage);
      return null;
    }
  }, []);

  const createBlogPost = useCallback(async (formData: BlogFormData): Promise<boolean> => {
    try {
      // Convert FAQ objects to strings in the format "Question|Answer"
      const faqStrings = formData.faqs.map(faq => `${faq.question}|${faq.answer}`);

      const response = await fetch("/api/blog", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...formData,
          faqs: faqStrings,
        }),
      });

      if (response.ok) {
        toast.success("Blog post created successfully!");
        await fetchBlogs(); // Refresh the list
        return true;
      } else {
        const error = await response.json();
        if (response.status === 409) {
          toast.error("Slug already exists. Please choose a different slug.");
        } else {
          toast.error(error.error || "Failed to create blog post");
        }
        return false;
      }
    } catch (err) {
      toast.error("Failed to create blog post. Please try again.");
      console.error(err);
      return false;
    }
  }, [fetchBlogs]);

  const updateBlogPost = useCallback(async (id: string, formData: BlogFormData): Promise<boolean> => {
    try {
      // Convert FAQ objects to strings in the format "Question|Answer"
      const faqStrings = formData.faqs.map(faq => `${faq.question}|${faq.answer}`);

      const response = await fetch(`/api/blog/${id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...formData,
          faqs: faqStrings,
        }),
      });

      if (response.ok) {
        toast.success("Blog post updated successfully!");
        await fetchBlogs(); // Refresh the list
        return true;
      } else {
        const error = await response.json();
        if (response.status === 409) {
          toast.error("Slug already exists. Please choose a different slug.");
        } else {
          toast.error(error.error || "Failed to update blog post");
        }
        return false;
      }
    } catch (err) {
      toast.error("Failed to update blog post. Please try again.");
      console.error(err);
      return false;
    }
  }, [fetchBlogs]);

  const deleteBlogPost = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await fetch("/api/blog", {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ id }),
      });

      if (response.ok) {
        toast.success("Blog post deleted successfully");
        await fetchBlogs(); // Refresh the list
        return true;
      } else {
        toast.error("Failed to delete blog post");
        return false;
      }
    } catch (err) {
      console.error("Error deleting blog:", err);
      toast.error("Failed to delete blog post");
      return false;
    }
  }, [fetchBlogs]);

  const generateSlug = useCallback((title: string): string => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }, []);

  const parseFaqsFromStrings = useCallback((faqStrings: string[]): Array<{ question: string; answer: string }> => {
    return faqStrings.map(faqString => {
      const [question, answer] = faqString.split('|');
      return { question: question || '', answer: answer || '' };
    });
  }, []);

  const stripHtml = useCallback((html: string): string => {
    return html.replace(/<[^>]+>/g, '');
  }, []);

  const truncateText = useCallback((text: string, maxLength: number): string => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  }, []);

  // Initialize data on mount
  useEffect(() => {
    fetchBlogs();
    fetchAuthors();
    fetchCategories();
  }, [fetchBlogs, fetchAuthors, fetchCategories]);

  return {
    // Data
    blogs,
    authors,
    categories,
    loading,
    error,
    
    // Actions
    fetchBlogs,
    fetchAuthors,
    fetchCategories,
    fetchBlogPost,
    createBlogPost,
    updateBlogPost,
    deleteBlogPost,
    
    // Utilities
    generateSlug,
    parseFaqsFromStrings,
    stripHtml,
    truncateText,
  };
}

export type { BlogPost, BlogFormData, Author, Category };
