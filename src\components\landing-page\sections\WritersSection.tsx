import React from "react";
import Image from "next/image";
import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Star,
  Award,
  GraduationCap,
  Globe,
  Sparkles,
  TrendingUp,
  Zap,
  Heart,
} from "lucide-react";
import { writersData } from "./WritersData";

const WritersSection = () => {
  return (
    <section className="py-16 bg-gradient-to-br from-background via-muted/30 to-background">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 bg-primary/10 text-primary px-3 py-1.5 rounded-full text-sm font-medium mb-4">
            <Award className="w-4 h-4" />
            Meet Our Expert Writers
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
            Work with Top-Rated Academic Writers
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
            Our carefully vetted team of PhD holders and subject matter experts
            delivers exceptional academic writing that meets the highest
            standards.
          </p>
        </div>

        {/* Writers Carousel with improved vertical spacing */}
        <div className="relative px-16 py-4">
          {/* Floating Background Elements */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            <div className="absolute top-10 left-10 w-24 h-24 bg-primary/5 rounded-full blur-2xl animate-pulse"></div>
            <div className="absolute bottom-10 right-10 w-32 h-32 bg-chart-2/5 rounded-full blur-2xl animate-pulse delay-1000"></div>
            <div className="absolute top-20 right-20 w-20 h-20 bg-chart-1/5 rounded-full blur-xl animate-pulse delay-500"></div>
          </div>

          <Carousel
            opts={{
              align: "start",
              loop: true,
              slidesToScroll: 1,
            }}
            className="w-full h-auto py-4"
          >
            <CarouselContent className="-ml-2 md:-ml-3 py-2">
              {writersData.map((writer) => (
                <CarouselItem
                  key={writer.id}
                  className="pl-2 md:pl-3 basis-full sm:basis-1/2 lg:basis-1/3 py-2"
                >
                  <div className="relative group h-full">
                    {/* Floating Animation Wrapper */}
                    <div className="absolute inset-0 bg-gradient-to-r from-primary/10 via-chart-2/10 to-chart-1/10 rounded-xl blur-lg opacity-0 group-hover:opacity-100 transition-all duration-700 animate-pulse"></div>

                    <Card className="relative h-full group hover:shadow-xl transition-all duration-500 border-0 bg-gradient-to-br from-card/80 via-card/60 to-card/40 backdrop-blur-lg hover:from-card hover:via-card/90 hover:to-card/80 hover:scale-[1.02] overflow-hidden">
                      {/* Animated Border */}
                      <div className="absolute inset-0 bg-gradient-to-r from-primary/20 via-chart-2/20 to-chart-1/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                      {/* Floating Stars Animation */}
                      <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-all duration-300 delay-200">
                        <Sparkles className="w-3 h-3 text-primary animate-pulse" />
                      </div>

                      <CardContent className="relative p-4">
                        {/* Writer Image and Basic Info */}
                        <div className="flex items-start gap-3 mb-4">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <div className="relative">
                                  <div className="absolute inset-0 bg-gradient-to-r from-primary to-chart-2 rounded-full p-0.5 group-hover:animate-spin-slow">
                                    <div className="w-full h-full bg-background rounded-full"></div>
                                  </div>
                                  <Image
                                    src={writer.image}
                                    alt={`${writer.name} - Academic Writer`}
                                    width={56}
                                    height={56}
                                    className="relative w-14 h-14 rounded-full object-cover border-2 border-transparent group-hover:border-primary/30 transition-all duration-300 z-10"
                                  />
                                  {writer.topRated && (
                                    <div className="absolute -top-1 -right-1 z-20">
                                      <div className="relative">
                                        <div className="absolute inset-0 bg-yellow-400 rounded-full blur-sm animate-pulse"></div>
                                        <Badge className="relative bg-gradient-to-r from-yellow-400 to-orange-400 hover:from-yellow-500 hover:to-orange-500 text-white text-xs px-1.5 py-0.5 shadow-lg">
                                          <Award className="w-2.5 h-2.5 mr-0.5" />
                                          Top
                                        </Badge>
                                      </div>
                                    </div>
                                  )}

                                  {/* Floating Heart for Favorites */}
                                  <div className="absolute -bottom-0.5 -right-1 opacity-0 group-hover:opacity-100 transition-all duration-300 delay-300">
                                    <div className="bg-red-500 rounded-full p-1 shadow-lg animate-bounce">
                                      <Heart className="w-2.5 h-2.5 text-white fill-white" />
                                    </div>
                                  </div>
                                </div>
                              </TooltipTrigger>
                              <TooltipContent className="max-w-xs p-3 bg-gradient-to-r from-card to-card/90 border-primary/20">
                                <p className="font-medium mb-1 text-sm">
                                  {writer.name}
                                </p>
                                <p className="text-xs text-muted-foreground">
                                  {writer.description}
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>

                          <div className="flex-1 min-w-0">
                            <h3 className="font-bold text-lg mb-1 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent group-hover:from-primary group-hover:to-chart-2 transition-all duration-300 leading-tight">
                              {writer.name}
                            </h3>
                            <div className="flex items-center gap-2 mb-2">
                              <div className="flex items-center gap-1 bg-yellow-50 dark:bg-yellow-900/20 px-1.5 py-0.5 rounded-full">
                                <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                                <span className="font-bold text-xs">
                                  {writer.rating}
                                </span>
                              </div>
                              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                <TrendingUp className="w-2.5 h-2.5" />
                                <span>
                                  {writer.completedOrders.toLocaleString()}
                                </span>
                              </div>
                            </div>
                            <div className="flex items-center gap-1.5 text-xs text-muted-foreground bg-muted/30 px-2 py-0.5 rounded-full">
                              <GraduationCap className="w-3 h-3 text-primary" />
                              <span className="font-medium">
                                {writer.degree.split(" ")[0]}
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Specializations with Animated Tags */}
                        <div className="mb-4">
                          <div className="flex flex-wrap gap-1">
                            {writer.specializations
                              .slice(0, 3)
                              .map((spec, specIndex) => (
                                <Badge
                                  key={specIndex}
                                  variant="secondary"
                                  className="text-xs bg-gradient-to-r from-primary/10 to-chart-2/10 hover:from-primary/20 hover:to-chart-2/20 border-primary/20 transition-all duration-300 hover:scale-105 px-1.5 py-0.5"
                                >
                                  {spec}
                                </Badge>
                              ))}
                            {writer.specializations.length > 3 && (
                              <Badge
                                variant="secondary"
                                className="text-xs bg-muted/50 px-1.5 py-0.5"
                              >
                                +{writer.specializations.length - 3}
                              </Badge>
                            )}
                          </div>
                        </div>

                        {/* Enhanced Stats Grid */}
                        <div className="grid grid-cols-2 gap-2 mb-4">
                          <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-2 rounded-lg border border-green-200/50 dark:border-green-700/30">
                            <div className="text-xs text-green-600 dark:text-green-400 font-medium mb-0.5">
                              Success Rate
                            </div>
                            <div className="font-bold text-sm text-green-700 dark:text-green-300">
                              {writer.successRate}%
                            </div>
                          </div>
                          <div className="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 p-2 rounded-lg border border-blue-200/50 dark:border-blue-700/30">
                            <div className="text-xs text-blue-600 dark:text-blue-400 font-medium mb-0.5">
                              Response
                            </div>
                            <div className="font-bold text-sm text-blue-700 dark:text-blue-300">
                              {writer.responseTime}
                            </div>
                          </div>
                          <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-2 rounded-lg border border-purple-200/50 dark:border-purple-700/30">
                            <div className="text-xs text-purple-600 dark:text-purple-400 font-medium mb-0.5">
                              Experience
                            </div>
                            <div className="font-bold text-sm text-purple-700 dark:text-purple-300">
                              {writer.experience}
                            </div>
                          </div>
                          <div className="bg-gradient-to-br from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 p-2 rounded-lg border border-orange-200/50 dark:border-orange-700/30">
                            <div className="text-xs text-orange-600 dark:text-orange-400 font-medium mb-0.5 flex items-center gap-1">
                              <Globe className="w-2.5 h-2.5" />
                              Languages
                            </div>
                            <div className="font-bold text-sm text-orange-700 dark:text-orange-300">
                              {writer.languages.length}
                            </div>
                          </div>
                        </div>

                        {/* CTA Button with Enhanced Animation */}
                        <Link href="/create-order">
                          <Button className="w-full bg-gradient-to-r from-primary to-chart-2 hover:from-primary/90 hover:to-chart-2/90 text-primary-foreground transition-all duration-300 hover:scale-105 hover:shadow-lg group relative overflow-hidden text-sm py-2">
                            <span className="relative z-10 flex items-center justify-center gap-1.5">
                              <Zap className="w-3 h-3" />
                              Hire {writer.name.split(" ")[1]}
                            </span>
                            <div className="absolute inset-0 bg-gradient-to-r from-chart-1/20 to-chart-3/20 translate-x-[-100%] group-hover:translate-x-0 transition-transform duration-300"></div>
                          </Button>
                        </Link>
                      </CardContent>
                    </Card>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>

            {/* Enhanced Navigation with better vertical positioning */}
            <CarouselPrevious className="absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 bg-background/95 backdrop-blur-md hover:bg-primary hover:text-primary-foreground border border-primary/20 shadow-lg transition-all duration-300 hover:scale-110 z-10" />
            <CarouselNext className="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 bg-background/95 backdrop-blur-md hover:bg-primary hover:text-primary-foreground border border-primary/20 shadow-lg transition-all duration-300 hover:scale-110 z-10" />
          </Carousel>
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-2">
          <div className="inline-flex flex-col sm:flex-row gap-3 items-center">
            <Link href="/create-order">
              <Button
                size="lg"
                className="bg-primary hover:bg-primary/90 text-primary-foreground px-6"
              >
                Hire Our Writers
              </Button>
            </Link>
            <Link href="/how-it-works">
              <Button size="lg" variant="outline" className="px-6">
                How It Works
              </Button>
            </Link>
          </div>
          <p className="text-sm text-muted-foreground mt-3">
            Can&apos;t find the right writer? Our team will match you with the
            perfect expert for your project.
          </p>
        </div>
      </div>
    </section>
  );
};

export default WritersSection;
