"use client";

import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AnalyticsCard } from "./analytics-card";
import type { RevenueAnalytics } from "@/types/analytics";
import { LineChartComponent } from "./charts/line-chart";
import { PieChartComponent } from "./charts/pie-chart";
import { AreaChartComponent } from "./charts/area-chart";
import { DataTable } from "@/components/ui/data-table";
import { ColumnDef } from "@tanstack/react-table";
import { IconCash, IconTrendingUp, IconChartBar } from "@tabler/icons-react";
import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";

// Define table columns for recent transactions
const transactionsColumns: ColumnDef<RevenueAnalytics["recentTransactions"][0]>[] = [
  {
    accessorKey: "id",
    header: "ID",
    cell: ({ row }) => {
      const id = row.getValue("id") as string;
      return id.substring(0, 8); // Show only first 8 characters
    },
  },
  {
    accessorKey: "clientName",
    header: "Client",
  },
  {
    accessorKey: "amount",
    header: "Amount",
    cell: ({ row }) => {
      const value = row.getValue("amount") as number;
      return `$${value.toFixed(2)}`;
    },
  },
  {
    accessorKey: "date",
    header: "Date",
    cell: ({ row }) => {
      const value = row.getValue("date");
      // Handle both Date objects and date strings
      return value ? new Date(value as string).toLocaleDateString() : "N/A";
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      return (
        <span
          className={cn(
            "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold",
            status === "Completed" && "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
            status === "Pending" && "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
            status === "Failed" && "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
          )}
        >
          {status}
        </span>
      );
    },
  },
];

interface RevenueAnalyticsProps {
  variant: "card" | "chart" | "full";
  className?: string;
}

export function RevenueAnalytics({ variant, className }: RevenueAnalyticsProps) {
  const [data, setData] = useState<RevenueAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch("/api/analytics/revenue");
        if (!response.ok) {
          throw new Error(`Error fetching revenue analytics: ${response.statusText}`);
        }
        const result = await response.json();
        setData(result);
      } catch (err) {
        setError((err as Error).message || "Failed to fetch revenue analytics");
        console.error("Error fetching revenue analytics:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle><Skeleton className="h-4 w-[200px]" /></CardTitle>
          <CardDescription><Skeleton className="h-4 w-[300px]" /></CardDescription>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[300px] w-full" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle>Error Loading Revenue Analytics</CardTitle>
          <CardDescription>{error}</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  if (!data) {
    return null;
  }

  // Card variant - summary statistics
  if (variant === "card") {
    return (
      <AnalyticsCard
        title="Total Revenue"
        value={`$${data.totalRevenue.toFixed(2)}`}
        description={`$${data.monthlyRevenue.toFixed(2)} this month`}
        icon={<IconCash className="h-4 w-4" />}
        trend={{
          value: data.revenueGrowth,
          isPositive: data.revenueGrowth > 0,
        }}
        className={className}
      />
    );
  }

  // Chart variant - revenue by month and projected revenue
  if (variant === "chart") {
    // Combine actual and projected revenue data
    const combinedData = [
      ...data.revenueByMonth,
      ...data.projectedRevenue,
    ].map(item => ({
      ...item,
      date: `${item.date}-01`, // Convert to YYYY-MM-DD format
    }));

    return (
      <LineChartComponent
        title="Revenue Trend & Projection"
        description="Historical and projected revenue"
        data={combinedData}
        categories={[
          { name: "value", color: "hsl(var(--primary))" },
        ]}
        className={className}
      />
    );
  }

  // Full variant - comprehensive revenue analytics
  return (
    <div className={cn("space-y-6", className)}>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <AnalyticsCard
          title="Total Revenue"
          value={`$${data.totalRevenue.toFixed(2)}`}
          icon={<IconCash className="h-4 w-4" />}
        />
        <AnalyticsCard
          title="Monthly Revenue"
          value={`$${data.monthlyRevenue.toFixed(2)}`}
          description="Current month"
          icon={<IconCash className="h-4 w-4" />}
          trend={{
            value: data.revenueGrowth,
            isPositive: data.revenueGrowth > 0,
          }}
        />
        <AnalyticsCard
          title="Revenue Growth"
          value={`${data.revenueGrowth.toFixed(1)}%`}
          description="Month over month"
          icon={<IconTrendingUp className="h-4 w-4" />}
          trend={{
            value: data.revenueGrowth,
            isPositive: data.revenueGrowth > 0,
          }}
        />
        <AnalyticsCard
          title="Avg. Order Value"
          value={`$${data.averageOrderValue.toFixed(2)}`}
          description="Per assignment"
          icon={<IconChartBar className="h-4 w-4" />}
        />
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <LineChartComponent
          title="Revenue Trend"
          description="Monthly revenue over the past year"
          data={data.revenueByMonth.map(item => ({
            ...item,
            date: `${item.date}-01`, // Convert to YYYY-MM-DD format
          }))}
          categories={[{ name: "value", color: "hsl(var(--primary))" }]}
        />
        <PieChartComponent
          title="Revenue by Category"
          description="Distribution by academic level"
          data={data.revenueByCategory.map(({ category, value }) => ({ name: category, value }))}
        />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Revenue Projection</CardTitle>
          <CardDescription>Estimated revenue for the next 6 months</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <AreaChartComponent
              title=""
              data={[
                ...data.revenueByMonth.slice(-6), // Last 6 months of actual data
                ...data.projectedRevenue, // 6 months of projected data
              ].map(item => ({
                ...item,
                date: `${item.date}-01`, // Convert to YYYY-MM-DD format
              }))}
              categories={[{ name: "value", color: "hsl(var(--primary))" }]}
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Recent Transactions</CardTitle>
          <CardDescription>Latest payments received</CardDescription>
        </CardHeader>
        <CardContent>
          <DataTable columns={transactionsColumns} data={data.recentTransactions} />
        </CardContent>
      </Card>
    </div>
  );
}
