"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { useCoupon } from "@/hooks/use-coupon";
import {
  Ticket,
  Check,
  X,
  Loader2,
  Tag,
  Percent,
  DollarSign,
} from "lucide-react";
import { cn } from "@/lib/utils";

interface CouponInputProps {
  originalPrice: number;
  onCouponApplied?: (discountAmount: number, finalPrice: number, couponCode: string) => void;
  onCouponRemoved?: () => void;
  className?: string;
  disabled?: boolean;
}

export function CouponInput({
  originalPrice,
  onCouponApplied,
  onCouponRemoved,
  className,
  disabled = false,
}: CouponInputProps) {
  const [couponCode, setCouponCode] = useState("");
  const {
    couponState,
    isValidating,
    isApplying,
    validationError,
    applyCoupon,
    removeCoupon,
    updateOriginalPrice,
  } = useCoupon(originalPrice);

  // Console error for validateCoupon if needed for debugging
  console.error("validateCoupon function available but not used in this component");

  // Update original price when it changes
  React.useEffect(() => {
    updateOriginalPrice(originalPrice);
  }, [originalPrice, updateOriginalPrice]);

  // Handle coupon application
  const handleApplyCoupon = async () => {
    if (!couponCode.trim()) return;

    const result = await applyCoupon(couponCode.trim().toUpperCase());
    
    if (result.success && result.discountAmount && result.finalPrice) {
      onCouponApplied?.(result.discountAmount, result.finalPrice, couponCode.trim().toUpperCase());
      setCouponCode("");
    }
  };

  // Handle coupon removal
  const handleRemoveCoupon = () => {
    removeCoupon();
    onCouponRemoved?.();
  };

  // Handle Enter key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !disabled && !isValidating && !isApplying) {
      e.preventDefault();
      handleApplyCoupon();
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Coupon Input Section */}
      {!couponState.isApplied && (
        <div className="space-y-3">
          <Label htmlFor="coupon-code" className="flex items-center gap-2">
            <Ticket className="h-4 w-4" />
            Have a coupon code?
          </Label>
          <div className="flex gap-2">
            <Input
              id="coupon-code"
              placeholder="Enter coupon code (e.g., 123-456-789)"
              value={couponCode}
              onChange={(e) => setCouponCode(e.target.value.toUpperCase())}
              onKeyPress={handleKeyPress}
              disabled={disabled || isValidating || isApplying}
              className="font-mono"
            />
            <Button
              type="button"
              variant="outline"
              onClick={handleApplyCoupon}
              disabled={disabled || !couponCode.trim() || isValidating || isApplying}
            >
              {isValidating || isApplying ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                "Apply"
              )}
            </Button>
          </div>
          
          {/* Validation Error */}
          {validationError && (
            <div className="flex items-center gap-2 text-sm text-destructive">
              <X className="h-4 w-4" />
              {validationError}
            </div>
          )}
        </div>
      )}

      {/* Applied Coupon Display */}
      {couponState.isApplied && couponState.coupon && (
        <Card className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
          <CardContent className="p-4">
            <div className="flex items-start justify-between">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-600" />
                  <span className="font-medium text-green-800 dark:text-green-200">
                    Coupon Applied!
                  </span>
                </div>
                
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <Tag className="h-3 w-3 text-green-600" />
                    <code className="text-sm font-mono bg-green-100 dark:bg-green-900 px-2 py-1 rounded">
                      {couponState.couponCode}
                    </code>
                  </div>
                  
                  <p className="text-sm text-green-700 dark:text-green-300">
                    {couponState.coupon.description}
                  </p>
                  
                  <div className="flex items-center gap-4 text-sm">
                    <div className="flex items-center gap-1">
                      <Percent className="h-3 w-3 text-green-600" />
                      <span className="text-green-700 dark:text-green-300">
                        {couponState.coupon.discountPercentage}% OFF
                      </span>
                    </div>
                    
                    <div className="flex items-center gap-1">
                      <DollarSign className="h-3 w-3 text-green-600" />
                      <span className="text-green-700 dark:text-green-300">
                        Save ${couponState.discountAmount.toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={handleRemoveCoupon}
                disabled={disabled}
                className="text-green-700 hover:text-green-800 hover:bg-green-100 dark:text-green-300 dark:hover:text-green-200 dark:hover:bg-green-900"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Price Summary */}
      {originalPrice > 0 && (
        <div className="space-y-2">
          <Separator />
          <div className="space-y-1">
            {couponState.isApplied ? (
              <>
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>Original Price:</span>
                  <span className="line-through">${couponState.originalPrice.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm text-green-600">
                  <span>Discount:</span>
                  <span>-${couponState.discountAmount.toFixed(2)}</span>
                </div>
                <div className="flex justify-between font-semibold text-lg">
                  <span>Final Price:</span>
                  <span className="text-green-600">${couponState.finalPrice.toFixed(2)}</span>
                </div>
              </>
            ) : (
              <div className="flex justify-between font-semibold">
                <span>Total Price:</span>
                <span>${originalPrice.toFixed(2)}</span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

// Simplified version for inline use
export function InlineCouponInput({
  originalPrice,
  onCouponApplied,
  onCouponRemoved,
  className,
  disabled = false,
}: CouponInputProps) {
  const [couponCode, setCouponCode] = useState("");
  const {
    couponState,
    isValidating,
    isApplying,
    validationError,
    applyCoupon,
    removeCoupon,
    updateOriginalPrice,
  } = useCoupon(originalPrice);

  // Update original price when it changes
  React.useEffect(() => {
    updateOriginalPrice(originalPrice);
  }, [originalPrice, updateOriginalPrice]);

  // Handle coupon application
  const handleApplyCoupon = async () => {
    if (!couponCode.trim()) return;

    const result = await applyCoupon(couponCode.trim().toUpperCase());
    
    if (result.success && result.discountAmount && result.finalPrice) {
      onCouponApplied?.(result.discountAmount, result.finalPrice, couponCode.trim().toUpperCase());
      setCouponCode("");
    }
  };

  // Handle coupon removal
  const handleRemoveCoupon = () => {
    removeCoupon();
    onCouponRemoved?.();
  };

  if (couponState.isApplied) {
    return (
      <div className={cn("flex items-center gap-2 p-2 bg-green-50 dark:bg-green-950 rounded-lg border border-green-200 dark:border-green-800", className)}>
        <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
          <Ticket className="h-3 w-3 mr-1" />
          {couponState.couponCode}
        </Badge>
        <span className="text-sm text-green-700 dark:text-green-300">
          {couponState.coupon?.discountPercentage}% OFF
        </span>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={handleRemoveCoupon}
          disabled={disabled}
          className="h-6 w-6 p-0 text-green-700 hover:text-green-800"
        >
          <X className="h-3 w-3" />
        </Button>
      </div>
    );
  }

  return (
    <div className={cn("flex gap-2", className)}>
      <Input
        placeholder="Coupon code"
        value={couponCode}
        onChange={(e) => setCouponCode(e.target.value.toUpperCase())}
        disabled={disabled || isValidating || isApplying}
        className="font-mono text-sm"
      />
      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={handleApplyCoupon}
        disabled={disabled || !couponCode.trim() || isValidating || isApplying}
      >
        {isValidating || isApplying ? (
          <Loader2 className="h-3 w-3 animate-spin" />
        ) : (
          "Apply"
        )}
      </Button>
      {validationError && (
        <div className="absolute mt-8 text-xs text-destructive">
          {validationError}
        </div>
      )}
    </div>
  );
}
