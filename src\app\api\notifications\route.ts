import { NextRequest } from "next/server";
import { getSession } from "@/lib/auth-utils";
import { apiSuccess, apiError, parseRequestBody, formatZodErrors } from "@/lib/api-utils";
import prisma from "@/lib/prisma";
import { NotificationType } from "@prisma/client";
import { notificationCreateSchema, notificationUpdateSchema, notificationQuerySchema } from "@/lib/validations";

export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return apiError("Authentication required", 401);
    }

    // Parse and validate request body
    const parsed = await parseRequestBody(request, notificationCreateSchema);
    if ("success" in parsed && parsed.success === false) {
      return apiError(parsed.message, 400, parsed.errors);
    }

    const { targetUserId, notification } = parsed as {
      targetUserId: string;
      notification: {
        type: NotificationType;
        title: string;
        message: string;
        taskId?: string;
        assignmentId?: string;
      };
    };

    // Store notification in database
    const savedNotification = await prisma.notification.create({
      data: {
        userId: targetUserId,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        taskId: notification.taskId,
        assignmentId: notification.assignmentId,
        read: false,
      },
    });

    return apiSuccess(savedNotification, "Notification sent successfully");
  } catch (error) {
    console.error("Error sending notification:", error);
    const message =
      error instanceof Error ? error.message : "Failed to send notification";
    return apiError(message, 500);
  }
}

// Get notifications for the current user
export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return apiError("Authentication required", 401);
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = {
      read: searchParams.get("read") ? searchParams.get("read") === "true" : undefined,
      type: searchParams.get("type") as NotificationType | undefined,
      limit: searchParams.get("limit") ? parseInt(searchParams.get("limit")!) : undefined,
      offset: searchParams.get("offset") ? parseInt(searchParams.get("offset")!) : undefined,
    };

    // Validate query parameters
    const validatedQuery = notificationQuerySchema.safeParse(queryParams);
    if (!validatedQuery.success) {
      return apiError("Invalid query parameters", 400, formatZodErrors(validatedQuery.error.errors));
    }

    const { read, type, limit = 50, offset = 0 } = validatedQuery.data;

    // Build where clause
    const where: {
      userId: string;
      read?: boolean;
      type?: NotificationType;
    } = {
      userId: session.user.id,
    };

    if (read !== undefined) {
      where.read = read;
    }

    if (type) {
      where.type = type;
    }

    const notifications = await prisma.notification.findMany({
      where,
      orderBy: {
        createdAt: "desc",
      },
      take: limit,
      skip: offset,
    });

    return apiSuccess(notifications);
  } catch (error) {
    console.error("Error fetching notifications:", error);
    const message =
      error instanceof Error ? error.message : "Failed to fetch notifications";
    return apiError(message, 500);
  }
}

// Mark notifications as read
export async function PATCH(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return apiError("Authentication required", 401);
    }

    const parsed = await parseRequestBody(request, notificationUpdateSchema);
    if ("success" in parsed && parsed.success === false) {
      return apiError(parsed.message, 400, parsed.errors);
    }

    const { notificationIds } = parsed as { notificationIds: string[] };

    await prisma.notification.updateMany({
      where: {
        id: {
          in: notificationIds,
        },
        userId: session.user.id, // Ensure user can only update their own notifications
      },
      data: {
        read: true,
      },
    });

    return apiSuccess(undefined, "Notifications marked as read");
  } catch (error) {
    console.error("Error updating notifications:", error);
    const message =
      error instanceof Error ? error.message : "Failed to update notifications";
    return apiError(message, 500);
  }
}

// Delete all notifications for the current user
export async function DELETE() {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return apiError("Authentication required", 401);
    }

    await prisma.notification.deleteMany({
      where: {
        userId: session.user.id,
      },
    });

    return apiSuccess(undefined, "All notifications cleared");
  } catch (error) {
    console.error("Error clearing notifications:", error);
    const message =
      error instanceof Error ? error.message : "Failed to clear notifications";
    return apiError(message, 500);
  }
}
