// src/app/api/users/writers/[id]/route.ts
import { NextRequest } from "next/server";
import type { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import bcrypt from "bcryptjs";
import {
  apiSuccess,
  apiError,
  parseRequestBody,
  checkPermission,
  getCurrentUserId,
  safeJsonDate,
} from "@/lib/api-utils";
import { userUpdateSchema } from "@/lib/validations";
import type { UserResponse, UserUpdateData } from "@/types/api";

// GPT WRITERS: - use async params signature
interface RouteParams {
  params: Promise<{ id: string }>;
}

// GET a single writer
export async function GET(
  _req: NextRequest,
  { params }: RouteParams
): Promise<NextResponse> {
  const { id } = await params;
  const me = await getCurrentUserId();
  if (!me) return apiError("Authentication required", 401);

  const you = await prisma.user.findUnique({
    where: { id: me },
    select: { role: true },
  });
  const allowed = you?.role === "ADMIN" || you?.role === "CLIENT" || me === id;
  if (!allowed) return apiError("No permission", 403);

  try {
    const w = await prisma.user.findUnique({
      where: { id, role: "WRITER" },
      select: {
        id: true,
        accountId: true,
        email: true,
        name: true,
        phone: true,
        role: true,
        isApproved: true,
        emailVerified: true,
        professionalSummary: true,
        experience: true,
        competencies: true,
        educationLevel: true,
        rating: true,
        createdAt: true,
        updatedAt: true,
        image: true,
        writerBids: {
          select: { id: true, status: true, createdAt: true },
          take: 5,
          orderBy: { createdAt: "desc" },
        },
        assignedJobs: {
          select: { id: true, status: true, startDate: true, deadline: true },
          take: 5,
          orderBy: { createdAt: "desc" },
        },
        _count: { select: { writerBids: true, assignedJobs: true } },
      },
    });
    if (!w) return apiError("Writer not found", 404);

    const resp = {
      id: w.id,
      accountId: w.accountId,
      email: you?.role === "CLIENT" && me !== id ? "[Protected]" : w.email,
      name: w.name,
      phone: w.phone,
      role: w.role,
      isApproved: w.isApproved,
      emailVerified: w.emailVerified,
      professionalSummary: w.professionalSummary,
      experience: w.experience,
      competencies: w.competencies,
      educationLevel: w.educationLevel,
      rating: w.rating,
      createdAt: w.createdAt.toISOString(),
      updatedAt: w.updatedAt.toISOString(),
      image: w.image,
      bidCount: w._count.writerBids,
      jobCount: w._count.assignedJobs,
      recentBids: w.writerBids,
      recentJobs: w.assignedJobs,
    };

    return apiSuccess(safeJsonDate(resp));
  } catch (err) {
    console.error("Error fetching writer:", err);
    return apiError("Failed to fetch writer", 500);
  }
}

// PUT update a writer
export async function PUT(
  request: NextRequest,
  props: RouteParams
): Promise<NextResponse> {
  try {
    // 1) get the writer ID
    const { id: writerId } = await props.params;

    // 2) ensure authenticated
    const currentUserId = await getCurrentUserId();
    if (!currentUserId) {
      return apiError("Authentication required", 401);
    }

    // 3) fetch current user's role
    const currentUser = await prisma.user.findUnique({
      where: { id: currentUserId },
      select: { role: true },
    });
    const isAdmin = currentUser?.role === "ADMIN";
    const isSelf = currentUserId === writerId;

    // 4) permission check
    if (!isAdmin && !isSelf) {
      return apiError("You don't have permission to update this writer", 403);
    }

    // 5) parse & validate body
    const parsed = await parseRequestBody(request, userUpdateSchema);
    if ("success" in parsed && parsed.success === false) {
      return apiError(parsed.message, 400, parsed.errors);
    }
    const updateData = parsed as UserUpdateData;

    // 6) ensure writer exists
    const existing = await prisma.user.findUnique({
      where: { id: writerId, role: "WRITER" },
    });
    if (!existing) {
      return apiError("Writer not found", 404);
    }

    // 7) writers themselves can't change role or approval
    if (!isAdmin && isSelf) {
      if (
        updateData.role !== undefined ||
        updateData.isApproved !== undefined
      ) {
        return apiError("Cannot change role or approval", 403);
      }
    }

    // 8) hash password if provided
    if (updateData.password) {
      updateData.password = await bcrypt.hash(updateData.password, 10);
    }

    // 9) perform update - updateData should work directly since your types are already correct
    const updated = await prisma.user.update({
      where: { id: writerId },
      data: updateData,
      select: {
        id: true,
        accountId: true,
        email: true,
        name: true,
        phone: true,
        role: true,
        isApproved: true,
        emailVerified: true,
        professionalSummary: true,
        experience: true,
        competencies: true,
        educationLevel: true,
        rating: true,
        createdAt: true,
        updatedAt: true,
        image: true,
      },
    });

    // 10) format response exactly matching UserResponse type
    const formatted: UserResponse = {
      id: updated.id,
      accountId: updated.accountId,
      email: updated.email,
      name: updated.name,
      phone: updated.phone,
      role: updated.role,
      isApproved: updated.isApproved,
      emailVerified: updated.emailVerified,
      professionalSummary: updated.professionalSummary,
      experience: updated.experience,
      competencies: updated.competencies,
      educationLevel: updated.educationLevel,
      rating: updated.rating,
      createdAt: updated.createdAt.toISOString(),
      updatedAt: updated.updatedAt.toISOString(),
      image: updated.image,
    };

    return apiSuccess(formatted, "Writer updated successfully");
  } catch (error) {
    console.error("Error updating writer:", error);
    if (error instanceof Error && error.message.includes("Unique constraint")) {
      return apiError("Email is already in use by another user", 409);
    }
    return apiError("Failed to update writer", 500);
  }
}

// DELETE a writer
export async function DELETE(
  _req: NextRequest,
  { params }: RouteParams
): Promise<NextResponse> {
  const permissionError = await checkPermission(["ADMIN"]);
  if (permissionError) return permissionError;

  const { id } = await params;
  try {
    const ex = await prisma.user.findUnique({ where: { id, role: "WRITER" } });
    if (!ex) return apiError("Writer not found", 404);

    await prisma.user.delete({ where: { id } });
    return apiSuccess(null, "Writer deleted successfully");
  } catch (err) {
    console.error("Error deleting writer:", err);
    if (err instanceof Error && err.message.includes("foreign key")) {
      return apiError("Cannot delete writer with related data", 409);
    }
    return apiError("Failed to delete writer", 500);
  }
}
