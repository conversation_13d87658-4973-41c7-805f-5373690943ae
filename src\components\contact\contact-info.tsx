"use client";

import React from "react";
import { motion } from "framer-motion";
import { useCompanyInfo } from "@/hooks/use-company-info";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { 
  Phone, 
  Mail, 
  MapPin, 
  Clock, 
  MessageCircle,
  Globe,
  Users,
  Shield,
  Zap,
  HeartHandshake
} from "lucide-react";

const supportFeatures = [
  {
    icon: Zap,
    title: "Fast Response",
    description: "Quick replies to all inquiries",
    color: "text-yellow-500 bg-yellow-100",
  },
  {
    icon: Shield,
    title: "Secure & Private",
    description: "Your information is protected",
    color: "text-green-500 bg-green-100",
  },
  {
    icon: Users,
    title: "Expert Team",
    description: "Knowledgeable support staff",
    color: "text-blue-500 bg-blue-100",
  },
  {
    icon: HeartHandshake,
    title: "Personal Touch",
    description: "Friendly, human support",
    color: "text-purple-500 bg-purple-100",
  },
];

export function ContactInfo() {
  const { companyInfo } = useCompanyInfo();

  const contactMethods = [
    {
      icon: MessageCircle,
      title: "Live Chat",
      description: "Get instant responses from our support team",
      info: "Available 24/7",
      action: "Start Chat",
      color: "bg-green-100 text-green-700",
      available: true,
    },
    {
      icon: Mail,
      title: "Email Support",
      description: "Send us detailed questions or concerns",
      info: companyInfo?.supportEmail || "<EMAIL>",
      action: "Send Email",
      color: "bg-blue-100 text-blue-700",
      available: true,
    },
    {
      icon: Phone,
      title: "Phone Support",
      description: "Speak directly with our support team",
      info: companyInfo?.phone || "+****************",
      action: "Call Now",
      color: "bg-purple-100 text-purple-700",
      available: true,
    },
    {
      icon: Globe,
      title: "International",
      description: "Global support for international clients",
      info: companyInfo?.internationalPhone || "+****************",
      action: "Call International",
      color: "bg-orange-100 text-orange-700",
      available: true,
    },
  ];

  const businessHours = [
    { day: "Monday - Friday", hours: "9:00 AM - 6:00 PM EST" },
    { day: "Saturday", hours: "10:00 AM - 4:00 PM EST" },
    { day: "Sunday", hours: "12:00 PM - 4:00 PM EST" },
  ];

  return (
    <div className="space-y-8">
      {/* Contact Methods */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card className="shadow-lg border-0">
          <CardHeader>
            <CardTitle className="text-2xl font-bold flex items-center gap-2">
              <MessageCircle className="w-6 h-6 text-primary" />
              Contact Methods
            </CardTitle>
            <CardDescription>
              Choose the best way to reach our support team
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {contactMethods.map((method, index) => {
              const Icon = method.icon;
              return (
                <motion.div
                  key={method.title}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="p-4 rounded-lg border hover:shadow-md transition-all duration-300 hover:border-primary/20 dark:border-gray-700 dark:hover:border-primary/30 space-y-4"
                >
                  <div className="flex items-start gap-4">
                    <div className={`p-3 rounded-full ${method.color} dark:bg-opacity-20 shrink-0`}>
                      <Icon className="w-5 h-5" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-1">
                        <h4 className="font-semibold text-gray-900 dark:text-gray-100">{method.title}</h4>
                        {method.available && (
                          <Badge variant="outline" className="text-xs border-green-200 text-green-700 bg-green-50 dark:border-green-700 dark:text-green-400 dark:bg-green-900/20 w-fit">
                            Available
                          </Badge>
                        )}
                      </div>
                      <p className="text-gray-600 dark:text-gray-400 text-sm mb-2">{method.description}</p>
                      <p className="text-sm font-medium text-gray-800 dark:text-gray-200 break-words">{method.info}</p>
                    </div>
                  </div>
                  <div className="flex justify-end">
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full sm:w-auto text-xs sm:text-sm px-3 py-2 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
                    >
                      {method.action}
                    </Button>
                  </div>
                </motion.div>
              );
            })}
          </CardContent>
        </Card>
      </motion.div>

      {/* Business Hours */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Card className="shadow-lg border-0">
          <CardHeader>
            <CardTitle className="text-xl font-bold flex items-center gap-2">
              <Clock className="w-5 h-5 text-primary" />
              Business Hours
            </CardTitle>
            <CardDescription>
              When our support team is available
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {businessHours.map((schedule) => (
                <div key={schedule.day} className="flex flex-col sm:flex-row sm:justify-between sm:items-center py-2 gap-1 sm:gap-0">
                  <span className="font-medium text-gray-700 dark:text-gray-300">{schedule.day}</span>
                  <span className="text-gray-600 dark:text-gray-400 text-sm sm:text-base">{schedule.hours}</span>
                </div>
              ))}
            </div>
            <Separator className="my-4 dark:bg-gray-700" />
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border dark:border-blue-800/30">
              <p className="text-sm text-blue-800 dark:text-blue-200">
                <strong>Emergency Support:</strong> For urgent matters outside business hours,
                please mark your inquiry as &quot;High Priority&quot; and we$apos;ll respond as soon as possible.
              </p>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Office Location */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <Card className="shadow-lg border-0">
          <CardHeader>
            <CardTitle className="text-xl font-bold flex items-center gap-2">
              <MapPin className="w-5 h-5 text-primary" />
              Office Location
            </CardTitle>
            <CardDescription>
              Our main office address
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p className="font-medium text-gray-800 dark:text-gray-200">
                {companyInfo?.companyName || "Academic Writing Services"}
              </p>
              <p className="text-gray-600 dark:text-gray-400">
                {companyInfo?.address || "1234 Academic Way, Suite 500"}
              </p>
              <p className="text-gray-600 dark:text-gray-400">
                {companyInfo?.city || "New York"}, {companyInfo?.state || "NY"} {companyInfo?.zipCode || "10001"}
              </p>
              <p className="text-gray-600 dark:text-gray-400">
                {companyInfo?.country || "United States"}
              </p>
            </div>
            <Separator className="my-4 dark:bg-gray-700" />
            <Button variant="outline" className="w-full dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700">
              <MapPin className="w-4 h-4 mr-2" />
              View on Map
            </Button>
          </CardContent>
        </Card>
      </motion.div>

      {/* Support Features */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.6 }}
      >
        <Card className="shadow-lg border-0 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 dark:border-gray-700">
          <CardHeader>
            <CardTitle className="text-xl font-bold dark:text-gray-100">Why Choose Our Support?</CardTitle>
            <CardDescription className="dark:text-gray-400">
              We$apos;re committed to providing exceptional customer service
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {supportFeatures.map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <motion.div
                    key={feature.title}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: 0.8 + index * 0.1 }}
                    className="flex items-start gap-3 p-3 rounded-lg bg-white/50 hover:bg-white/80 dark:bg-gray-800/50 dark:hover:bg-gray-800/80 transition-colors"
                  >
                    <div className={`p-2 rounded-full ${feature.color} dark:bg-opacity-20`}>
                      <Icon className="w-4 h-4" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-1">{feature.title}</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{feature.description}</p>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Emergency Contact */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.8 }}
      >
        <Card className="shadow-lg border-0 bg-gradient-to-r from-red-500 to-pink-600 dark:from-red-600 dark:to-pink-700 text-white">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row sm:items-center gap-3 mb-3">
              <Zap className="w-6 h-6 shrink-0" />
              <h3 className="text-xl font-bold">Emergency Deadline?</h3>
            </div>
            <p className="mb-4 text-white/90">
              Need your paper in 3 hours or less? Contact our emergency team now for immediate assistance!
            </p>
            <Button variant="secondary" className="w-full bg-white text-red-600 hover:bg-gray-100 dark:bg-gray-100 dark:text-red-600 dark:hover:bg-gray-200">
              <Phone className="w-4 h-4 mr-2" />
              Emergency Assistance
            </Button>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
