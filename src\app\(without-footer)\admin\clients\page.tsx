// src/app/admin/clients/page.tsx
"use client";

import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialog<PERSON>itle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Search,
  Eye,
  MoreHorizontal,
  Trash2,
  User,
  ChevronLeft,
  ChevronRight,
  Filter,
  Loader2,
} from "lucide-react";
import { cn } from "@/lib/utils";
import AddClientDialog from "../../../../components/dashboard/admin/add-client-dialog";
import ViewClientDialog from "@/components/dashboard/admin/view-client-dialog";
import { toast } from "sonner"; // Add this import for toast notifications
import { useDebouncedCallback } from "@/hooks/use-debounced-callback";

// Types - Updated to match API response
interface Client {
  id: string;
  name: string;
  email: string;
  phone: string;
  role: string;
  isApproved: boolean;
  createdAt: string;
  updatedAt: string;
  image?: string;
}

interface ClientsResponse {
  clients: Client[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

interface ColumnVisibility {
  name: boolean;
  email: boolean;
  phone: boolean;
  isApproved: boolean;
}

// Column definitions
const columns = [
  { key: "name" as keyof ColumnVisibility, label: "Name", sortable: true },
  { key: "email" as keyof ColumnVisibility, label: "Email", sortable: true },
  { key: "phone" as keyof ColumnVisibility, label: "Phone", sortable: false },
  { key: "isApproved" as keyof ColumnVisibility, label: "Approved", sortable: true },
];

export default function ClientsPage(): React.JSX.Element {
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [searchOpen, setSearchOpen] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [deletingClientId, setDeletingClientId] = useState<string | null>(null);
  const [columnVisibility, setColumnVisibility] = useState<ColumnVisibility>({
    name: true,
    email: true,
    phone: true,
    isApproved: true,
  });

  const limit = 10;

  const fetchClients = async (page: number = 1, search: string = ""): Promise<void> => {
    try {
      setLoading(true);
      const searchParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(search && { search }),
      });

      const url = `/api/users/clients?${searchParams}`;
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch clients: ${response.status} ${response.statusText}`);
      }

      const apiResponse = await response.json();
      const data: ClientsResponse = apiResponse.data;
      
      const clientsData = data.clients || [];
      setClients(clientsData);
      setTotalPages(data.pagination?.pages || 1);
      setTotalCount(data.pagination?.total || 0);
      setError(null);
    } catch (err) {
      console.error("❌ Error in fetchClients:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch clients");
      setClients([]);
    } finally {
      setLoading(false);
    }
  };

  // Delete client function
  const handleDeleteClient = async (clientId: string, clientName: string): Promise<void> => {
    try {
      setDeletingClientId(clientId);
      
      const response = await fetch(`/api/users/clients/${clientId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to delete client: ${response.status}`);
      }

      // Remove the client from the local state
      setClients(prevClients => prevClients.filter(client => client.id !== clientId));
      
      // Update total count
      setTotalCount(prevCount => prevCount - 1);

      // Show success toast
      toast.success(`${clientName} has been successfully deleted`);

      // If current page becomes empty and it's not the first page, go to previous page
      const remainingClients = clients.filter(client => client.id !== clientId);
      if (remainingClients.length === 0 && currentPage > 1) {
        setCurrentPage(currentPage - 1);
      }

    } catch (err) {
      console.error("Error deleting client:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to delete client";
      
      // Show error toast
      toast.error(errorMessage);
      
      // Handle specific error cases
      if (errorMessage.includes("foreign key") || errorMessage.includes("related data")) {
        toast.error("Cannot delete client with active assignments");
      }
    } finally {
      setDeletingClientId(null);
    }
  };

  // Main useEffect for fetching data
  useEffect(() => {
    fetchClients(currentPage, searchTerm);
  }, [currentPage, searchTerm]);

  // Debounced search to avoid too many API calls
  const debouncedSearch = useDebouncedCallback((value: string) => {
    setSearchTerm(value);
    setCurrentPage(1); // Reset to first page when searching
  }, 300);

  // Handle search (server-side search with pagination reset)
  const handleSearch = (value: string): void => {
    debouncedSearch(value);
  };

  // Handle column visibility toggle
  const toggleColumnVisibility = (columnKey: keyof ColumnVisibility): void => {
    setColumnVisibility(prev => ({
      ...prev,
      [columnKey]: !prev[columnKey],
    }));
  };

  // Handle pagination
  const handlePageChange = (page: number): void => {
    setCurrentPage(page);
  };

  // Format date
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Get approval status badge
  const getApprovalBadge = (isApproved: boolean): React.JSX.Element => {
    return (
      <Badge
        variant={isApproved ? "default" : "secondary"}
        className={cn(
          isApproved 
            ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200" 
            : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
        )}
      >
        {isApproved ? "Approved" : "Pending"}
      </Badge>
    );
  };

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="flex items-center justify-center h-64">
            <div className="text-center">
              <p className="text-destructive mb-4">{error}</p>
              <Button onClick={() => fetchClients(currentPage, "")}>
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Clients</h1>
          <p className="text-muted-foreground">
            Manage and monitor all clients in the system
          </p>
        </div>
        <AddClientDialog onClientAdded={() => fetchClients(currentPage, "")} />
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Clients</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCount}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved</CardTitle>
            <User className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {clients.filter(c => c.isApproved).length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <User className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {clients.filter(c => !c.isApproved).length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Table Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Clients List</CardTitle>
            <div className="flex items-center space-x-2">
              {/* Search */}
              <Popover open={searchOpen} onOpenChange={setSearchOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 border-dashed"
                  >
                    <Search className="mr-2 h-4 w-4" />
                    Search
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-0" align="end">
                  <Command>
                    <CommandInput
                      placeholder="Search by name or email..."
                      value={searchTerm}
                      onValueChange={handleSearch}
                    />
                    <CommandList>
                      <CommandEmpty>No results found.</CommandEmpty>
                      <CommandGroup>
                        {clients.slice(0, 5).map((client) => (
                          <CommandItem
                            key={client.id}
                            onSelect={() => {
                              setSearchTerm(client.name);
                              setSearchOpen(false);
                            }}
                          >
                            <User className="mr-2 h-4 w-4" />
                            <div className="flex flex-col">
                              <span>{client.name}</span>
                              <span className="text-xs text-muted-foreground">
                                {client.email}
                              </span>
                            </div>
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>

              {/* Column Visibility */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 border-dashed"
                  >
                    <Filter className="mr-2 h-4 w-4" />
                    View
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  {columns.map((column) => (
                    <DropdownMenuCheckboxItem
                      key={column.key}
                      checked={columnVisibility[column.key]}
                      onCheckedChange={() => toggleColumnVisibility(column.key)}
                    >
                      {column.label}
                    </DropdownMenuCheckboxItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
          
          {/* Search Input (visible when searching) */}
          {searchTerm && (
            <div className="flex items-center space-x-2">
              <Input
                placeholder="Search clients..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="h-8 w-[200px] lg:w-[300px]"
              />
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleSearch("")}
                className="h-8 px-2"
              >
                Clear
              </Button>
            </div>
          )}
        </CardHeader>

        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading clients...</span>
            </div>
          ) : (
            <>
              {/* Table */}
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      {columnVisibility.name && <TableHead>Name</TableHead>}
                      {columnVisibility.email && <TableHead>Email</TableHead>}
                      {columnVisibility.phone && (
                        <TableHead className="hidden md:table-cell">Phone</TableHead>
                      )}
                      {columnVisibility.isApproved && (
                        <TableHead>Approval</TableHead>
                      )}
                      <TableHead className="w-[100px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {clients.length === 0 ? (
                      <TableRow>
                        <TableCell
                          colSpan={Object.values(columnVisibility).filter(Boolean).length + 1}
                          className="h-24 text-center"
                        >
                          No clients found.
                        </TableCell>
                      </TableRow>
                    ) : (
                      clients.map((client) => (
                        <TableRow key={client.id}>
                          {columnVisibility.name && (
                            <TableCell>
                              <div className="flex items-center space-x-3">
                                <div className="flex-shrink-0">
                                  <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                                    <User className="h-4 w-4 text-primary" />
                                  </div>
                                </div>
                                <div>
                                  <div className="font-medium">{client.name}</div>
                                  <div className="text-xs text-muted-foreground">
                                    Joined {formatDate(client.createdAt)}
                                  </div>
                                </div>
                              </div>
                            </TableCell>
                          )}
                          {columnVisibility.email && (
                            <TableCell className="text-sm">{client.email}</TableCell>
                          )}
                          {columnVisibility.phone && (
                            <TableCell className="hidden md:table-cell text-sm">
                              {client.phone || "N/A"}
                            </TableCell>
                          )}
                          {columnVisibility.isApproved && (
                            <TableCell>{getApprovalBadge(client.isApproved)}</TableCell>
                          )}
                          <TableCell>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <div className="flex flex-col space-y-1 p-1">
                                  {/* View Client Dialog */}
                                  <ViewClientDialog 
                                    clientId={client.id}
                                    trigger={
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        className="justify-start w-full"
                                      >
                                        <Eye className="mr-2 h-4 w-4" />
                                        View
                                      </Button>
                                    }
                                  />
                                  
                                  {/* Delete Client Dialog */}
                                  <AlertDialog>
                                    <AlertDialogTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        className="justify-start text-destructive hover:text-destructive w-full"
                                        disabled={deletingClientId === client.id}
                                      >
                                        {deletingClientId === client.id ? (
                                          <>
                                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                            Deleting...
                                          </>
                                        ) : (
                                          <>
                                            <Trash2 className="mr-2 h-4 w-4" />
                                            Delete
                                          </>
                                        )}
                                      </Button>
                                    </AlertDialogTrigger>
                                    <AlertDialogContent>
                                      <AlertDialogHeader>
                                        <AlertDialogTitle>
                                          Delete Client
                                        </AlertDialogTitle>
                                        <AlertDialogDescription>
                                          Are you sure you want to delete <strong>{client.name}</strong>? 
                                          This action cannot be undone and will remove all associated data.
                                        </AlertDialogDescription>
                                      </AlertDialogHeader>
                                      <AlertDialogFooter>
                                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                                        <AlertDialogAction
                                          className="bg-destructive hover:bg-destructive/90"
                                          onClick={() => handleDeleteClient(client.id, client.name)}
                                          disabled={deletingClientId === client.id}
                                        >
                                          {deletingClientId === client.id ? (
                                            <>
                                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                              Deleting...
                                            </>
                                          ) : (
                                            "Delete"
                                          )}
                                        </AlertDialogAction>
                                      </AlertDialogFooter>
                                    </AlertDialogContent>
                                  </AlertDialog>
                                </div>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between px-2 py-4">
                  <div className="text-sm text-muted-foreground">
                    Showing {((currentPage - 1) * limit) + 1} to{" "}
                    {Math.min(currentPage * limit, totalCount)} of {totalCount} clients
                  </div>
                  <div className="flex items-center space-x-6 lg:space-x-8">
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                      >
                        <ChevronLeft className="h-4 w-4" />
                        Previous
                      </Button>
                      <div className="flex items-center gap-1">
                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                          const pageNum = i + 1;
                          return (
                            <Button
                              key={pageNum}
                              variant={currentPage === pageNum ? "default" : "outline"}
                              size="sm"
                              onClick={() => handlePageChange(pageNum)}
                              className="w-8 h-8 p-0"
                            >
                              {pageNum}
                            </Button>
                          );
                        })}
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                      >
                        Next
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}