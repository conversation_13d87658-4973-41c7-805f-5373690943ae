import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

/**
 * Quick API endpoint to check email verification status
 * Used by middleware to prevent redirect flash for already verified users
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    const { userId } = await req.json();

    if (!userId) {
      return NextResponse.json(
        { error: "User ID is required" },
        { status: 400 }
      );
    }

    // Quick database query to get current email verification status
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { emailVerified: true, isApproved: true },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      emailVerified: user.emailVerified,
      isApproved: user.isApproved,
    });
  } catch (error) {
    console.error("❌ Error in check-verification API:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
