"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  User,
  Mail,
  CheckCircle,
  XCircle,
  Copy,
  Loader2,
  Award,
  FileText,
  GraduationCap,
  Clock,
} from "lucide-react";
import Image from "next/image";
import { toast } from "sonner";

// Proper type definitions
interface QuizQuestion {
  question: string;
  options: string[];
  correctAnswer: string;
}

interface EssayExam {
  topic: string;
  rubrics: string;
}

// This represents the structure from Prisma JSON
interface PrismaMultipleChoiceAnswers {
  [key: string]: string; // Numeric keys as strings: "0": "Answer A", "1": "Answer B"
}

interface PrismaWriterAnswer {
  writerId: string;
  multipleChoiceAnswers: PrismaMultipleChoiceAnswers | string; // Either object with numeric keys or JSON string
  essayText: string;
  status?: "Passed" | "Failed";
}

// This is our parsed/normalized version
interface ParsedWriterAnswer {
  writerId: string;
  multipleChoiceAnswers: string[];
  essayText: string;
  status?: "Passed" | "Failed";
}

interface Assessment {
  id: string;
  title: string;
  multipleChoiceQuiz: QuizQuestion[] | string; // Could be JSON string from Prisma
  essayExam: EssayExam | string; // Could be JSON string from Prisma
  writersAnswers: PrismaWriterAnswer[];
  isActive: boolean;
}

interface WriterInfo {
  id: string;
  name: string;
  email: string;
  imageUrl?: string;
}

interface WriterAssessmentAnswersProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  writerId: string;
  assessment: Assessment;
  writerAnswer: PrismaWriterAnswer;
  onStatusUpdate: (status: "Passed" | "Failed") => void;
}

export function WriterAssessmentAnswers({
  isOpen,
  onOpenChange,
  writerId,
  assessment,
  writerAnswer,
  onStatusUpdate,
}: WriterAssessmentAnswersProps) {
  const [writerInfo, setWriterInfo] = useState<WriterInfo | null>(null);
  const [loadingWriter, setLoadingWriter] = useState(false);
  const [writerError, setWriterError] = useState<string | null>(null);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);

  // Enhanced JSON parsing for Prisma data
  const safeParseJson = <T,>(data: T | string, fallback: T): T => {
    if (!data) return fallback;

    // If it's already the expected type, return as is
    if (typeof data === "object" && data !== null) return data as T;

    // If it's a string, try to parse it
    if (typeof data === "string") {
      try {
        const parsed = JSON.parse(data);
        return parsed || fallback;
      } catch (e) {
        console.warn("Failed to parse JSON:", e, "Data:", data);
        return fallback;
      }
    }

    return fallback;
  };

  // Parse multiple choice answers with better handling for Prisma JSON format
  const parseMultipleChoiceAnswers = (
    answers: PrismaMultipleChoiceAnswers | string
  ): string[] => {
    if (!answers) return [];

    // If it's a string, try to parse it
    if (typeof answers === "string") {
      try {
        const parsed = JSON.parse(answers);
        if (Array.isArray(parsed)) return parsed;

        // If parsed result is an object with numeric keys, convert to array
        if (typeof parsed === "object" && parsed !== null) {
          const keys = Object.keys(parsed).sort(
            (a, b) => parseInt(a) - parseInt(b)
          );
          return keys.map((key) => parsed[key]);
        }
        return [];
      } catch (e) {
        console.warn("Failed to parse multiple choice answers:", e);
        return [];
      }
    }

    // Handle the Prisma JSON object with numeric keys (most common case)
    if (typeof answers === "object" && answers !== null) {
      const keys = Object.keys(answers).sort(
        (a, b) => parseInt(a) - parseInt(b)
      );
      return keys.map((key) => answers[key]);
    }

    return [];
  };

  // Parse assessment data safely - explicitly handle Prisma JSON types
  const parsedMultipleChoiceQuiz = safeParseJson<QuizQuestion[]>(
    assessment.multipleChoiceQuiz,
    []
  );

  const parsedEssayExam = safeParseJson<EssayExam>(assessment.essayExam, {
    topic: "",
    rubrics: "",
  });

  // Explicitly parse multipleChoiceAnswers with the correct type expectation
  const parsedMultipleChoiceAnswers = parseMultipleChoiceAnswers(
    writerAnswer.multipleChoiceAnswers as PrismaMultipleChoiceAnswers | string
  );

  // Debug the raw answer format
  useEffect(() => {
    console.log(
      "🔬 Raw multipleChoiceAnswers:",
      typeof writerAnswer.multipleChoiceAnswers,
      writerAnswer.multipleChoiceAnswers
    );
  }, [writerAnswer.multipleChoiceAnswers]);

  // Create normalized writer answer
  const normalizedWriterAnswer: ParsedWriterAnswer = {
    writerId: writerAnswer.writerId,
    multipleChoiceAnswers: parsedMultipleChoiceAnswers,
    essayText: writerAnswer.essayText || "",
    status: writerAnswer.status,
  };

  // Debug logging with better structure
  useEffect(() => {
    console.log("🔍 [DialogDebug] Raw data:", {
      assessment: {
        id: assessment.id,
        title: assessment.title,
        multipleChoiceQuiz: assessment.multipleChoiceQuiz,
        essayExam: assessment.essayExam,
      },
      writerAnswer: {
        writerId: writerAnswer.writerId,
        multipleChoiceAnswers: writerAnswer.multipleChoiceAnswers,
        essayText: writerAnswer.essayText?.length || 0,
        status: writerAnswer.status,
      },
    });

    console.log("🔍 [DialogDebug] Parsed data:", {
      parsedMultipleChoiceQuiz: parsedMultipleChoiceQuiz.length,
      parsedEssayExam,
      parsedMultipleChoiceAnswers: parsedMultipleChoiceAnswers.length,
      normalizedWriterAnswer,
    });
  }, [
    assessment,
    writerAnswer,
    parsedMultipleChoiceQuiz,
    parsedEssayExam,
    parsedMultipleChoiceAnswers,
    normalizedWriterAnswer,
  ]);

  // Fetch writer information
  useEffect(() => {
    if (isOpen && writerId) {
      const fetchWriterInfo = async () => {
        setLoadingWriter(true);
        setWriterError(null);
        try {
          const response = await fetch(`/api/users/writers/${writerId}`, {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
            credentials: "include",
          });

          if (!response.ok) {
            throw new Error(`Failed to fetch writer info: ${response.status}`);
          }

          const result = await response.json();
          console.log("✅ Fetched writer info:", result);

          if (result.success && result.data) {
            const writerData = result.data;
            setWriterInfo({
              id: writerData.id,
              name: writerData.name,
              email: writerData.email,
              imageUrl: writerData.image, // Note: this might be null based on the response
            });
          } else {
            throw new Error("Failed to get writer data from response");
          }
        } catch (err) {
          console.error("❌ Error fetching writer info:", err);
          setWriterError("Failed to load writer information");
        } finally {
          setLoadingWriter(false);
        }
      };

      fetchWriterInfo();
    }
  }, [isOpen, writerId]);

  // Calculate multiple choice score
  const calculateScore = () => {
    if (
      !Array.isArray(parsedMultipleChoiceQuiz) ||
      !Array.isArray(parsedMultipleChoiceAnswers)
    ) {
      return { correct: 0, total: 0, percentage: 0 };
    }

    const correctAnswers = parsedMultipleChoiceAnswers.filter(
      (answer, index) =>
        answer === parsedMultipleChoiceQuiz[index]?.correctAnswer
    ).length;

    const totalQuestions = parsedMultipleChoiceQuiz.length;

    return {
      correct: correctAnswers,
      total: totalQuestions,
      percentage:
        totalQuestions > 0
          ? Math.round((correctAnswers / totalQuestions) * 100)
          : 0,
    };
  };

  const score = calculateScore();

  const copyEssayText = async () => {
    try {
      const textToCopy =
        normalizedWriterAnswer.essayText || "No essay submitted";
      await navigator.clipboard.writeText(textToCopy);
      alert("Essay text copied to clipboard");
    } catch (err) {
      console.error("Failed to copy text:", err);
      alert("Failed to copy text");
    }
  };

  const handleStatusUpdate = async (status: "Passed" | "Failed") => {
    setIsUpdatingStatus(true);
    try {
      // Make API call to update status
      const response = await fetch(
        `/api/admin/assessments/${assessment.id}/writers/${writerId}/status`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify({ status }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to update status");
      }

      // Update parent component's state
      onStatusUpdate(status);

      // Close the dialog
      onOpenChange(false);
    } catch (err) {
      console.error("Error updating status:", err);
      toast.error("Failed to update assessment status");
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  const getScoreBadgeColor = (percentage: number) => {
    if (percentage >= 80)
      return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
    if (percentage >= 60)
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
    return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
  };

  const getStatusBadge = () => {
    if (!normalizedWriterAnswer.status) return null;

    return (
      <Badge
        variant={
          normalizedWriterAnswer.status === "Passed" ? "default" : "destructive"
        }
        className="flex items-center gap-1"
      >
        {normalizedWriterAnswer.status === "Passed" ? (
          <CheckCircle className="h-3 w-3" />
        ) : (
          <XCircle className="h-3 w-3" />
        )}
        {normalizedWriterAnswer.status}
      </Badge>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="min-w-[85vw] w-[85vw] h-[96vh] p-0 overflow-hidden flex flex-col border-0 gap-0">
        {/* Header Section - Compact and fixed at top */}
        <DialogHeader className="border-b p-4 pb-3 flex-shrink-0 space-y-1">
          <DialogTitle className="flex items-center gap-3 justify-between text-xl">
            <div className="flex items-center gap-2">
              <GraduationCap className="h-6 w-6" />
              <span>Assessment Review</span>
              {getStatusBadge()}
            </div>
            <Badge
              className={`${getScoreBadgeColor(score.percentage)} font-semibold text-base px-3`}
            >
              Score: {score.percentage}%
            </Badge>
          </DialogTitle>
          <DialogDescription className="text-base">
            Review and evaluate {writerInfo?.name || "writer"}&apos;s assessment
            submission for &quot;{assessment.title}&quot;
          </DialogDescription>
        </DialogHeader>

        {/* Writer Information Section - Compact */}
        <div className="px-4 py-3 border-b bg-muted/20 flex-shrink-0">
          {loadingWriter ? (
            <div className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-base text-muted-foreground">
                Loading writer information...
              </span>
            </div>
          ) : writerError ? (
            <div className="text-base text-destructive">{writerError}</div>
          ) : writerInfo ? (
            <div className="flex items-center gap-4">
              <Avatar className="h-12 w-12">
                <AvatarImage src={writerInfo.imageUrl} alt={writerInfo.name} />
                <AvatarFallback className="text-sm">
                  <Image
                    src="/avatars/shadcn.jpg"
                    alt="Avatar"
                    width={48}
                    height={48}
                  />
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium text-base">
                    {writerInfo.name}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">
                    {writerInfo.email}
                  </span>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm text-muted-foreground">Performance</p>
                <p className="text-base font-semibold">
                  {score.correct}/{score.total}
                </p>
              </div>
            </div>
          ) : (
            <div className="text-base text-muted-foreground">
              Writer ID: {writerId.slice(0, 12)}...
            </div>
          )}
        </div>

        {/* Main Content Area - Fully scrollable */}
        <div className="flex-1 overflow-hidden">
          <Tabs defaultValue="multiple-choice" className="h-full flex flex-col">
            {/* Tab Navigation - Compact */}
            <div className="px-4 py-2 border-b flex-shrink-0">
              <TabsList className="grid w-full grid-cols-2 h-9">
                <TabsTrigger
                  value="multiple-choice"
                  className="flex items-center gap-2 text-sm"
                >
                  <Award className="h-4 w-4" />
                  Multiple Choice ({parsedMultipleChoiceQuiz.length})
                </TabsTrigger>
                <TabsTrigger
                  value="essay"
                  className="flex items-center gap-2 text-sm"
                >
                  <FileText className="h-4 w-4" />
                  Essay Review
                </TabsTrigger>
              </TabsList>
            </div>

            {/* Tab Content - Scrollable with reduced spacing */}
            <TabsContent
              value="multiple-choice"
              className="flex-1 overflow-hidden m-0 data-[state=active]:flex data-[state=active]:flex-col"
            >
              <ScrollArea className="flex-1 h-full">
                <div className="p-2">
                  <div className="space-y-3">
                    {Array.isArray(parsedMultipleChoiceQuiz) &&
                    parsedMultipleChoiceQuiz.length > 0 ? (
                      parsedMultipleChoiceQuiz.map(
                        (question: QuizQuestion, index: number) => {
                          const userAnswer =
                            parsedMultipleChoiceAnswers[index] || "";
                          const isCorrect =
                            userAnswer === question.correctAnswer;

                          return (
                            <Card
                              key={index}
                              className={`border-l-4 ${isCorrect ? "border-l-green-500" : "border-l-red-500"}`}
                            >
                              <CardHeader className="pb-2 px-4 pt-3">
                                <div className="flex items-start gap-3">
                                  <div className="flex-shrink-0 mt-0.5">
                                    {isCorrect ? (
                                      <CheckCircle className="h-4 w-4 text-green-600" />
                                    ) : (
                                      <XCircle className="h-4 w-4 text-red-600" />
                                    )}
                                  </div>
                                  <div className="flex-1">
                                    <div className="flex items-center justify-between mb-1">
                                      <CardTitle className="text-sm font-medium">
                                        Question {index + 1}
                                      </CardTitle>
                                      <Badge
                                        variant={
                                          isCorrect ? "default" : "destructive"
                                        }
                                        className="text-xs h-5"
                                      >
                                        {isCorrect ? "Correct" : "Incorrect"}
                                      </Badge>
                                    </div>
                                    <div className="text-sm text-muted-foreground whitespace-pre-wrap leading-relaxed">
                                      {question.question}
                                    </div>
                                  </div>
                                </div>
                              </CardHeader>
                              <CardContent className="pt-0 px-4 pb-3">
                                <div className="space-y-2">
                                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-2">
                                    {Array.isArray(question.options) &&
                                      question.options.map(
                                        (
                                          option: string,
                                          optionIndex: number
                                        ) => (
                                          <div
                                            key={optionIndex}
                                            className={`p-2 rounded-md border text-sm ${
                                              option === question.correctAnswer
                                                ? "bg-green-50 border-green-200 text-green-800 dark:bg-green-950 dark:border-green-800 dark:text-green-300"
                                                : option === userAnswer &&
                                                    !isCorrect
                                                  ? "bg-red-50 border-red-200 text-red-800 dark:bg-red-950 dark:border-red-800 dark:text-red-300"
                                                  : "bg-muted/50 border-muted"
                                            }`}
                                          >
                                            <div className="flex items-center justify-between">
                                              <span className="text-xs leading-relaxed">
                                                {option}
                                              </span>
                                              <div className="flex gap-1 ml-2">
                                                {option ===
                                                  question.correctAnswer && (
                                                  <CheckCircle className="h-3 w-3 text-green-600 flex-shrink-0" />
                                                )}
                                                {option === userAnswer &&
                                                  option !==
                                                    question.correctAnswer && (
                                                    <XCircle className="h-3 w-3 text-red-600 flex-shrink-0" />
                                                  )}
                                              </div>
                                            </div>
                                          </div>
                                        )
                                      )}
                                  </div>

                                  <Separator className="my-2" />

                                  <div className="flex flex-wrap gap-3 text-xs">
                                    <div className="flex items-center gap-1">
                                      <span className="font-medium">
                                        Writer&apos;s Answer:
                                      </span>
                                      <Badge
                                        variant={
                                          isCorrect ? "default" : "destructive"
                                        }
                                        className="text-xs h-4"
                                      >
                                        {userAnswer || "No answer"}
                                      </Badge>
                                    </div>
                                    {!isCorrect && (
                                      <div className="flex items-center gap-1">
                                        <span className="font-medium">
                                          Correct Answer:
                                        </span>
                                        <Badge
                                          variant="secondary"
                                          className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 text-xs h-4"
                                        >
                                          {question.correctAnswer}
                                        </Badge>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          );
                        }
                      )
                    ) : (
                      <div className="text-center text-muted-foreground py-8">
                        <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No multiple choice questions found</p>
                        <p className="text-xs mt-2">
                          Debug: Quiz data type:{" "}
                          {typeof assessment.multipleChoiceQuiz}, Parsed length:{" "}
                          {parsedMultipleChoiceQuiz.length}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent
              value="essay"
              className="flex-1 overflow-hidden m-0 data-[state=active]:flex data-[state=active]:flex-col"
            >
              <ScrollArea className="flex-1 h-full">
                <div className="p-4">
                  <div className="space-y-3">
                    <Card>
                      <CardHeader className="pb-2 px-4 pt-3">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-sm font-medium flex items-center gap-2">
                            <FileText className="h-4 w-4" />
                            Essay Topic & Requirements
                          </CardTitle>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={copyEssayText}
                            className="flex items-center gap-1 h-7 text-xs"
                          >
                            <Copy className="h-3 w-3" />
                            Copy Essay
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-3 px-4 pb-3">
                        <div>
                          <h4 className="font-medium mb-1 flex items-center gap-2 text-sm">
                            <FileText className="h-3 w-3" />
                            Topic:
                          </h4>
                          <p className="text-xs text-muted-foreground p-2 bg-muted rounded-md">
                            {parsedEssayExam.topic || "No topic specified"}
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium mb-1 flex items-center gap-2 text-sm">
                            <Award className="h-3 w-3" />
                            Rubrics:
                          </h4>
                          <p className="text-xs text-muted-foreground p-2 bg-muted rounded-md">
                            {parsedEssayExam.rubrics || "No rubrics specified"}
                          </p>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-2 px-4 pt-3">
                        <CardTitle className="text-sm font-medium flex items-center gap-2">
                          <User className="h-4 w-4" />
                          Writer&apos;s Submission
                          <Badge
                            variant="outline"
                            className="ml-auto text-xs h-5"
                          >
                            {normalizedWriterAnswer.essayText?.length || 0}{" "}
                            characters
                          </Badge>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="px-4 pb-3">
                        {/* TODO: Increase height here - Changed from min-h-[300px] to min-h-[600px] for taller essay area */}
                        <div className="border rounded-md p-3 min-h-[600px] max-h-[800px] overflow-y-auto">
                          <div className="text-sm leading-relaxed whitespace-pre-wrap">
                            {normalizedWriterAnswer.essayText || (
                              <div className="text-muted-foreground italic flex items-center gap-2">
                                <Clock className="h-4 w-4" />
                                No essay submitted
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </div>

        {/* Action Buttons - Compact and fixed at bottom */}
        <div className="px-4 py-3 border-t bg-background flex-shrink-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Clock className="h-3 w-3" />
              Assessment Review Panel
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={() => onOpenChange(false)}
                size="sm"
                className="h-8 text-xs"
              >
                Close
              </Button>
              <Button
                variant="destructive"
                onClick={() => handleStatusUpdate("Failed")}
                disabled={isUpdatingStatus}
                className="flex items-center gap-1 h-8 text-xs"
              >
                {isUpdatingStatus ? (
                  <Loader2 className="h-3 w-3 animate-spin" />
                ) : (
                  <XCircle className="h-3 w-3" />
                )}
                Fail Assessment
              </Button>
              <Button
                onClick={() => handleStatusUpdate("Passed")}
                disabled={isUpdatingStatus}
                className="flex items-center gap-1 h-8 text-xs"
              >
                {isUpdatingStatus ? (
                  <Loader2 className="h-3 w-3 animate-spin" />
                ) : (
                  <CheckCircle className="h-3 w-3" />
                )}
                Pass Assessment
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
