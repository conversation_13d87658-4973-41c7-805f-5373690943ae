import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authConfig as authOptions } from "@/auth";
import prisma from "@/lib/prisma";
import { ChatParticipantRole } from "@prisma/client";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ assignmentId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { assignmentId } = await params;

    // Get the assignment to verify access
    const assignment = await prisma.assignment.findUnique({
      where: { id: assignmentId },
      include: {
        client: true,
        assignedWriter: true,
      },
    });

    if (!assignment) {
      return NextResponse.json(
        { success: false, message: "Assignment not found" },
        { status: 404 }
      );
    }

    // Get user role
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (!user) {
      return NextResponse.json(
        { success: false, message: "User not found" },
        { status: 404 }
      );
    }

    // Check if user has access to this chat
    const hasAccess =
      user.role === "ADMIN" ||
      assignment.clientId === session.user.id ||
      assignment.assignedWriterId === session.user.id;

    if (!hasAccess) {
      return NextResponse.json(
        { success: false, message: "Access denied" },
        { status: 403 }
      );
    }

    // Find or create chat
    let chat = await prisma.chat.findUnique({
      where: { assignmentId },
      include: {
        messages: {
          include: {
            sender: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
              },
            },
          },
          orderBy: { createdAt: "asc" },
        },
        participants: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
              },
            },
          },
        },
      },
    });

    if (!chat) {
      // Create new chat with participants
      const participants: { userId: string; role: ChatParticipantRole }[] = [
        { userId: assignment.clientId, role: "CLIENT" },
      ];

      // Add writer if assigned
      if (assignment.assignedWriterId) {
        participants.push({
          userId: assignment.assignedWriterId,
          role: "WRITER",
        });
      }

      // Add admin (find first admin user)
      const adminUser = await prisma.user.findFirst({
        where: { role: "ADMIN" },
        select: { id: true },
      });

      if (adminUser) {
        participants.push({
          userId: adminUser.id,
          role: "ADMIN",
        });
      }

      chat = await prisma.chat.create({
        data: {
          assignmentId,
          participants: {
            create: participants,
          },
        },
        include: {
          messages: {
            include: {
              sender: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  role: true,
                },
              },
            },
            orderBy: { createdAt: "asc" },
          },
          participants: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  role: true,
                },
              },
            },
          },
        },
      });
    }

    return NextResponse.json({
      success: true,
      data: chat,
    });
  } catch (error) {
    console.error("Error fetching chat:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}
