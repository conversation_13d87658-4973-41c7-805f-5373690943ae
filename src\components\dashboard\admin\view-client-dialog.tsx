// src/components/dashboard/admin/ViewClientDialog.tsx

/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Eye,
  // User,
  Mail,
  Phone,
  Calendar,
  Award,
  Building2,
  Hash,
  Loader2,
  Users,
} from "lucide-react";
import { cn } from "@/lib/utils";

// Client interface to match API response
interface Client {
  id: string;
  accountId: string | null;
  name: string;
  email: string;
  phone: string;
  role: string;
  isApproved: boolean;
  createdAt: string;
  updatedAt: string;
  image?: string;
  assignmentCount?: number;
  recentAssignments?: any[];
}

interface ViewClientDialogProps {
  clientId: string;
  trigger?: React.ReactNode;
}

export default function ViewClientDialog({
  clientId,
  trigger,
}: ViewClientDialogProps) {
  const [open, setOpen] = useState(false);
  const [client, setClient] = useState<Client | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch client data when dialog opens
  const fetchClient = async () => {
    if (!clientId) return;

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/users/clients/${clientId}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch client: ${response.status}`);
      }

      const result = await response.json();
      setClient(result.data);
    } catch (err) {
      console.error("Error fetching client:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch client");
    } finally {
      setLoading(false);
    }
  };

  // Fetch data when dialog opens
  useEffect(() => {
    if (open && clientId) {
      fetchClient();
    }
  }, [open, clientId]);

  // Reset state when dialog closes
  useEffect(() => {
    if (!open) {
      setClient(null);
      setError(null);
    }
  }, [open]);

  // Format date helper
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Get approval status badge
  const getApprovalBadge = (isApproved: boolean) => {
    return (
      <Badge
        variant={isApproved ? "default" : "secondary"}
        className={cn(
          isApproved
            ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
            : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
        )}
      >
        {isApproved ? "Approved" : "Pending Approval"}
      </Badge>
    );
  };

  const defaultTrigger = (
    <Button variant="ghost" size="sm" className="justify-start">
      <Eye className="mr-2 h-4 w-4" />
      View
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{trigger || defaultTrigger}</DialogTrigger>

      <DialogContent className="max-w-3xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
              <Building2 className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="flex flex-col">
              <span className="text-xl">
                {loading ? "Loading..." : client?.name || "Client Profile"}
              </span>
              <span className="text-sm text-muted-foreground font-normal">
                Client Profile
              </span>
            </div>
          </DialogTitle>
          <DialogDescription>
            Complete client information and account details
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="max-h-[calc(90vh-120px)]">
          <div className="space-y-6 pr-4">
            {loading && (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="h-8 w-8 animate-spin mr-2" />
                <span>Loading client details...</span>
              </div>
            )}

            {error && (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <p className="text-destructive mb-4">{error}</p>
                  <Button onClick={fetchClient} size="sm">
                    Try Again
                  </Button>
                </div>
              </div>
            )}

            {client && !loading && !error && (
              <>
                {/* Basic Information */}
                <Card className="border-blue-200 dark:border-blue-800">
                  <CardHeader className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 rounded-t-lg">
                    <CardTitle className="flex items-center gap-2 text-blue-800 dark:text-blue-200">
                      <Building2 className="h-5 w-5" />
                      Client Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-6 space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-3">
                        <div className="flex items-center gap-2 text-sm">
                          <Hash className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">Client ID:</span>
                          <span className="font-mono bg-muted px-2 py-1 rounded text-xs">
                            {client.id}
                          </span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <Hash className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">Account ID:</span>
                          <span className="font-mono bg-muted px-2 py-1 rounded text-xs">
                            {client.accountId || "Not assigned"}
                          </span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">Email:</span>
                          <span className="break-all">{client.email}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">Phone:</span>
                          <span>{client.phone}</span>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div className="flex items-center gap-2 text-sm">
                          <Users className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">Role:</span>
                          <Badge
                            variant="outline"
                            className="text-xs bg-blue-50 text-blue-700 border-blue-200"
                          >
                            {client.role}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <Award className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">Status:</span>
                          {getApprovalBadge(client.isApproved)}
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">Joined:</span>
                          <span>{formatDate(client.createdAt)}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">Last Updated:</span>
                          <span>{formatDate(client.updatedAt)}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Activity Stats */}
                <Card className="border-green-200 dark:border-green-800">
                  <CardHeader className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950 dark:to-green-900 rounded-t-lg">
                    <CardTitle className="text-green-800 dark:text-green-200 text-lg">
                      Activity Overview
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center p-4 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900 rounded-lg">
                        <div className="text-3xl font-bold text-green-600 dark:text-green-400">
                          {client.assignmentCount || 0}
                        </div>
                        <p className="text-sm text-green-700 dark:text-green-300 font-medium">
                          Total Assignments
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          Projects posted
                        </p>
                      </div>
                      <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 rounded-lg">
                        <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                          {client.isApproved ? "Active" : "Pending"}
                        </div>
                        <p className="text-sm text-blue-700 dark:text-blue-300 font-medium">
                          Account Status
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          Current state
                        </p>
                      </div>
                      <div className="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900 rounded-lg">
                        <div className="text-3xl font-bold text-purple-600 dark:text-purple-400">
                          {Math.floor(
                            (new Date().getTime() -
                              new Date(client.createdAt).getTime()) /
                              (1000 * 60 * 60 * 24)
                          )}
                        </div>
                        <p className="text-sm text-purple-700 dark:text-purple-300 font-medium">
                          Days Active
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          Since joining
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Recent Assignments */}
                {client.recentAssignments &&
                  client.recentAssignments.length > 0 && (
                    <Card className="border-orange-200 dark:border-orange-800">
                      <CardHeader className="bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900 rounded-t-lg">
                        <CardTitle className="text-orange-800 dark:text-orange-200">
                          Recent Assignments
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="pt-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          {client.recentAssignments
                            .slice(0, 4)
                            .map((assignment: any) => (
                              <div
                                key={assignment.id}
                                className="flex items-center justify-between p-3 bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900 rounded-lg"
                              >
                                <div className="flex flex-col">
                                  <span className="font-mono text-xs text-muted-foreground">
                                    {assignment.id.slice(0, 8)}...
                                  </span>
                                  <span className="text-sm font-medium truncate">
                                    {assignment.title || "Assignment"}
                                  </span>
                                </div>
                                <Badge variant="outline" className="text-xs">
                                  {assignment.status}
                                </Badge>
                              </div>
                            ))}
                        </div>
                      </CardContent>
                    </Card>
                  )}

                {/* Account Summary */}
                <Card className="border-gray-200 dark:border-gray-700">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="flex items-center justify-center gap-2">
                        <Building2 className="h-5 w-5 text-muted-foreground" />
                        <span className="text-sm font-medium">
                          Client Account Summary
                        </span>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Account created on {formatDate(client.createdAt)} • Last
                        activity on {formatDate(client.updatedAt)}
                      </p>
                      <div className="flex justify-center gap-4 pt-2">
                        <div className="text-center">
                          <div className="text-sm font-semibold text-blue-600">
                            {client.assignmentCount || 0}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Projects
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="text-sm font-semibold text-green-600">
                            {client.isApproved ? "✓" : "⏳"}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Status
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
