// src/hooks/use-sse-chat.ts
"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { toast } from "sonner";

interface Message {
  id: string;
  chatId: string;
  senderId: string;
  content: string;
  type: "TEXT" | "EMOJI";
  isRead: boolean;
  createdAt: string;
  updatedAt: string;
  sender: {
    id: string;
    name: string;
    email: string;
    role: string;
    image?: string;
  };
}

interface ChatParticipant {
  id: string;
  chatId: string;
  userId: string;
  role: "CLIENT" | "WRITER" | "ADMIN";
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
    image?: string;
  };
}

interface UserData {
  id: string;
  name: string;
  email: string;
  role: string;
  image?: string;
  [key: string]: unknown;
}

interface UseSSEChatProps {
  assignmentId: string;
  clientId: string;
  writerId?: string;
  userRole: "ADMIN" | "CLIENT" | "WRITER";
  enabled: boolean;
  chatType?: "client" | "writer"; // Add chatType prop
}

interface UseSSEChatReturn {
  messages: Message[];
  participants: ChatParticipant[];
  isConnected: boolean;
  isLoading: boolean;
  error: string | null;
  sendMessage: (
    content: string,
    type?: "TEXT" | "EMOJI",
    chatType?: "client" | "writer"
  ) => Promise<void>;
  markMessageAsRead: (messageId: string) => Promise<void>;
  getUnreadCount: (participantRole?: "CLIENT" | "WRITER") => number;
  clientData: UserData | null;
  writerData: UserData | null;
  reconnect: () => void;
}

export function useSSEChat({
  assignmentId,
  clientId,
  writerId,
  userRole,
  enabled,
  chatType = "client", // Default to client chat
}: UseSSEChatProps): UseSSEChatReturn {
  const [messages, setMessages] = useState<Message[]>([]);
  const [participants, setParticipants] = useState<ChatParticipant[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [clientData, setClientData] = useState<UserData | null>(null);
  const [writerData, setWriterData] = useState<UserData | null>(null);

  const eventSourceRef = useRef<EventSource | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  // Fetch client and writer data
  const fetchUserData = useCallback(async () => {
    try {
      // Fetch client data
      if (clientId) {
        const clientResponse = await fetch(`/api/users/clients/${clientId}`);
        if (clientResponse.ok) {
          const clientResult = await clientResponse.json();
          setClientData(clientResult.data);
        }
      }

      // Fetch writer data
      if (writerId) {
        const writerResponse = await fetch(`/api/users/writers/${writerId}`);
        if (writerResponse.ok) {
          const writerResult = await writerResponse.json();
          setWriterData(writerResult.data);
        }
      }
    } catch (error) {
      console.error("Error fetching user data:", error);
    }
  }, [clientId, writerId]);

  const connect = useCallback(() => {
    if (!enabled || eventSourceRef.current) return;

    setIsLoading(true);
    setError(null);

    // FIXED: Include chatType in SSE URL
    const sseUrl = `/api/chat/${assignmentId}/sse?chatType=${chatType}`;
    const eventSource = new EventSource(sseUrl);
    eventSourceRef.current = eventSource;

    eventSource.onopen = () => {
      setIsConnected(true);
      setIsLoading(false);
      setError(null);
      reconnectAttempts.current = 0;
    };

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        switch (data.type) {
          case "INITIAL_DATA":
            setMessages(data.data.messages);
            setParticipants(data.data.participants);
            break;

          case "NEW_MESSAGE":
            setMessages((prev) => {
              const exists = prev.find((msg) => msg.id === data.data.id);
              if (exists) return prev;
              return [data.data, ...prev];
            });
            break;

          case "MESSAGE_READ":
            setMessages((prev) =>
              prev.map((msg) =>
                msg.id === data.data.messageId ? { ...msg, isRead: true } : msg
              )
            );
            break;

          case "HEARTBEAT":
            // Keep connection alive
            break;

          default:
            console.log("Unknown message type:", data.type);
        }
      } catch (error) {
        console.error("Error parsing SSE message:", error);
      }
    };

    eventSource.onerror = (event) => {
      console.error("SSE connection error:", event);
      setIsConnected(false);
      setIsLoading(false);

      if (reconnectAttempts.current < maxReconnectAttempts) {
        setError(
          `Connection lost. Reconnecting... (${reconnectAttempts.current + 1}/${maxReconnectAttempts})`
        );

        reconnectTimeoutRef.current = setTimeout(
          () => {
            reconnectAttempts.current++;
            eventSource.close();
            eventSourceRef.current = null;
            connect();
          },
          Math.pow(2, reconnectAttempts.current) * 1000
        );
      } else {
        setError("Connection failed. Please refresh the page.");
      }
    };

    return eventSource;
  }, [assignmentId, enabled, chatType]); // Add chatType to dependencies

  const disconnect = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    setIsConnected(false);
  }, []);

  const reconnect = useCallback(() => {
    disconnect();
    reconnectAttempts.current = 0;
    connect();
  }, [disconnect, connect]);

  const sendMessage = useCallback(
    async (
      content: string,
      type: "TEXT" | "EMOJI" = "TEXT",
      messageChatType?: "client" | "writer"
    ) => {
      if (!content.trim()) return;

      try {
        const response = await fetch(`/api/chat/${assignmentId}/messages`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            content: content.trim(),
            type,
            chatType: messageChatType || chatType, // Use passed chatType or default
          }),
        });

        if (!response.ok) {
          throw new Error("Failed to send message");
        }

        const result = await response.json();

        if (!result.success) {
          throw new Error(result.message || "Failed to send message");
        }
      } catch (error) {
        console.error("Error sending message:", error);
        toast.error("Failed to send message. Please try again.");
        throw error;
      }
    },
    [assignmentId, chatType]
  );

  const markMessageAsRead = useCallback(
    async (messageId: string) => {
      try {
        const response = await fetch(
          `/api/chat/${assignmentId}/messages/${messageId}/read`,
          {
            method: "PATCH",
          }
        );

        if (!response.ok) {
          throw new Error("Failed to mark message as read");
        }
      } catch (error) {
        console.error("Error marking message as read:", error);
      }
    },
    [assignmentId]
  );

  const getUnreadCount = useCallback(
    (participantRole?: "CLIENT" | "WRITER") => {
      if (!participantRole) {
        return messages.filter(
          (msg) => !msg.isRead && msg.sender.id !== userRole
        ).length;
      }

      return messages.filter((msg) => {
        if (msg.isRead) return false;

        const senderRole = msg.sender.role;
        return senderRole === participantRole;
      }).length;
    },
    [messages, userRole]
  );

  // Initialize connection when chatType changes
  useEffect(() => {
    if (enabled) {
      // Disconnect existing connection
      disconnect();
      // Clear messages for new chat type
      setMessages([]);
      // Fetch user data
      fetchUserData();
      // Connect with new chat type
      connect();
    }

    return () => {
      disconnect();
    };
  }, [enabled, chatType, fetchUserData, connect, disconnect]); // Add chatType to dependencies

  // Auto-mark messages as read when viewing
  useEffect(() => {
    if (messages.length > 0 && isConnected) {
      const unreadMessages = messages.filter(
        (msg) => !msg.isRead && msg.sender.id !== userRole
      );

      unreadMessages.forEach((msg) => {
        markMessageAsRead(msg.id);
      });
    }
  }, [messages, isConnected, markMessageAsRead, userRole]);

  return {
    messages,
    participants,
    isConnected,
    isLoading,
    error,
    sendMessage,
    markMessageAsRead,
    getUnreadCount,
    clientData,
    writerData,
    reconnect,
  };
}
