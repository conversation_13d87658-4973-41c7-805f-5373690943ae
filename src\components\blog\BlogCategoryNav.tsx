"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import LoadingSkeleton from "@/components/ui/LoadingSkeleton";

interface Category {
  id: string;
  name: string;
  slug: string;
}

export function BlogCategoryNav() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const pathname = usePathname();

  // Default "All Posts" category
  const defaultCategory = { id: "all-posts", name: "All Posts", slug: "all" };

  useEffect(() => {
    async function fetchCategories() {
      try {
        setIsLoading(true);
        const response = await fetch('/api/blog/categories');
        
        if (!response.ok) {
          throw new Error('Failed to fetch categories');
        }
        
        const data = await response.json();
        // Check if "All Posts" category already exists in the data
        const allPostsExists = data.some((cat: Category) => cat.slug === 'all');
        
        // Only add the default category if it doesn't already exist
        if (!allPostsExists) {
          setCategories([defaultCategory, ...data]);
        } else {
          setCategories(data);
        }
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError('Failed to load categories');
        // Fallback to at least having the "All Posts" option
        setCategories([defaultCategory]);
      } finally {
        setIsLoading(false);
      }
    }

    fetchCategories();
  }, []);

  // Determine if a category is active based on the current URL
  const isActive = (slug: string) => {
    if (slug === 'all' && pathname === '/blog') {
      return true;
    }
    return pathname === `/blog/category/${slug}`;
  };

  return (
    <nav aria-label="Blog categories" className="mb-6">
      <div className="flex flex-wrap gap-2">
        {isLoading ? (
          <div className="flex gap-2">
            <LoadingSkeleton className="h-8 w-24" count={4} />
          </div>
        ) : error ? (
          <div className="text-sm text-red-500">{error}</div>
        ) : (
          categories.map((cat) => (
            <Link
              key={cat.slug}
              href={cat.slug === 'all' ? '/blog' : `/blog/category/${cat.slug}`}
              className={cn(
                "inline-flex px-4 py-2 rounded-md text-sm font-medium transition-colors",
                "hover:bg-primary/10",
                isActive(cat.slug)
                  ? "bg-primary text-primary-foreground hover:bg-primary/90"
                  : "bg-muted text-muted-foreground"
              )}
            >
              {cat.name}
            </Link>
          ))
        )}
      </div>
    </nav>
  );
}
