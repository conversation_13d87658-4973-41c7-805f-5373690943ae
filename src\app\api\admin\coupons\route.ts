import { NextRequest, NextResponse } from "next/server";
import { apiSuccess, apiError, checkPermission, getCurrentUser } from "@/lib/api-utils";
import { couponService } from "@/lib/coupon-service";
import { couponCreateSchema } from "@/lib/validations";

// Get all coupons (Admin only)
export async function GET(): Promise<NextResponse> {
  try {
    // Only admins can access coupons
    const permissionError = await checkPermission(["ADMIN"]);
    if (permissionError) return permissionError;

    const coupons = await couponService.getAllCoupons();
    return apiSuccess(coupons);
  } catch (error) {
    console.error("Error fetching coupons:", error);
    return apiError("Failed to fetch coupons", 500);
  }
}

// Create new coupon (Admin only)
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    console.log("🔍 Coupon creation request received");

    // Check current user session
    const currentUser = await getCurrentUser();
    console.log("Current user:", currentUser);

    // Only admins can create coupons
    const permissionError = await checkPermission(["ADMIN"]);
    if (permissionError) {
      console.log("❌ Permission denied for coupon creation");
      return permissionError;
    }

    console.log("✅ Permission check passed");

    const body = await req.json();

    // Debug logging
    console.log("Received coupon creation request:", JSON.stringify(body, null, 2));

    const parsed = couponCreateSchema.safeParse(body);

    if (!parsed.success) {
      console.log("Validation failed:", parsed.error.flatten().fieldErrors);
      return apiError("Invalid input data", 400, parsed.error.flatten().fieldErrors);
    }

    const { description, discountPercentage, maxUses, expiresAt } = parsed.data;

    const result = await couponService.createCoupon({
      description,
      discountPercentage,
      maxUses,
      expiresAt,
    });

    if (!result.success) {
      return apiError(result.error || "Failed to create coupon", 500);
    }

    return apiSuccess(result.coupon, "Coupon created successfully");
  } catch (error) {
    console.error("Error creating coupon:", error);
    return apiError("Failed to create coupon", 500);
  }
}
