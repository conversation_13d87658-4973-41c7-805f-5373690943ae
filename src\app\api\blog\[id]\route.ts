import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import prisma from "@/lib/prisma";

const BlogSchema = z.object({
  title: z.string().min(3, "Title must be at least 3 characters"),
  body: z.string().min(10, "Body must be at least 10 characters"),
  slug: z.string().min(3, "Slug must be at least 3 characters"),
  metaTitle: z.string().min(3, "Meta title must be at least 3 characters"),
  metaDescription: z.string().min(3, "Meta description must be at least 3 characters"),
  imageUrl: z.string().url("Please provide a valid image URL"),
  imageAlt: z.string().min(3, "Image alt text must be at least 3 characters").max(125, "Alt text should be under 125 characters for SEO"),
  categoryId: z.string().min(1, "Please select a blog category"),
  authorId: z.string().min(1, "Please select a blog author"),
  keywords: z.array(z.string()).min(1, "Please add at least one keyword"),
  faqs: z.array(z.string()).min(1, "Please add at least one FAQ"),
});

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  if (!id) return NextResponse.json({ error: "Missing blog id" }, { status: 400 });
  const blog = await prisma.blog.findUnique({
    where: { id },
    include: { author: true, category: true },
  });
  if (!blog) return NextResponse.json({ error: "Blog not found" }, { status: 404 });
  return NextResponse.json(blog);
}

export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  if (!id) return NextResponse.json({ error: "Missing blog id" }, { status: 400 });
  const data = await req.json();
  const parsed = BlogSchema.partial().safeParse(data);
  if (!parsed.success) {
    return NextResponse.json({ error: parsed.error.flatten() }, { status: 400 });
  }
  const blog = await prisma.blog.update({
    where: { id },
    data: parsed.data,
  });
  return NextResponse.json(blog);
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  if (!id) return NextResponse.json({ error: "Missing blog id" }, { status: 400 });
  await prisma.blog.delete({ where: { id } });
  return NextResponse.json({ success: true });
}
