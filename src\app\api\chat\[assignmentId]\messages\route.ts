// src/app/api/chat/[assignmentId]/messages/route.ts
import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authConfig } from "@/auth";
import type { ChatParticipantRole } from "@prisma/client";
import { broadcastMessage } from "@/lib/chat-broadcast";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ assignmentId: string }> }
) {
  try {
    const session = await getServerSession(authConfig);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { assignmentId } = await params;
    const userId = session.user.id;
    const userRole = session.user.role;

    const body = await request.json();
    const { content, type = "TEXT", chatType } = body; // Add chatType from frontend

    if (!content || !content.trim()) {
      return NextResponse.json(
        { success: false, message: "Message content is required" },
        { status: 400 }
      );
    }

    // Verify user has access to this assignment
    const assignment = await prisma.assignment.findUnique({
      where: { id: assignmentId },
    });

    if (!assignment) {
      return NextResponse.json(
        { success: false, message: "Assignment not found" },
        { status: 404 }
      );
    }

    // Check access permissions
    const hasAccess =
      userRole === "ADMIN" ||
      (userRole === "CLIENT" && assignment.clientId === userId) ||
      (userRole === "WRITER" && assignment.assignedWriterId === userId);

    if (!hasAccess) {
      return NextResponse.json(
        { success: false, message: "Forbidden" },
        { status: 403 }
      );
    }

    // Get or create chat
    let chat = await prisma.chat.findUnique({
      where: { assignmentId },
    });

    if (!chat) {
      chat = await prisma.chat.create({
        data: {
          assignmentId,
          participants: {
            create: [
              {
                userId: assignment.clientId,
                role: "CLIENT" as ChatParticipantRole,
              },
              ...(assignment.assignedWriterId
                ? [
                    {
                      userId: assignment.assignedWriterId,
                      role: "WRITER" as ChatParticipantRole,
                    },
                  ]
                : []),
            ],
          },
        },
      });
    }

    if (!chat) {
      return NextResponse.json(
        { success: false, message: "Failed to create or retrieve chat" },
        { status: 500 }
      );
    }

    // Determine target participant and conversation type
    let targetParticipantId: string | null = null;
    let conversationType: string | null = null;

    if (userRole === "ADMIN") {
      // Admin is sending - set target based on chatType (fix case sensitivity)
      if (chatType === "client") {
        // Changed from "CLIENT" to "client"
        targetParticipantId = assignment.clientId;
        conversationType = "client";
      } else if (chatType === "writer" && assignment.assignedWriterId) {
        // Changed from "WRITER" to "writer"
        targetParticipantId = assignment.assignedWriterId;
        conversationType = "writer";
      }
    } else if (userRole === "CLIENT") {
      // Client is always sending to admin, but we need to identify admin user
      const adminUser = await prisma.user.findFirst({
        where: { role: "ADMIN" },
      });
      if (adminUser) {
        targetParticipantId = adminUser.id;
        conversationType = "client";
      }
    } else if (userRole === "WRITER") {
      // Writer is always sending to admin
      const adminUser = await prisma.user.findFirst({
        where: { role: "ADMIN" },
      });
      if (adminUser) {
        targetParticipantId = adminUser.id;
        conversationType = "writer";
      }
    }

    if (!targetParticipantId) {
      return NextResponse.json(
        { success: false, message: "Unable to determine message recipient" },
        { status: 400 }
      );
    }

    // Create message with conversation context
    const message = await prisma.message.create({
      data: {
        chatId: chat.id,
        senderId: userId,
        content: content.trim(),
        type,
        targetParticipantId,
        conversationType,
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            image: true,
          },
        },
      },
    });

    // Broadcast message only to relevant participants
    // Determine who should receive this message
    const targetUserIds = [userId]; // Always include sender
    if (targetParticipantId) {
      targetUserIds.push(targetParticipantId);
    }

    await broadcastMessage(assignmentId, message);

    return NextResponse.json({
      success: true,
      message: "Message sent successfully",
      data: message,
    });
  } catch (error) {
    console.error("Error sending message:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}
