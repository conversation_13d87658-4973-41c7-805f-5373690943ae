// "use client";

// import * as React from "react";
// import { useState, useEffect } from "react";
// import { Input } from "@/components/ui/input";
// import { Textarea } from "@/components/ui/textarea";
// import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
// import { Button } from "@/components/ui/button";
// import { Toaster, toast } from "sonner";
// import QuillEditor from "@/components/quill-editor";

// interface Author {
//   id: string;
//   name: string;
//   qualifications: string;
// }
// interface Category {
//   id: string;
//   name: string;
//   slug: string;
// }

// export default function BlogPage() {
//   const [title, setTitle] = useState("");
//   const [slug, setSlug] = useState("");
//   const [metaTitle, setMetaTitle] = useState("");
//   const [metaDescription, setMetaDescription] = useState("");
//   const [imageUrl, setImageUrl] = useState("");
//   const [authorId, setAuthorId] = useState("");
//   const [categoryId, setCategoryId] = useState("");
//   const [keywords, setKeywords] = useState<string[]>([]);
//   const [keywordInput, setKeywordInput] = useState("");
//   const [faqs, setFaqs] = useState<string[]>([]);
//   const [faqInput, setFaqInput] = useState("");
//   // Blog body as HTML string for Quill
//   const [body, setBody] = useState<string>("");
//   const [authors, setAuthors] = useState<Author[]>([]);
//   const [categories, setCategories] = useState<Category[]>([]);
//   const [loading, setLoading] = useState(false);

//   useEffect(() => {
//     fetch("/api/blog/authors").then(res => res.json()).then(setAuthors);
//     fetch("/api/blog/categories").then(res => res.json()).then(setCategories);
//   }, []);

//   useEffect(() => {
//     // Auto-generate slug from title
//     setSlug(title.toLowerCase().replace(/[^a-z0-9]+/g, "-").replace(/(^-|-$)/g, ""));
//   }, [title]);

//   const handleAddKeyword = () => {
//     if (keywordInput && !keywords.includes(keywordInput)) {
//       setKeywords([...keywords, keywordInput]);
//       setKeywordInput("");
//     }
//   };

//   const handleRemoveKeyword = (kw: string) => setKeywords(keywords.filter(k => k !== kw));

//   const handleAddFaq = () => {
//     if (faqInput && !faqs.includes(faqInput)) {
//       setFaqs([...faqs, faqInput]);
//       setFaqInput("");
//     }
//   };
//   const handleRemoveFaq = (f: string) => setFaqs(faqs.filter(q => q !== f));

//   const handleSubmit = async (e: React.FormEvent) => {
//     e.preventDefault();
//     setLoading(true);
//     try {
//       const html = body;
//       if (!html || html.replace(/<[^>]+>/g, '').trim().length < 10) {
//         toast.error("Blog body must be at least 10 characters.");
//         setLoading(false);
//         return;
//       }
//       const res = await fetch("/api/blog", {
//         method: "POST",
//         headers: { "Content-Type": "application/json" },
//         body: JSON.stringify({
//           title,
//           body: html,
//           slug,
//           metaTitle,
//           metaDescription,
//           imageUrl,
//           categoryId,
//           authorId,
//           keywords,
//           faqs,
//         }),
//       });
//       if (!res.ok) {
//         const err = await res.json();
//         toast.error(err.error ? JSON.stringify(err.error) : "Failed to create blog post");
//         console.error(err)
//         setLoading(false);
//         return;
//       }
//       toast.success("Blog post created!");
//       // Optionally reset form
//       setTitle("");
//       setSlug("");
//       setMetaTitle("");
//       setMetaDescription("");
//       setImageUrl("");
//       setAuthorId("");
//       setCategoryId("");
//       setKeywords([]);
//       setFaqs([]);
//       setBody("");
//     } catch (err) {
//       toast.error("Failed to create blog post");
//       console.error(err)
//     } finally {
//       setLoading(false);
//     }
//   };

//   return (
//     <div className="max-w-2xl mx-auto py-8 px-2">
//       <h1 className="text-2xl font-bold mb-4">Create Blog Post</h1>
//       <form className="space-y-6" onSubmit={handleSubmit}>
//         <div>
//           <label className="block mb-1 font-medium">Title</label>
//           <Input value={title} onChange={e => setTitle(e.target.value)} required />
//         </div>
//         <div>
//           <label className="block mb-1 font-medium">Slug</label>
//           <Input value={slug} onChange={e => setSlug(e.target.value)} required />
//         </div>
//         <div>
//           <label className="block mb-1 font-medium">Meta Title</label>
//           <Input value={metaTitle} onChange={e => setMetaTitle(e.target.value)} required />
//         </div>
//         <div>
//           <label className="block mb-1 font-medium">Meta Description</label>
//           <Textarea value={metaDescription} onChange={e => setMetaDescription(e.target.value)} required />
//         </div>
//         <div>
//           <label className="block mb-1 font-medium">Image URL</label>
//           <Input value={imageUrl} onChange={e => setImageUrl(e.target.value)} type="url" required />
//         </div>
//         <div>
//           <label className="block mb-1 font-medium">Author</label>
//           <Select value={authorId} onValueChange={setAuthorId}>
//   <SelectTrigger className="w-full" aria-label="Select author">
//     <SelectValue placeholder="Select author" />
//   </SelectTrigger>
//   <SelectContent>
//     {authors.map(a => (
//       <SelectItem key={a.id} value={a.id}>
//         <span className="flex flex-col items-start">
//           <span className="font-medium">{a.name}</span>
//           <span className="text-xs text-muted-foreground">{a.qualifications}</span>
//         </span>
//       </SelectItem>
//     ))}
//   </SelectContent>
// </Select>
//         </div>
//         <div>
//           <label className="block mb-1 font-medium">Category</label>
//           <Select value={categoryId} onValueChange={setCategoryId}>
//   <SelectTrigger className="w-full" aria-label="Select category">
//     <SelectValue placeholder="Select category" />
//   </SelectTrigger>
//   <SelectContent>
//     {categories.map(c => (
//       <SelectItem key={c.id} value={c.id}>{c.name}</SelectItem>
//     ))}
//   </SelectContent>
// </Select>
//         </div>
//         <div>
//           <label className="block mb-1 font-medium">Keywords (comma separated)</label>
//           <div className="flex gap-2 mb-2">
//             <Input
//               value={keywordInput}
//               onChange={e => setKeywordInput(e.target.value)}
//               onKeyDown={e => { if (e.key === 'Enter') { e.preventDefault(); handleAddKeyword(); }}}
//               placeholder="Add keyword"
//             />
//             <Button type="button" onClick={handleAddKeyword} variant="secondary">Add</Button>
//           </div>
//           <div className="flex flex-wrap gap-2">
//             {keywords.map(kw => (
//               <span key={kw} className="bg-muted px-2 py-1 rounded text-xs flex items-center gap-1">
//                 {kw}
//                 <button type="button" onClick={() => handleRemoveKeyword(kw)} className="ml-1 text-red-500">&times;</button>
//               </span>
//             ))}
//           </div>
//         </div>
//         <div>
//           <label className="block mb-1 font-medium">FAQs (comma separated)</label>
//           <div className="flex gap-2 mb-2">
//             <Input
//               value={faqInput}
//               onChange={e => setFaqInput(e.target.value)}
//               onKeyDown={e => { if (e.key === 'Enter') { e.preventDefault(); handleAddFaq(); }}}
//               placeholder="Add FAQ"
//             />
//             <Button type="button" onClick={handleAddFaq} variant="secondary">Add</Button>
//           </div>
//           <div className="flex flex-wrap gap-2">
//             {faqs.map(f => (
//               <span key={f} className="bg-muted px-2 py-1 rounded text-xs flex items-center gap-1">
//                 {f}
//                 <button type="button" onClick={() => handleRemoveFaq(f)} className="ml-1 text-red-500">&times;</button>
//               </span>
//             ))}
//           </div>
//         </div>
//         <div>
//           <label className="block mb-1 font-medium">Blog Body</label>
//           <div className="border rounded-md overflow-hidden">
//             <QuillEditor
//               value={body}
//               onChange={setBody}
//               placeholder="Write your blog content here..."
//               className="min-h-[250px]"
//             />
//           </div>
//         </div>
//         <Button type="submit" disabled={loading} className="w-full">{loading ? "Creating..." : "Create Blog Post"}</Button>
//       </form>
//       <Toaster richColors />
//     </div>
//   );
// }

