"use client";

import { motion } from "framer-motion";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { useCompanyInfo } from "@/hooks/use-company-info";
import {
  ArrowRight,
  CheckCircle,
  Clock,
  Star,
  MessageCircle,
  FileText,
  Users
} from "lucide-react";

const quickActions = [
  {
    icon: FileText,
    title: "Place Your Order",
    description: "Get started in minutes",
    href: "/create-order",
    color: "text-blue-600"
  },
  {
    icon: MessageCircle,
    title: "Chat with Support",
    description: "Get instant help",
    href: "/contact-us",
    color: "text-green-600"
  },
  {
    icon: Users,
    title: "View Testimonials",
    description: "Read success stories",
    href: "/testimonials",
    color: "text-purple-600"
  }
];

const benefits = [
  "Expert PhD Writers",
  "24/7 Customer Support", 
  "100% Plagiarism-Free",
  "On-Time Delivery",
  "Free Revisions",
  "Money-Back Guarantee"
];

export function WhyUsCTA() {
  const { companyInfo } = useCompanyInfo();
  const companyName = companyInfo?.companyName || "Essay App";

  return (
    <div className="container mx-auto px-4">
      <div className="max-w-6xl mx-auto">
        {/* Main CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <div className="bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10 rounded-3xl p-8 md:p-12 border border-primary/20 relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 bg-grid-pattern opacity-5" />
            
            <div className="relative z-10">
              {/* Badge */}
              <Badge 
                variant="secondary" 
                className="px-4 py-2 text-sm font-medium bg-primary/20 text-primary border-primary/30 mb-6"
              >
                <Star className="w-4 h-4 mr-2" />
                Ready to Excel in Your Studies?
              </Badge>

              {/* Main Heading */}
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 bg-gradient-to-r from-primary via-primary/80 to-primary/60 bg-clip-text text-transparent">
                Start Your Academic Success Journey Today
              </h2>

              {/* Description */}
              <p className="text-lg md:text-xl text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed">
                Join thousands of students who have transformed their academic performance with our expert writing services. 
                Your success story starts with a single click.
              </p>

              {/* Benefits Grid */}
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-8 max-w-2xl mx-auto">
                {benefits.map((benefit, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="flex items-center text-sm"
                  >
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                    <span className="text-muted-foreground">{benefit}</span>
                  </motion.div>
                ))}
              </div>

              {/* Primary CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
                <Link href="/create-order">
                  <Button 
                    size="lg" 
                    className="bg-primary text-primary-foreground hover:bg-primary/90 px-8 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 group"
                  >
                    <FileText className="w-5 h-5 mr-2" />
                    Order Now
                    <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </Link>
                
                <Link href="/contact-us">
                  <Button 
                    variant="outline" 
                    size="lg"
                    className="px-8 py-4 text-lg font-semibold border-2 hover:border-primary hover:text-primary transition-all duration-300"
                  >
                    <MessageCircle className="w-5 h-5 mr-2" />
                    Get Free Quote
                  </Button>
                </Link>
              </div>

              {/* Trust Indicators */}
              <div className="flex flex-wrap justify-center items-center gap-6 text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  <span>Live Chat Available</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  <span>Quick Response Time</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-primary" />
                  <span>Satisfaction Guaranteed</span>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid md:grid-cols-3 gap-6"
        >
          {quickActions.map((action, index) => (
            <Link key={index} href={action.href}>
              <Card className="hover:shadow-lg transition-all duration-300 border-2 hover:border-primary/20 group cursor-pointer">
                <CardContent className="p-6 text-center">
                  <div className="inline-flex p-3 rounded-lg bg-muted/50 group-hover:bg-primary/10 transition-colors mb-4">
                    <action.icon className={`w-6 h-6 ${action.color} group-hover:text-primary transition-colors`} />
                  </div>
                  <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                    {action.title}
                  </h3>
                  <p className="text-muted-foreground text-sm">
                    {action.description}
                  </p>
                  <ArrowRight className="w-4 h-4 text-muted-foreground group-hover:text-primary group-hover:translate-x-1 transition-all duration-300 mx-auto mt-3" />
                </CardContent>
              </Card>
            </Link>
          ))}
        </motion.div>

        {/* Final Trust Message */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center mt-12 pt-8 border-t border-border/50"
        >
          <p className="text-muted-foreground">
            Trusted by students from over 150 countries worldwide. Your academic success is {companyName}&apos;s mission.
          </p>
        </motion.div>
      </div>
    </div>
  );
}
