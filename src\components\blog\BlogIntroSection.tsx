import React from "react";
import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

import { Separator } from "@/components/ui/separator";

export function BlogIntroSection() {
  return (
    <>
      <section className="flex flex-col md:flex-row items-center gap-8 py-8 md:py-16">
        <div className="flex-1 flex justify-center">
          <Image
            src="/assets/blog.jpg"
            alt="Blog introduction"
            width={480}
            height={320}
            className="rounded-lg shadow-lg object-cover"
            priority
          />
        </div>
        <div className="flex-1 space-y-4 text-center md:text-left">
          <h2 className="text-2xl md:text-3xl font-bold">Start Your Academic Success Journey</h2>
          <p className="text-muted-foreground">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque efficitur, tortor sed cursus feugiat, sapien sem cursus urna, nec dictum nisi urna et urna.
          </p>
          <Button asChild variant="outline" className="mt-2">
            <Link href="/faq">Go to FAQ</Link>
          </Button>
        </div>
      </section>
      <Separator />
    </>
  );
}
