"use client";

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";

interface AreaChartProps {
  title: string;
  description?: string;
  data: Array<{
    date: string;
    [key: string]: string | number;
  }>;
  categories: Array<{
    name: string;
    color: string;
  }>;
  className?: string;
}

export function AreaChartComponent({
  title,
  description,
  data,
  categories,
  className,
}: AreaChartProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent className="pl-2">
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart
              data={data}
              margin={{
                top: 10,
                right: 30,
                left: 0,
                bottom: 0,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="date" 
                tickFormatter={(value) => {
                  const date = new Date(value);
                  return `${date.getMonth() + 1}/${date.getFullYear().toString().slice(2)}`;
                }}
              />
              <YAxis />
              <Tooltip 
                formatter={(value: number, name: string) => [`${value}`, name]}
                labelFormatter={(label) => {
                  const date = new Date(label);
                  return `${date.toLocaleString('default', { month: 'long' })} ${date.getFullYear()}`;
                }}
              />
              <Legend />
              {categories.map((category, index) => (
                <Area
                  key={index}
                  type="monotone"
                  dataKey={category.name}
                  stroke={category.color}
                  fill={category.color}
                  fillOpacity={0.3}
                  stackId={1}
                />
              ))}
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
