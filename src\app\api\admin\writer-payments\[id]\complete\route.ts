import { NextRequest } from "next/server";
import prisma from "@/lib/prisma";
import { checkPermission, apiSuccess, apiError } from "@/lib/api-utils";
import { notificationService } from "@/lib/notification-service";
import { NotificationType } from "@prisma/client";

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

export async function POST(req: NextRequest, { params }: RouteParams) {
  try {
    // Only admins can complete writer payments
    const permissionError = await checkPermission(["ADMIN"]);
    if (permissionError) return permissionError;

    const { id } = await params;
    const body = await req.json();
    const {
      writerPaypalOrderId,
      writerPaypalPaymentId,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      manualPayment = false,
      paymentMethod = "paypal"
    } = body;

    // Find the assignment
    const assignment = await prisma.assignment.findUnique({
      where: { id },
      include: {
        assignedWriter: true,
      },
    });

    if (!assignment) {
      return apiError("Assignment not found", 404);
    }

    if (!assignment.assignedWriterId) {
      return apiError("Assignment is not assigned to a writer", 400);
    }

    if (assignment.isWriterPaid) {
      return apiError("Writer has already been paid for this assignment", 400);
    }

    if (assignment.status !== "COMPLETED") {
      return apiError("Assignment must be completed before payment", 400);
    }

    // Update assignment with payment information
    const updatedAssignment = await prisma.assignment.update({
      where: { id },
      data: {
        isWriterPaid: true,
        writerPaymentDate: new Date(),
        ...(writerPaypalOrderId && { writerPaypalOrderId }),
        ...(writerPaypalPaymentId && { writerPaypalPaymentId }),
      },
    });

    // Send notification to writer about payment
    if (assignment.assignedWriter) {
      await notificationService.sendNotification({
        userId: assignment.assignedWriter.id,
        type: NotificationType.PAYMENT_RECEIVED,
        title: "Payment Received",
        message: `You have received payment of $${assignment.writerCompensation?.toFixed(2) || '0.00'} for assignment "${assignment.title}"`,
        assignmentId: assignment.id,
        taskId: assignment.taskId,
        emailData: {
          userEmail: assignment.assignedWriter.email,
          userName: assignment.assignedWriter.name || "Writer",
          assignmentTitle: assignment.title,
          amount: assignment.writerCompensation || 0,
          customMessage: `Payment method: ${paymentMethod === "paypal" ? "PayPal" : "M-Pesa"}`,
        },
      });
    }

    // Send notification to all admins about payment completion
    const admins = await prisma.user.findMany({
      where: { role: "ADMIN" },
      select: { id: true },
    });

    if (admins.length > 0) {
      await notificationService.sendNotificationToMultipleUsers(
        admins.map(admin => admin.id),
        NotificationType.PAYMENT_COMPLETED,
        "Writer Payment Completed",
        `Payment of $${assignment.writerCompensation?.toFixed(2) || '0.00'} has been disbursed to ${assignment.assignedWriter?.name || 'writer'} for assignment "${assignment.title}"`,
        assignment.taskId,
        assignment.id
      );
    }

    return apiSuccess({
      message: "Writer payment completed successfully",
      assignment: {
        id: updatedAssignment.id,
        title: updatedAssignment.title,
        taskId: updatedAssignment.taskId,
        isWriterPaid: updatedAssignment.isWriterPaid,
        writerPaymentDate: updatedAssignment.writerPaymentDate?.toISOString(),
        writerCompensation: updatedAssignment.writerCompensation,
      },
    });
  } catch (error) {
    console.error("Error completing writer payment:", error);
    return apiError("Failed to complete writer payment", 500);
  }
}

// Get payment details for a specific assignment
export async function GET(req: NextRequest, { params }: RouteParams) {
  try {
    // Only admins can view payment details
    const permissionError = await checkPermission(["ADMIN"]);
    if (permissionError) return permissionError;

    const { id } = await params;

    const assignment = await prisma.assignment.findUnique({
      where: { id },
      include: {
        assignedWriter: true,
      },
    });

    if (!assignment) {
      return apiError("Assignment not found", 404);
    }

    const paymentDetails = {
      id: assignment.id,
      assignmentId: assignment.id,
      writerId: assignment.assignedWriterId,
      writerCompensation: assignment.writerCompensation || 0,
      isWriterPaid: assignment.isWriterPaid,
      writerPaypalEmail: assignment.writerPaypalEmail || assignment.assignedWriter?.email,
      writerPaymentDate: assignment.writerPaymentDate?.toISOString(),
      writerPaypalOrderId: assignment.writerPaypalOrderId,
      writerPaypalPaymentId: assignment.writerPaypalPaymentId,
      assignment: {
        id: assignment.id,
        title: assignment.title,
        taskId: assignment.taskId,
        status: assignment.status,
        price: assignment.price,
        pageCount: assignment.pageCount,
        createdAt: assignment.createdAt.toISOString(),
        updatedAt: assignment.updatedAt.toISOString(),
      },
      writer: assignment.assignedWriter,
    };

    return apiSuccess(paymentDetails);
  } catch (error) {
    console.error("Error fetching payment details:", error);
    return apiError("Failed to fetch payment details", 500);
  }
}
