import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { Resend } from "resend";
import { v4 as uuidv4 } from "uuid";

const resend = new Resend(process.env.RESEND_API_KEY);
const fromAddress = process.env.RESEND_VERIFIED_DOMAIN
  ? `noreply@${process.env.RESEND_VERIFIED_DOMAIN}`
  : "<EMAIL>";

function getEmailVerificationTemplate(verificationUrl: string): string {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Verify Your Email Address</title>
      <style>
        body {
          margin: 0;
          padding: 0;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
          line-height: 1.6;
          color: #333333;
          background-color: #f8f9fa;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          background-color: #ffffff;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          padding: 40px 30px;
          text-align: center;
        }
        .header h1 {
          color: #ffffff;
          margin: 0;
          font-size: 28px;
          font-weight: 600;
        }
        .content {
          padding: 40px 30px;
        }
        .welcome-text {
          font-size: 18px;
          color: #2d3748;
          margin-bottom: 20px;
          text-align: center;
        }
        .description {
          font-size: 16px;
          color: #4a5568;
          margin-bottom: 30px;
          text-align: center;
          line-height: 1.7;
        }
        .verify-button {
          display: block;
          width: fit-content;
          margin: 30px auto;
          padding: 16px 32px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: #ffffff;
          text-decoration: none;
          border-radius: 8px;
          font-weight: 600;
          font-size: 16px;
          text-align: center;
          transition: transform 0.2s ease;
        }
        .verify-button:hover {
          transform: translateY(-2px);
        }
        .alternative-link {
          margin-top: 30px;
          padding: 20px;
          background-color: #f7fafc;
          border-radius: 6px;
          border-left: 4px solid #667eea;
        }
        .alternative-link p {
          margin: 0 0 10px 0;
          font-size: 14px;
          color: #4a5568;
        }
        .alternative-link a {
          color: #667eea;
          word-break: break-all;
          text-decoration: none;
        }
        .footer {
          background-color: #f7fafc;
          padding: 30px;
          text-align: center;
          border-top: 1px solid #e2e8f0;
        }
        .footer p {
          margin: 0;
          font-size: 14px;
          color: #718096;
        }
        .security-note {
          margin-top: 20px;
          padding: 15px;
          background-color: #fef5e7;
          border-radius: 6px;
          border-left: 4px solid #f6ad55;
        }
        .security-note p {
          margin: 0;
          font-size: 14px;
          color: #744210;
        }
        @media (max-width: 600px) {
          .container {
            margin: 0 10px;
          }
          .header, .content, .footer {
            padding: 20px;
          }
          .header h1 {
            font-size: 24px;
          }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>📧 Email Verification</h1>
        </div>

        <div class="content">
          <p class="welcome-text">Welcome to Academic App!</p>

          <p class="description">
            Thank you for creating your account. To get started and ensure the security of your account,
            please verify your email address by clicking the button below.
          </p>

          <a href="${verificationUrl}" class="verify-button">
            ✅ Verify Email Address
          </a>

          <div class="alternative-link">
            <p><strong>Button not working?</strong></p>
            <p>Copy and paste this link into your browser:</p>
            <a href="${verificationUrl}">${verificationUrl}</a>
          </div>

          <div class="security-note">
            <p>
              <strong>🔒 Security Note:</strong> This verification link will expire in 24 hours.
              If you didn't create an account with Academic App, please ignore this email.
            </p>
          </div>
        </div>

        <div class="footer">
          <p>
            This email was sent from Academic App.<br>
            If you have any questions, please contact our support team.
          </p>
        </div>
      </div>
    </body>
    </html>
  `;
}

export async function POST(req: NextRequest): Promise<NextResponse> {
  const { email, userId } = await req.json();
  if (!email || !userId) {
    return NextResponse.json(
      { error: "Missing email or userId" },
      { status: 400 }
    );
  }
  // Remove any existing token for this user
  await prisma.emailVerificationToken.deleteMany({ where: { userId } });
  // Generate new token
  const token = uuidv4();
  const expires = new Date(Date.now() + 1000 * 60 * 60 * 24); // 24 hours
  await prisma.emailVerificationToken.create({
    data: { userId, token, expires },
  });
  // Send email via Resend
  const verificationUrl = `${process.env.NEXT_PUBLIC_BASE_URL || process.env.NEXTAUTH_URL}/verify-email?token=${token}`;
  try {
    const result = await resend.emails.send({
      from: fromAddress,
      to: email,
      subject: "Verify your email address - Academic App",
      html: getEmailVerificationTemplate(verificationUrl),
    });

    if (result.error) {
      console.error("Resend error:", result.error);
      return NextResponse.json(
        { error: "Failed to send email", details: result.error },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Resend send exception:", error);
    return NextResponse.json(
      { error: "Failed to send email", details: error },
      { status: 500 }
    );
  }
  return NextResponse.json({ success: true });
}
