"use client"

import { useState, useCallback } from 'react';
import { UploadResult, UploadError, UploadHookReturn } from '@/types/upload';

export const useCloudinaryUpload = (): UploadHookReturn => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);

  const resetError = useCallback(() => {
    setError(null);
  }, []);

  const uploadImage = useCallback(async (file: File): Promise<UploadResult> => {
    setIsUploading(true);
    setUploadProgress(0);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('file', file);

      const xhr = new XMLHttpRequest();

      return new Promise((resolve, reject) => {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100);
            setUploadProgress(progress);
          }
        });

        xhr.addEventListener('load', () => {
          if (xhr.status === 200) {
            try {
              const result = JSON.parse(xhr.responseText) as UploadResult;
              resolve(result);
            } catch (parseError) {
              reject(new Error('Failed to parse response'));
              console.error(parseError);
            }
          } else {
            try {
              const errorResult = JSON.parse(xhr.responseText) as UploadError;
              reject(new Error(errorResult.error || 'Upload failed'));
            } catch (parseError) {
              reject(new Error('Upload failed'));
              console.error(parseError);
            }
          }
        });

        xhr.addEventListener('error', () => {
          reject(new Error('Network error occurred'));
        });

        xhr.open('POST', '/api/upload');
        xhr.send(formData);
      });

    } catch (uploadError) {
      const errorMessage = uploadError instanceof Error ? uploadError.message : 'Upload failed';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  }, []);

  return {
    uploadImage,
    isUploading,
    uploadProgress,
    error,
    resetError
  };
};