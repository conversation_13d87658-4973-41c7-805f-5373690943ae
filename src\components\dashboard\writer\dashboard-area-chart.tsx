"use client";

import { TrendingUp, Loader2 } from "lucide-react";
import { Area, AreaChart, CartesianGrid, XA<PERSON><PERSON>, <PERSON>Axi<PERSON> } from "recharts";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { useWriterAssignments } from "@/hooks/use-writer-assignments";

const chartConfig = {
  assignments: {
    label: "Assignments",
    color: "var(--color-chart-1)",
  },
} satisfies ChartConfig;

export default function WriterAssignmentsChart() {
  const { monthlyData, loading, error, totalCompleted } = useWriterAssignments();

  if (loading) {
    return (
      <Card className="border-0 shadow-none bg-transparent h-full flex flex-col">
        <CardContent className="flex items-center justify-center h-full">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">Loading assignments...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="border-0 shadow-none bg-transparent h-full flex flex-col">
        <CardContent className="flex items-center justify-center h-full">
          <div className="text-center">
            <p className="text-sm text-destructive">Error loading assignments</p>
            <p className="text-xs text-muted-foreground mt-1">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const currentYear = new Date().getFullYear();
  const hasData = monthlyData.some(item => item.assignments > 0);

  return (
    <Card className="border-0 shadow-none bg-transparent h-full flex flex-col">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Assignment Progress</CardTitle>
        <CardDescription>
          Monthly assignments completed this year
        </CardDescription>
      </CardHeader>
      <CardContent className="flex-1 pb-2 min-h-0">
        {hasData ? (
          <ChartContainer config={chartConfig} className="h-full w-full min-h-[200px] max-h-[300px]">
            <AreaChart
              accessibilityLayer
              data={monthlyData}
              margin={{
                left: 12,
                right: 12,
                top: 12,
                bottom: 12,
              }}
            >
              <defs>
                <linearGradient id="fillAssignments" x1="0" y1="0" x2="0" y2="1">
                  <stop
                    offset="5%"
                    stopColor="var(--color-chart-1)"
                    stopOpacity={0.8}
                  />
                  <stop
                    offset="95%"
                    stopColor="var(--color-chart-1)"
                    stopOpacity={0.1}
                  />
                </linearGradient>
              </defs>
              <CartesianGrid vertical={false} />
              <XAxis
                dataKey="month"
                tickLine={false}
                axisLine={false}
                tickMargin={8}
                tickFormatter={(value) => value.slice(0, 3)}
              />
              <YAxis
                tickLine={false}
                axisLine={false}
                tickMargin={8}
                tickCount={6}
              />
              <ChartTooltip
                cursor={false}
                content={<ChartTooltipContent indicator="line" />}
              />
              <Area
                dataKey="assignments"
                type="natural"
                fill="url(#fillAssignments)"
                fillOpacity={1}
                stroke="var(--color-chart-1)"
                strokeWidth={2}
                stackId="a"
              />
            </AreaChart>
          </ChartContainer>
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">No completed assignments yet</p>
              <p className="text-xs text-muted-foreground mt-1">Start completing assignments to see your progress</p>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="pt-0">
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 font-medium leading-none">
              {totalCompleted} assignments completed <TrendingUp className="h-4 w-4" />
            </div>
            <div className="flex items-center gap-2 leading-none text-muted-foreground">
              {currentYear} progress
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
