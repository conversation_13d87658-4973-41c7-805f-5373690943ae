"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { VerificationInput } from "@/components/ui/verification-input";
import { toast } from "sonner";
import { Eye, EyeOff, ArrowLeft, Loader2, CheckCircle, RefreshCw } from "lucide-react";

export function ResetPasswordForm({ className, ...props }: React.ComponentProps<"div">) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [step, setStep] = useState<'code' | 'password' | 'success'>('code');
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);
  
  const [formData, setFormData] = useState({
    code: "",
    newPassword: "",
    confirmPassword: "",
  });
  
  const [errors, setErrors] = useState({
    code: "",
    password: "",
    confirmPassword: "",
  });

  const email = searchParams.get('email') || '';

  const validatePassword = (password: string) => {
    if (password.length < 8) {
      return "Password must be at least 8 characters long";
    }
    return "";
  };

  const validateConfirmPassword = (password: string, confirmPassword: string) => {
    if (password !== confirmPassword) {
      return "Passwords do not match";
    }
    return "";
  };

  const handleCodeChange = (code: string) => {
    setFormData(prev => ({ ...prev, code }));
    setErrors(prev => ({ ...prev, code: "" }));
  };

  const handleCodeComplete = async (code: string) => {
    setIsVerifying(true);
    
    try {
      const response = await fetch(`/api/auth/reset-password?code=${code}`);
      const data = await response.json();

      if (response.ok) {
        toast.success("Code verified! Please enter your new password.");
        setStep('password');
      } else {
        setErrors(prev => ({ ...prev, code: data.message || "Invalid verification code" }));
        toast.error(data.message || "Invalid verification code");
      }
    } catch (error) {
      console.error("Code verification error:", error);
      setErrors(prev => ({ ...prev, code: "Failed to verify code. Please try again." }));
      toast.error("Failed to verify code. Please try again.");
    } finally {
      setIsVerifying(false);
    }
  };

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Validate passwords
    const passwordError = validatePassword(formData.newPassword);
    const confirmPasswordError = validateConfirmPassword(formData.newPassword, formData.confirmPassword);

    if (passwordError || confirmPasswordError) {
      setErrors({
        code: "",
        password: passwordError,
        confirmPassword: confirmPasswordError,
      });
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch("/api/auth/reset-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          code: formData.code,
          newPassword: formData.newPassword,
          confirmPassword: formData.confirmPassword,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setStep('success');
        toast.success("Password reset successfully!");
      } else {
        toast.error(data.message || "Failed to reset password");
      }
    } catch (error) {
      console.error("Password reset error:", error);
      toast.error("An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    if (!email) {
      toast.error("Email address is required to resend code");
      return;
    }

    setIsResending(true);

    try {
      const response = await fetch("/api/auth/forgot-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      if (response.ok) {
        toast.success("New verification code sent to your email");
        setFormData(prev => ({ ...prev, code: "" }));
        setErrors(prev => ({ ...prev, code: "" }));
      } else {
        toast.error("Failed to resend code. Please try again.");
      }
    } catch (error) {
      console.error("Resend code error:", error);
      toast.error("Failed to resend code. Please try again.");
    } finally {
      setIsResending(false);
    }
  };

  if (step === 'success') {
    return (
      <div className={cn("flex flex-col gap-6", className)} {...props}>
        <Card className="overflow-hidden">
          <CardContent className="p-8">
            <div className="flex flex-col items-center text-center space-y-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              
              <div className="space-y-2">
                <h1 className="text-2xl font-bold">Password Reset Successfully!</h1>
                <p className="text-muted-foreground max-w-md">
                  Your password has been updated. You can now log in with your new password.
                </p>
              </div>

              <Button 
                onClick={() => router.push('/login/client')}
                className="w-full max-w-sm"
              >
                Continue to Login
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card className="overflow-hidden">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold">
            {step === 'code' ? 'Enter Verification Code' : 'Create New Password'}
          </CardTitle>
          <p className="text-muted-foreground">
            {step === 'code' 
              ? `We sent a 6-digit code to ${email || 'your email'}`
              : 'Please enter your new password below'
            }
          </p>
        </CardHeader>
        <CardContent className="p-8">
          {step === 'code' ? (
            <div className="space-y-6">
              <div className="space-y-4">
                <Label className="text-center block">Verification Code</Label>
                <VerificationInput
                  value={formData.code}
                  onChange={handleCodeChange}
                  onComplete={handleCodeComplete}
                  disabled={isVerifying}
                  error={!!errors.code}
                  className="justify-center"
                />
                {errors.code && (
                  <p className="text-sm text-red-500 text-center">{errors.code}</p>
                )}
                {isVerifying && (
                  <p className="text-sm text-muted-foreground text-center flex items-center justify-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Verifying code...
                  </p>
                )}
              </div>

              <div className="text-center space-y-2">
                <p className="text-sm text-muted-foreground">
                  Didn&apos;t receive the code?
                </p>
                <Button 
                  variant="ghost" 
                  onClick={handleResendCode}
                  disabled={isResending}
                  className="text-sm"
                >
                  {isResending ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Resend Code
                    </>
                  )}
                </Button>
              </div>

              <div className="text-center">
                <Button variant="ghost" asChild>
                  <Link href="/forgot-password" className="text-sm">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Use Different Email
                  </Link>
                </Button>
              </div>
            </div>
          ) : (
            <form onSubmit={handlePasswordSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="newPassword">New Password</Label>
                <div className="relative">
                  <Input
                    id="newPassword"
                    name="newPassword"
                    type={passwordVisible ? "text" : "password"}
                    required
                    value={formData.newPassword}
                    onChange={(e) => {
                      setFormData(prev => ({ ...prev, newPassword: e.target.value }));
                      setErrors(prev => ({ ...prev, password: "" }));
                    }}
                    disabled={isLoading}
                    className="h-12 pr-10"
                    placeholder="Enter your new password"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-12 px-3"
                    onClick={() => setPasswordVisible(!passwordVisible)}
                  >
                    {passwordVisible ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                {errors.password && (
                  <p className="text-sm text-red-500">{errors.password}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirm New Password</Label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={confirmPasswordVisible ? "text" : "password"}
                    required
                    value={formData.confirmPassword}
                    onChange={(e) => {
                      setFormData(prev => ({ ...prev, confirmPassword: e.target.value }));
                      setErrors(prev => ({ ...prev, confirmPassword: "" }));
                    }}
                    disabled={isLoading}
                    className="h-12 pr-10"
                    placeholder="Confirm your new password"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-12 px-3"
                    onClick={() => setConfirmPasswordVisible(!confirmPasswordVisible)}
                  >
                    {confirmPasswordVisible ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                {errors.confirmPassword && (
                  <p className="text-sm text-red-500">{errors.confirmPassword}</p>
                )}
              </div>

              <Button
                type="submit"
                className="w-full h-12"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Resetting Password...
                  </>
                ) : (
                  "Reset Password"
                )}
              </Button>

              <div className="text-center">
                <Button variant="ghost" asChild>
                  <Link href="/login/client" className="text-sm">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Login
                  </Link>
                </Button>
              </div>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
