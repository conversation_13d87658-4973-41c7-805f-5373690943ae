// src/middleware.ts
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { getToken } from "next-auth/jwt";
import type { UserRole } from "@prisma/client";

export async function middleware(req: NextRequest) {
  const { nextUrl } = req;
  const pathname = nextUrl.pathname;

  // CLAUDE MIDDLEWARE: - First, check if the current page is a login/register page to prevent redirect loops
  if (
    pathname.startsWith("/login") ||
    pathname.startsWith("/register") ||
    pathname === "/unauthorized" ||
    pathname === "/verify-email-required" ||
    pathname.startsWith("/auth-error") ||
    pathname.startsWith("/verify-email") ||
    pathname === "/writer-assessment"
  ) {
    return NextResponse.next();
  }

  //Expanded public routes list to include blog and any other public sections
  const publicRoutes = [
    "/",
    "/about-us",
    "/contact-us",
    "/why-us",
    "/how-it-works",
    "/careers",
    "/faqs",
    "/contact",
    "/login",
    "/register",
    "/create-order",
    "/blog", // Main blog page
    "/terms",
    "/cookies-policy",
    "/code-of-conduct",
    "/refund-policy",
    "/privacy-policy",
    "/services",
    "/services/custom-essay-writing",
    "/services/dissertation",
    "/services/research-paper",
    "/services/literature-review",
    "/services/term-paper",
    "/services/dissertation",
    "/testimonials",
    "/privacy",
    "/sitemap",
    "/robots.txt",
  ];

  //Added pattern matching for blog posts and other dynamic public content
  const publicPatterns = [
    /^\/blog\/.*$/, // All blog post paths like /blog/some-post-slug
    /^\/samples\/.*$/, // All samples post paths like /samples/some-post-slug
    /^\/assets\/.*$/, // Public assets if applicable
    /^\/categories\/.*$/, // Category pages if applicable
  ];

  //Check if current path is in public routes or matches a public pattern
  if (
    publicRoutes.some((route) => pathname === route) ||
    publicPatterns.some((pattern) => pattern.test(pathname))
  ) {
    return NextResponse.next();
  }

  // Get token from session cookie
  const token = await getToken({
    req,
    secret: process.env.NEXTAUTH_SECRET,
  });

  // Redirect unauthenticated users to login
  if (!token) {
    const loginUrl = new URL("/login/client", nextUrl.origin);
    return NextResponse.redirect(loginUrl);
  }

  // Check if user's email is verified (for social login users)
  if (token.emailVerified === false) {
    const verifyEmailUrl = new URL("/verify-email-required", nextUrl.origin);
    return NextResponse.redirect(verifyEmailUrl);
  }

  // Check if writer is approved before allowing access to writer dashboard
  if (token.role === "WRITER" && pathname.startsWith("/writer/dashboard")) {
    if (token.isApproved === false) {
      const assessmentUrl = new URL("/writer-assessment", nextUrl.origin);
      return NextResponse.redirect(assessmentUrl);
    }
  }

  // Role-based path restrictions
  const rolePaths: Record<string, UserRole> = {
    "/admin": "ADMIN",
    "/writer": "WRITER",
    "/client": "CLIENT",
  };

  // Check if user has permission for the requested path
  for (const [path, role] of Object.entries(rolePaths)) {
    if (pathname.startsWith(path) && token.role !== role) {
      return NextResponse.redirect(new URL("/unauthorized", nextUrl.origin));
    }
  }

  return NextResponse.next();
}

// Use the matcher to apply middleware only to protected routes
// This excludes public routes from middleware processing
export const config = {
  matcher: [
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
    // '/admin/:path*',
    // '/writer/:path*',
    // '/client/:path*',

    // '/((?!api|_next/static|_next/image|favicon.ico|login|register|unauthorized).*)',
  ],
};
