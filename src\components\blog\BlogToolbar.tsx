"use client";
import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import LoadingSkeleton from "@/components/ui/LoadingSkeleton";

interface Category {
  id: string;
  name: string;
  slug: string;
}

// Default category for "All Posts"
const defaultCategory = { id: "all-posts", name: "All Posts", slug: "all" };

export function BlogToolbar({
  selectedCategory,
  onCategoryChange,
  onSearchChange,
  searchValue,
}: {
  selectedCategory: string;
  onCategoryChange: (value: string) => void;
  onSearchChange: (value: string) => void;
  searchValue: string;
}) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchCategories() {
      try {
        setIsLoading(true);
        const response = await fetch('/api/blog/categories');
        
        if (!response.ok) {
          throw new Error('Failed to fetch categories');
        }
        
        const data = await response.json();
        // Check if "All Posts" category already exists in the data
        const allPostsExists = data.some((cat: Category) => cat.slug === 'all');
        
        // Only add the default category if it doesn't already exist
        if (!allPostsExists) {
          setCategories([defaultCategory, ...data]);
        } else {
          setCategories(data);
        }
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError('Failed to load categories');
        // Fallback to at least having the "All Posts" option
        setCategories([defaultCategory]);
      } finally {
        setIsLoading(false);
      }
    }

    fetchCategories();
  }, []);

  return (
    <nav aria-label="Blog categories" className="mb-8">
      <div className="flex flex-col md:flex-row md:items-center gap-4">
        <Tabs value={selectedCategory} onValueChange={onCategoryChange} className="flex-1">
          <TabsList className="flex flex-wrap gap-2">
            {isLoading ? (
              <div className="flex gap-2">
                <LoadingSkeleton className="h-8 w-24" count={4} />
              </div>
            ) : error ? (
              <div className="text-sm text-red-500">{error}</div>
            ) : (
              categories.map((cat) => (
                <TabsTrigger
                  key={cat.slug}
                  value={cat.slug}
                  className={cn(
                    "capitalize px-4 py-1 text-sm transition-colors",
                    "data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm data-[state=active]:hover:bg-primary/90",
                    "data-[state=active]:dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground dark:data-[state=active]:hover:bg-primary/90",
                    "bg-muted text-muted-foreground hover:bg-primary/20"
                  )}
                >
                  {cat.name}
                </TabsTrigger>
              ))
            )}
          </TabsList>
        </Tabs>
        <div className="relative w-full md:w-auto md:min-w-[240px]">
          <Input
            type="search"
            placeholder="Search posts..."
            className="w-full"
            value={searchValue}
            onChange={(e) => onSearchChange(e.target.value)}
          />
        </div>
      </div>
    </nav>
  );
}
