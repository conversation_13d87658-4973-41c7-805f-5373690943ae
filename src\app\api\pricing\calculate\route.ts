import { NextRequest, NextResponse } from "next/server";
import { apiSuccess, apiError } from "@/lib/api-utils";
import { pricingService } from "@/lib/pricing-service";
import { AcademicLevel, Priority, Spacing } from "@prisma/client";
import { z } from "zod";

// Schema for price calculation request
const priceCalculationSchema = z.object({
  academicLevel: z.nativeEnum(AcademicLevel),
  priority: z.nativeEnum(Priority),
  spacing: z.nativeEnum(Spacing),
  pageCount: z.number().min(1, "Page count must be at least 1"),
});

// Calculate price for order form
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    const body = await req.json();
    const parsed = priceCalculationSchema.safeParse(body);
    
    if (!parsed.success) {
      return apiError("Invalid input data", 400, parsed.error.flatten().fieldErrors);
    }

    const { academicLevel, priority, spacing, pageCount } = parsed.data;

    const breakdown = await pricingService.calculatePrice({
      academicLevel,
      priority,
      spacing,
      pageCount,
    });

    const writerCompensation = await pricingService.calculateWriterCompensation(
      breakdown.finalPrice,
      pageCount
    );

    return apiSuccess({
      priceBreakdown: breakdown,
      writerCompensation,
      finalPrice: breakdown.finalPrice,
    });
  } catch (error) {
    console.error("Error calculating price:", error);
    return apiError("Failed to calculate price", 500);
  }
}
