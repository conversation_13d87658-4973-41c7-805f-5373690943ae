// src/components/dashboard/client/dashboard-orders-charts.tsx
"use client";

import { TrendingUp, Loader2, ShoppingCart, CheckCircle } from "lucide-react";
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from "recharts";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { useClientAssignments } from "@/hooks/use-client-assignments";

const totalOrdersConfig = {
  orders: {
    label: "Total Orders",
    color: "var(--color-chart-2)",
  },
} satisfies ChartConfig;

const completedOrdersConfig = {
  orders: {
    label: "Completed Orders",
    color: "var(--color-chart-3)",
  },
} satisfies ChartConfig;

interface OrdersChartProps {
  type: "total" | "completed";
}

export function OrdersChart({ type }: OrdersChartProps) {
  const {
    totalOrdersData,
    completedOrdersData,
    loading,
    error,
    totalOrders,
    completedOrders,
  } = useClientAssignments();

  const chartData = type === "total" ? totalOrdersData : completedOrdersData;
  const chartConfig = type === "total" ? totalOrdersConfig : completedOrdersConfig;
  const orderCount = type === "total" ? totalOrders : completedOrders;
  const title = type === "total" ? "Total Orders" : "Completed Orders";
  const description = type === "total"
    ? "Monthly orders submitted this year"
    : "Monthly orders completed this year";
  const icon = type === "total" ? ShoppingCart : CheckCircle;
  const IconComponent = icon;
  const chartColor = type === "total" ? "var(--color-chart-2)" : "var(--color-chart-3)";
  const gradientId = type === "total" ? "fillTotalOrders" : "fillCompletedOrders";

  if (loading) {
    return (
      <Card className="border-0 shadow-none bg-transparent h-full flex flex-col">
        <CardContent className="flex items-center justify-center h-full">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">Loading orders...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="border-0 shadow-none bg-transparent h-full flex flex-col">
        <CardContent className="flex items-center justify-center h-full">
          <div className="text-center">
            <p className="text-sm text-destructive">Error loading orders</p>
            <p className="text-xs text-muted-foreground mt-1">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const hasData = chartData.some(item => item.orders > 0);
  const currentYear = new Date().getFullYear();

  return (
    <Card className="border-0 shadow-none bg-transparent h-full flex flex-col">
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          <div className="p-2 rounded-lg bg-primary/10 border border-primary/20">
            <IconComponent className="h-4 w-4 text-primary" />
          </div>
          <div>
            <CardTitle className="text-lg">{title}</CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="flex-1 pb-2 min-h-0">
        {hasData ? (
          <ChartContainer config={chartConfig} className="h-full w-full min-h-[180px] max-h-[240px]">
            <AreaChart
              accessibilityLayer
              data={chartData}
              margin={{
                left: 12,
                right: 12,
                top: 12,
                bottom: 12,
              }}
            >
              <defs>
                <linearGradient id={gradientId} x1="0" y1="0" x2="0" y2="1">
                  <stop
                    offset="5%"
                    stopColor={chartColor}
                    stopOpacity={0.8}
                  />
                  <stop
                    offset="95%"
                    stopColor={chartColor}
                    stopOpacity={0.1}
                  />
                </linearGradient>
              </defs>
              <CartesianGrid vertical={false} />
              <XAxis
                dataKey="month"
                tickLine={false}
                axisLine={false}
                tickMargin={8}
                tickFormatter={(value) => value.slice(0, 3)}
              />
              <YAxis
                tickLine={false}
                axisLine={false}
                tickMargin={8}
                tickCount={6}
              />
              <ChartTooltip
                cursor={false}
                content={<ChartTooltipContent indicator="line" />}
              />
              <Area
                dataKey="orders"
                type="natural"
                fill={`url(#${gradientId})`}
                fillOpacity={1}
                stroke={chartColor}
                strokeWidth={2}
                stackId="a"
              />
            </AreaChart>
          </ChartContainer>
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">
                {type === "total" ? "No orders submitted yet" : "No orders completed yet"}
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                {type === "total" 
                  ? "Start submitting orders to see your activity" 
                  : "Complete orders to see your progress"
                }
              </p>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="pt-0">
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 font-medium leading-none">
              {orderCount} {type === "total" ? "total orders" : "completed orders"} 
              <TrendingUp className="h-4 w-4" />
            </div>
            <div className="flex items-center gap-2 leading-none text-muted-foreground">
              {currentYear} activity
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}

// Export both chart components
export function TotalOrdersChart() {
  return <OrdersChart type="total" />;
}

export function CompletedOrdersChart() {
  return <OrdersChart type="completed" />;
}
