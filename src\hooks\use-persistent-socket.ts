// CLAUDE: Create this as a new file: hooks/use-persistent-socket.ts
import { useEffect, useRef, useCallback } from "react";
import { io, Socket } from "socket.io-client";
import { toast } from "sonner";

interface Message {
  id: string;
  chatId: string;
  senderId: string;
  content: string;
  type: "TEXT" | "EMOJI" | "SYSTEM";
  isRead: boolean;
  createdAt: string;
  targetParticipantId?: string;
  conversationType?: string;
  sender: {
    id: string;
    name: string | null;
    email: string;
    role: "ADMIN" | "CLIENT" | "WRITER";
  };
}

export interface SocketEventHandlers {
  onNewMessage?: (message: Message) => void;
  onMessageRead?: (data: { messageId: string }) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  // CLAUDE: Add typing event handlers
  onUserTyping?: (data: { userId: string; userRole: string; isTyping: boolean; conversationType?: string }) => void;
}

export interface UsePersistentSocketProps {
  userId: string | null;
  assignmentId: string;
  clientId: string;
  userRole: "ADMIN" | "CLIENT" | "WRITER";
  writerId?: string;
  eventHandlers?: SocketEventHandlers;
}

export function usePersistentSocket({
  userId,
  assignmentId,
  clientId,
  userRole,
  writerId,
  eventHandlers,
}: UsePersistentSocketProps) {
  const socketRef = useRef<Socket | null>(null);
  const eventHandlersRef = useRef<SocketEventHandlers | undefined>(
    eventHandlers
  );

  // CLAUDE: Update event handlers ref when they change
  useEffect(() => {
    eventHandlersRef.current = eventHandlers;
  }, [eventHandlers]);

  // CLAUDE: Initialize socket connection once per session
  useEffect(() => {
    if (!userId) return;

    // CLAUDE: Only create socket if it doesn't exist
    if (!socketRef.current) {
      console.log(
        "Initializing persistent socket connection for userId:",
        userId,
        "assignmentId:",
        assignmentId,
        "clientId:",
        clientId,
        "userRole:",
        userRole
      );

      const newSocket = io(
        process.env.NEXT_PUBLIC_SOCKET_URL || "http://localhost:3001",
        {
          query: { userId, assignmentId, clientId, userRole, writerId },
        }
      );

      // CLAUDE: Set up event listeners
      newSocket.on("connect", () => {
        console.log("Connected to chat server (persistent)");
        newSocket.emit("join-assignment-chat", {
          assignmentId,
          userRole,
          userId,
          clientId,
          writerId,
        });
        eventHandlersRef.current?.onConnect?.();
      });

      newSocket.on("new-message", (message: Message) => {
        console.log("Received new message via persistent socket:", message);
        eventHandlersRef.current?.onNewMessage?.(message);
      });

      newSocket.on("message-read", (data: { messageId: string }) => {
        eventHandlersRef.current?.onMessageRead?.(data);
      });

      // CLAUDE: Add typing event listener
      newSocket.on("user-typing", (data: { userId: string; userRole: string; isTyping: boolean; conversationType?: string }) => {
        console.log("User typing event:", data);
        eventHandlersRef.current?.onUserTyping?.(data);
      });

      newSocket.on("disconnect", () => {
        console.log("Disconnected from chat server (persistent)");
        eventHandlersRef.current?.onDisconnect?.();
      });

      newSocket.on("connect_error", (error) => {
        console.error("Socket connection error:", error);
        toast.error("Failed to connect to chat server");
      });

      socketRef.current = newSocket;
    }

    // CLAUDE: Cleanup only when component unmounts or userId changes
    return () => {
      if (socketRef.current) {
        console.log("Cleaning up persistent socket connection");
        socketRef.current.disconnect();
        socketRef.current = null;
      }
    };
  }, [userId, assignmentId, clientId, userRole, writerId]);

  // CLAUDE: Memoized send message function
  const sendMessage = useCallback(
    async (messageData: {
      content: string;
      type: "TEXT" | "EMOJI";
      targetParticipantId?: string;
      conversationType?: string;
    }) => {
      if (!socketRef.current) {
        toast.error("Not connected to chat server");
        return null;
      }

      try {
        const response = await fetch(`/api/chat/${assignmentId}/messages`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(messageData),
        });

        const data = await response.json();
        if (data.success) {
          socketRef.current.emit("send-message", {
            assignmentId,
            message: data.data,
            conversationType: messageData.conversationType,
          });
          return data.data;
        } else {
          toast.error("Failed to send message");
          return null;
        }
      } catch (error) {
        console.error("Error sending message:", error);
        toast.error("Failed to send message");
        return null;
      }
    },
    [assignmentId]
  );

  // CLAUDE: Add typing functionality
  const sendTyping = useCallback(
    (isTyping: boolean, conversationType?: string) => {
      if (!socketRef.current || !userId) return;
      
      socketRef.current.emit("typing", {
        assignmentId,
        isTyping,
        conversationType,
      });
    },
    [assignmentId, userId]
  );

  return {
    socket: socketRef.current,
    sendMessage,
    sendTyping,
    isConnected: socketRef.current?.connected ?? false,
  };
}
