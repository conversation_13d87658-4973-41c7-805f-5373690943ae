import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { checkPermission, apiError } from "@/lib/api-utils";

interface CreatePayoutRequest {
  orderId: string; // Assignment ID or bulk payment ID (format: bulk_{writerId}_{timestamp})
  amount: number;
  payeeEmail?: string; // Writer's PayPal email
}

interface CreatePayoutResponse {
  batch_id: string;
  payout_item_id: string;
}

interface PayPalApiError {
  error?: {
    message: string;
    details?: Array<{ issue: string; description: string }>;
  };
}

// Helper function to process PayPal payout
// eslint-disable-next-line @typescript-eslint/no-unused-vars
async function processPayPalPayout(body: string, orderId: string): Promise<NextResponse> {
  // Get PayPal configuration
  const environment = process.env.PAYPAL_ENVIRONMENT;
  const isSandbox = environment !== "production";
  const PAYPAL_CLIENT_ID = isSandbox
    ? process.env.PAYPAL_CLIENT_ID_SANDBOX
    : process.env.PAYPAL_CLIENT_ID_LIVE;
  const PAYPAL_SECRET = isSandbox
    ? process.env.PAYPAL_SECRET_SANDBOX
    : process.env.PAYPAL_SECRET_LIVE;
  const PAYPAL_API_URL = isSandbox
    ? "https://api-m.sandbox.paypal.com"
    : "https://api-m.paypal.com";

  const basicAuth = Buffer.from(`${PAYPAL_CLIENT_ID}:${PAYPAL_SECRET}`).toString("base64");

  // Get access token
  const tokenRes = await fetch(`${PAYPAL_API_URL}/v1/oauth2/token`, {
    method: "POST",
    headers: {
      "Authorization": `Basic ${basicAuth}`,
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: "grant_type=client_credentials",
  });

  const tokenData = await tokenRes.json();
  if (!tokenRes.ok || !tokenData.access_token) {
    console.error("[Writer PayPal] Token error:", tokenData);
    return apiError("Failed to authenticate with PayPal", 500);
  }

  console.log("[Writer PayPal Payout] Token obtained successfully");

  // Create PayPal Payout - this sends money directly from business account to writer
  const payoutRes = await fetch(`${PAYPAL_API_URL}/v1/payments/payouts`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${tokenData.access_token}`,
    },
    body,
  });

  const payoutData = await payoutRes.json();
  console.log("[Writer PayPal Payout] Payout creation response:", payoutData);

  if (!payoutRes.ok) {
    const error = payoutData as PayPalApiError;
    console.error("[Writer PayPal Payout] Payout creation failed:", error);
    return apiError(
      error.error?.message || payoutData.message || "Failed to create PayPal payout",
      payoutRes.status
    );
  }

  // Extract payout details from batch header
  // Note: PayPal Payout creation response only includes batch info, not individual items
  // Items are available when querying the batch status later
  if (!payoutData.batch_header?.payout_batch_id) {
    console.error("[Writer PayPal Payout] No batch ID found in response");
    return apiError("Payout batch ID not found in response", 500);
  }

  // For the initial response, we'll use the batch ID as both values
  // The actual payout item ID will be retrieved when checking status
  const response: CreatePayoutResponse = {
    batch_id: payoutData.batch_header.payout_batch_id,
    payout_item_id: `${payoutData.batch_header.payout_batch_id}_item_1`, // Temporary ID, will be updated when checking status
  };

  console.log("[Writer PayPal Payout] Payout created successfully:", response);
  return NextResponse.json(response);
}

export async function POST(req: NextRequest) {
  try {
    // Only admins can create writer payouts
    const permissionError = await checkPermission(["ADMIN"]);
    if (permissionError) return permissionError;

    const { orderId, amount, payeeEmail }: CreatePayoutRequest = await req.json();
    console.log("[Writer PayPal Payout] Incoming request:", { orderId, amount, payeeEmail });
    console.log("[Writer PayPal Payout] Using NEW PAYOUT API implementation");

    // Check if this is a bulk payment (format: bulk_{writerId}_{timestamp})
    const isBulkPayment = orderId.startsWith("bulk_");

    if (isBulkPayment) {
      console.log("[Writer PayPal Payout] Processing bulk payment");

      // Extract writer ID from bulk payment ID
      const bulkIdParts = orderId.split("_");
      if (bulkIdParts.length < 3) {
        return apiError("Invalid bulk payment ID format", 400);
      }

      const writerId = bulkIdParts[1];
      console.log("[Writer PayPal Payout] Bulk payment for writer:", writerId);

      // Fetch all unpaid completed assignments for this writer
      const assignments = await prisma.assignment.findMany({
        where: {
          assignedWriterId: writerId,
          status: "COMPLETED",
          isWriterPaid: false,
          writerCompensation: { not: null },
        },
        include: {
          assignedWriter: {
            select: {
              email: true,
              name: true,
            },
          },
        },
      });

      if (assignments.length === 0) {
        return apiError("No unpaid assignments found for this writer", 404);
      }

      // Use the first assignment's writer info for email
      const writerEmail = payeeEmail || assignments[0].assignedWriter?.email;
      if (!writerEmail) {
        return apiError("Writer email not found", 400);
      }

      // Calculate total amount and create payout items
      const totalCalculatedAmount = assignments.reduce((sum, assignment) => sum + (assignment.writerCompensation || 0), 0);

      // Verify the amount matches what was sent
      if (Math.abs(totalCalculatedAmount - amount) > 0.01) {
        console.warn(`[Writer PayPal Payout] Amount mismatch: calculated ${totalCalculatedAmount}, received ${amount}`);
      }

      // Generate unique batch ID for this bulk payout
      const senderBatchId = `bulk_writer_payout_${writerId}_${Date.now()}`;

      // Prepare PayPal Payout payload for bulk payment
      const body = JSON.stringify({
        sender_batch_header: {
          sender_batch_id: senderBatchId,
          email_subject: "You have received a bulk payment from Academic App",
          email_message: `Bulk payment for ${assignments.length} completed assignments`,
        },
        items: [
          {
            recipient_type: "EMAIL",
            amount: {
              value: amount.toFixed(2),
              currency: "USD",
            },
            receiver: writerEmail,
            note: `Bulk payment for ${assignments.length} assignments (${assignments.map(a => a.taskId).join(", ")})`,
            sender_item_id: orderId,
          },
        ],
      });

      // Continue with PayPal API call (same logic as single payment)
      return await processPayPalPayout(body, orderId);

    } else {
      console.log("[Writer PayPal Payout] Processing single assignment payment");

      // Original single assignment logic
      const assignment = await prisma.assignment.findUnique({
        where: { id: orderId },
        include: {
          assignedWriter: {
            select: {
              email: true,
              name: true,
            },
          },
        },
      });

      if (!assignment) {
        return apiError("Assignment not found", 404);
      }

      if (!assignment.assignedWriterId) {
        return apiError("Assignment is not assigned to a writer", 400);
      }

      if (assignment.status !== "COMPLETED") {
        return apiError("Assignment must be completed before payment", 400);
      }

      if (assignment.isWriterPaid) {
        return apiError("Writer has already been paid for this assignment", 400);
      }

      // Use writer's email as payee if not provided
      const writerEmail = payeeEmail || assignment.assignedWriter?.email;
      if (!writerEmail) {
        return apiError("Writer email not found", 400);
      }

      // Generate unique batch ID for this payout
      const senderBatchId = `writer_payout_${orderId}_${Date.now()}`;

      // Prepare PayPal Payout payload - this sends money FROM business account TO writer
      const body = JSON.stringify({
        sender_batch_header: {
          sender_batch_id: senderBatchId,
          email_subject: "You have received a payment from Academic App",
          email_message: `Payment for completed assignment: ${assignment.title}`,
        },
        items: [
          {
            recipient_type: "EMAIL",
            amount: {
              value: amount.toFixed(2),
              currency: "USD",
            },
            receiver: writerEmail,
            note: `Payment for assignment: ${assignment.title} (Task ID: ${assignment.taskId})`,
            sender_item_id: orderId,
          },
        ],
      });

      // Continue with PayPal API call
      return await processPayPalPayout(body, orderId);
    }
  } catch (error) {
    console.error("[Writer PayPal Payout] Unexpected error:", error);
    return apiError("Internal server error", 500);
  }
}
