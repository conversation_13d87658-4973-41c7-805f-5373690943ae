/* eslint-disable @typescript-eslint/no-explicit-any */

// components/blog/single/BlogSchema.tsx
interface BlogPost {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    title: string;
    body: string;
    slug: string;
    metaTitle: string;
    metaDescription: string;
    imageUrl: string;
    imageAlt: string;
    keywords: string[];
    faqs: string[];
    pageViews: number;
    category: {
      id: string;
      name: string;
      slug: string;
    };
    author: {
      id: string;
      name: string;
      qualifications: string;
    };
}

interface BlogSchemaProps {
    post: BlogPost;
}

// Site configuration
const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
const siteName = "Academic App";

export default function BlogSchema({ post }: BlogSchemaProps) {
  // Use slug instead of ID for SEO-friendly URLs
  const articleUrl = `${baseUrl}/blog/${post.slug}`;
    const wordCount = post.body.split(' ').length;
    const readingTime = Math.ceil(wordCount / 200);

    const structuredData = {
      '@context': 'https://schema.org',
      '@graph': [
        // Article Schema
        {
          '@type': 'Article',
          '@id': `${articleUrl}#article`,
          headline: post.title,
          description: post.metaDescription,
          image: {
            '@type': 'ImageObject',
            url: post.imageUrl,
            width: 1200,
            height: 630,
          },
          datePublished: post.createdAt.toISOString(),
          dateModified: post.updatedAt ? new Date(post.updatedAt).toISOString() : post.createdAt.toISOString(),
          author: {
            '@type': 'Person',
            '@id': `${baseUrl}#author-${post.author.id}`,
            name: post.author.name,
            description: post.author.qualifications,
          },
          publisher: {
            '@type': 'Organization',
            '@id': `${baseUrl}#organization`,
            name: siteName,
            logo: {
              '@type': 'ImageObject',
              url: `${baseUrl}/logo.png`, // Replace with your actual logo URL
            },
          },
          mainEntityOfPage: {
            '@type': 'WebPage',
            '@id': articleUrl,
          },
          articleSection: post.category.name,
          keywords: post.keywords.join(', '),
          wordCount: wordCount,
          timeRequired: `PT${readingTime}M`,
          url: articleUrl,
          isPartOf: {
            '@type': 'Blog',
            '@id': `${baseUrl}/blog#blog`,
          },
        },
        
        // WebPage Schema
        {
          '@type': 'WebPage',
          '@id': articleUrl,
          url: articleUrl,
          name: post.metaTitle,
          description: post.metaDescription,
          isPartOf: {
            '@type': 'WebSite',
            '@id': `${baseUrl}#website`,
          },
          primaryImageOfPage: {
            '@type': 'ImageObject',
            url: post.imageUrl,
          },
          datePublished: post.createdAt.toISOString(),
          dateModified: post.updatedAt ? new Date(post.updatedAt).toISOString() : post.createdAt.toISOString(),
        },

        // BreadcrumbList Schema
        {
          '@type': 'BreadcrumbList',
          '@id': `${articleUrl}#breadcrumb`,
          itemListElement: [
            {
              '@type': 'ListItem',
              position: 1,
              name: 'Home',
              item: baseUrl,
            },
            {
              '@type': 'ListItem',
              position: 2,
              name: 'Blog',
              item: `${baseUrl}/blog`,
            },
            {
              '@type': 'ListItem',
              position: 3,
              name: post.category.name,
              item: `${baseUrl}/blog/category/${post.category.slug}`,
            },
            {
              '@type': 'ListItem',
              position: 4,
              name: post.title,
              item: articleUrl,
            },
          ],
        },

        // Person Schema for Author
        {
          '@type': 'Person',
          '@id': `${baseUrl}#author-${post.author.id}`,
          name: post.author.name,
          description: post.author.qualifications,
          url: `${baseUrl}/author/${post.author.id}`, // Adjust if you have author pages
        },

        // Organization Schema
        {
          '@type': 'Organization',
          '@id': `${baseUrl}#organization`,
          name: siteName,
          url: baseUrl,
          logo: {
            '@type': 'ImageObject',
            url: `${baseUrl}/logo.png`, // Replace with your actual logo URL
          },
          sameAs: [
            // Add your social media URLs here
            'https://twitter.com/yourblog',
            'https://facebook.com/yourblog',
            'https://linkedin.com/company/yourblog',
          ],
        },

        // WebSite Schema
        {
          '@type': 'WebSite',
          '@id': `${baseUrl}#website`,
          url: baseUrl,
          name: siteName,
          description: 'Academic resources and educational content',
          publisher: {
            '@id': `${baseUrl}#organization`,
          },
          potentialAction: [
            {
              '@type': 'SearchAction',
              target: {
                '@type': 'EntryPoint',
                urlTemplate: `${baseUrl}/search?q={search_term_string}`,
              },
              'query-input': 'required name=search_term_string',
            },
          ],
        },
      ],
    };

    // Add FAQ Schema if FAQs exist
    if (post.faqs && post.faqs.length > 0) {
      // Create a separate FAQPage schema for better visibility in search results
      const faqSchema = {
        '@context': 'https://schema.org',
        '@type': 'FAQPage',
        '@id': `${articleUrl}#faq`,
        mainEntity: post.faqs.map((faq, index) => {
          // Assuming FAQ format is "Question|Answer" - adjust as needed
          const [question, answer] = faq.split('|');
          return {
            '@type': 'Question',
            '@id': `${articleUrl}#faq-${index}`,
            name: question?.trim() || faq,
            acceptedAnswer: {
              '@type': 'Answer',
              text: answer?.trim() || 'Answer not provided',
            },
          };
        }),
      };
      
      // Use type assertion to avoid TypeScript error
      structuredData['@graph'].push(faqSchema as any);
    }

    return (
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
    );
  }