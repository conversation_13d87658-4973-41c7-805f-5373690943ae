// components/footer/Logo.tsx
"use client";

import Link from 'next/link';
import { FileText } from 'lucide-react';
import { useCompanyInfo } from "@/hooks/use-company-info";

export function Logo() {
  const { companyInfo } = useCompanyInfo();

  return (
    <Link href="/" className="flex items-center space-x-2">
      <div className="bg-primary rounded-md p-1">
        <FileText size={24} className="text-primary-foreground" />
      </div>
      <span className="font-bold text-xl footer-text">
        {companyInfo?.companyName?.toUpperCase() || "ESSAY APP"}
      </span>
    </Link>
  );
}