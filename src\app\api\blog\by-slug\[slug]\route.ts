import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    const url = new URL(req.url);
    const action = url.searchParams.get('action');

    if (!slug) {
      return NextResponse.json(
        { error: "Slug parameter is required" },
        { status: 400 }
      );
    }

    // Handle page views request
    if (action === 'views') {
      const blog = await prisma.blog.findUnique({
        where: { slug },
        select: {
          id: true,
          pageViews: true,
          title: true,
        },
      });

      if (!blog) {
        return NextResponse.json(
          { error: "Blog post not found" },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        pageViews: blog.pageViews,
        title: blog.title,
      });
    }

    // Default: return full blog post
    const post = await prisma.blog.findUnique({
      where: { slug },
      include: {
        author: true,
        category: true,
      },
    });

    if (!post) {
      return NextResponse.json(
        { error: "Blog post not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(post);
  } catch (error) {
    console.error("Error fetching blog post by slug:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  req: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    const url = new URL(req.url);
    const action = url.searchParams.get('action');

    if (!slug) {
      return NextResponse.json(
        { error: "Slug parameter is required" },
        { status: 400 }
      );
    }

    // Handle page view tracking
    if (action === 'track-view') {
      const updatedBlog = await prisma.blog.update({
        where: { slug },
        data: {
          pageViews: {
            increment: 1,
          },
        },
        select: {
          id: true,
          pageViews: true,
        },
      });

      return NextResponse.json({
        success: true,
        pageViews: updatedBlog.pageViews,
      });
    }

    return NextResponse.json(
      { error: "Invalid action" },
      { status: 400 }
    );
  } catch (error) {
    console.error("Error processing blog action:", error);

    // If blog post doesn't exist, return 404
    if (error instanceof Error && error.message.includes("Record to update not found")) {
      return NextResponse.json(
        { error: "Blog post not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: "Failed to process request" },
      { status: 500 }
    );
  }
}
