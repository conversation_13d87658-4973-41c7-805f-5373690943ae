// "use client";

// import React, { useState, useEffect } from "react";
// import {
//   Dialog,
//   DialogContent,
//   DialogDescription,
//   DialogHeader,
//   DialogTitle,
// } from "@/components/ui/dialog";
// import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
// import { Button } from "@/components/ui/button";
// import { Badge } from "@/components/ui/badge";
// import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
// import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
// import { Separator } from "@/components/ui/separator";
// import { ScrollArea } from "@/components/ui/scroll-area";
// import {
//   User,
//   Mail,
//   CheckCircle,
//   XCircle,
//   Copy,
//   Loader2,
//   Award,
//   FileText,
// } from "lucide-react";
// import { toast } from "sonner";

// interface WriterAnswer {
//   writerId: string;
//   multipleChoiceAnswers: string[];
//   essayText: string;
//   status?: "Passed" | "Failed";
// }

// interface Assessment {
//   id: string;
//   title: string;
//   multipleChoiceQuiz: Array<{
//     question: string;
//     options: string[];
//     correctAnswer: string;
//   }>;
//   essayExam: {
//     topic: string;
//     rubrics: string;
//   };
//   writersAnswers: WriterAnswer[];
// }

// interface WriterInfo {
//   id: string;
//   name: string;
//   email: string;
//   imageUrl?: string;
// }

// interface WriterAssessmentAnswersProps {
//   isOpen: boolean;
//   onOpenChange: (open: boolean) => void;
//   writerId: string;
//   assessment: Assessment;
//   writerAnswer: WriterAnswer;
//   onStatusUpdate: (status: "Passed" | "Failed") => void;
// }

// export function WriterAssessmentAnswers({
//   isOpen,
//   onOpenChange,
//   writerId,
//   assessment,
//   writerAnswer,
//   onStatusUpdate,
// }: WriterAssessmentAnswersProps) {
//   const [writerInfo, setWriterInfo] = useState<WriterInfo | null>(null);
//   const [loadingWriter, setLoadingWriter] = useState(false);
//   const [writerError, setWriterError] = useState<string | null>(null);

//   // Fetch writer information
//   useEffect(() => {
//     if (isOpen && writerId) {
//       const fetchWriterInfo = async () => {
//         setLoadingWriter(true);
//         setWriterError(null);
//         try {
//           console.log("🔄 Fetching writer info for:", writerId);
//           const response = await fetch(`/api/users/writers/${writerId}`, {
//             method: "GET",
//             headers: {
//               "Content-Type": "application/json",
//             },
//             credentials: "include",
//           });

//           if (!response.ok) {
//             throw new Error(`Failed to fetch writer info: ${response.status}`);
//           }

//           const writer = await response.json();
//           console.log("✅ Fetched writer info:", writer);
//           setWriterInfo(writer);
//         } catch (err) {
//           console.error("❌ Error fetching writer info:", err);
//           setWriterError("Failed to load writer information");
//         } finally {
//           setLoadingWriter(false);
//         }
//       };

//       fetchWriterInfo();
//     }
//   }, [isOpen, writerId]);

//   // Calculate multiple choice score
//   const calculateScore = () => {
//     const correctAnswers = writerAnswer.multipleChoiceAnswers.filter(
//       (answer, index) =>
//         answer === assessment.multipleChoiceQuiz[index]?.correctAnswer
//     ).length;
//     const totalQuestions = assessment.multipleChoiceQuiz.length;
//     return {
//       correct: correctAnswers,
//       total: totalQuestions,
//       percentage: Math.round((correctAnswers / totalQuestions) * 100),
//     };
//   };

//   const score = calculateScore();

//   const copyEssayText = async () => {
//     try {
//       await navigator.clipboard.writeText(writerAnswer.essayText);
//       toast.success("Essay text copied to clipboard");
//     } catch (err) {
//       console.error("Failed to copy text:", err);
//       toast.error("Failed to copy text");
//     }
//   };

//   const handleStatusUpdate = async (status: "Passed" | "Failed") => {
//     try {
//       const response = await fetch(
//         `/api/admin/assessments/${assessment.id}/writers/${writerId}/status`,
//         {
//           method: "PATCH",
//           headers: {
//             "Content-Type": "application/json",
//           },
//           credentials: "include",
//           body: JSON.stringify({ status }),
//         }
//       );

//       if (!response.ok) {
//         throw new Error("Failed to update status");
//       }

//       onStatusUpdate(status);
//     } catch (err) {
//       console.error("Error updating status:", err);
//       toast.error("Failed to update status");
//     }
//   };

//   const getScoreColor = (percentage: number) => {
//     if (percentage >= 80) return "text-green-600 dark:text-green-400";
//     if (percentage >= 60) return "text-yellow-600 dark:text-yellow-400";
//     return "text-red-600 dark:text-red-400";
//   };

//   const getScoreBadgeColor = (percentage: number) => {
//     if (percentage >= 80)
//       return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
//     if (percentage >= 60)
//       return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
//     return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
//   };

//   return (
//     <Dialog open={isOpen} onOpenChange={onOpenChange}>
//       <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
//         <DialogHeader>
//           <DialogTitle className="flex items-center gap-2">
//             <FileText className="h-5 w-5" />
//             Assessment Review
//           </DialogTitle>
//           <DialogDescription>
//             Review and evaluate the writer's assessment submission
//           </DialogDescription>
//         </DialogHeader>

//         <div className="flex flex-col h-full">
//           {/* Writer Information Section */}
//           <Card className="mb-4">
//             <CardHeader className="pb-3">
//               <CardTitle className="text-lg">Writer Information</CardTitle>
//             </CardHeader>
//             <CardContent>
//               {loadingWriter ? (
//                 <div className="flex items-center gap-2">
//                   <Loader2 className="h-4 w-4 animate-spin" />
//                   <span className="text-sm text-muted-foreground">
//                     Loading writer information...
//                   </span>
//                 </div>
//               ) : writerError ? (
//                 <div className="text-sm text-destructive">{writerError}</div>
//               ) : writerInfo ? (
//                 <div className="flex items-center gap-4">
//                   <Avatar className="h-12 w-12">
//                     <AvatarImage
//                       src={writerInfo.imageUrl}
//                       alt={writerInfo.name}
//                     />
//                     <AvatarFallback>
//                       <img
//                         src="/assets/shadcn.jpg"
//                         alt="Default avatar"
//                         className="h-full w-full object-cover"
//                       />
//                     </AvatarFallback>
//                   </Avatar>
//                   <div className="flex-1">
//                     <div className="flex items-center gap-2 mb-1">
//                       <User className="h-4 w-4 text-muted-foreground" />
//                       <span className="font-medium">{writerInfo.name}</span>
//                     </div>
//                     <div className="flex items-center gap-2">
//                       <Mail className="h-4 w-4 text-muted-foreground" />
//                       <span className="text-sm text-muted-foreground">
//                         {writerInfo.email}
//                       </span>
//                     </div>
//                   </div>
//                   <div className="text-right">
//                     <p className="text-sm text-muted-foreground">Writer ID</p>
//                     <p className="font-mono text-xs">
//                       {writerId.slice(0, 16)}...
//                     </p>
//                   </div>
//                 </div>
//               ) : null}
//             </CardContent>
//           </Card>

//           {/* Assessment Content */}
//           <div className="flex-1 overflow-hidden">
//             <Tabs
//               defaultValue="multiple-choice"
//               className="h-full flex flex-col"
//             >
//               <TabsList className="grid w-full grid-cols-2">
//                 <TabsTrigger
//                   value="multiple-choice"
//                   className="flex items-center gap-2"
//                 >
//                   <Award className="h-4 w-4" />
//                   Multiple Choice
//                 </TabsTrigger>
//                 <TabsTrigger value="essay" className="flex items-center gap-2">
//                   <FileText className="h-4 w-4" />
//                   Essay Review
//                 </TabsTrigger>
//               </TabsList>

//               <TabsContent
//                 value="multiple-choice"
//                 className="flex-1 overflow-hidden"
//               >
//                 <Card className="h-full">
//                   <CardHeader className="pb-3">
//                     <div className="flex items-center justify-between">
//                       <CardTitle className="text-lg">
//                         Multiple Choice Questions
//                       </CardTitle>
//                       <Badge
//                         variant="secondary"
//                         className={`${getScoreBadgeColor(score.percentage)} font-semibold`}
//                       >
//                         {score.correct}/{score.total} ({score.percentage}%)
//                       </Badge>
//                     </div>
//                   </CardHeader>
//                   <CardContent className="h-full overflow-hidden">
//                     <ScrollArea className="h-full pr-4">
//                       <div className="space-y-4">
//                         {assessment.multipleChoiceQuiz.map(
//                           (question, index) => {
//                             const writerAnswer =
//                               writerAnswer.multipleChoiceAnswers[index];
//                             const isCorrect =
//                               writerAnswer === question.correctAnswer;

//                             return (
//                               <div
//                                 key={index}
//                                 className="border rounded-lg p-4"
//                               >
//                                 <div className="flex items-start gap-3 mb-3">
//                                   <div className="flex-shrink-0 mt-1">
//                                     {isCorrect ? (
//                                       <CheckCircle className="h-5 w-5 text-green-600" />
//                                     ) : (
//                                       <XCircle className="h-5 w-5 text-red-600" />
//                                     )}
//                                   </div>
//                                   <div className="flex-1">
//                                     <p className="font-medium mb-2">
//                                       Question {index + 1}
//                                     </p>
//                                     <p className="text-sm text-muted-foreground mb-3">
//                                       {question.question}
//                                     </p>
//                                   </div>
//                                 </div>

//                                 <div className="ml-8 space-y-2">
//                                   <div className="flex items-center gap-2">
//                                     <span className="text-sm font-medium">
//                                       Writer's Answer:
//                                     </span>
//                                     <Badge
//                                       variant={
//                                         isCorrect ? "default" : "destructive"
//                                       }
//                                     >
//                                       {writerAnswer || "No answer provided"}
//                                     </Badge>
//                                   </div>
//                                   {!isCorrect && (
//                                     <div className="flex items-center gap-2">
//                                       <span className="text-sm font-medium">
//                                         Correct Answer:
//                                       </span>
//                                       <Badge
//                                         variant="secondary"
//                                         className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
//                                       >
//                                         {question.correctAnswer}
//                                       </Badge>
//                                     </div>
//                                   )}
//                                 </div>
//                               </div>
//                             );
//                           }
//                         )}
//                       </div>
//                     </ScrollArea>
//                   </CardContent>
//                 </Card>
//               </TabsContent>

//               <TabsContent value="essay" className="flex-1 overflow-hidden">
//                 <Card className="h-full">
//                   <CardHeader className="pb-3">
//                     <div className="flex items-center justify-between">
//                       <CardTitle className="text-lg">
//                         Essay Submission
//                       </CardTitle>
//                       <Button
//                         variant="outline"
//                         size="sm"
//                         onClick={copyEssayText}
//                         className="flex items-center gap-2"
//                       >
//                         <Copy className="h-4 w-4" />
//                         Copy Text
//                       </Button>
//                     </div>
//                   </CardHeader>
//                   <CardContent className="h-full overflow-hidden">
//                     <div className="space-y-4 h-full">
//                       <div>
//                         <h4 className="font-medium mb-2">Topic:</h4>
//                         <p className="text-sm text-muted-foreground p-3 bg-muted rounded-lg">
//                           {assessment.essayExam.topic}
//                         </p>
//                       </div>

//                       <div>
//                         <h4 className="font-medium mb-2">Rubrics:</h4>
//                         <p className="text-sm text-muted-foreground p-3 bg-muted rounded-lg">
//                           {assessment.essayExam.rubrics}
//                         </p>
//                       </div>

//                       <Separator />

//                       <div className="flex-1 overflow-hidden">
//                         <h4 className="font-medium mb-2">Writer's Essay:</h4>
//                         <ScrollArea className="h-64 border rounded-lg p-4">
//                           <div className="text-sm leading-relaxed whitespace-pre-wrap">
//                             {writerAnswer.essayText.length > 500
//                               ? `${writerAnswer.essayText.substring(0, 500)}...`
//                               : writerAnswer.essayText}
//                           </div>
//                         </ScrollArea>
//                         {writerAnswer.essayText.length > 500 && (
//                           <p className="text-xs text-muted-foreground mt-2">
//                             Text truncated. Use the copy button above to get the
//                             full text.
//                           </p>
//                         )}
//                       </div>
//                     </div>
//                   </CardContent>
//                 </Card>
//               </TabsContent>
//             </Tabs>
//           </div>

//           {/* Action Buttons */}
//           <div className="flex items-center justify-end gap-3 pt-4 border-t">
//             <Button variant="outline" onClick={() => onOpenChange(false)}>
//               Close
//             </Button>
//             <Button
//               variant="destructive"
//               onClick={() => handleStatusUpdate("Failed")}
//               className="flex items-center gap-2"
//             >
//               <XCircle className="h-4 w-4" />
//               Fail
//             </Button>
//             <Button
//               onClick={() => handleStatusUpdate("Passed")}
//               className="flex items-center gap-2"
//             >
//               <CheckCircle className="h-4 w-4" />
//               Pass
//             </Button>
//           </div>
//         </div>
//       </DialogContent>
//     </Dialog>
//   );
// }
