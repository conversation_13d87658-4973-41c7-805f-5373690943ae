// src/lib/auth-utils.ts
import { getServerSession } from "next-auth";
import { authConfig } from "@/auth";
import type { UserRole } from "@prisma/client";
import type { Session } from "next-auth";

// Server-side functions
export async function getSession(): Promise<Session | null> {
  const session = await getServerSession(authConfig);

  return session;
}

export async function isAuthenticated(): Promise<boolean> {
  const session = await getSession();
  const isAuth = !!session && "user" in session;

  return isAuth;
}

export async function hasRole(role: UserRole): Promise<boolean> {
  const session = await getSession();
  const hasRole =
    !!session &&
    "user" in session &&
    "role" in session.user &&
    session.user.role === role;

  return hasRole;
}

// Helper to redirect based on role (for server components)
export async function getRoleDashboardPath(role?: UserRole): Promise<string> {
  if (!role) {
    return "/login/client";
  }

  switch (role) {
    case "ADMIN":
      return "/admin/dashboard";
    case "WRITER": {
      const session = await getSession();

      if (!session?.user?.id) {
        return "/login/writer";
      }

      try {
        const response = await fetch(
          `${process.env.NEXTAUTH_URL}/api/users/writers/${session.user.id}`
        );
        const data = await response.json();
        const isApproved = data.success && data.data?.isApproved;

        const path = isApproved ? "/writer/dashboard" : "/writer-assessment";

        return path;
      } catch (error) {
        console.error(
          "❌ [getRoleDashboardPath] Error checking writer status:",
          error
        );
        return "/writer-assessment";
      }
    }
    case "CLIENT":
      return "/client/dashboard";
    default:
      return "/client/dashboard";
  }
}

// Helper to get writer's current accessible path
export async function getWriterPath(): Promise<string> {
  const session = await getSession();

  if (!session?.user?.id || session.user.role !== "WRITER") {
    return "/login/writer";
  }

  try {
    const response = await fetch(
      `${process.env.NEXTAUTH_URL}/api/users/writers/${session.user.id}`
    );
    const data = await response.json();
    const isApproved = data.success && data.data?.isApproved;
    const path = isApproved ? "/writer/dashboard" : "/writer-assessment";

    return path;
  } catch (error) {
    console.error("❌ [getWriterPath] Error checking writer status:", error);
    return "/writer-assessment";
  }
}

// Client-side functions
export async function getWriterApprovalStatus(
  userId: string
): Promise<boolean> {
  try {
    const response = await fetch(`/api/users/writers/${userId}`);
    const data = await response.json();

    return data.success && data.data?.isApproved;
  } catch (error) {
    console.error("❌ [getWriterApprovalStatus] Error:", error);
    return false;
  }
}

// Client-side path determination
export async function getClientSideWriterPath(
  userId: string,
  role: string
): Promise<string> {
  if (role !== "WRITER") {
    return "/login/writer";
  }

  const isApproved = await getWriterApprovalStatus(userId);
  const path = isApproved ? "/writer/dashboard" : "/writer-assessment";

  return path;
}
