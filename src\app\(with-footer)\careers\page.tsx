import { Suspense } from "react";
import { <PERSON>ada<PERSON> } from "next";
import { Skeleton } from "@/components/ui/skeleton";
import { CareersPage } from "@/components/careers/careers-page";
import { generateDynamicMetadata } from "@/lib/metadata-utils";

export async function generateMetadata(): Promise<Metadata> {
  return await generateDynamicMetadata({
    title: "Join Our Team - Academic Writing Careers",
    description: "Join our team of expert academic writers and earn competitive rates while helping students succeed. Flexible work, excellent pay, and professional growth opportunities.",
    keywords: [
      "academic writing jobs",
      "freelance writing careers",
      "writer opportunities",
      "work from home",
      "academic writer jobs",
      "essay writing careers",
      "research writing jobs",
      "flexible work",
      "competitive rates",
      "writer recruitment"
    ],
    path: "/careers",
    ogImage: "/images/careers-og.jpg",
  });
}

function CareersSkeleton() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto space-y-8">
          {/* Hero Section Skeleton */}
          <div className="text-center space-y-4">
            <Skeleton className="h-16 w-96 mx-auto" />
            <Skeleton className="h-6 w-[600px] mx-auto" />
            <Skeleton className="h-12 w-48 mx-auto" />
          </div>

          {/* Benefits Grid Skeleton */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="space-y-4">
                <Skeleton className="h-48 w-full rounded-lg" />
              </div>
            ))}
          </div>

          {/* Earnings Section Skeleton */}
          <div className="space-y-6">
            <Skeleton className="h-8 w-48 mx-auto" />
            <div className="grid md:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <Skeleton key={i} className="h-32 w-full rounded-lg" />
              ))}
            </div>
          </div>

          {/* Application Process Skeleton */}
          <div className="space-y-6">
            <Skeleton className="h-8 w-48 mx-auto" />
            <div className="grid md:grid-cols-4 gap-4">
              {[1, 2, 3, 4].map((i) => (
                <Skeleton key={i} className="h-24 w-full rounded-lg" />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function Careers() {
  return (
    <Suspense fallback={<CareersSkeleton />}>
      <CareersPage />
    </Suspense>
  );
}
