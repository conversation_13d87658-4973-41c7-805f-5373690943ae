import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authConfig as authOptions } from "../../../../../auth";
import prisma from "@/lib/prisma";

// Types for Assessment
interface MultipleChoiceQuestion {
  question: string;
  options: string[];
  correctAnswer: string;
}

interface EssayExam {
  topic: string;
  rubrics: string;
}

export interface AssessmentPayload {
  title: string;
  multipleChoiceQuiz: MultipleChoiceQuestion[];
  essayExam: EssayExam;
}

// Import Prisma's InputJsonValue type
import { Prisma } from "@prisma/client";

// Type for writer answer to match Prisma's expected input
interface WriterAnswer {
  writerId: string;
  multipleChoiceAnswers?: unknown;
  essayText?: string;
  [key: string]: unknown;
}

// Helper for admin check
// eslint-disable-next-line @typescript-eslint/no-unused-vars
async function isAdmin(req: NextRequest): Promise<boolean> {
  const session = await getServerSession(authOptions);
  return session?.user?.role === "ADMIN";
}

// Type guard for WriterAnswer
function isWriterAnswer(value: unknown): value is WriterAnswer {
  return (
    typeof value === "object" &&
    value !== null &&
    "writerId" in value &&
    typeof (value as WriterAnswer).writerId === "string"
  );
}

// PUT: Update assessment by id
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  if (!(await isAdmin(req))) {
    // Check if it's a writer submitting answers
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || session.user.role !== "WRITER") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
  }

  const { id } = await params;
  const body = await req.json();

  try {
    // Get the current assessment to preserve existing answers
    const currentAssessment = await prisma.assessment.findUnique({
      where: { id },
    });

    if (!currentAssessment) {
      return NextResponse.json(
        { error: "Assessment not found" },
        { status: 404 }
      );
    }

    // If updating writer answers, merge with existing answers
    let updatedWritersAnswers: Prisma.InputJsonValue[] = [];

    if (currentAssessment.writersAnswers) {
      updatedWritersAnswers =
        currentAssessment.writersAnswers as Prisma.InputJsonValue[];
    }

    if (body.writersAnswers) {
      const newAnswer = body.writersAnswers[0] as WriterAnswer;
      // Remove any existing answer from this writer
      updatedWritersAnswers = updatedWritersAnswers.filter(
        (answer: Prisma.InputJsonValue) => {
          if (!isWriterAnswer(answer)) return true;
          return answer.writerId !== newAnswer.writerId;
        }
      );
      // Add the new answer
      updatedWritersAnswers.push(newAnswer as Prisma.InputJsonValue);
    }

    // If setting this assessment to active, deactivate all others first
    if (body.isActive && body.isActive !== currentAssessment.isActive) {
      await prisma.assessment.updateMany({
        where: {
          id: { not: id },
          isActive: true
        },
        data: { isActive: false },
      });
    }

    const updated = await prisma.assessment.update({
      where: { id },
      data: {
        title: body.title || currentAssessment.title,
        multipleChoiceQuiz:
          body.multipleChoiceQuiz || currentAssessment.multipleChoiceQuiz,
        essayExam: body.essayExam || currentAssessment.essayExam,
        isActive: body.isActive !== undefined ? body.isActive : currentAssessment.isActive,
        writersAnswers: updatedWritersAnswers,
      },
    });
    return NextResponse.json(updated);
  } catch (error) {
    return NextResponse.json(
      { error: "Failed to update assessment", details: String(error) },
      { status: 400 }
    );
  }
}

// DELETE: Delete assessment by id
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  if (!(await isAdmin(req))) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  const { id } = await params;
  try {
    await prisma.assessment.delete({ where: { id } });
    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json(
      { error: "Failed to delete assessment", details: String(error) },
      { status: 400 }
    );
  }
}
