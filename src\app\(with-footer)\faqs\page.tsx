import { Suspense } from "react";
import { Metada<PERSON> } from "next";
import { FAQPageContent } from "@/components/faqs/faq-page-content";
import { FAQSkeleton } from "@/components/faqs/faq-skeleton";
import { generateFAQMetadata } from "@/lib/faq-metadata";

export async function generateMetadata(): Promise<Metadata> {
  return await generateFAQMetadata();
}

export default function FAQsPage() {
  return (
    <div className="min-h-screen bg-background">
      <Suspense fallback={<FAQSkeleton />}>
        <FAQPageContent />
      </Suspense>
    </div>
  );
}
