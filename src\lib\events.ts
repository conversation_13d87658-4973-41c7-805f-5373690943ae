type EventMap = {
  assignmentCreated: void;
  assignmentDeleted: void;
  assignmentUpdated: void;
};

class EventEmitter {
  private listeners: {
    [K in keyof EventMap]?: Array<(payload: EventMap[K]) => void>;
  } = {};

  on<K extends keyof EventMap>(
    event: K,
    callback: (payload: EventMap[K]) => void
  ) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event]?.push(callback);
  }

  off<K extends keyof EventMap>(
    event: K,
    callback: (payload: EventMap[K]) => void
  ) {
    if (!this.listeners[event]) return;
    this.listeners[event] = this.listeners[event]?.filter(
      (cb) => cb !== callback
    );
  }

  emit<K extends keyof EventMap>(event: K, payload?: EventMap[K]) {
    this.listeners[event]?.forEach((callback) => callback(payload!));
  }
}

export const eventEmitter = new EventEmitter();
