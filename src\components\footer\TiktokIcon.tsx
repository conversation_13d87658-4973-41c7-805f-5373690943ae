// components/footer/TiktokIcon.tsx
export function TiktokIcon({ size = 20 }: { size?: number }) {
    return (
      <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M16.6 5.82C16.6 5.24 16.28 4.44 15.42 3.87C14.74 3.42 14.47 3.42 14.05 3.45C13.39 3.49 12.73 3.67 12.18 3.98L12.18 8.5C12.8 8.23 13.49 8.11 14.18 8.14C15.59 8.2 16.63 8.53 16.63 8.53L16.6 5.82ZM12.18 17.47C12.53 17.76 12.94 17.97 13.37 18.08C13.81 18.17 14.26 18.17 14.7 18.07C15.13 17.97 15.54 17.78 15.9 17.51C16.25 17.24 16.55 16.9 16.77 16.5C16.83 16.4 16.87 16.3 16.91 16.19C17.09 15.64 17.11 15.06 16.97 14.51C16.82 13.95 16.53 13.45 16.11 13.04C15.69 12.63 15.16 12.33 14.58 12.17C14.01 12.01 13.41 12 12.83 12.14L12.83 8.82L12.18 8.82L12.18 17.47Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M8.94 15.73C8.95 16.5 9.43 17.22 10.17 17.5C10.66 17.69 11.21 17.69 11.71 17.5C12.59 17.16 13.15 16.12 12.97 15.08" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M8.94 14.04C8.94 13.28 9.41 12.55 10.17 12.27C10.92 12 11.78 12.21 12.3 12.85" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      </svg>
    );
  }