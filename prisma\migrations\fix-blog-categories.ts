import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function main() {
  // Get all blog categories
  const categories = await prisma.blogCategory.findMany();

  // Update each category with default timestamps
  const now = new Date();

  for (const category of categories) {
    await prisma.blogCategory.update({
      where: { id: category.id },
      data: {
        createdAt: now,
        updatedAt: now,
      },
    });
  }
}

main()
  .catch((e) => {
    console.error("Error updating categories:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
