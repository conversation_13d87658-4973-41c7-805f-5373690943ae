// src/components/dashboard/admin/data-table.tsx

"use client";

import * as React from "react";
import { useState } from "react";
import {
  IconChevronDown,
  IconChevronLeft,
  IconChevronRight,
  IconChevronsLeft,
  IconChevronsRight,
  IconDotsVertical,
  IconLayoutColumns,
} from "@tabler/icons-react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { z } from "zod";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Schema for the original table
export const schema = z.object({
  id: z.number(),
  header: z.string(),
  type: z.string(),
  status: z.string(),
  target: z.string(),
  limit: z.string(),
  reviewer: z.string(),
});

// User schema for user data tables
export const userSchema = z.object({
  id: z.string(),
  name: z.string().nullable(),
  email: z.string(),
  role: z.string(),
  isApproved: z.boolean(),
  createdAt: z.string(),
  image: z.string().nullable(),
  phone: z.string().nullable(),
});

// Client schema extends user schema with assignment count
export const clientSchema = userSchema.extend({
  assignmentCount: z.number().optional(),
});

// Writer schema for detailed writer information
export const writerSchema = z.object({
  id: z.string(),
  accountId: z.string(),
  name: z.string().nullable(),
  email: z.string(),
  phone: z.string().nullable(),
  role: z.string(),
  isApproved: z.boolean(),
  emailVerified: z.boolean().nullable(),
  professionalSummary: z.string().nullable(),
  experience: z.string().nullable(),
  competencies: z.string().nullable(),
  educationLevel: z.string().nullable(),
  createdAt: z.string(),
  updatedAt: z.string(),
  image: z.string().nullable(),
  bidCount: z.number(),
  assignmentCount: z.number(),
});



// User columns for Total Users tab
const userColumns: ColumnDef<z.infer<typeof userSchema>>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <div className="flex items-center justify-center">
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      </div>
    ),
    cell: ({ row }) => (
      <div className="flex items-center justify-center">
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: "User",
    cell: ({ row }) => {
      const user = row.original;
      return (
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            <AvatarImage src={user.image || undefined} alt={user.name || user.email} />
            <AvatarFallback>
              {user.name ? user.name.charAt(0).toUpperCase() : user.email.charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium">{user.name || "No name"}</div>
            <div className="text-sm text-muted-foreground">{user.email}</div>
          </div>
        </div>
      );
    },
    enableHiding: false,
  },
  {
    accessorKey: "role",
    header: "Role",
    cell: ({ row }) => {
      const role = row.original.role;
      const roleColors = {
        ADMIN: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
        CLIENT: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
        WRITER: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
      };
      return (
        <Badge
          variant="outline"
          className={`${roleColors[role as keyof typeof roleColors] || "bg-gray-100 text-gray-800"} px-2 py-1`}
        >
          {role}
        </Badge>
      );
    },
  },
  {
    accessorKey: "isApproved",
    header: "Status",
    cell: ({ row }) => {
      const isApproved = row.original.isApproved;
      return (
        <Badge variant={isApproved ? "default" : "destructive"}>
          {isApproved ? "Approved" : "Pending"}
        </Badge>
      );
    },
  },
  {
    accessorKey: "phone",
    header: "Phone",
    cell: ({ row }) => {
      const phone = row.original.phone;
      return (
        <div className="text-sm">
          {phone || <span className="text-muted-foreground">No phone</span>}
        </div>
      );
    },
  },
  {
    accessorKey: "createdAt",
    header: "Joined",
    cell: ({ row }) => {
      const date = new Date(row.original.createdAt);
      return (
        <div className="text-sm">
          {date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
          })}
        </div>
      );
    },
  },
  {
    id: "actions",
    cell: () => (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="data-[state=open]:bg-muted text-muted-foreground flex size-8"
            size="icon"
          >
            <IconDotsVertical />
            <span className="sr-only">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-32">
          <DropdownMenuItem>View Details</DropdownMenuItem>
          <DropdownMenuItem>Edit User</DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem variant="destructive">Delete</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    ),
  },
];

// Client columns for Clients tab (extends user columns with assignment count)
const clientColumns: ColumnDef<z.infer<typeof clientSchema>>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <div className="flex items-center justify-center">
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      </div>
    ),
    cell: ({ row }) => (
      <div className="flex items-center justify-center">
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: "Client",
    cell: ({ row }) => {
      const user = row.original;
      return (
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            <AvatarImage src={user.image || undefined} alt={user.name || user.email} />
            <AvatarFallback>
              {user.name ? user.name.charAt(0).toUpperCase() : user.email.charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium">{user.name || "No name"}</div>
            <div className="text-sm text-muted-foreground">{user.email}</div>
          </div>
        </div>
      );
    },
    enableHiding: false,
  },
  {
    accessorKey: "assignmentCount",
    header: "Assignments",
    cell: ({ row }) => {
      const count = row.original.assignmentCount || 0;
      return (
        <div className="text-center">
          <Badge variant="outline" className="px-2 py-1">
            {count}
          </Badge>
        </div>
      );
    },
  },
  {
    accessorKey: "isApproved",
    header: "Status",
    cell: ({ row }) => {
      const isApproved = row.original.isApproved;
      return (
        <Badge variant={isApproved ? "default" : "destructive"}>
          {isApproved ? "Approved" : "Pending"}
        </Badge>
      );
    },
  },
  {
    accessorKey: "phone",
    header: "Phone",
    cell: ({ row }) => {
      const phone = row.original.phone;
      return (
        <div className="text-sm">
          {phone || <span className="text-muted-foreground">No phone</span>}
        </div>
      );
    },
  },
  {
    accessorKey: "createdAt",
    header: "Joined",
    cell: ({ row }) => {
      const date = new Date(row.original.createdAt);
      return (
        <div className="text-sm">
          {date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
          })}
        </div>
      );
    },
  },
  {
    id: "actions",
    cell: () => (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="data-[state=open]:bg-muted text-muted-foreground flex size-8"
            size="icon"
          >
            <IconDotsVertical />
            <span className="sr-only">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-32">
          <DropdownMenuItem>View Details</DropdownMenuItem>
          <DropdownMenuItem>View Assignments</DropdownMenuItem>
          <DropdownMenuItem>Edit Client</DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem variant="destructive">Delete</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    ),
  },
];

interface User {
  id: string;
  name: string | null;
  email: string;
  role: string;
  isApproved: boolean;
  createdAt: string;
  image: string | null;
  phone: string | null;
}

interface Client extends User {
  assignmentCount?: number;
}

interface UsersApiResponse {
  success: boolean;
  message: string;
  data: {
    users: User[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      pages: number;
    };
  };
}

interface ClientsApiResponse {
  success: boolean;
  message: string;
  data: {
    clients: Client[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      pages: number;
    };
  };
}

interface Writer {
  id: string;
  accountId: string;
  name: string | null;
  email: string;
  phone: string | null;
  role: string;
  isApproved: boolean;
  emailVerified: boolean | null;
  professionalSummary: string | null;
  experience: string | null;
  competencies: string | null;
  educationLevel: string | null;
  createdAt: string;
  updatedAt: string;
  image: string | null;
  bidCount: number;
  assignmentCount: number;
}

interface WritersApiResponse {
  success: boolean;
  message: string;
  data: {
    writers: Writer[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      pages: number;
    };
  };
}

// User Details Dialog Component
function UserDetailsDialog({ user, children }: { user: Writer; children: React.ReactNode }) {
  const [openDialog, setOpenDialog] = useState(false);

  console.log('UserDetailsDialog render:', {
    userId: user.id,
    userName: user.name,
    userEmail: user.email,
    openDialog
  });

  const handleOpenChange = (open: boolean) => {
    console.log('Dialog open change:', { open, userId: user.id });
    setOpenDialog(open);
  };

  return (
    <Dialog open={openDialog} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">        
        <DialogHeader>
          <DialogTitle className="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
            <Avatar className="h-16 w-16 mx-auto sm:mx-0">
              <AvatarImage
                src={user.image || "/avatars/shadcn.jpg"}
                alt={user.name || user.email}
              />
              <AvatarFallback>
                {user.name ? user.name.charAt(0).toUpperCase() : user.email.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="text-center sm:text-left">
              <h3 className="text-xl font-semibold">{user.name || "No name"}</h3>
              <p className="text-sm text-muted-foreground">{user.email}</p>
              <p className="text-xs text-muted-foreground font-mono">ID: {user.accountId}</p>
            </div>
          </DialogTitle>
          <DialogDescription>
            Comprehensive profile and performance information for this writer
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
              Basic Information
            </h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <Label className="text-xs text-muted-foreground">Phone</Label>
                <p className="text-sm">{user.phone || "Not provided"}</p>
              </div>
              <div>
                <Label className="text-xs text-muted-foreground">Role</Label>
                <Badge variant="outline" className="w-fit">
                  {user.role}
                </Badge>
              </div>
              <div>
                <Label className="text-xs text-muted-foreground">Status</Label>
                <Badge variant={user.isApproved ? "default" : "destructive"} className="w-fit">
                  {user.isApproved ? "Approved" : "Pending"}
                </Badge>
              </div>
              <div>
                <Label className="text-xs text-muted-foreground">Email Verified</Label>
                <Badge variant={user.emailVerified ? "default" : "secondary"} className="w-fit">
                  {user.emailVerified ? "Verified" : "Unverified"}
                </Badge>
              </div>
              <div>
                <Label className="text-xs text-muted-foreground">Education Level</Label>
                <p className="text-sm">{user.educationLevel || "Not specified"}</p>
              </div>
            </div>
          </div>

          {/* Professional Information */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
              Professional Information
            </h4>
            <div className="space-y-4">
              <div>
                <Label className="text-xs text-muted-foreground">Professional Summary</Label>
                <p className="text-sm mt-1 p-3 bg-muted/50 rounded-md">
                  {user.professionalSummary || "No professional summary provided"}
                </p>
              </div>
              <div>
                <Label className="text-xs text-muted-foreground">Experience</Label>
                <p className="text-sm mt-1 p-3 bg-muted/50 rounded-md">
                  {user.experience || "No experience information provided"}
                </p>
              </div>
              <div>
                <Label className="text-xs text-muted-foreground">Competencies</Label>
                <p className="text-sm mt-1 p-3 bg-muted/50 rounded-md">
                  {user.competencies || "No competencies listed"}
                </p>
              </div>
            </div>
          </div>

          {/* Statistics */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
              Statistics
            </h4>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <p className="text-3xl font-bold text-primary">{user.assignmentCount}</p>
                <p className="text-xs text-muted-foreground">Assignments</p>
              </div>
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <p className="text-3xl font-bold text-primary">{user.bidCount}</p>
                <p className="text-xs text-muted-foreground">Bids</p>
              </div>
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <p className="text-3xl font-bold text-primary">
                  {user.bidCount > 0 ? ((user.assignmentCount / user.bidCount) * 100).toFixed(1) : 0}%
                </p>
                <p className="text-xs text-muted-foreground">Success Rate</p>
              </div>
            </div>
          </div>

          {/* Timestamps */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
              Account Information
            </h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label className="text-xs text-muted-foreground">Joined</Label>
                <p className="text-sm">
                  {new Date(user.createdAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                  })}
                </p>
              </div>
              <div>
                <Label className="text-xs text-muted-foreground">Last Updated</Label>
                <p className="text-sm">
                  {new Date(user.updatedAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                  })}
                </p>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Total Users Tab Component
function TotalUsersTab() {
  const [users, setUsers] = React.useState<User[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [pagination, setPagination] = React.useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const [totalUsers, setTotalUsers] = React.useState(0);

  const fetchUsers = React.useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/users?page=${pagination.pageIndex + 1}&limit=${pagination.pageSize}`);
      const data: UsersApiResponse = await response.json();

      if (data.success && data.data?.users) {
        setUsers(data.data.users);
        setTotalUsers(data.data.pagination.total);
      } else {
        console.error('Failed to fetch users:', data);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  }, [pagination.pageIndex, pagination.pageSize]);

  React.useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  const [rowSelection, setRowSelection] = React.useState({});
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [sorting, setSorting] = React.useState<SortingState>([]);

  const table = useReactTable({
    data: users,
    columns: userColumns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      pagination,
    },
    pageCount: Math.ceil(totalUsers / pagination.pageSize),
    manualPagination: true,
    getRowId: (row) => row.id,
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">All Users</h3>
          <p className="text-sm text-muted-foreground">
            Manage all users in the system ({totalUsers} total)
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Input
            placeholder="Filter users..."
            value={(table.getColumn("name")?.getFilterValue() as string) ?? ""}
            onChange={(event) =>
              table.getColumn("name")?.setFilterValue(event.target.value)
            }
            className="max-w-sm"
          />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <IconLayoutColumns />
                Columns
                <IconChevronDown />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) => column.toggleVisibility(!!value)}
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={userColumns.length} className="h-24 text-center">
                  No users found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-between space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="flex items-center space-x-6 lg:space-x-8">
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium">Rows per page</p>
            <Select
              value={`${table.getState().pagination.pageSize}`}
              onValueChange={(value) => {
                table.setPageSize(Number(value));
              }}
            >
              <SelectTrigger className="h-8 w-[70px]">
                <SelectValue placeholder={table.getState().pagination.pageSize} />
              </SelectTrigger>
              <SelectContent side="top">
                {[10, 20, 30, 40, 50].map((pageSize) => (
                  <SelectItem key={pageSize} value={`${pageSize}`}>
                    {pageSize}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex w-[100px] items-center justify-center text-sm font-medium">
            Page {table.getState().pagination.pageIndex + 1} of{" "}
            {table.getPageCount()}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              <span className="sr-only">Go to first page</span>
              <IconChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <span className="sr-only">Go to previous page</span>
              <IconChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              <span className="sr-only">Go to next page</span>
              <IconChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              <span className="sr-only">Go to last page</span>
              <IconChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Clients Tab Component
function ClientsTab() {
  const [clients, setClients] = React.useState<Client[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [pagination, setPagination] = React.useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const [totalClients, setTotalClients] = React.useState(0);

  const fetchClients = React.useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/users/clients?page=${pagination.pageIndex + 1}&limit=${pagination.pageSize}`);
      const data: ClientsApiResponse = await response.json();

      if (data.success && data.data?.clients) {
        setClients(data.data.clients);
        setTotalClients(data.data.pagination.total);
      } else {
        console.error('Failed to fetch clients:', data);
      }
    } catch (error) {
      console.error('Error fetching clients:', error);
    } finally {
      setLoading(false);
    }
  }, [pagination.pageIndex, pagination.pageSize]);

  React.useEffect(() => {
    fetchClients();
  }, [fetchClients]);

  const [rowSelection, setRowSelection] = React.useState({});
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [sorting, setSorting] = React.useState<SortingState>([]);

  const table = useReactTable({
    data: clients,
    columns: clientColumns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      pagination,
    },
    pageCount: Math.ceil(totalClients / pagination.pageSize),
    manualPagination: true,
    getRowId: (row) => row.id,
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Clients</h3>
          <p className="text-sm text-muted-foreground">
            Manage client accounts and their assignments ({totalClients} total)
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Input
            placeholder="Filter clients..."
            value={(table.getColumn("name")?.getFilterValue() as string) ?? ""}
            onChange={(event) =>
              table.getColumn("name")?.setFilterValue(event.target.value)
            }
            className="max-w-sm"
          />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <IconLayoutColumns />
                Columns
                <IconChevronDown />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) => column.toggleVisibility(!!value)}
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={clientColumns.length} className="h-24 text-center">
                  No clients found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-between space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="flex items-center space-x-6 lg:space-x-8">
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium">Rows per page</p>
            <Select
              value={`${table.getState().pagination.pageSize}`}
              onValueChange={(value) => {
                table.setPageSize(Number(value));
              }}
            >
              <SelectTrigger className="h-8 w-[70px]">
                <SelectValue placeholder={table.getState().pagination.pageSize} />
              </SelectTrigger>
              <SelectContent side="top">
                {[10, 20, 30, 40, 50].map((pageSize) => (
                  <SelectItem key={pageSize} value={`${pageSize}`}>
                    {pageSize}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex w-[100px] items-center justify-center text-sm font-medium">
            Page {table.getState().pagination.pageIndex + 1} of{" "}
            {table.getPageCount()}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              <span className="sr-only">Go to first page</span>
              <IconChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <span className="sr-only">Go to previous page</span>
              <IconChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              <span className="sr-only">Go to next page</span>
              <IconChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              <span className="sr-only">Go to last page</span>
              <IconChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

function KeyPersonnelTab() {
  const [writers, setWriters] = React.useState<Writer[]>([]);
  const [loading, setLoading] = React.useState(true);

  console.log('KeyPersonnelTab render:', { writersCount: writers.length, loading });
  const [pagination, setPagination] = React.useState({
    pageIndex: 0,
    pageSize: 20,
  });
  const [totalWriters, setTotalWriters] = React.useState(0);

  const fetchWriters = React.useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/users/writers?page=${pagination.pageIndex + 1}&limit=${pagination.pageSize}&approvedOnly=true`);
      const data: WritersApiResponse = await response.json();

      if (data.success && data.data?.writers) {
        setWriters(data.data.writers);
        setTotalWriters(data.data.pagination.total);
      } else {
        console.error('Failed to fetch writers:', data);
      }
    } catch (error) {
      console.error('Error fetching writers:', error);
    } finally {
      setLoading(false);
    }
  }, [pagination.pageIndex, pagination.pageSize]);

  React.useEffect(() => {
    fetchWriters();
  }, [fetchWriters]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold mb-2">Key Personnel - Writers</h3>
          <p className="text-muted-foreground text-sm">
            Approved writers in the system ({totalWriters} total)
          </p>
        </div>

        <div className="grid gap-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {writers.map((writer) => (
            <div
              key={writer.id}
              className="border rounded-lg p-4 space-y-3 hover:shadow-md transition-shadow bg-card"
            >
              <div className="flex items-start space-x-3">
                <Avatar className="h-12 w-12 flex-shrink-0">
                  <AvatarImage
                    src={writer.image || "/avatars/shadcn.jpg"}
                    alt={writer.name || writer.email}
                  />
                  <AvatarFallback>
                    {writer.name ? writer.name.charAt(0).toUpperCase() : writer.email.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0 space-y-1">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div>
                        <UserDetailsDialog user={writer}>
                          <button
                            className="text-left hover:text-primary transition-colors cursor-pointer w-full"
                            onClick={(e) => {
                              console.log('Name button clicked:', {
                                writerId: writer.id,
                                writerName: writer.name,
                                event: e
                              });
                            }}
                          >
                            <h4 className="font-semibold text-sm truncate">{writer.name || "No name"}</h4>
                          </button>
                        </UserDetailsDialog>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Click to view more details</p>
                    </TooltipContent>
                  </Tooltip>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div>
                        <UserDetailsDialog user={writer}>
                          <button
                            className="text-xs text-muted-foreground hover:text-primary transition-colors cursor-pointer truncate block w-full text-left"
                            onClick={(e) => {
                              console.log('Email button clicked:', {
                                writerId: writer.id,
                                writerEmail: writer.email,
                                event: e
                              });
                            }}
                          >
                            {writer.email}
                          </button>
                        </UserDetailsDialog>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Click to view more details</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between text-xs">
                  <span className="text-muted-foreground">Account ID</span>
                  <span className="font-mono text-xs">{writer.accountId}</span>
                </div>

                <div className="grid grid-cols-2 gap-2">
                  <div className="text-center p-2 bg-muted/30 rounded-md">
                    <p className="text-lg font-bold text-primary">{writer.assignmentCount}</p>
                    <p className="text-xs text-muted-foreground">Assignments</p>
                  </div>
                  <div className="text-center p-2 bg-muted/30 rounded-md">
                    <p className="text-lg font-bold text-primary">{writer.bidCount}</p>
                    <p className="text-xs text-muted-foreground">Bids</p>
                  </div>
                </div>

                <div className="flex items-center justify-between text-xs">
                  <span className="text-muted-foreground">Status</span>
                  <Badge variant={writer.isApproved ? "default" : "destructive"} className="text-xs px-2 py-1">
                    {writer.isApproved ? "Approved" : "Pending"}
                  </Badge>
                </div>

                {writer.educationLevel && (
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-muted-foreground">Education</span>
                    <span className="text-xs truncate max-w-[100px]" title={writer.educationLevel}>
                      {writer.educationLevel}
                    </span>
                  </div>
                )}

                {writer.bidCount > 0 && (
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-muted-foreground">Success Rate</span>
                    <span className="text-xs font-medium">
                      {((writer.assignmentCount / writer.bidCount) * 100).toFixed(1)}%
                    </span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {writers.length === 0 && (
          <div className="text-center py-8">
            <p className="text-muted-foreground">No approved writers found</p>
          </div>
        )}

        {/* Pagination */}
        {totalWriters > pagination.pageSize && (
          <div className="flex flex-col sm:flex-row items-center justify-between space-y-2 sm:space-y-0 py-4">
            <div className="text-sm text-muted-foreground">
              Showing {pagination.pageIndex * pagination.pageSize + 1} to{" "}
              {Math.min((pagination.pageIndex + 1) * pagination.pageSize, totalWriters)} of{" "}
              {totalWriters} writers
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPagination(prev => ({ ...prev, pageIndex: Math.max(0, prev.pageIndex - 1) }))}
                disabled={pagination.pageIndex === 0}
              >
                <IconChevronLeft className="h-4 w-4" />
                <span className="hidden sm:inline">Previous</span>
              </Button>
              <span className="text-sm text-muted-foreground px-2">
                Page {pagination.pageIndex + 1} of {Math.ceil(totalWriters / pagination.pageSize)}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPagination(prev => ({ ...prev, pageIndex: prev.pageIndex + 1 }))}
                disabled={pagination.pageIndex >= Math.ceil(totalWriters / pagination.pageSize) - 1}
              >
                <span className="hidden sm:inline">Next</span>
                <IconChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </div>
    </TooltipProvider>
  );
}



export function DataTable() {
  const [totalUsers, setTotalUsers] = React.useState(0);
  const [totalClients, setTotalClients] = React.useState(0);
  const [totalWriters, setTotalWriters] = React.useState(0);

  // Fetch counts for badges
  React.useEffect(() => {
    const fetchCounts = async () => {
      try {
        // Fetch total users count
        const usersResponse = await fetch('/api/users?page=1&limit=1');
        const usersData = await usersResponse.json();
        if (usersData.success) {
          setTotalUsers(usersData.data.pagination.total);
        }

        // Fetch total clients count
        const clientsResponse = await fetch('/api/users/clients?page=1&limit=1');
        const clientsData = await clientsResponse.json();
        if (clientsData.success) {
          setTotalClients(clientsData.data.pagination.total);
        }

        // Fetch writers count
        const writersResponse = await fetch('/api/analytics/writers');
        const writersData = await writersResponse.json();
        if (writersData.totalWriters) {
          setTotalWriters(writersData.totalWriters);
        }
      } catch (error) {
        console.error('Error fetching counts:', error);
      }
    };

    fetchCounts();
  }, []);

  // No table needed in main component - each tab has its own table implementation

  return (
    <Tabs
      defaultValue="total-users"
      className="w-full flex-col justify-start gap-6"
    >
      <div className="flex items-center justify-between px-4 lg:px-6">
        <Label htmlFor="view-selector" className="sr-only">
          View
        </Label>
        <Select defaultValue="total-users">
          <SelectTrigger
            className="flex w-fit @4xl/main:hidden"
            size="sm"
            id="view-selector"
          >
            <SelectValue placeholder="Select a view" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="total-users">Total Users ({totalUsers})</SelectItem>
            <SelectItem value="clients">Clients ({totalClients})</SelectItem>
            <SelectItem value="key-personnel">Key Personnel ({totalWriters})</SelectItem>
          </SelectContent>
        </Select>
        <TabsList className="**:data-[slot=badge]:bg-muted-foreground/30 hidden **:data-[slot=badge]:size-5 **:data-[slot=badge]:rounded-full **:data-[slot=badge]:px-1 @4xl/main:flex">
          <TabsTrigger value="total-users">
            Total Users <Badge variant="secondary">{totalUsers}</Badge>
          </TabsTrigger>
          <TabsTrigger value="clients">
            Clients <Badge variant="secondary">{totalClients}</Badge>
          </TabsTrigger>
          <TabsTrigger value="key-personnel">
            Key Personnel <Badge variant="secondary">{totalWriters}</Badge>
          </TabsTrigger>
        </TabsList>
        
      </div>
      <TabsContent
        value="total-users"
        className="relative flex flex-col gap-4 overflow-auto px-4 lg:px-6"
      >
        <TotalUsersTab />
      </TabsContent>
      <TabsContent
        value="clients"
        className="flex flex-col px-4 lg:px-6"
      >
        <ClientsTab />
      </TabsContent>
      <TabsContent value="key-personnel" className="flex flex-col px-4 lg:px-6">
        <KeyPersonnelTab />
      </TabsContent>
    </Tabs>
  );
}




