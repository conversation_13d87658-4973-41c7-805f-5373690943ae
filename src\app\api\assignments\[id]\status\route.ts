// src/app/api/assignments/[id]/status/route.ts
import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import {
  apiSuccess,
  apiError,
  parseRequestBody,
  getCurrentUserId,
  getCurrentUserRole,
} from "@/lib/api-utils";
import { AssignmentStatus, UserRole } from "@prisma/client";
import { z } from "zod";
import { notificationService } from "@/lib/notification-service";

// Status update schema (could be moved to validations.ts)
const statusUpdateSchema = z.object({
  status: z.string(),
});

// Update assignment status endpoint
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const currentUserId = await getCurrentUserId();
    const userRole = await getCurrentUserRole();

    if (!currentUserId) {
      return apiError("Authentication required", 401);
    }

    const { id: assignmentId } = await params;

    // Check if assignment exists
    const assignment = await prisma.assignment.findUnique({
      where: { id: assignmentId },
    });

    if (!assignment) {
      return apiError("Assignment not found", 404);
    }

    // Parse and validate the request body
    const parsed = await parseRequestBody(req, statusUpdateSchema);
    if ("success" in parsed && parsed.success === false) {
      return apiError(parsed.message, 400, parsed.errors);
    }

    const { status } = parsed as { status: string };

    // Validate the status is a valid AssignmentStatus
    if (!Object.values(AssignmentStatus).includes(status as AssignmentStatus)) {
      return apiError("Invalid assignment status", 400);
    }

    // Determine if the user can update the status based on role and current status
    let canUpdateStatus = false;
    let statusTransitionAllowed = false;

    switch (userRole) {
      case UserRole.ADMIN:
        // Admins can update to any status
        canUpdateStatus = true;
        statusTransitionAllowed = true;
        break;

      case UserRole.CLIENT:
        // Clients can update status only for their own assignments
        if (assignment.clientId !== currentUserId) {
          return apiError(
            "You can only update status for your own assignments",
            403
          );
        }

        // Clients can only transition between certain statuses
        canUpdateStatus = true;

        // DRAFT -> PENDING (submit draft for review)
        // PENDING -> DRAFT (take back for more edits)
        // COMPLETED -> REVISION (request revisions)
        statusTransitionAllowed =
          (assignment.status === AssignmentStatus.DRAFT &&
            status === AssignmentStatus.PENDING) ||
          (assignment.status === AssignmentStatus.PENDING &&
            status === AssignmentStatus.DRAFT) ||
          (assignment.status === AssignmentStatus.COMPLETED &&
            status === AssignmentStatus.REVISION);
        break;

      case UserRole.WRITER:
        // Writers can only update status if they are assigned to the assignment
        if (assignment.assignedWriterId !== currentUserId) {
          return apiError(
            "You can only update status for assignments assigned to you",
            403
          );
        }

        // Writers can only transition between certain statuses
        canUpdateStatus = true;

        // ASSIGNED -> PENDING (start work)
        // PENDING -> COMPLETED (mark as complete)
        statusTransitionAllowed =
          (assignment.status === AssignmentStatus.ASSIGNED &&
            status === AssignmentStatus.PENDING) ||
          (assignment.status === AssignmentStatus.PENDING &&
            status === AssignmentStatus.COMPLETED);
        break;
    }

    if (!canUpdateStatus) {
      return apiError(
        "You don't have permission to update the assignment status",
        403
      );
    }

    if (!statusTransitionAllowed) {
      return apiError(
        `Invalid status transition from ${assignment.status} to ${status}`,
        400
      );
    }

    // Update the assignment status
    const updatedAssignment = await prisma.assignment.update({
      where: { id: assignmentId },
      data: { status: status as AssignmentStatus },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        assignedWriter: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Send notifications for status changes
    try {
      const oldStatus = assignment.status;
      const newStatus = status as AssignmentStatus;

      // Notify writer for REVISION or CANCELLED status
      if (
        (newStatus === AssignmentStatus.REVISION || newStatus === AssignmentStatus.CANCELLED) &&
        updatedAssignment.assignedWriter
      ) {
        await notificationService.sendStatusChangeNotification(
          updatedAssignment.assignedWriter.id,
          updatedAssignment.assignedWriter.email,
          updatedAssignment.assignedWriter.name || "Writer",
          updatedAssignment.id,
          updatedAssignment.title,
          updatedAssignment.taskId,
          oldStatus,
          newStatus,
          newStatus === AssignmentStatus.REVISION
            ? "Please review the feedback and make the requested revisions."
            : "This assignment has been cancelled."
        );
      }

      // Notify client for COMPLETED status
      if (newStatus === AssignmentStatus.COMPLETED && updatedAssignment.client) {
        await notificationService.sendJobCompletedNotification(
          updatedAssignment.client.id,
          updatedAssignment.client.email,
          updatedAssignment.client.name || "Client",
          updatedAssignment.id,
          updatedAssignment.title,
          updatedAssignment.taskId,
          updatedAssignment.assignedWriter?.name || undefined
        );
      }

      // Notify admins for COMPLETED status
      if (newStatus === AssignmentStatus.COMPLETED) {
        const admins = await notificationService.getAllAdmins();
        for (const admin of admins) {
          await notificationService.sendJobCompletedNotification(
            admin.id,
            admin.email,
            admin.name || "Admin",
            updatedAssignment.id,
            updatedAssignment.title,
            updatedAssignment.taskId,
            updatedAssignment.assignedWriter?.name || undefined
          );
        }
      }
    } catch (notificationError) {
      console.error("Error sending status change notifications:", notificationError);
      // Don't fail the request if notifications fail
    }

    return apiSuccess(
      {
        id: updatedAssignment.id,
        status: updatedAssignment.status,
        updatedAt: updatedAssignment.updatedAt.toISOString(),
      },
      "Assignment status updated successfully"
    );
  } catch (error) {
    console.error("Error updating assignment status:", error);
    return apiError("Failed to update assignment status", 500);
  }
}
