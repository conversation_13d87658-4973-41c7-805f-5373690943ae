import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { RevenueAnalytics } from "@/types/analytics";
import { PaymentStatus } from "@prisma/client";

export async function GET() {
  try {
    // Fetch all assignments with payment information
    const assignments = await prisma.assignment.findMany({
      select: {
        id: true,
        price: true,
        paymentStatus: true,
        createdAt: true,
        updatedAt: true,
        academicLevel: true,
        client: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Filter only PAID assignments for revenue calculation
    const paidAssignments = assignments.filter(
      (assignment) => assignment.paymentStatus === PaymentStatus.PAID
    );

    // Calculate total revenue from PAID assignments only
    const totalRevenue = paidAssignments.reduce(
      (sum, assignment) => sum + assignment.price,
      0
    );

    // Calculate monthly revenue (current month) - based on payment date (updatedAt)
    const currentDate = new Date();
    const startOfMonth = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      1
    );

    const monthlyPaidAssignments = paidAssignments.filter(
      (assignment) => new Date(assignment.updatedAt) >= startOfMonth
    );

    const monthlyRevenue = monthlyPaidAssignments.reduce(
      (sum, assignment) => sum + assignment.price,
      0
    );

    // Calculate revenue growth compared to previous month (based on payment dates)
    const startOfPreviousMonth = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth() - 1,
      1
    );
    const endOfPreviousMonth = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      0
    );

    const previousMonthPaidAssignments = paidAssignments.filter((assignment) => {
      const paymentDate = new Date(assignment.updatedAt);
      return paymentDate >= startOfPreviousMonth && paymentDate <= endOfPreviousMonth;
    });

    const previousMonthRevenue = previousMonthPaidAssignments.reduce(
      (sum, assignment) => sum + assignment.price,
      0
    );

    const revenueGrowth =
      previousMonthRevenue > 0
        ? ((monthlyRevenue - previousMonthRevenue) / previousMonthRevenue) * 100
        : monthlyRevenue > 0 ? 100 : 0;

    // Calculate average order value from PAID assignments only
    const averageOrderValue =
      paidAssignments.length > 0 ? totalRevenue / paidAssignments.length : 0;

    // Generate revenue by month data (last 12 months) - based on payment dates
    const revenueByMonth = [];
    for (let i = 11; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
      const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);

      const monthlyPaidAssignments = paidAssignments.filter((assignment) => {
        const paymentDate = new Date(assignment.updatedAt);
        return paymentDate >= monthStart && paymentDate < monthEnd;
      });

      const monthlyRevenue = monthlyPaidAssignments.reduce(
        (sum, assignment) => sum + assignment.price,
        0
      );

      revenueByMonth.push({
        date: `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
          2,
          "0"
        )}`,
        value: monthlyRevenue,
      });
    }

    // Generate revenue by category (academic level) - from PAID assignments only
    const revenueByCategory: Record<string, number> = {};
    paidAssignments.forEach((assignment) => {
      const category = assignment.academicLevel || "OTHER";
      revenueByCategory[category] = (revenueByCategory[category] || 0) + assignment.price;
    });

    const formattedRevenueByCategory = Object.entries(revenueByCategory).map(
      ([category, value]) => ({
        category,
        value,
      })
    );

    // Recent transactions - only PAID assignments
    const recentTransactions = paidAssignments
      .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
      .slice(0, 10)
      .map((assignment) => ({
        id: assignment.id,
        clientName: assignment.client?.name || "Anonymous",
        amount: assignment.price,
        date: assignment.updatedAt,
        status: "Paid",
      }));

    // Generate projected revenue for next 6 months
    // Simple projection based on average growth rate
    const projectedRevenue = [];
    let lastMonthRevenue = monthlyRevenue;
    const averageGrowthRate = revenueGrowth / 100; // Convert percentage to decimal

    for (let i = 1; i <= 6; i++) {
      const date = new Date();
      date.setMonth(date.getMonth() + i);
      
      // Project revenue with growth rate (minimum 0)
      const projectedMonthlyRevenue = Math.max(
        lastMonthRevenue * (1 + averageGrowthRate),
        0
      );
      
      projectedRevenue.push({
        date: `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}`,
        value: projectedMonthlyRevenue,
      });
      
      lastMonthRevenue = projectedMonthlyRevenue;
    }

    const analyticsData: RevenueAnalytics = {
      totalRevenue,
      monthlyRevenue,
      revenueGrowth,
      averageOrderValue,
      revenueByMonth,
      revenueByCategory: formattedRevenueByCategory,
      recentTransactions,
      projectedRevenue,
    };

    return NextResponse.json(analyticsData);
  } catch (error) {
    console.error("Error fetching revenue analytics:", error);
    return NextResponse.json(
      { error: "Failed to fetch revenue analytics" },
      { status: 500 }
    );
  }
}
