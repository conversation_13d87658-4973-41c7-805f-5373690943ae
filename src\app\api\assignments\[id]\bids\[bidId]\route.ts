// src/app/api/assignments/[id]/bids/[bidId]/route.ts

import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import {
  apiSuccess,
  apiError,
  parseRequestBody,
  getCurrentUserId,
  getCurrentUserRole,
} from "@/lib/api-utils";
import {
  AssignmentStatus,
  BidStatus,
  UserRole,
  JobStatus,
} from "@prisma/client";
import { z } from "zod";
import { notificationService } from "@/lib/notification-service";

// Bid update schema (could be moved to validations.ts)
const bidUpdateSchema = z.object({
  status: z.string(),
  message: z.string().optional(),
});

// - Update bid status (accept/reject) - Made params async
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string; bidId: string }> }
): Promise<NextResponse> {
  try {
    const currentUserId = await getCurrentUserId();
    const userRole = await getCurrentUserRole();

    if (!currentUserId) {
      return apiError("Authentication required", 401);
    }

    // - Await the params to resolve the Promise
    const { id: assignmentId, bidId } = await params;

    // Check if assignment and bid exist
    const assignment = await prisma.assignment.findUnique({
      where: { id: assignmentId },
    });

    if (!assignment) {
      return apiError("Assignment not found", 404);
    }

    const bid = await prisma.bid.findUnique({
      where: { id: bidId },
      include: {
        writer: true,
      },
    });

    if (!bid || bid.assignmentId !== assignmentId) {
      return apiError("Bid not found for this assignment", 404);
    }

    // Check permissions - only admins and the assignment's client can update bid status
    const canUpdateBid =
      userRole === UserRole.ADMIN ||
      (userRole === UserRole.CLIENT && assignment.clientId === currentUserId) ||
      (userRole === UserRole.WRITER && bid.writerId === currentUserId);

    if (!canUpdateBid) {
      return apiError("You don't have permission to update this bid", 403);
    }

    // Parse and validate the request body
    const parsed = await parseRequestBody(req, bidUpdateSchema);
    if ("success" in parsed && parsed.success === false) {
      return apiError(parsed.message, 400, parsed.errors);
    }

    const { status, message } = parsed as { status: string; message?: string };

    // Validate the status is a valid BidStatus
    if (!Object.values(BidStatus).includes(status as BidStatus)) {
      return apiError("Invalid bid status", 400);
    }

    // Writer can only update the message, not the status
    if (userRole === UserRole.WRITER) {
      if (status !== bid.status) {
        return apiError(
          "Writers can only update the bid message, not the status",
          403
        );
      }

      if (!message) {
        return apiError("Message is required when updating a bid", 400);
      }

      // Update just the message
      const updatedBid = await prisma.bid.update({
        where: { id: bidId },
        data: { message },
        include: {
          writer: {
            select: {
              id: true,
              name: true,
              email: true,
              accountId: true, // Add this
            },
          },
        },
      });

      return apiSuccess(
        {
          id: updatedBid.id,
          message: updatedBid.message,
          status: updatedBid.status,
          writerId: updatedBid.writerId,
          assignmentId: updatedBid.assignmentId,
          createdAt: updatedBid.createdAt.toISOString(),
          updatedAt: updatedBid.updatedAt.toISOString(),
          writer: updatedBid.writer,
        },
        "Bid message updated successfully"
      );
    }

    // Handle accepting a bid (for admin and client)
    if (status === BidStatus.ACCEPTED) {
      // Check if any bid has already been accepted for this assignment
      const existingAcceptedBid = await prisma.bid.findFirst({
        where: {
          assignmentId,
          status: BidStatus.ACCEPTED,
        },
      });

      if (existingAcceptedBid && existingAcceptedBid.id !== bidId) {
        return apiError(
          "Another bid has already been accepted for this assignment. Reject it first before accepting a new one.",
          409
        );
      }

      console.log(`🔄 Starting bid assignment process for bidId: ${bidId}, assignmentId: ${assignmentId}`);

      // Check for existing JobAssignment BEFORE starting transaction to avoid conflicts
      console.log(`🔍 Pre-checking for existing JobAssignment with bidId: ${bidId}`);
      const existingJobAssignment = await prisma.jobAssignment.findUnique({
        where: { bidId },
      });

      if (existingJobAssignment) {
        console.log(`⚠️  JobAssignment already exists for bidId: ${bidId}, skipping creation`);
        return apiError("This bid has already been assigned", 400);
      }

      // Start a transaction to ensure all related updates happen together
      const updatedBid = await prisma.$transaction(async (prisma) => {
        // 1. Update the bid status
        console.log(`📝 Updating bid status to ACCEPTED for bidId: ${bidId}`);
        const updatedBid = await prisma.bid.update({
          where: { id: bidId },
          data: { status: BidStatus.ACCEPTED },
          include: {
            writer: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        });

        // 2. Update the assignment status and assigned writer
        console.log(`📋 Updating assignment status to ASSIGNED for assignmentId: ${assignmentId}, writerId: ${bid.writerId}`);
        await prisma.assignment.update({
          where: { id: assignmentId },
          data: {
            status: AssignmentStatus.ASSIGNED,
            assignedWriterId: bid.writerId,
          },
        });

        // 3. Create a job assignment record (check if it doesn't already exist)
        const adminId =
          userRole === UserRole.ADMIN ? currentUserId : assignment.clientId;

        // Check if JobAssignment already exists for this bid
        console.log(`🔍 Checking for existing JobAssignment with bidId: ${bidId}`);
        const existingJobAssignment = await prisma.jobAssignment.findUnique({
          where: { bidId },
        });

        if (!existingJobAssignment) {
          // Set start date to now, deadline to the assignment's estimated time
          const startDate = new Date();
          const deadline = assignment.estTime;

          console.log(`✨ Creating new JobAssignment for bidId: ${bidId}, writerId: ${bid.writerId}, adminId: ${adminId}`);
          await prisma.jobAssignment.create({
            data: {
              assignmentId,
              writerId: bid.writerId,
              adminId,
              bidId,
              startDate,
              deadline,
              status: JobStatus.IN_PROGRESS,
            },
          });
          console.log(`✅ JobAssignment created successfully for bidId: ${bidId}`);
        } else {
          console.log(`⚠️  JobAssignment already exists for bidId: ${bidId}, skipping creation`);
        }

        // 4. Reject all other bids for this assignment
        await prisma.bid.updateMany({
          where: {
            assignmentId,
            id: { not: bidId },
            status: BidStatus.PENDING,
          },
          data: {
            status: BidStatus.REJECTED,
          },
        });

        return updatedBid;
      }, {
        timeout: 15000, // 15 seconds timeout instead of default 5 seconds
      });

      // Send notification to writer about assignment
      console.log(`📧 Sending assignment notification to writer: ${updatedBid.writer.email}`);
      try {
        await notificationService.sendWriterAssignedNotification(
          updatedBid.writer.id,
          updatedBid.writer.email,
          updatedBid.writer.name || "Writer",
          assignmentId,
          assignment.title,
          assignment.taskId,
          assignment.estTime.toISOString()
        );
        console.log(`✅ Assignment notification sent successfully to: ${updatedBid.writer.email}`);
      } catch (notificationError) {
        console.error("❌ Error sending writer assignment notification:", notificationError);
        // Don't fail the request if notification fails
      }

      console.log(`🎉 Bid assignment completed successfully! BidId: ${bidId}, WriterId: ${updatedBid.writerId}, AssignmentId: ${assignmentId}`);

      return apiSuccess(
        {
          id: updatedBid.id,
          message: updatedBid.message,
          status: updatedBid.status,
          writerId: updatedBid.writerId,
          assignmentId: updatedBid.assignmentId,
          createdAt: updatedBid.createdAt.toISOString(),
          updatedAt: updatedBid.updatedAt.toISOString(),
          writer: updatedBid.writer,
        },
        "Bid accepted and assignment assigned successfully"
      );
    } else if (status === BidStatus.REJECTED) {
      // Simple rejection without changing assignment status
      const updatedBid = await prisma.bid.update({
        where: { id: bidId },
        data: { status: BidStatus.REJECTED },
        include: {
          writer: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      return apiSuccess(
        {
          id: updatedBid.id,
          message: updatedBid.message,
          status: updatedBid.status,
          writerId: updatedBid.writerId,
          assignmentId: updatedBid.assignmentId,
          createdAt: updatedBid.createdAt.toISOString(),
          updatedAt: updatedBid.updatedAt.toISOString(),
          writer: updatedBid.writer,
        },
        "Bid rejected successfully"
      );
    }

    // Should never reach here, but just in case
    return apiError("Invalid operation", 400);
  } catch (error) {
    console.error("Error updating bid:", error);
    return apiError("Failed to update bid", 500);
  }
}

// - Delete a bid - Made params async
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string; bidId: string }> }
): Promise<NextResponse> {
  try {
    const currentUserId = await getCurrentUserId();
    const userRole = await getCurrentUserRole();

    if (!currentUserId) {
      return apiError("Authentication required", 401);
    }

    // - Await the params to resolve the Promise
    const { id: assignmentId, bidId } = await params;

    // Check if assignment and bid exist
    const assignment = await prisma.assignment.findUnique({
      where: { id: assignmentId },
    });

    if (!assignment) {
      return apiError("Assignment not found", 404);
    }

    const bid = await prisma.bid.findUnique({
      where: { id: bidId },
    });

    if (!bid || bid.assignmentId !== assignmentId) {
      return apiError("Bid not found for this assignment", 404);
    }

    // Check permissions - admins, the client of the assignment, or the writer who created the bid
    const canDeleteBid =
      userRole === UserRole.ADMIN ||
      (userRole === UserRole.CLIENT && assignment.clientId === currentUserId) ||
      (userRole === UserRole.WRITER && bid.writerId === currentUserId);

    if (!canDeleteBid) {
      return apiError("You don't have permission to delete this bid", 403);
    }

    // Check if the bid is part of an active job assignment
    const jobAssignment = await prisma.jobAssignment.findUnique({
      where: { bidId },
    });

    if (jobAssignment) {
      return apiError(
        "Cannot delete a bid that is part of an active job assignment. Please cancel the job assignment first.",
        400
      );
    }

    // Delete the bid
    await prisma.bid.delete({
      where: { id: bidId },
    });

    return apiSuccess({ deleted: true }, "Bid deleted successfully");
  } catch (error) {
    console.error("Error deleting bid:", error);
    return apiError("Failed to delete bid", 500);
  }
}
