// /* eslint-disable @typescript-eslint/no-explicit-any */

// "use client";
// import React, { useMemo } from "react";
// import dynamic from "next/dynamic";
// import { cn } from "@/lib/utils";
// import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
// import "quill/dist/quill.snow.css";

// // Dynamically import ReactQuill
// const ReactQuill = dynamic(() => import("react-quill-new"), {
//   ssr: false,
//   loading: () => (
//     <div className="quill-editor">
//       <div className="h-32 bg-gray-50 border border-gray-200 rounded animate-pulse flex items-center justify-center">
//         <span className="text-gray-400">Loading editor...</span>
//       </div>
//     </div>
//   ),
// });

// // Default color palette that <PERSON>uil<PERSON> uses
// const colors = [
//   '#000000', '#e60000', '#ff9900', '#ffff00', '#008a00', '#0066cc', '#9933ff',
//   '#ffffff', '#facccc', '#ffebcc', '#ffffcc', '#cce8cc', '#cce0f5', '#ebd6ff',
//   '#bbbbbb', '#f06666', '#ffc266', '#ffff66', '#66b966', '#66a3e0', '#c285ff',
//   '#888888', '#a10000', '#b26b00', '#b2b200', '#006100', '#0047b2', '#6b24b2',
//   '#444444', '#5c0000', '#663d00', '#666600', '#003700', '#002966', '#3d1466'
// ];

// const toolbarOptions = [
//   ["bold", "italic", "underline", "strike"],
//   ["blockquote", "code-block"],
//   ["link", "image", "video", "formula"],
//   [{ header: 1 }, { header: 2 }],
//   [{ list: "ordered" }, { list: "bullet" }, { list: "check" }],
//   [{ script: "sub" }, { script: "super" }],
//   [{ indent: "-1" }, { indent: "+1" }],
//   [{ direction: "rtl" }],
//   [{ size: ["small", false, "large", "huge"] }],
//   [{ header: [1, 2, 3, 4, 5, 6, false] }],
//   [{ color: colors }, { background: colors }],
//   [{ font: [] }],
//   [{ align: [] }],
//   ["clean"],
// ];

// // Map toolbar button to tooltip label
// const TOOLTIP_LABELS: Record<string, string> = {
//   bold: "Bold",
//   italic: "Italic",
//   underline: "Underline",
//   strike: "Strikethrough",
//   blockquote: "Blockquote",
//   "code-block": "Code Block",
//   link: "Insert Link",
//   image: "Insert Image",
//   video: "Insert Video",
//   formula: "Insert Formula",
//   header: "Header",
//   list: "List",
//   ordered: "Numbered List",
//   bullet: "Bullet List",
//   check: "Checklist",
//   script: "Script",
//   sub: "Subscript",
//   super: "Superscript",
//   indent: "Indent",
//   direction: "Text Direction (RTL)",
//   size: "Font Size",
//   color: "Font Color",
//   background: "Background Color",
//   font: "Font Family",
//   align: "Text Align",
//   clean: "Remove Formatting",
//   "-1": "Decrease Indent",
//   "+1": "Increase Indent",
//   "rtl": "Right to Left"
// };

// // Items that should be rendered as select dropdowns
// const SELECT_ITEMS = new Set(['size', 'header', 'color', 'background', 'font', 'align']);

// // Helper function to render select options properly
// function renderSelectOptions(item: Record<string, unknown>, key: string) {
//   if (!Object.prototype.hasOwnProperty.call(item, key)) {
//     return null;
//   }

//   const value = item[key];
  
//   if (Array.isArray(value)) {
//     return value.map((val, idx) => {
//       if (val === false) {
//         return (
//           <option value="" key={idx}>
//             Normal
//           </option>
//         );
//       }
//       return (
//         <option value={val} key={idx}>
//           {key === 'header' && typeof val === 'number' ? `Heading ${val}` : 
//            key === 'size' && val === 'small' ? 'Small' :
//            key === 'size' && val === 'large' ? 'Large' :
//            key === 'size' && val === 'huge' ? 'Huge' :
//            String(val)}
//         </option>
//       );
//     });
//   }

//   return null;
// }

// // Custom Toolbar with shadcn tooltips
// function QuillCustomToolbar() {
//   return (
//     <div id="quill-toolbar">
//       <TooltipProvider>
//         {toolbarOptions.map((group, i) => (
//           <span key={i} className="ql-formats">
//             {group.map((item, j) => {
//               if (typeof item === "string") {
//                 // String items are always buttons
//                 const label = TOOLTIP_LABELS[item] || item;
//                 return (
//                   <Tooltip key={j}>
//                     <TooltipTrigger asChild>
//                       <button type="button" className={`ql-${item}`} aria-label={label} />
//                     </TooltipTrigger>
//                     <TooltipContent>{label}</TooltipContent>
//                   </Tooltip>
//                 );
//               } else {
//                 // Handle object items
//                 const key = Object.keys(item)[0];
//                 const value = (item as any)[key]; // Type assertion to fix TypeScript error
                
//                 // Check if this should be a select dropdown
//                 if (SELECT_ITEMS.has(key) && Array.isArray(value)) {
//                   const label = TOOLTIP_LABELS[key] || key;
//                   return (
//                     <Tooltip key={j}>
//                       <TooltipTrigger asChild>
//                         <select className={`ql-${key}`} aria-label={label}>
//                           <option value="">
//                             {key === 'header' ? 'Normal' :
//                              key === 'size' ? 'Normal' :
//                              key === 'font' ? 'Sans Serif' :
//                              key === 'align' ? 'Left' :
//                              'Default'}
//                           </option>
//                           {renderSelectOptions(item, key)}
//                         </select>
//                       </TooltipTrigger>
//                       <TooltipContent>{label}</TooltipContent>
//                     </Tooltip>
//                   );
//                 } else if (SELECT_ITEMS.has(key) && (Array.isArray(value) && value.length === 0)) {
//                   // Handle empty arrays for color, background, font, align
//                   const label = TOOLTIP_LABELS[key] || key;
//                   return (
//                     <Tooltip key={j}>
//                       <TooltipTrigger asChild>
//                         <select className={`ql-${key}`} aria-label={label}>
//                           {/* These will be populated by Quill with theme defaults */}
//                         </select>
//                       </TooltipTrigger>
//                       <TooltipContent>{label}</TooltipContent>
//                     </Tooltip>
//                   );
//                 } else {
//                   // Everything else should be buttons (list, script, indent, direction)
//                   const label = TOOLTIP_LABELS[key] || TOOLTIP_LABELS[String(value)] || `${key}: ${value}`;
//                   return (
//                     <Tooltip key={j}>
//                       <TooltipTrigger asChild>
//                         <button 
//                           type="button" 
//                           className={`ql-${key}`} 
//                           value={String(value)}
//                           aria-label={label}
//                         />
//                       </TooltipTrigger>
//                       <TooltipContent>{label}</TooltipContent>
//                     </Tooltip>
//                   );
//                 }
//               }
//             })}
//           </span>
//         ))}
//       </TooltipProvider>
//     </div>
//   );
// }

// export interface QuillEditorProps {
//   value: string;
//   onChange: (value: string) => void;
//   className?: string;
//   placeholder?: string;
//   readOnly?: boolean;
// }

// export default function QuillEditor({ value, onChange, className, placeholder, readOnly }: QuillEditorProps) {
//   const modules = useMemo(() => ({
//     toolbar: { 
//       container: "#quill-toolbar"
//     }
//   }), []);

//   return (
//     <div className={cn("quill-editor", className)}>
//       <QuillCustomToolbar />
//       <ReactQuill
//         value={value}
//         onChange={onChange}
//         modules={modules}
//         theme="snow"
//         placeholder={placeholder}
//         readOnly={readOnly}
//       />
//     </div>
//   );
// }