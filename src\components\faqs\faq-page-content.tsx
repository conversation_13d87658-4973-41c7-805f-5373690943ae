"use client";

import { useState, useEffect } from "react";
import { <PERSON>QHeader } from "./faq-header";
import { FAQFilters } from "./faq-filters";
import { FAQList } from "./faq-list";
import { FAQSearch } from "./faq-search";
import { FAQSkeleton } from "./faq-skeleton";
import { FAQStructuredData } from "./faq-structured-data";
import { toast } from "sonner";

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: string | null;
  order: number;
  createdAt: string;
  updatedAt: string;
}

interface FAQData {
  faqs: FAQ[];
  categories: string[];
  total: number;
}

export function FAQPageContent() {
  const [data, setData] = useState<FAQData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredFAQs, setFilteredFAQs] = useState<FAQ[]>([]);

  useEffect(() => {
    fetchFAQs();
  }, []);

  useEffect(() => {
    if (data) {
      filterFAQs();
    }
  }, [data, selectedCategory, searchQuery]);

  const fetchFAQs = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/faqs");
      
      if (!response.ok) {
        throw new Error("Failed to fetch FAQs");
      }

      const result = await response.json();
      
      if (result.success) {
        setData(result.data);
      } else {
        throw new Error(result.message || "Failed to fetch FAQs");
      }
    } catch (error) {
      console.error("Error fetching FAQs:", error);
      toast.error("Failed to load FAQs. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const filterFAQs = () => {
    if (!data) return;

    let filtered = data.faqs;

    // Filter by category
    if (selectedCategory !== "all") {
      filtered = filtered.filter(faq => faq.category === selectedCategory);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(faq => 
        faq.question.toLowerCase().includes(query) ||
        faq.answer.toLowerCase().includes(query)
      );
    }

    setFilteredFAQs(filtered);
  };

  if (loading) {
    return <FAQSkeleton />;
  }

  if (!data) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-4">
            Unable to Load FAQs
          </h1>
          <p className="text-muted-foreground mb-4">
            We&apos;re having trouble loading the FAQ content. Please try refreshing the page.
          </p>
          <button
            onClick={fetchFAQs}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <FAQStructuredData faqs={data.faqs} />
      <div className="container mx-auto px-4 py-8 space-y-8">
        <FAQHeader totalCount={data.total} />
      
      <div className="flex flex-col lg:flex-row gap-8">
        <aside className="lg:w-1/4 space-y-6">
          <FAQSearch 
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
          />
          <FAQFilters
            categories={data.categories}
            selectedCategory={selectedCategory}
            onCategoryChange={setSelectedCategory}
            totalCount={data.total}
            filteredCount={filteredFAQs.length}
          />
        </aside>
        
        <main className="lg:w-3/4">
          <FAQList 
            faqs={filteredFAQs}
            searchQuery={searchQuery}
          />
        </main>
      </div>
    </div>
    </>
  );
}
