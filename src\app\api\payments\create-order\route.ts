import { NextRequest, NextResponse } from "next/server";
import type { CreateOrderRequest, CreateOrderResponse, PayPalApiError } from "@/types/paypal";

export async function POST(req: NextRequest) {
  try {
    const { orderId, amount }: CreateOrderRequest = await req.json();
console.log("[PayPal Debug] Incoming request:", { orderId, amount });
// Prepare PayPal order payload
const body = JSON.stringify({
  intent: "CAPTURE",
  purchase_units: [
    {
      reference_id: orderId,
      amount: {
        currency_code: "USD",
        value: amount.toFixed(2),
      },
    },
  ],
});
    // Get access token
    const environment = process.env.PAYPAL_ENVIRONMENT;
    const isSandbox = environment !== "production";
    const PAYPAL_CLIENT_ID = isSandbox
      ? process.env.PAYPAL_CLIENT_ID_SANDBOX
      : process.env.PAYPAL_CLIENT_ID_LIVE;
    const PAYPAL_SECRET = isSandbox
      ? process.env.PAYPAL_SECRET_SANDBOX
      : process.env.PAYPAL_SECRET_LIVE;
    const PAYPAL_API_URL = isSandbox
      ? "https://api-m.sandbox.paypal.com"
      : "https://api-m.paypal.com";
const basicAuth = Buffer.from(`${PAYPAL_CLIENT_ID}:${PAYPAL_SECRET}`).toString("base64");
    const tokenRes = await fetch(`${PAYPAL_API_URL}/v1/oauth2/token`, {
  method: "POST",
  headers: {
    "Authorization": `Basic ${basicAuth}`,
    "Content-Type": "application/x-www-form-urlencoded",
  },
  body: "grant_type=client_credentials",
});
const tokenData = await tokenRes.json();
console.log("[PayPal Debug] Token response:", tokenData);
if (!tokenRes.ok || !tokenData.access_token) {
  console.error("[PayPal Debug] PayPal token error", tokenData);
  return NextResponse.json<PayPalApiError>({ message: "Failed to get PayPal access token" }, { status: 500 });
}
    // Create order
    const orderRes = await fetch(`${PAYPAL_API_URL}/v2/checkout/orders`, {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
    "Authorization": `Bearer ${tokenData.access_token}`,
  },
  body,
});
const orderData = await orderRes.json();
console.log("[PayPal Debug] Create order response:", orderData);
if (!orderRes.ok || !orderData.id) {
  console.error("[PayPal Debug] PayPal order error", orderData);
  return NextResponse.json<PayPalApiError>({ message: orderData.message || "Failed to create PayPal order" }, { status: 500 });
}
console.log("[PayPal Debug] Successfully created order with ID:", orderData.id);
return NextResponse.json<CreateOrderResponse>({ id: orderData.id });
  } catch (error) {
    console.error("[PayPal Debug] Caught exception:", error);
    return NextResponse.json<PayPalApiError>({ message: error instanceof Error ? error.message : "Unknown error" }, { status: 500 });
  }
}
