// src/components/layout/DynamicBreadcrumbs.tsx
'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

interface BreadcrumbsProps {
  className?: string;
  homeLabel?: string;
  excludePaths?: string[];
  pathMap?: Record<string, string>;
}

export function DynamicBreadcrumbs({
  className,
  homeLabel = "Home",
  excludePaths = [],
  pathMap = {},
}: BreadcrumbsProps) {
  const pathname = usePathname();
  const [breadcrumbs, setBreadcrumbs] = useState<Array<{ path: string, label: string, isCurrentPage: boolean }>>([]);

  useEffect(() => {
    if (!pathname) return;

    const generateBreadcrumbs = () => {
      const segments = pathname.split('/').filter(Boolean);
      
      // Always include home
      const crumbs = [{ path: '/', label: homeLabel, isCurrentPage: pathname === '/' }];
      
      // Build up breadcrumb paths
      let currentPath = '';
      
      segments.forEach((segment) => {
        currentPath += `/${segment}`;
        
        // Skip excluded paths
        if (excludePaths.includes(segment)) return;
        
        const isCurrentPage = currentPath === pathname;
        const label = pathMap[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);
        
        crumbs.push({
          path: currentPath,
          label,
          isCurrentPage,
        });
      });
      
      return crumbs;
    };
    
    setBreadcrumbs(generateBreadcrumbs());
  }, [pathname, homeLabel, excludePaths, pathMap]);

  if (breadcrumbs.length <= 1) return null;

  return (
    <Breadcrumb className={cn("py-2", className)}>
      <BreadcrumbList>
        {breadcrumbs.map((crumb, index) => (
          <React.Fragment key={crumb.path}>
            {index > 0 && <BreadcrumbSeparator />}
            <BreadcrumbItem>
              {crumb.isCurrentPage ? (
                <BreadcrumbPage>{crumb.label}</BreadcrumbPage>
              ) : (
                <BreadcrumbLink href={crumb.path}>{crumb.label}</BreadcrumbLink>
              )}
            </BreadcrumbItem>
          </React.Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
}