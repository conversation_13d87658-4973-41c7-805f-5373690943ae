// components/blog/single/BlogSidebar.tsx
import Link from 'next/link';
import { Calendar, User, Tag, Hash } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { NewsletterSubscription } from '@/components/newsletter/newsletter-subscription';

interface Author {
  id: string;
  name: string;
  qualifications: string;
}

interface Category {
  id: string;
  name: string;
  slug: string;
}

interface BlogSidebarProps {
  author: Author;
  category: Category;
  keywords: string[];
  publishedDate: Date;
}

export default function BlogSidebar({ 
  author, 
  category, 
  keywords, 
  publishedDate 
}: BlogSidebarProps) {
  const formatDate = (date: Date): string => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };

  return (
    <div className="space-y-6">
      {/* Author Card */}
      <Card className="overflow-hidden">
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center space-x-2">
            <User className="w-4 h-4 text-primary" />
            <span>About the Author</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
              <span className="text-lg font-bold text-primary">
                {author.name.charAt(0).toUpperCase()}
              </span>
            </div>
            <div>
              <h4 className="font-medium text-foreground">{author.name}</h4>
              <p className="text-xs text-muted-foreground line-clamp-2">
                {author.qualifications}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Article Info */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">Article Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-3 text-sm">
            <Calendar className="w-4 h-4 text-primary flex-shrink-0" />
            <div>
              <span className="text-muted-foreground">Published</span>
              <p className="font-medium">
                {formatDate(publishedDate)}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3 text-sm">
            <Tag className="w-4 h-4 text-primary flex-shrink-0" />
            <div>
              <span className="text-muted-foreground">Category</span>
              <p className="font-medium">
                <Link 
                  href={`/blog/category/${category.slug}`}
                  className="hover:text-primary transition-colors"
                >
                  {category.name}
                </Link>
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Keywords/Tags */}
      {keywords.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center space-x-2">
              <Hash className="w-4 h-4 text-primary" />
              <span>Tags</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {keywords.map((keyword, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2.5 py-1 text-xs font-medium bg-primary/10 text-primary rounded-full hover:bg-primary/20 transition-colors cursor-pointer"
                >
                  {keyword}
                </span>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Newsletter Signup */}
      <Card className="bg-gradient-to-br from-primary/5 to-secondary/5 border-primary/20">
        <CardHeader className="pb-3">
          <CardTitle className="text-base">Stay Updated</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            Get the latest articles delivered straight to your inbox.
          </p>
          <NewsletterSubscription
            source="blog-sidebar"
            placeholder="Enter your email"
            buttonText="Subscribe"
            variant="compact"
            showIcon={false}
            className="w-full"
          />
        </CardContent>
      </Card>

      {/* Quick Navigation */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">Quick Links</CardTitle>
        </CardHeader>
        <CardContent>
          <nav className="space-y-2">
            <Link
              href="/blog"
              className="block text-sm text-muted-foreground hover:text-primary transition-colors py-1"
            >
              ← Back to Blog
            </Link>
            <Link
              href={`/blog/category/${category.slug}`}
              className="block text-sm text-muted-foreground hover:text-primary transition-colors py-1"
            >
              More in {category.name}
            </Link>
            {/* <Link
              href="#comments"
              className="block text-sm text-muted-foreground hover:text-primary transition-colors py-1"
            >
              Jump to Comments
            </Link> */}
          </nav>
        </CardContent>
      </Card>
    </div>
  );
}