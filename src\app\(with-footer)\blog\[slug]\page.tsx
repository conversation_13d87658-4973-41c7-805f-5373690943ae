//src/app/(with-footer)/blog/[slug]/page.tsx
import { Metadata } from "next";
import { notFound } from "next/navigation";
import dynamic from "next/dynamic";
import { Suspense } from "react";
import prisma from "@/lib/prisma"; // Adjust path as needed

// Site configuration
const baseUrl = process.env.NEXTAUTH_URL || "http://localhost:3000";
const siteName = "Academic App";
import BlogHeader from "@/components/blog/single/blog-header";
import BlogContent from "@/components/blog/single/blog-content";
import BlogSidebar from "@/components/blog/single/blog-sidebar";
import BlogSchema from "@/components/blog/single/blog-schema";
import LoadingSkeleton from "@/components/ui/LoadingSkeleton";
import { PageViewsTracker } from "@/components/blog/page-views-tracker";
import { insertCallToActionInContent, hasCallToActionInserted } from "@/lib/blog-utils";

// Lazy load components
const RelatedPosts = dynamic(
  () => import("@/components/blog/single/related-posts"),
  {
    loading: () => <div className="animate-pulse bg-muted h-64 rounded-lg" />,
  }
);

const BlogFAQs = dynamic(() => import("@/components/blog/single/blog-faqs"), {
  loading: () => <div className="animate-pulse bg-muted h-48 rounded-lg" />,
});

// Type definitions based on Prisma schema
interface BlogPost {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  title: string;
  body: string;
  slug: string;
  metaTitle: string;
  metaDescription: string;
  imageUrl: string;
  imageAlt: string;
  keywords: string[];
  faqs: string[];
  pageViews: number;
  category: {
    id: string;
    name: string;
    slug: string;
  };
  author: {
    id: string;
    name: string;
    qualifications: string;
  };
}

interface BlogPageProps {
  params: Promise<{
    slug: string;
  }>;
}

async function getBlogPost(slug: string): Promise<BlogPost | null> {
  try {
    const post = await prisma.blog.findUnique({
      where: { slug },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        author: {
          select: {
            id: true,
            name: true,
            qualifications: true,
          },
        },
      },
    });

    if (post) {
      // Process the blog content to insert CTA before returning
      if (!hasCallToActionInserted(post.body)) {
        post.body = insertCallToActionInContent(post.body);
      }
    }

    return post;
  } catch (error) {
    console.error("Error fetching blog post:", error);
    return null;
  }
}

async function getRelatedPosts(
  categoryId: string,
  currentPostId: string,
  limit: number = 4
) {
  try {
    return await prisma.blog.findMany({
      where: {
        categoryId,
        id: { not: currentPostId },
      },
      select: {
        id: true,
        title: true,
        slug: true,
        imageUrl: true,
        imageAlt: true,
        createdAt: true,
        metaDescription: true,
        author: {
          select: {
            name: true,
          },
        },
      },
      take: limit,
      orderBy: {
        createdAt: "desc",
      },
    });
  } catch (error) {
    console.error("Error fetching related posts:", error);
    return [];
  }
}

export async function generateMetadata({
  params,
}: BlogPageProps): Promise<Metadata> {
  // Await params before accessing its properties
  const resolvedParams = await params;
  const post = await getBlogPost(resolvedParams.slug);

  if (!post) {
    return {
      title: "Post Not Found",
      description: "The requested blog post could not be found.",
    };
  }

  const publishedTime = post.createdAt.toISOString();
  const modifiedTime = post.updatedAt
    ? new Date(post.updatedAt).toISOString()
    : publishedTime;

  // Full URLs for SEO
  const postUrl = `${baseUrl}/blog/${post.slug}`;
  const ogImageUrl = post.imageUrl || `${baseUrl}/opengraph-image.png`;

  return {
    title: post.metaTitle || `${post.title} | ${siteName}`,
    description: post.metaDescription,
    keywords: post.keywords?.join(", ") || "",
    authors: [
      { name: post.author.name, url: `${baseUrl}/authors/${post.author.id}` },
    ],
    category: post.category.name,
    creator: post.author.name,
    publisher: siteName,
    metadataBase: new URL(baseUrl),
    openGraph: {
      title: post.metaTitle || post.title,
      description: post.metaDescription,
      type: "article",
      publishedTime,
      modifiedTime,
      authors: [post.author.name],
      url: postUrl,
      siteName: siteName,
      locale: "en_US",
      images: [
        {
          url: ogImageUrl,
          width: 1200,
          height: 630,
          alt: post.imageAlt || post.title,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: post.metaTitle || post.title,
      description: post.metaDescription,
      creator: "@academic",
      images: [ogImageUrl],
    },
    alternates: {
      canonical: postUrl,
    },
    robots: {
      index: true,
      follow: true,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  };
}

export default async function BlogPostPage({ params }: BlogPageProps) {
  // Await params before accessing its properties
  const resolvedParams = await params;
  const post = await getBlogPost(resolvedParams.slug);

  if (!post) {
    notFound();
  }

  const relatedPosts = post
    ? await getRelatedPosts(post.category.id, post.id)
    : [];

  return (
    <>
      <BlogSchema post={post} />

      <article className="min-h-screen bg-background">
        {/* Hero Section */}
        <BlogHeader post={post} />

        {/* Main Content */}
        <div className="container mx-auto px-4 py-8 lg:py-12">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 max-w-7xl mx-auto">
            {/* Main Content Area */}
            <div className="lg:col-span-3">
              <BlogContent post={post} />

              {/* FAQs Section */}
              {post.faqs.length > 0 && (
                <Suspense fallback={<LoadingSkeleton className="h-48 mt-12" />}>
                  <BlogFAQs faqs={post.faqs} />
                </Suspense>
              )}

              {/* Related Posts */}
              {relatedPosts.length > 0 && (
                <Suspense fallback={<LoadingSkeleton className="h-64 mt-12" />}>
                  <RelatedPosts posts={relatedPosts} />
                </Suspense>
              )}

              {/* Page Views Tracker - Only visible to admins */}
              <PageViewsTracker blogSlug={post.slug} />
            </div>

            {/* Sidebar */}
            <aside className="lg:col-span-1">
              <div className="sticky top-8 space-y-6">
                <BlogSidebar
                  author={post.author}
                  category={post.category}
                  keywords={post.keywords}
                  publishedDate={post.createdAt}
                />
              </div>
            </aside>
          </div>
        </div>
      </article>
    </>
  );
}
