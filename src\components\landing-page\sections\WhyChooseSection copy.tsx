// // src/components/landing-page/sections/WhyChooseSection.tsx
// import React from "react";
// import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
// import { Badge } from "@/components/ui/badge";
// import { Progress } from "@/components/ui/progress";
// import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
// import { CheckCircle, Users, Award, Clock, Book, BadgeCheck, <PERSON><PERSON> } from "lucide-react";
// import { FeatureType } from "../types";

// export const WhyChooseSection = () => {
//   const features: FeatureType[] = [
//     {
//       id: 1,
//       title: "Expert Writers",
//       description: "Our team consists of highly qualified writers with advanced degrees across all academic disciplines.",
//       icon: <Users className="h-8 w-8 text-indigo-500" />,
//       stat: {
//         value: "750+",
//         label: "Professional Writers"
//       }
//     },
//     {
//       id: 2,
//       title: "Plagiarism-Free Guarantee",
//       description: "Every paper is thoroughly checked to ensure 100% originality before delivery to our clients.",
//       icon: <BadgeCheck className="h-8 w-8 text-indigo-500" />,
//       stat: {
//         value: "100%",
//         label: "Originality"
//       }
//     },
//     {
//       id: 3,
//       title: "On-Time Delivery",
//       description: "We pride ourselves on meeting deadlines, no matter how tight they may be.",
//       icon: <Clock className="h-8 w-8 text-indigo-500" />,
//       stat: {
//         value: "99.8%",
//         label: "On-time delivery rate"
//       }
//     },
//     {
//       id: 4,
//       title: "Comprehensive Support",
//       description: "Our customer support team is available 24/7 to assist you with any questions or concerns.",
//       icon: <Award className="h-8 w-8 text-indigo-500" />
//     },
//     {
//       id: 5,
//       title: "Subject Matter Expertise",
//       description: "We cover over 50+ academic disciplines with specialized experts in each field.",
//       icon: <Book className="h-8 w-8 text-indigo-500" />,
//       stat: {
//         value: "50+",
//         label: "Academic disciplines"
//       }
//     },
//     {
//       id: 6,
//       title: "Client Satisfaction",
//       description: "Our high customer satisfaction rate speaks to our commitment to quality and excellence.",
//       icon: <PieChart className="h-8 w-8 text-indigo-500" />,
//       stat: {
//         value: "4.9/5",
//         label: "Average customer rating"
//       }
//     }
//   ];

//   return (
//     <section id="why-choose-us" className="py-8 bg-background">
//       <div className="container mx-auto px-6">
//         <div className="text-center mb-16">
//           <Badge variant="outline" className="mb-4 px-4 py-1 border-primary/20 text-primary bg-primary/5">
//             Why Choose Us
//           </Badge>
//           <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
//             Academic Excellence <span className="text-primary">Guaranteed</span>
//           </h2>
//           <p className="text-muted-foreground max-w-2xl mx-auto text-lg">
//             What sets our academic writing service apart from the competition
//           </p>
//         </div>

//         <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
//           {features.map((feature) => (
//             <Card key={feature.id} className="border-border shadow-md hover:shadow-lg transition-shadow">
//               <CardHeader className="pb-4">
//                 <div className="flex justify-between items-start">
//                   <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
//                     {React.cloneElement(feature.icon, { className: "h-8 w-8 text-primary" })}
//                   </div>
//                   {feature.stat && (
//                     <Badge variant="secondary" className="text-lg font-bold py-1 px-3">
//                       {feature.stat.value}
//                     </Badge>
//                   )}
//                 </div>
//                 <CardTitle className="text-xl">{feature.title}</CardTitle>
//               </CardHeader>
//               <CardContent>
//                 <p className="text-muted-foreground">{feature.description}</p>
                
//                 {feature.stat && (
//                   <div className="mt-4">
//                     <div className="flex justify-between text-sm text-muted-foreground mb-1">
//                       <span>{feature.stat.label}</span>
//                       {feature.stat.value.includes("%") && <span>{feature.stat.value}</span>}
//                     </div>
//                     <Progress value={parseInt(feature.stat.value) || 95} className="h-2" />
//                   </div>
//                 )}
//               </CardContent>
//             </Card>
//           ))}
//         </div>

//         <div className="mt-16 max-w-3xl mx-auto">
//           <Collapsible className="bg-card rounded-lg shadow-sm p-6">
//             <CollapsibleTrigger className="flex justify-between items-center w-full">
//               <div className="flex items-center gap-2">
//                 <CheckCircle className="h-5 w-5 text-primary" />
//                 <h3 className="text-lg font-semibold text-foreground">Our Quality Guarantee</h3>
//               </div>
//               <div className="rounded-full bg-muted p-1">
//                 <svg
//                   className="h-5 w-5 transition-transform text-foreground"
//                   fill="none"
//                   stroke="currentColor"
//                   viewBox="0 0 24 24"
//                   xmlns="http://www.w3.org/2000/svg"
//                 >
//                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
//                 </svg>
//               </div>
//             </CollapsibleTrigger>
//             <CollapsibleContent>
//               <div className="pt-4 pl-7 space-y-2 text-foreground">
//                 <p>
//                   We stand behind the quality of our work with our satisfaction guarantee:
//                 </p>
//                 <ul className="list-disc pl-5 space-y-1 text-muted-foreground">
//                   <li>Unlimited free revisions within 14 days</li>
//                   <li>100% money-back guarantee if we can&#39;t meet your requirements</li>
//                   <li>Plagiarism-free papers verified by multiple checking tools</li>
//                   <li>Expert writers with verified credentials</li>
//                   <li>Strict confidentiality and privacy protection</li>
//                 </ul>
//               </div>
//             </CollapsibleContent>
//           </Collapsible>
//         </div>
//       </div>
//     </section>
//   );
// };

// export default WhyChooseSection;