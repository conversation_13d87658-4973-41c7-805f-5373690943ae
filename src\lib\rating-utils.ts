// src/lib/rating-utils.ts

/**
 * Writer Rating System
 *
 * This utility provides functions to calculate and update writer ratings
 * based on objective performance metrics rather than subjective feedback.
 *
 * Rating Scale: 0.0 to 5.0 (with one decimal place precision)
 */

// Constants for rating calculations
const RATING_MIN = 0.0;
const RATING_MAX = 5.0;
const RATING_DEFAULT = 1.0;

// Performance thresholds
const COMPLETED_ORDERS_MILESTONE = 10; // Number of completed orders for first rating boost
const COMPLETED_ORDERS_TIER_SIZE = 25; // Additional completed orders for subsequent rating boosts
const REJECTED_ORDERS_PENALTY_THRESHOLD = 5; // Number of rejected orders before rating penalty

// Rating adjustments
const COMPLETION_BONUS = 0.1; // Rating increase per milestone
const REJECTION_PENALTY = 0.2; // Rating decrease per rejection threshold
const ON_TIME_COMPLETION_BONUS = 0.05; // Small bonus for completing before deadline
const REVISION_MINOR_PENALTY = 0.05; // Small penalty for revisions

/**
 * Calculate writer rating based on performance metrics
 *
 * @param completedOrdersCount Total number of completed orders
 * @param rejectedOrdersCount Total number of rejected orders
 * @param onTimeCompletionsCount Number of orders completed before deadline
 * @param revisionsCount Number of orders that required revision
 * @param currentRating Current rating (defaults to 3.0 if not provided)
 * @returns New calculated rating between 0.0 and 5.0
 */
export function calculateWriterRating(
  completedOrdersCount: number,
  rejectedOrdersCount: number,
  onTimeCompletionsCount: number,
  revisionsCount: number,
  currentRating: number = RATING_DEFAULT
): number {
  let rating = currentRating;

  // Bonus for completed orders milestones
  if (completedOrdersCount >= COMPLETED_ORDERS_MILESTONE) {
    const completionTiers = Math.floor(
      completedOrdersCount / COMPLETED_ORDERS_TIER_SIZE
    );
    rating += completionTiers * COMPLETION_BONUS;
  }

  // Penalty for rejected orders
  if (rejectedOrdersCount >= REJECTED_ORDERS_PENALTY_THRESHOLD) {
    const rejectionTiers = Math.floor(
      rejectedOrdersCount / REJECTED_ORDERS_PENALTY_THRESHOLD
    );
    rating -= rejectionTiers * REJECTION_PENALTY;
  }

  // Bonus for on-time completions (as a percentage of total completed)
  if (completedOrdersCount > 0) {
    const onTimeRatio = onTimeCompletionsCount / completedOrdersCount;
    rating += onTimeRatio * ON_TIME_COMPLETION_BONUS;
  }

  // Minor penalty for revisions (as a percentage of total completed)
  if (completedOrdersCount > 0) {
    const revisionRatio = revisionsCount / completedOrdersCount;
    rating -= revisionRatio * REVISION_MINOR_PENALTY;
  }

  // Ensure rating stays within bounds and has one decimal place
  return Math.max(
    RATING_MIN,
    Math.min(RATING_MAX, parseFloat(rating.toFixed(1)))
  );
}

/**
 * Determine the rating color class based on rating value
 *
 * @param rating Writer's current rating
 * @returns CSS color class name
 */
export function getRatingColorClass(rating: number): string {
  if (rating >= 4.5) return "text-emerald-600";
  if (rating >= 4.0) return "text-green-600";
  if (rating >= 3.5) return "text-lime-600";
  if (rating >= 3.0) return "text-yellow-600";
  if (rating >= 2.5) return "text-amber-600";
  if (rating >= 2.0) return "text-orange-600";
  return "text-red-600";
}

/**
 * Get a descriptive label for the current rating
 *
 * @param rating Writer's current rating
 * @returns Text description of rating level
 */
export function getRatingLabel(rating: number): string {
  if (rating >= 4.5) return "Exceptional";
  if (rating >= 4.0) return "Excellent";
  if (rating >= 3.5) return "Very Good";
  if (rating >= 3.0) return "Good";
  if (rating >= 2.5) return "Satisfactory";
  if (rating >= 2.0) return "Fair";
  if (rating >= 1.0) return "Poor";
  return "Unsatisfactory";
}
