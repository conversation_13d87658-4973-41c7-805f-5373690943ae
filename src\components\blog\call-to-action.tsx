"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { CheckCircle, PenTool } from 'lucide-react';
import Link from 'next/link';
import { motion } from 'framer-motion';

interface CallToActionProps {
  className?: string;
}

export default function CallToAction({ className }: CallToActionProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className={className}
    >
      <Card className="relative overflow-hidden bg-gradient-to-br from-orange-50 via-teal-50 to-orange-50 dark:from-orange-950/20 dark:via-teal-950/20 dark:to-orange-950/20 border-2 border-orange-200/50 dark:border-orange-800/30 shadow-xl">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}
          />
        </div>

        <div className="relative p-8 lg:p-12">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            {/* Left Content */}
            <div className="space-y-6">
              <div className="space-y-4">
                <motion.h2 
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="text-3xl lg:text-4xl font-bold leading-tight"
                >
                  <span className="bg-gradient-to-r from-orange-600 to-orange-500 bg-clip-text text-transparent">
                    Enhance your
                  </span>
                  <br />
                  <span className="bg-gradient-to-r from-teal-600 to-teal-500 bg-clip-text text-transparent">
                    Academic
                  </span>
                  <br />
                  <span className="bg-gradient-to-r from-teal-600 to-teal-500 bg-clip-text text-transparent">
                    Performance
                  </span>
                  <br />
                  <span className="bg-gradient-to-r from-orange-600 to-orange-500 bg-clip-text text-transparent">
                    And Reach Your Goals
                  </span>
                </motion.h2>

                <motion.p 
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                  className="text-lg text-muted-foreground leading-relaxed"
                >
                  See how quick and easy it is to get an exceptional essay with minimal effort on our platform
                </motion.p>
              </div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <Link href="/create-order">
                  <Button 
                    size="lg" 
                    className="bg-gradient-to-r from-teal-600 to-teal-500 hover:from-teal-700 hover:to-teal-600 text-white font-semibold px-8 py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                  >
                    <PenTool className="mr-2 h-5 w-5" />
                    GET HOMEWORK HELP
                  </Button>
                </Link>
              </motion.div>
            </div>

            {/* Right Illustration */}
            <motion.div 
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="relative flex justify-center lg:justify-end"
            >
              <div className="relative">
                {/* Main Card */}
                <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl p-6 w-80 border border-orange-200/30 dark:border-orange-800/30">
                  {/* Header */}
                  <div className="flex items-center justify-between mb-6">
                    <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                  </div>

                  {/* Content Lines with Checkmarks */}
                  <div className="space-y-4">
                    {[1, 2, 3, 4, 5].map((item, index) => (
                      <motion.div 
                        key={item}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.4, delay: 0.6 + (index * 0.1) }}
                        className="flex items-center space-x-3"
                      >
                        <CheckCircle className="h-5 w-5 text-teal-500 flex-shrink-0" />
                        <div className="flex-1">
                          <div className={`h-2 bg-gradient-to-r from-gray-200 to-gray-100 dark:from-gray-600 dark:to-gray-700 rounded-full ${
                            index === 0 ? 'w-full' :
                            index === 1 ? 'w-4/5' :
                            index === 2 ? 'w-3/4' :
                            index === 3 ? 'w-5/6' :
                            'w-2/3'
                          }`} />
                        </div>
                      </motion.div>
                    ))}
                  </div>

                  {/* Bottom Section */}
                  <motion.div 
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 1.1 }}
                    className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700"
                  >
                    <div className="flex items-center space-x-3">
                      <CheckCircle className="h-6 w-6 text-teal-500" />
                      <div className="h-3 bg-gradient-to-r from-teal-400 to-teal-300 rounded-full w-20" />
                    </div>
                  </motion.div>
                </div>

                {/* Character Illustration */}
                <motion.div 
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: 0.8 }}
                  className="absolute -right-8 -bottom-4 z-10"
                >
                  <div className="w-24 h-32 bg-gradient-to-b from-orange-400 to-orange-500 rounded-full relative">
                    {/* Simple character representation */}
                    <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-16 h-16 bg-orange-300 rounded-full" />
                    <div className="absolute top-6 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-white rounded-full" />
                    <div className="absolute top-6 right-4 w-2 h-2 bg-white rounded-full" />
                    <div className="absolute top-10 left-1/2 transform -translate-x-1/2 w-4 h-1 bg-white rounded-full" />
                    
                    {/* Pencil */}
                    <motion.div 
                      initial={{ rotate: -45, scale: 0 }}
                      animate={{ rotate: 0, scale: 1 }}
                      transition={{ duration: 0.4, delay: 1.2 }}
                      className="absolute -top-2 -right-2 w-8 h-2 bg-yellow-400 rounded-full transform rotate-45"
                    >
                      <div className="absolute right-0 top-0 w-2 h-2 bg-gray-600 rounded-full" />
                    </motion.div>
                  </div>
                </motion.div>

                {/* Floating Elements */}
                <motion.div 
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.0, repeat: Infinity, repeatType: "reverse", repeatDelay: 2 }}
                  className="absolute -top-4 -left-4 w-8 h-8 bg-orange-200 dark:bg-orange-800 rounded-full flex items-center justify-center"
                >
                  <CheckCircle className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                </motion.div>

                <motion.div 
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.3, repeat: Infinity, repeatType: "reverse", repeatDelay: 3 }}
                  className="absolute top-8 -right-12 w-6 h-6 bg-teal-200 dark:bg-teal-800 rounded-full flex items-center justify-center"
                >
                  <CheckCircle className="h-3 w-3 text-teal-600 dark:text-teal-400" />
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </Card>
    </motion.div>
  );
}
