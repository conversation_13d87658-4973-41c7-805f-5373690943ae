import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authConfig as authOptions } from "@/auth";
import prisma from "@/lib/prisma";
import { ChatParticipantRole } from "@prisma/client";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ assignmentId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { assignmentId } = await params;
    const body = await request.json();
    const {
      content,
      type = "TEXT",
      targetParticipantId,
      conversationType,
    } = body;

    if (!content?.trim()) {
      return NextResponse.json(
        { success: false, message: "Message content is required" },
        { status: 400 }
      );
    }

    // Validate message type
    if (!["TEXT", "EMOJI", "SYSTEM"].includes(type)) {
      return NextResponse.json(
        { success: false, message: "Invalid message type" },
        { status: 400 }
      );
    }

    // Get the assignment to verify access
    const assignment = await prisma.assignment.findUnique({
      where: { id: assignmentId },
      select: {
        id: true,
        clientId: true,
        assignedWriterId: true,
      },
    });

    if (!assignment) {
      return NextResponse.json(
        { success: false, message: "Assignment not found" },
        { status: 404 }
      );
    }

    // Get user role
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (!user) {
      return NextResponse.json(
        { success: false, message: "User not found" },
        { status: 404 }
      );
    }

    // Check if user has access to this chat
    const hasAccess =
      user.role === "ADMIN" ||
      assignment.clientId === session.user.id ||
      assignment.assignedWriterId === session.user.id;

    if (!hasAccess) {
      return NextResponse.json(
        { success: false, message: "Access denied" },
        { status: 403 }
      );
    }

    // Validate targetParticipantId and conversationType for admin messages
    if (user.role === "ADMIN") {
      if (!targetParticipantId || !conversationType) {
        return NextResponse.json(
          {
            success: false,
            message:
              "Admin messages require targetParticipantId and conversationType",
          },
          { status: 400 }
        );
      }

      // Validate that targetParticipantId is valid for this assignment
      if (
        conversationType === "client" &&
        targetParticipantId !== assignment.clientId
      ) {
        return NextResponse.json(
          {
            success: false,
            message: "Invalid target participant for client conversation",
          },
          { status: 400 }
        );
      }

      if (
        conversationType === "writer" &&
        targetParticipantId !== assignment.assignedWriterId
      ) {
        return NextResponse.json(
          {
            success: false,
            message: "Invalid target participant for writer conversation",
          },
          { status: 400 }
        );
      }
    }

    // Find or create chat
    let chat = await prisma.chat.findUnique({
      where: { assignmentId },
      select: { id: true },
    });

    if (!chat) {
      // Create new chat with participants
      const participants: { userId: string; role: ChatParticipantRole }[] = [
        { userId: assignment.clientId, role: "CLIENT" },
      ];

      // Add writer if assigned
      if (assignment.assignedWriterId) {
        participants.push({
          userId: assignment.assignedWriterId,
          role: "WRITER",
        });
      }

      // Add admin (find first admin user)
      const adminUser = await prisma.user.findFirst({
        where: { role: "ADMIN" },
        select: { id: true },
      });

      if (adminUser) {
        participants.push({
          userId: adminUser.id,
          role: "ADMIN",
        });
      }

      chat = await prisma.chat.create({
        data: {
          assignmentId,
          participants: {
            create: participants,
          },
        },
        select: { id: true },
      });
    }

    // Create the message with conversation context
    const message = await prisma.message.create({
      data: {
        chatId: chat.id,
        senderId: session.user.id,
        content: content.trim(),
        type: type as "TEXT" | "EMOJI" | "SYSTEM",
        isRead: false,
        // Add conversation context fields
        targetParticipantId: targetParticipantId || null,
        conversationType: conversationType || null,
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: message,
    });
  } catch (error) {
    console.error("Error creating message:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ assignmentId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { assignmentId } = await params;
    const body = await request.json();
    const { messageIds } = body;

    if (!Array.isArray(messageIds) || messageIds.length === 0) {
      return NextResponse.json(
        { success: false, message: "Message IDs are required" },
        { status: 400 }
      );
    }

    // Get the chat
    const chat = await prisma.chat.findUnique({
      where: { assignmentId },
      include: {
        assignment: {
          select: {
            clientId: true,
            assignedWriterId: true,
          },
        },
      },
    });

    if (!chat) {
      return NextResponse.json(
        { success: false, message: "Chat not found" },
        { status: 404 }
      );
    }

    // Get user role
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (!user) {
      return NextResponse.json(
        { success: false, message: "User not found" },
        { status: 404 }
      );
    }

    // Check if user has access to this chat
    const hasAccess =
      user.role === "ADMIN" ||
      chat.assignment.clientId === session.user.id ||
      chat.assignment.assignedWriterId === session.user.id;

    if (!hasAccess) {
      return NextResponse.json(
        { success: false, message: "Access denied" },
        { status: 403 }
      );
    }

    // Mark messages as read
    await prisma.message.updateMany({
      where: {
        id: { in: messageIds },
        chatId: chat.id,
        senderId: { not: session.user.id }, // Don't mark own messages as read
      },
      data: {
        isRead: true,
      },
    });

    return NextResponse.json({
      success: true,
      message: "Messages marked as read",
    });
  } catch (error) {
    console.error("Error marking messages as read:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}
